package com.three.http.callback;

/**
 * Created by three on 2018/1/25.
 */

public class Result<T> {
    public int code;
    public String msg;
    public T data;
    public Object response;

    public Result() {
    }

    public Result(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Result(int code, String msg, T data, Object response) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.response = response;
    }

    /**
     * @return code == 200 成功，否则失败
     */
    public boolean isOK() {
        return code == 200;
    }
}
