package com.wepie.module.medal.rank

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.three.http.callback.LifeDataCallback
import com.three.http.callback.Result
import com.wejoy.weplay.ex.lifecycle.LifeViewModel
import com.wepie.module.medal.base.RequestStatus
import com.wepie.module.medal.netapi.MedalHttpApi

internal class MedalRankViewModel : LifeViewModel() {

    private val _rankListLiveData = MutableLiveData<MedalRankList>()
    val rankListLiveData: LiveData<MedalRankList> = _rankListLiveData

    private val _myRankLiveData = MutableLiveData<MedalRankDetail>()
    val myRankLiveData: LiveData<MedalRankDetail> = _myRankLiveData

    private val _statusLiveData = MutableLiveData<RequestStatus>()
    val statusLiveData: LiveData<RequestStatus> = _statusLiveData

    private val _refreshStatusLiveData = MutableLiveData<RequestStatus>()
    val refreshStatusLiveData: LiveData<RequestStatus> = _refreshStatusLiveData

    private var page = 1

    private val refreshCallback = object : LifeDataCallback<MedalRankInfo>(this) {
        override fun onSuccess(result: Result<MedalRankInfo>?) {
            _refreshStatusLiveData.postValue(RequestStatus.REQUEST_SUCCESS)
        }

        override fun onFail(code: Int, msg: String?) {
            _refreshStatusLiveData.postValue(RequestStatus.fail(code, msg))
        }
    }

    fun initData() {
        _statusLiveData.value = RequestStatus.REQUEST_START
        requestData(1, object : LifeDataCallback<MedalRankInfo>(this) {

            override fun onSuccess(result: Result<MedalRankInfo>) {
                _statusLiveData.postValue(RequestStatus.REQUEST_SUCCESS)
            }

            override fun onFail(code: Int, msg: String?) {
                _statusLiveData.postValue(RequestStatus.fail(code, msg))
            }

        })
    }

    fun nextData() {
        _refreshStatusLiveData.value = RequestStatus.REQUEST_START
        requestData(page + 1, refreshCallback)
    }

    fun refresh() {
        _refreshStatusLiveData.value = RequestStatus.REQUEST_START
        requestData(1, refreshCallback)
    }

    private fun requestData(currentPage: Int, callback: LifeDataCallback<MedalRankInfo>? = null) {
        val isRefresh = currentPage == 1
        MedalHttpApi.getMedalRankInfo(
            currentPage,
            PAGE_SIZE,
            object : LifeDataCallback<MedalRankInfo>(this) {

                override fun onSuccess(result: Result<MedalRankInfo>) {
                    val info = result.data
                    _rankListLiveData.postValue(
                        MedalRankList(
                            isRefresh,
                            info.rankList.size == PAGE_SIZE,
                            info.rankList
                        )
                    )
                    _myRankLiveData.postValue(info.myRankInfo)
                    page = currentPage
                    callback?.onSuccess(result)
                }

                override fun onFail(code: Int, msg: String?) {
                    _rankListLiveData.postValue(
                        MedalRankList(
                            isRefresh,
                            true,
                            emptyList()
                        )
                    )
                    callback?.onFail(code, msg)
                }

            })
    }

    companion object {
        const val PAGE_SIZE = 30
    }

}

data class MedalRankList(
    val isRefresh: Boolean,
    val hasMore: Boolean,
    val data: List<MedalRankDetail>
)