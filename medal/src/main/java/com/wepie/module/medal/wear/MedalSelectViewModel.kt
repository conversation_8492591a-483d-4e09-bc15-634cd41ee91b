package com.wepie.module.medal.wear

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.huiwan.user.UserService
import com.three.http.callback.LifeDataCallback
import com.three.http.callback.Result
import com.wejoy.weplay.ex.lifecycle.LifeViewModel
import com.wepie.module.medal.MedalViewModel
import com.wepie.module.medal.base.AwardedMedalInfo
import com.wepie.module.medal.base.RequestStatus
import com.wepie.module.medal.netapi.MedalHttpApi

class MedalSelectViewModel : LifeViewModel() {

    private val _statusLiveData = MutableLiveData<RequestStatus>()
    val statusLiveData: LiveData<RequestStatus> = _statusLiveData

    private val _selectedMedalsLiveData =
        MutableLiveData<MutableList<AwardedMedalInfo>>(ArrayList())
    val selectedMedalsLiveData: LiveData<out List<AwardedMedalInfo>> = _selectedMedalsLiveData

    fun wearMedals(viewModel: MedalViewModel) {
        val list = selectedMedalsLiveData.value ?: emptyList()
        val params = ArrayList<Int>(list.size)
        list.forEach {
            params.add(it.medalId)
        }
        _statusLiveData.value = RequestStatus.REQUEST_START
        MedalHttpApi.wearMedal(params, object : LifeDataCallback<Any>(this) {
            override fun onSuccess(result: Result<Any>) {
                viewModel.wearMedals(list)
                UserService.get().refreshSelfInfo(null)
                _statusLiveData.value = RequestStatus.REQUEST_SUCCESS
            }

            override fun onFail(code: Int, msg: String?) {
                _statusLiveData.value = RequestStatus.fail(code, msg)
            }
        })
    }

    fun selectMedals(list: List<AwardedMedalInfo>) {
        val data = _selectedMedalsLiveData.value ?: ArrayList()
        data.clear()
        data.addAll(list)
        _selectedMedalsLiveData.postValue(data)
    }

    fun updateMedalState(info: AwardedMedalInfo, isSelect: Boolean) {
        val data = _selectedMedalsLiveData.value ?: ArrayList()
        if (isSelect) {
            data.add(info)
        } else {
            val iterator = data.iterator()
            while (iterator.hasNext()) {
                val d = iterator.next()
                if (d.medalId == info.medalId) {
                    iterator.remove()
                    break
                }
            }
        }
        _selectedMedalsLiveData.postValue(data)
    }
}