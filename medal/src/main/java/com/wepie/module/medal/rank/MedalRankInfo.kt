package com.wepie.module.medal.rank

import com.google.gson.annotations.SerializedName
import com.wepie.module.medal.base.AwardedMedalInfo

/**
 * 排行榜
 * /ranking/get_medal_rank
 */
class MedalRankInfo {
    /**
     * 我的信息
     */
    @SerializedName("my_rank_info")
    var myRankInfo = MedalRankDetail()

    /**
     * 我的排名 0 无排名
     */
    @SerializedName("my_rank")
    var myRank = 0

    /**
     * 排名数据
     */
    @SerializedName("rank_infos")
    val rankList: List<MedalRankDetail> = emptyList()
}

class MedalRankDetail {
    @SerializedName("uid")
    val uid = -1

    /**
     * 排名
     */
    @SerializedName("rank")
    var rank = 0

    /**
     * 昵称
     */
    @SerializedName("nickname")
    var nickName = ""

    /**
     * 头像
     */
    @SerializedName("head_img_url")
    var headImg = ""

    /**
     * 头像框
     */
    @SerializedName("picture_frame")
    var pictureFrame: Int = 0

    /**
     * 当前佩戴勋章
     */
    @SerializedName("wearing_medal")
    var wearingMedalDetailList: List<AwardedMedalInfo> = emptyList()

    /**
     * 分值
     */
    @SerializedName("score")
    val score = 0
}