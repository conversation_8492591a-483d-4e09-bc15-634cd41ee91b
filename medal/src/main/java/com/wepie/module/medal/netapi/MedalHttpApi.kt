package com.wepie.module.medal.netapi

import com.huiwan.user.UserService
import com.three.http.callback.DataCallback
import com.three.http.core.HttpUtil
import com.three.http.core.KtResult
import com.three.http.core.postSuspend
import com.wepie.module.medal.detail.UserMedalDetailInfo
import com.wepie.module.medal.detail.UserWearingMedal
import com.wepie.module.medal.rank.MedalRankInfo
import com.wepie.module.medal.wear.MedalSceneInfo

/**
 * 勋章请求api类
 * <AUTHOR>
 * 2022/3/14
 */
object MedalHttpApi {

    //获取用户当前佩戴的勋章
    private const val MEDAL_GET_USER_WEARING = "/medal/get_user_wearing_medal"

    //获取用户勋章详细信息
    private const val MEDAL_GET_DETAIL_INFO = "/medal/get_user_medal_info"

    /**
     * 获取勋章使用场景
     */
    private const val MEDAL_SHOW_CONFIG = "/const_api_v1/get_medal_show_config"

    /**
     * 配置勋章
     */
    private const val MEDAL_WEAR_MEDAL = "/medal/wear_medal"

    /**
     * 排行榜
     */
    private const val MEDAL_RANKING = "/ranking/get_medal_rank"

    /**
     * 获取用户当前佩戴的勋章
     *
     * @param uid
     * @param callback
     */
    suspend fun getUserWearingMedal(uid: Int): KtResult<UserWearingMedal> {
        val builder = HttpUtil.newBuilder()
            .uri(MEDAL_GET_USER_WEARING)
            .addParam("uid", UserService.get().loginUid.toString())
            .addParam("target_uid", uid.toString())
        return builder.build().postSuspend<UserWearingMedal>()
    }

    /**
     * 获取用户勋章详情
     *
     * @param targetUid 目标用户
     * @param uid
     * @param callback
     */
    suspend fun getUserMedalDetailInfo(
        targetUid: Int, uid: Int, notTriggerRecent: Boolean = false
    ): KtResult<UserMedalDetailInfo> {
        val builder = HttpUtil.newBuilder()
            .uri(MEDAL_GET_DETAIL_INFO)
            .addParam("uid", uid.toString())
            .addParam("not_trigger_recent", notTriggerRecent.toString())
            .addParam("target_uid", targetUid.toString())
        return builder.build().postSuspend<UserMedalDetailInfo>()
    }

    /**
     * 获取勋章使用场景
     */
    suspend fun getMedalUsingScene(): KtResult<MedalSceneInfo> {
        val builder = HttpUtil.newBuilder()
        builder.uri = MEDAL_SHOW_CONFIG
        return builder.build().postSuspend<MedalSceneInfo>()
    }

    /**
     * 配置勋章
     */
    fun wearMedal(medalIdList: List<Int>, callback: DataCallback<Any>) {
        val builder = HttpUtil.newBuilder()
        builder.uri = MEDAL_WEAR_MEDAL
        builder.params["medal_id_list"] = medalIdList.toString()
        builder.build().post(callback)
    }

    /**
     * 获取排行榜数据
     */
    fun getMedalRankInfo(page: Int, pageSize: Int, callback: DataCallback<MedalRankInfo>) {
        val builder = HttpUtil.newBuilder()
        builder.uri = MEDAL_RANKING
        builder.params["uid"] = UserService.get().loginUid.toString()
        builder.params["page"] = page.toString()
        builder.params["page_size"] = pageSize.toString()
        builder.build().post(callback)
    }
}