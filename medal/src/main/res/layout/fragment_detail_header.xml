<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/medal_detail_head_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/medal_detail_head"
        android:layout_width="@dimen/wp64"
        android:layout_height="@dimen/wp64"
        android:layout_marginStart="@dimen/wp20"
        android:layout_marginTop="@dimen/wp10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/medal_empty_icon" />

    <com.huiwan.decorate.NameTextView
        android:id="@+id/user_name_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/wp14"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxWidth="@dimen/wp100"
        android:maxLines="1"
        android:textColor="@color/medal_user_name_color"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/medal_user_wearing_mil"
        app:layout_constraintEnd_toStartOf="@+id/user_vip_label_view"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/medal_detail_head"
        app:layout_constraintTop_toTopOf="@+id/medal_detail_head"
        tools:text="吴呜呜12313122123" />

    <com.huiwan.decorate.vip.VipLabelView
        android:id="@+id/user_vip_label_view"
        android:layout_width="@dimen/vip_icon_width"
        android:layout_height="@dimen/vip_icon_height"
        android:layout_marginStart="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/user_name_text"
        app:layout_constraintEnd_toStartOf="@+id/user_charm_iv"
        app:layout_constraintStart_toEndOf="@+id/user_name_text"
        app:layout_constraintTop_toTopOf="@+id/user_name_text"
        app:layout_goneMarginStart="@dimen/wp5"
        tools:src="@drawable/vip_label_1" />

    <ImageView
        android:id="@+id/user_charm_iv"
        android:layout_width="@dimen/wp20"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/wp5"
        app:layout_constraintBottom_toBottomOf="@+id/user_name_text"
        app:layout_constraintEnd_toStartOf="@+id/wear_medal_bt"
        app:layout_constraintStart_toEndOf="@+id/user_vip_label_view"
        app:layout_constraintTop_toTopOf="@+id/user_name_text"
        tools:src="@drawable/user_charm_level_18" />

    <com.wepie.module.medal.base.MedalIconListView
        android:id="@+id/medal_user_wearing_mil"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:hide_empty_medal="false"
        app:layout_constraintBottom_toBottomOf="@+id/medal_detail_head"
        app:layout_constraintStart_toStartOf="@+id/user_name_text"
        app:layout_constraintTop_toBottomOf="@+id/user_name_text"
        app:medal_empty_src="@drawable/ic_default_medal_stage"
        app:medal_width_height="@dimen/wp30" />

    <TextView
        android:id="@+id/wear_medal_bt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/wp20"
        android:background="@drawable/wear_medal_white_round_shape"
        android:clickable="true"
        android:paddingStart="@dimen/wp16"
        android:paddingTop="@dimen/wp10"
        android:paddingEnd="@dimen/wp16"
        android:paddingBottom="@dimen/wp10"
        android:text="@string/medal_wear_medal"
        android:textColor="@color/medal_wear_text_color"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/medal_detail_head"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/medal_detail_head"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/medal_rank_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/wp10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/medal_detail_head"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/wp20"
            android:layout_marginTop="@dimen/wp7"
            android:layout_marginEnd="@dimen/wp20"
            android:background="@drawable/medal_ranking_round_background"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/wp16"
            android:paddingTop="@dimen/wp11"
            android:paddingEnd="@dimen/wp16"
            android:paddingBottom="@dimen/wp9">

            <TextView
                android:id="@+id/medal_self_ranking_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_medal_ranking"
                android:drawablePadding="@dimen/wp5"
                android:textColor="@color/white"
                android:textSize="@dimen/wp14"
                android:textStyle="bold"
                tools:text="@string/medal_ranking" />

            <View
                android:layout_width="@dimen/wp1"
                android:layout_height="@dimen/wp20"
                android:layout_marginStart="@dimen/wp12"
                android:layout_marginEnd="@dimen/wp12"
                android:background="@drawable/medal_detail_ranking_segment_line" />

            <TextView
                android:id="@+id/medal_self_points_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_ranking_point"
                android:drawablePadding="@dimen/wp2"
                android:textColor="@color/medal_text_tans80_color"
                android:textSize="@dimen/wp12"
                tools:text="3221" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/wp36"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/medal_ranking_num1_layout"
                android:layout_width="@dimen/wp32"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible">

                <com.huiwan.widget.CustomCircleImageView
                    android:id="@+id/medal_ranking_num1_civ"
                    android:layout_width="@dimen/wp32"
                    android:layout_height="@dimen/wp32"
                    app:civ_border_color="@color/white"
                    app:civ_border_width="@dimen/wp1"
                    tools:src="@color/action_bar_red_packet_title" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="30dp"
                    android:layout_height="@dimen/wp15"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/wp25"
                    android:background="@drawable/medal_ranking_num1_gradient_bg"
                    android:padding="@dimen/wp3"
                    app:srcCompat="@drawable/ic_medal_detail_ranking_1" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/medal_ranking_num2_layout"
                android:layout_width="@dimen/wp32"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/wp10"
                android:visibility="gone"
                tools:visibility="visible">

                <com.huiwan.widget.CustomCircleImageView
                    android:id="@+id/medal_ranking_num2_civ"
                    android:layout_width="@dimen/wp32"
                    android:layout_height="@dimen/wp32"
                    app:civ_border_color="@color/white"
                    app:civ_border_width="@dimen/wp1"
                    tools:src="@color/action_bar_red_packet_title" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="@dimen/wp30"
                    android:layout_height="@dimen/wp15"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/wp25"
                    android:background="@drawable/medal_ranking_num2_gradient_bg"
                    android:padding="@dimen/wp3"
                    app:srcCompat="@drawable/ic_medal_detail_ranking_2" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/medal_ranking_num3_layout"
                android:layout_width="@dimen/wp32"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/wp10"
                android:visibility="gone"
                tools:visibility="visible">

                <com.huiwan.widget.CustomCircleImageView
                    android:id="@+id/medal_ranking_num3_civ"
                    android:layout_width="@dimen/wp32"
                    android:layout_height="@dimen/wp32"
                    app:civ_border_color="@color/white"
                    app:civ_border_width="@dimen/wp1"
                    tools:src="@color/action_bar_red_packet_title" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="@dimen/wp30"
                    android:layout_height="@dimen/wp15"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/wp25"
                    android:background="@drawable/medal_ranking_num3_gradient_bg"
                    android:padding="@dimen/wp3"
                    app:srcCompat="@drawable/ic_medal_detail_ranking_3" />

            </FrameLayout>

        </LinearLayout>

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>