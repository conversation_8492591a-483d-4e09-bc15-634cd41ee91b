<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/medal_title_lay"
        layout="@layout/layout_medal_text_preview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/wp110"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/medal_icon_preview_lay"
        layout="@layout/layout_medal_icon_preview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/medal_real_state_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/medal_icon_preview_lay"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/wp4"
        android:gravity="center"
        android:textColor="@color/white_alpha50"
        android:textSize="@dimen/wp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/medal_icon_preview_lay"
        tools:text="已点亮" />

</androidx.constraintlayout.widget.ConstraintLayout>