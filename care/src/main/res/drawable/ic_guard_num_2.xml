<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="100dp"
    android:height="164dp"
    android:viewportWidth="100"
    android:viewportHeight="164">
  <path
      android:pathData="M26.97,15.98C32.77,10.55 40.94,7.17 50,7.17C59.06,7.17 67.23,10.55 73.03,15.98L96.43,18.51C98.46,18.73 100,20.45 100,22.49V112.72C99.42,116.91 90.49,127.76 75.91,131.68C61.82,135.48 53.8,141.36 50,147C46.2,141.36 38.18,135.48 24.09,131.68C9.51,127.76 0.58,116.91 0,112.72V22.49C0,20.45 1.54,18.73 3.57,18.51L26.97,15.98Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="50"
          android:startY="7.17"
          android:endX="50"
          android:endY="147"
          android:type="linear">
        <item android:offset="0" android:color="#FFD3DCE5"/>
        <item android:offset="1" android:color="#FFF3F9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M26.97,15.98C32.77,10.55 40.94,7.17 50,7.17C59.06,7.17 67.23,10.55 73.03,15.98L96.43,18.51C98.46,18.73 100,20.45 100,22.49V112.72C99.42,116.91 90.49,127.76 75.91,131.68C61.82,135.48 53.8,141.36 50,147C46.2,141.36 38.18,135.48 24.09,131.68C9.51,127.76 0.58,116.91 0,112.72V22.49C0,20.45 1.54,18.73 3.57,18.51L26.97,15.98Z"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M26.97,15.98L27.08,16.97L27.41,16.94L27.65,16.71L26.97,15.98ZM73.03,15.98L72.35,16.71L72.59,16.94L72.92,16.97L73.03,15.98ZM96.43,18.51L96.32,19.51L96.43,18.51ZM100,112.72L100.99,112.86L101,112.79V112.72H100ZM75.91,131.68L75.65,130.72L75.91,131.68ZM50,147L49.17,147.56L50,148.79L50.83,147.56L50,147ZM24.09,131.68L24.35,130.72L24.09,131.68ZM0,112.72H-1V112.79L-0.99,112.86L0,112.72ZM3.57,18.51L3.46,17.52L3.57,18.51ZM27.65,16.71C33.26,11.46 41.19,8.17 50,8.17V6.17C40.69,6.17 32.27,9.64 26.29,15.25L27.65,16.71ZM50,8.17C58.81,8.17 66.74,11.46 72.35,16.71L73.71,15.25C67.73,9.64 59.31,6.17 50,6.17V8.17ZM96.54,17.52L73.14,14.98L72.92,16.97L96.32,19.51L96.54,17.52ZM101,22.49C101,19.93 99.08,17.79 96.54,17.52L96.32,19.51C97.85,19.67 99,20.96 99,22.49H101ZM101,112.72V22.49H99V112.72H101ZM76.17,132.65C83.63,130.64 89.64,126.86 93.87,123.04C95.99,121.14 97.67,119.21 98.86,117.47C100.04,115.75 100.81,114.14 100.99,112.86L99.01,112.58C98.9,113.4 98.33,114.71 97.22,116.33C96.12,117.93 94.54,119.74 92.53,121.56C88.51,125.19 82.77,128.8 75.65,130.72L76.17,132.65ZM50.83,147.56C54.45,142.18 62.22,136.4 76.17,132.65L75.65,130.72C61.42,134.55 53.15,140.53 49.17,146.44L50.83,147.56ZM23.83,132.65C37.78,136.4 45.55,142.18 49.17,147.56L50.83,146.44C46.85,140.53 38.58,134.55 24.35,130.72L23.83,132.65ZM-0.99,112.86C-0.81,114.14 -0.04,115.75 1.14,117.47C2.33,119.21 4.01,121.14 6.13,123.04C10.36,126.86 16.37,130.64 23.83,132.65L24.35,130.72C17.23,128.8 11.49,125.19 7.47,121.56C5.46,119.74 3.88,117.93 2.78,116.33C1.67,114.71 1.1,113.4 0.99,112.58L-0.99,112.86ZM-1,22.49V112.72H1V22.49H-1ZM3.46,17.52C0.92,17.79 -1,19.93 -1,22.49H1C1,20.96 2.15,19.67 3.68,19.51L3.46,17.52ZM26.86,14.98L3.46,17.52L3.68,19.51L27.08,16.97L26.86,14.98Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="50"
            android:startY="7.17"
            android:endX="50"
            android:endY="147"
            android:type="linear">
          <item android:offset="0" android:color="#FFA0B1C2"/>
          <item android:offset="1" android:color="#FFE1E9F0"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M95.62,21.44L95.68,20.95L95.62,21.44ZM71.4,18.82L71.07,19.19L71.19,19.3L71.35,19.32L71.4,18.82ZM97.41,111.21V111.71H97.91V111.21H97.41ZM97.32,111.21V110.71H96.93L96.83,111.09L97.32,111.21ZM74.66,128.66L74.79,129.14L74.66,128.66ZM50.2,143.12L49.79,143.4L50.2,144.01L50.62,143.4L50.2,143.12ZM25.74,128.66L25.61,129.14L25.74,128.66ZM3.09,111.21L3.58,111.09L3.48,110.71H3.09V111.21ZM3,111.21H2.5V111.71H3V111.21ZM4.78,21.44L4.73,20.95H4.73L4.78,21.44ZM29.01,18.82L29.06,19.32L29.22,19.3L29.34,19.19L29.01,18.82ZM95.68,20.95L71.46,18.32L71.35,19.32L95.57,21.94L95.68,20.95ZM97.91,23.43C97.91,22.15 96.95,21.08 95.68,20.95L95.57,21.94C96.33,22.02 96.91,22.66 96.91,23.43H97.91ZM97.91,110.75V23.43H96.91V110.75H97.91ZM97.91,111.21V110.75H96.91V111.21H97.91ZM97.32,111.71H97.41V110.71H97.32V111.71ZM74.79,129.14C81.51,127.33 86.97,123.99 90.89,120.56C92.85,118.85 94.43,117.11 95.59,115.52C96.75,113.94 97.51,112.48 97.8,111.33L96.83,111.09C96.58,112.06 95.9,113.4 94.78,114.93C93.67,116.45 92.14,118.14 90.23,119.81C86.41,123.15 81.08,126.41 74.53,128.18L74.79,129.14ZM50.62,143.4C54.12,138.2 61.56,132.71 74.79,129.14L74.53,128.18C61.16,131.78 53.47,137.38 49.79,142.84L50.62,143.4ZM25.61,129.14C38.85,132.71 46.29,138.2 49.79,143.4L50.62,142.84C46.94,137.38 39.25,131.78 25.87,128.18L25.61,129.14ZM2.61,111.33C2.9,112.48 3.66,113.94 4.82,115.52C5.98,117.11 7.56,118.85 9.52,120.56C13.44,123.99 18.9,127.33 25.61,129.14L25.87,128.18C19.33,126.41 14,123.15 10.18,119.81C8.27,118.14 6.74,116.45 5.62,114.93C4.5,113.4 3.82,112.06 3.58,111.09L2.61,111.33ZM3,111.71H3.09V110.71H3V111.71ZM2.5,110.75V111.21H3.5V110.75H2.5ZM2.5,23.43V110.75H3.5V23.43H2.5ZM4.73,20.95C3.46,21.08 2.5,22.15 2.5,23.43H3.5C3.5,22.66 4.08,22.02 4.84,21.94L4.73,20.95ZM28.95,18.32L4.73,20.95L4.84,21.94L29.06,19.32L28.95,18.32ZM29.34,19.19C34.68,14.44 42.05,11.5 50.2,11.5V10.5C41.81,10.5 34.2,13.53 28.67,18.45L29.34,19.19ZM50.2,11.5C58.36,11.5 65.73,14.44 71.07,19.19L71.73,18.45C66.21,13.53 58.6,10.5 50.2,10.5V11.5Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="50.2"
          android:startY="10.69"
          android:endX="50.2"
          android:endY="144.02"
          android:type="linear">
        <item android:offset="0" android:color="#FFB9C7D6"/>
        <item android:offset="0.42" android:color="#FFC6D6E6"/>
        <item android:offset="1" android:color="#FFEAEEF7"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M3.01,28.23C3.06,28.28 7.79,23.39 10.14,21L3.23,21.91C3.21,23.8 2.95,28.17 3.01,28.23Z"
      android:fillColor="#BCCAD9"/>
  <path
      android:pathData="M97.13,28.23C97.08,28.28 92.35,23.39 90,21L96.91,21.91C96.93,23.8 97.19,28.17 97.13,28.23Z"
      android:fillColor="#BCCAD9"/>
  <path
      android:strokeWidth="1"
      android:pathData="M22.79,131.63C25.26,131.97 28.12,133.03 31.05,135.48C33.73,137.71 37.55,139.22 40.96,140.11C41.82,140.33 42.66,140.52 43.45,140.67C42.22,140.71 40.86,140.72 39.48,140.67C37.37,140.59 35.22,140.38 33.36,139.96C31.48,139.53 29.98,138.91 29.11,138.09C28.58,137.58 28.11,136.89 27.56,136.1L27.56,136.1C27.02,135.31 26.42,134.45 25.65,133.65C24.89,132.86 23.97,132.15 22.79,131.63Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="21.47"
          android:startY="132.5"
          android:endX="44.14"
          android:endY="141.74"
          android:type="linear">
        <item android:offset="0" android:color="#FFB7C7D7"/>
        <item android:offset="0.46" android:color="#FFF0F6FC"/>
        <item android:offset="1" android:color="#FFB7C7D7"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="20.11"
          android:startY="131.71"
          android:endX="41.57"
          android:endY="141.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFB3C5D8"/>
        <item android:offset="0.51" android:color="#FFD2DDE8"/>
        <item android:offset="1" android:color="#FFB3C5D8"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M77.21,131.63C74.74,131.97 71.88,133.03 68.95,135.48C66.27,137.71 62.45,139.22 59.04,140.11C58.18,140.33 57.34,140.52 56.55,140.67C57.78,140.71 59.14,140.72 60.52,140.67C62.63,140.59 64.78,140.38 66.64,139.96C68.52,139.53 70.02,138.91 70.89,138.09C71.42,137.58 71.89,136.89 72.44,136.1L72.44,136.1C72.98,135.31 73.58,134.45 74.35,133.65C75.11,132.86 76.03,132.15 77.21,131.63Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="78.53"
          android:startY="132.5"
          android:endX="55.86"
          android:endY="141.74"
          android:type="linear">
        <item android:offset="0" android:color="#FFB7C7D7"/>
        <item android:offset="0.46" android:color="#FFF0F6FC"/>
        <item android:offset="1" android:color="#FFB7C7D7"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="79.89"
          android:startY="131.71"
          android:endX="58.43"
          android:endY="141.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFB3C5D8"/>
        <item android:offset="0.51" android:color="#FFD2DDE8"/>
        <item android:offset="1" android:color="#FFB3C5D8"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M43.13,143.61C46.16,142.49 48.65,140.23 50.06,137.3C51.46,140.23 53.95,142.49 56.98,143.61C53.71,144.88 51.21,147.68 50.34,151.15L50.06,152.29L49.77,151.15C48.9,147.68 46.4,144.88 43.13,143.61Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="50.06"
          android:startY="136.02"
          android:endX="50.06"
          android:endY="154.36"
          android:type="linear">
        <item android:offset="0.31" android:color="#FFEFF6FC"/>
        <item android:offset="1" android:color="#FF8DAFD3"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="50.06"
          android:startY="136.02"
          android:endX="50.06"
          android:endY="156.7"
          android:type="linear">
        <item android:offset="0" android:color="#FFD4E0EC"/>
        <item android:offset="0.64" android:color="#FF98AEC8"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M50.06,141.36C50.68,142.43 51.61,143.29 52.71,143.82C51.47,144.47 50.51,145.59 50.06,146.94C49.61,145.59 48.65,144.47 47.4,143.82C48.51,143.29 49.44,142.43 50.06,141.36Z"
      android:fillColor="#E374FF">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="50.06"
          android:startY="141.36"
          android:endX="50.06"
          android:endY="150.06"
          android:type="linear">
        <item android:offset="0" android:color="#FFCE83FF"/>
        <item android:offset="0.72" android:color="#FF965BB7"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
