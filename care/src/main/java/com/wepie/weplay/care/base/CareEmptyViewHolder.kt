package com.wepie.weplay.care.base

import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.empty.HWUIEmptyView
import com.wepie.weplay.care.R

class CareEmptyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    companion object {
        fun create(
            parent: ViewGroup,
            top: Int = 0,
            textColor: Int = ResUtil.getColor(R.color.white_alpha50),
            msg: String = ""
        ): CareEmptyViewHolder {
            val view = HWUIEmptyView(parent.context)
            view.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            view.gravity = Gravity.TOP
            view.setType(HWUIEmptyView.base_empty_no_gift)
            view.setPaddingRelative(0, top, 0, 0)
            view.setTextColor(textColor)
            view.setText(msg)
            return CareEmptyViewHolder(view)
        }
    }
}