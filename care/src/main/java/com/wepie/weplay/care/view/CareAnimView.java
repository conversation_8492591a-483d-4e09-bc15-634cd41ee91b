package com.wepie.weplay.care.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.ScaleAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.huiwan.base.util.ScreenUtil;
import com.wepie.weplay.care.R;

import java.util.Random;

/**
 * Created by three on 15/9/10.
 */
public class CareAnimView extends FrameLayout {
    private final Context mContext;

    private ImageView icon;

    public CareAnimView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public CareAnimView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        setLayoutDirection(LAYOUT_DIRECTION_LTR);
        LayoutInflater.from(mContext).inflate(R.layout.guard_anim_view, this);
        icon = findViewById(R.id.care_anim_image_view);
        icon.setVisibility(INVISIBLE);
    }

    public void startCareAnim(final int endCenterX, final int endCenterY) {
        startAnim(endCenterX, endCenterY);
    }

    private void startAnim(final int endCenterX, final int endCenterY) {
        final int width = icon.getWidth();
        final int height = icon.getHeight();

        final int showCenterX = ScreenUtil.getScreenWidth() / 2;
        final int showCenterY = (ScreenUtil.getScreenHeight() - height) / 2;

        final int animCount = 200;
        final int duration = 1500;

        final int defaultRange = ScreenUtil.dip2px(mContext, 10);
        final LayoutParams params = (LayoutParams) icon.getLayoutParams();
        ValueAnimator lineAnim = ValueAnimator.ofInt(0, animCount);
        lineAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            int step = 0;
            int direction = 0;
            int range;

            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                int value = (Integer) valueAnimator.getAnimatedValue();
                float fraction = value * 1.0f / animCount;
                float x = showCenterX - (showCenterX - endCenterX) * fraction;
                float y = showCenterY - (showCenterY - endCenterY) * fraction;


                params.topMargin = (int) y - height / 2;

                if (value == 0) {
                    direction = new Random().nextInt(2);
                }

                range = value > 160 ? 1 : defaultRange;

                if (direction == 0) {
                    step += 1;
                    if (step >= range) direction = 1;
                } else {
                    step -= 1;
                    if (step <= -range) direction = 0;
                }
                params.setMarginStart((int) x - width / 2 + step);
                icon.setLayoutParams(params);

                if (value == animCount) {
                    startAnim3();
                }

            }
        });
        lineAnim.setInterpolator(new AccelerateInterpolator());
        icon.setVisibility(View.VISIBLE);
        lineAnim.setDuration(duration).start();
    }

    public static final int SCALE_DURATION = 500;

    private void startAnim3() {
        ScaleAnimation scaleAnimation = new ScaleAnimation(1, 2.0f, 1, 2.0f,
                Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        AlphaAnimation alphaAnimation = new AlphaAnimation(0.8f, 0);

        AnimationSet animSet = new AnimationSet(true);
        animSet.addAnimation(scaleAnimation);
        animSet.addAnimation(alphaAnimation);
        animSet.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                icon.setVisibility(View.INVISIBLE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        animSet.setDuration(SCALE_DURATION);
        icon.startAnimation(animSet);
        if (mOnShowTextAnimListener != null) {
            mOnShowTextAnimListener.onShowTextAnim();
        }
    }

    private OnShowTextAnimListener mOnShowTextAnimListener;

    public void registerOnShowTextAnimListener(OnShowTextAnimListener listener) {
        this.mOnShowTextAnimListener = listener;
    }

    public interface OnShowTextAnimListener {
        void onShowTextAnim();
    }


}
