package com.wepie.weplay.care.gift.wall

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.model.gift.Gift
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.user.FriendInfoCacheManager
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserListSimpleInfoCallback
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.three.http.core.KtResultSuccess
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.weplay.care.R
import com.wepie.weplay.care.api.CareApiImpl
import com.wepie.weplay.care.api.CareApiResposity
import com.wepie.weplay.care.api.ICareApi
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import kotlinx.coroutines.launch

class GiftWallSendDialogFragment : BaseDialogFragment() {
    private lateinit var senderHeadIv: DecorHeadImgView
    private lateinit var receiverHeadIv: DecorHeadImgView
    private lateinit var giftIv: ImageView
    private lateinit var giftNumTv: TextView
    private lateinit var sendPersonInfoTv: TextView
    private lateinit var sendGiftNumTv: TextView
    private lateinit var sendBtn: View
    private lateinit var closeBtn: View
    private lateinit var sendToLightTv: TextView

    private lateinit var wallItem: com.wepie.weplay.care.gift.entity.GiftWallItem
    private var targetUid = 0

    private fun init(uid: Int, wallItem: com.wepie.weplay.care.gift.entity.GiftWallItem) {
        this.targetUid = uid
        this.wallItem = wallItem
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_gift_wall_send, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        refresh()
    }

    private fun initView(view: View) {
        senderHeadIv = view.findViewById(R.id.sender_head_iv)
        receiverHeadIv = view.findViewById(R.id.receiver_head_iv)
        giftIv = view.findViewById(R.id.gift_iv)
        giftNumTv = view.findViewById(R.id.gift_num_info_tv)
        sendPersonInfoTv = view.findViewById(R.id.send_info_tv)
        sendGiftNumTv = view.findViewById(R.id.gift_num_tv)
        sendBtn = view.findViewById(R.id.send_flower_btn)
        closeBtn = view.findViewById(R.id.close_btn)
        sendToLightTv = view.findViewById(R.id.send_to_light_tip)

        sendBtn.setOnClickListener {
            //赠送礼物
            val api = ApiService.of(ICareApi::class.java)
            if (api is CareApiImpl) {
                api.getCallback()?.sendGit(requireContext(), targetUid, wallItem.giftId)
                dismissAllowingStateLoss()
            }
        }
        senderHeadIv.setOnClickListener {
            ApiService.of(HwApi::class.java)
                .gotoUserInfoDetailActivity(context, wallItem.maxSendUid, TrackSource.CARE)
        }
        receiverHeadIv.setOnClickListener {
            ApiService.of(HwApi::class.java)
                .gotoUserInfoDetailActivity(context, targetUid, TrackSource.CARE)
        }
        closeBtn.setOnClickListener { dismissAllowingStateLoss() }
    }

    private fun refresh() {
        WpImageLoader.load(wallItem.giftMediaUrl, giftIv, ImageLoadInfo.getGiftInfo())
        refreshGiftNum()
        senderHeadIv.showUserHeadWithDecorationCache(wallItem.maxSendUid)
        receiverHeadIv.showUserHeadWithDecorationCache(targetUid)
        UserService.get().getCacheSimpleUserList(
            listOf(wallItem.maxSendUid, targetUid),
            object : UserListSimpleInfoCallback {
                override fun onUserInfoSuccess(userSimpleInfos: List<UserSimpleInfo>) {
                    val senderName = userSimpleInfos[0].remarkNameLimit6
                    val receiverName = userSimpleInfos[1].remarkNameLimit6
                    val info = ResUtil.getResource()
                        .getString(R.string.gift_sent_from_to_s_s, senderName, receiverName)
                    sendPersonInfoTv.text = info
                }

                override fun onUserInfoFailed(description: String) = Unit
            })
        var canSend = false
        val gift =
            ConfigHelper.getInstance().giftConfig.shownGift.firstOrNull { it.gift_id == wallItem.giftId }
        if (gift != null) {
            canSend = FriendInfoCacheManager.getInstance().isFriend(targetUid) &&
                    gift.showInScene(Gift.GIFT_SCENE_CHAT) && !gift.isUserDefinedVipGift
        }
        if (canSend) {
            sendBtn.visibility = View.VISIBLE
            sendToLightTv.isVisible = wallItem.maxSendUid != LoginHelper.getLoginUid()
        } else {
            sendBtn.visibility = View.GONE
            sendToLightTv.visibility = View.GONE
        }
    }

    private fun refreshGiftNum() {
        lifecycleScope.launch {
            val result = CareApiResposity.getLighterInfo(targetUid, wallItem.giftId)
            if (result is KtResultSuccess) {
                val num: Int = result.data.num
                val dValue: Int = result.data.dValue
                giftNumTv.text = ResUtil.getStr(R.string.num_x_d, num)
                val numInfo = ResUtil.getResource().getQuantityString(
                    R.plurals.gift_name_num_s_s,
                    num,
                    num,
                    wallItem.giftUnit,
                    wallItem.giftName
                )
                sendGiftNumTv.text = numInfo
                sendToLightTv.text = ResUtil.getResource()
                    .getQuantityString(
                        R.plurals.gift_num_to_max_s_s,
                        dValue,
                        dValue,
                        wallItem.giftUnit
                    )
            } else {
                ToastUtil.show(result.failedDesc)
            }
        }

    }

    companion object {
        fun show(
            context: Context,
            uid: Int,
            wallItem: com.wepie.weplay.care.gift.entity.GiftWallItem
        ) {
            val activity = ContextUtil.getFragmentActivityFromContext(context) ?: return
            val fragment = GiftWallSendDialogFragment()
            fragment.init(uid, wallItem)
            fragment.show(activity.supportFragmentManager, "")
        }
    }
}