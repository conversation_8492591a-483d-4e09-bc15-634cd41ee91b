package com.huiwan.decorate

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.PixelFormat
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.text.TextPaint
import android.view.View
import com.huiwan.base.ktx.dp
import com.huiwan.base.util.FontUtil
import com.huiwan.base.util.PaintUtil
import com.huiwan.configservice.constentity.ConstV3Info
import com.huiwan.configservice.editionentity.instance
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoadListener
import com.wepie.libimageloader.WpImageLoader
import java.util.concurrent.atomic.AtomicInteger

class JackarooLevelDrawable(private val level: Int) : Drawable() {

    private val dp20 = 20.dp.toFloat()

    private var iconDrawable: Drawable? = null
    private var iconBgDrawable: Drawable? = null
    private var scale = 1.0F
    private var view: View? = null

    private val textPaint = TextPaint().apply {
        isAntiAlias = true
        textSize = 10.dp.toFloat()
        color = Color.WHITE
        typeface = FontUtil.getTypeface(android.graphics.Typeface.BOLD)
    }

    private val levelStr = level.toString()
    private var textWidth = textPaint.measureText(levelStr)

    init {
        val gradeLevel = ConstV3Info::class.instance().findGradeLevel(level)
        val flag = AtomicInteger(0)
        WpImageLoader.load(gradeLevel.icon, null, ImageLoadInfo.newInfo(), object :
            WpImageLoadListener<String> {
            override fun onComplete(model: String?, t: Drawable?): Boolean {
                iconDrawable = t
                invalidateView(flag.incrementAndGet())
                return true
            }

            override fun onFailed(model: String?, e: Exception?): Boolean = false
        })

        WpImageLoader.load(gradeLevel.iconBg, null, ImageLoadInfo.newInfo(), object :
            WpImageLoadListener<String> {
            override fun onComplete(model: String?, t: Drawable?): Boolean {
                iconBgDrawable = t
                invalidateView(flag.incrementAndGet())
                return true
            }

            override fun onFailed(model: String?, e: Exception?): Boolean = false
        })
    }

    fun attach(view: View?) {
        this.view = view
    }

    fun getGameLevel(): Int {
        return level
    }

    private fun invalidateView(flag: Int) {
        if (flag < 2) {
            return
        }
        updateScale(scale)
        view?.requestLayout()
        view?.invalidate()
    }

    fun updateScale(scale: Float) {
        if (iconDrawable == null || iconBgDrawable == null) {
            this.scale = scale
            return
        }
        val rect = updateBounds(scale)
        super.setBounds(rect)
    }

    override fun setBounds(left: Int, top: Int, right: Int, bottom: Int) {
        super.setBounds(left, top, right, bottom)
        val scale = dp20 / (bottom - top)
        if (scale != this.scale) {
            updateBounds(scale)
        }
    }

    private fun updateBounds(scale: Float): Rect {
        this.scale = scale
        textPaint.textSize = (scale * 10).dp.toFloat()
        val iconWidth = (scale * 16).dp
        val iconHeight = (scale * 20).dp

        iconDrawable?.setBounds(0, 0, iconWidth, iconHeight)
        textWidth = textPaint.measureText(levelStr)

        val width = iconWidth + textWidth + 6.dp
        val bgStart = (10 * scale).dp
        val bgHeight = (scale * 12).dp
        iconBgDrawable?.setBounds(
            bgStart,
            (iconHeight - bgHeight) / 2,
            width.toInt(),
            (iconHeight + bgHeight) / 2
        )
        return Rect(0, 0, width.toInt(), iconHeight)
    }

    override fun draw(canvas: Canvas) {
        if (iconDrawable == null || iconBgDrawable == null) {
            return
        }
        canvas.save()
        canvas.translate(bounds.left.toFloat(), bounds.top.toFloat())
        iconBgDrawable?.apply {
            alpha = <EMAIL>
            draw(canvas)
        }
        iconDrawable?.apply {
            alpha = <EMAIL>
            draw(canvas)
        }
        val baseline = PaintUtil.getTextBaseLine(textPaint, 0, bounds.height())
        canvas.drawText(
            levelStr, 0, levelStr.length,
            (iconDrawable?.bounds?.width() ?: 0) + 2.dp.toFloat(),
            baseline.toFloat(),
            textPaint
        )
        canvas.restore()
    }

    override fun getAlpha(): Int {
        return textPaint.alpha
    }

    override fun setAlpha(alpha: Int) {
        textPaint.alpha = alpha
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        textPaint.setColorFilter(colorFilter)
    }

    override fun getOpacity(): Int {
        return PixelFormat.TRANSLUCENT
    }
}