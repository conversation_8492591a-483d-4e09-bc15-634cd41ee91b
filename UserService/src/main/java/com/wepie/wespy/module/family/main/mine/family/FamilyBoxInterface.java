package com.wepie.wespy.module.family.main.mine.family;

/**
 * Created by <PERSON><PERSON> on 2019-10-17.
 */
public interface FamilyBoxInterface {

    // 1. 解锁中 2. 锁住中 3. 已解锁 4. 被偷取中 5. 已打开
    int STATUS_UNLOCKING = 1;
    int STATUS_LOCKED = 2;
    int STATUS_UNLOCKED = 3;
    int STATUS_STEALING = 4;
    int STATUS_OPENED = 5;

    //1. 普通 2. 高级 3. 稀有 4.至尊
    int LEVEL_NORMAL = 1;
    int LEVEL_ADVANCE = 2;
    int LEVEL_RARE = 3;
    int LEVEL_SUPREME = 4;

    public int getId();

    public long getGainTimeInSec();

    public int getStatus();

    public int getLevel();

    public int getStealCount();

    long getLeftProtectTimeInSec();
    long getLeftUnlockTimeInSec();
    String getBoxName();
    int getUnlockCoin();
}
