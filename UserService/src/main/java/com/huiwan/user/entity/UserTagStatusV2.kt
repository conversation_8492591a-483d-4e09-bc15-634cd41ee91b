package com.huiwan.user.entity

import com.google.gson.annotations.SerializedName


/**
 * 用户称号v2
 * create by He<PERSON>Han
 * 2023/12/3
 */
data class UserTagStatusV2(
    @SerializedName("total")
    val total: Int = 0, // 31
    //历史用的
    @SerializedName("list")
    val historyList: List<UserTitle> = listOf(),
    //用户当前可用的
    @SerializedName("valid_titles")
    val validTitles: List<UserTitle> = listOf()
)

data class UserTitle(
    @SerializedName("acquire_total")
    val acquireTotal: Int = 0, // 258
    @SerializedName("expire_time")
    val expireTime: Long = 0, // 2648701433
    @SerializedName("status")
    var status: Int = 0, // 1
    @SerializedName("title_id")
    val titleId: Int = 0, // 781
    //历史称号会有这个数据
    @SerializedName("add_time")
    val addTime: Int = 0, // 1701014700
)