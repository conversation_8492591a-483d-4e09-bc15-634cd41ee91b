package com.huiwan.user.entity;

import com.google.gson.annotations.SerializedName;
import com.huiwan.lib.api.plugins.IMedalApi;

import java.util.ArrayList;
import java.util.List;

public class UserPopWindow {
    @SerializedName("avatar_image_v2")
    public String avatarImage = "";
    @SerializedName("avatar_suit_id")
    public int avatarSuitId = 0;
    @SerializedName("game_info")
    public UserPopGameInfo gameInfo = new UserPopGameInfo();
    @SerializedName("wearing_medal")
    public List<IMedalApi.WearInfo> wearMedals = new ArrayList<>();
    @SerializedName("pop_game_achieve")
    public List<PopGameAchieveInfo> popGameAchieveInfos = new ArrayList<>();
}
