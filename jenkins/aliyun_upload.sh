#!/bin/bash
source /etc/profile
BUCKET="apk-dl"
DOMAIN="http://apk-dl.17zjh.com"
APK_DIR=$1

QINIU_PATH=$WORKSPACE/channelBuild/qiniu
CONFIG_PATH=$WORKSPACE/channelBuild/config
CONFIG_PATH_NEW=$WORKSPACE/wejoy-android/jenkins

if [ $# -lt 1 ];then
    echo "use: aliyun_upload.sh_upload.sh apk_dir"
    exit 1;
fi
echo "apk_dir：$1"
if [ $# -ge 2 ];then
    echo "apk_dir：$2"
    if [ $2 == "new" ];then
        CONFIG_PATH=$CONFIG_PATH_NEW;
    fi
fi

while read line
do
   [ -z $line ] && continue
   key=`echo $line | cut -d "/" -f 4`
#   echo "delete bucket file: $key"
#   ./qrsctl del $BUCKET $key
done < $CONFIG_PATH/qiniu_url.txt

rm -rf $CONFIG_PATH/qiniu_url.txt

echo "======list apks"
APKS=`ls $APK_DIR`
echo "所在目录.."
echo $APK_DIR
echo "apk文件列表"
echo $APKS
echo "目前所在目录 pwd"
pwd

ls $APK_DIR
for apk in $APKS; do
    echo "upload apk start"
    echo "find apk name...."
    echo "ls -l $apk"
    /usr/local/sbin/ossutil64 cp ${APK_DIR}/${apk} oss://${BUCKET}

    url=${DOMAIN}/${apk}
    echo "upload apk end url="$url
    echo $url >> $CONFIG_PATH/qiniu_url.txt
done

