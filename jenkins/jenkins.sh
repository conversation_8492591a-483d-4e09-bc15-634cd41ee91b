#!/bin/bash


function choose_jiagu_program()
{
    echo "choose_jiagu_program"
    if [[ ${ENV} == "local" ]];then
        echo "local"
        jiagu_path=$jiagu_path_local
    elif [[ ${ENV} == "jenkins" ]];then
        echo "jenkins"
        jiagu_path=$jiagu_path_jenkins
    fi
    echo $jiagu_path
}

function write_jiagu_channel()
{
    echo "write_jiagu_channel"
    rm $jiagu_channel_list
    echo "====change_channel=$CHANNEL"

    if [[ $CHANNEL = "all" ]];then
        echo "全渠道"
        touch $jiagu_channel_list
        cp $all_channel_list $jiagu_channel_list
    elif [[ $CHANNEL = "market-all" ]];then
        echo "市场全渠道"
        touch $jiagu_channel_list
        cp $market_channel_list $jiagu_channel_list
    elif [[ $CHANNEL = "market-all-ttks" ]];then
        echo "市场全渠道-头条快手"
        touch $jiagu_channel_list
        cp $market_channel_list_ttks $jiagu_channel_list
    else
        OLD_IFS="$IFS"
        IFS=","
        index=0
        for s in $CHANNEL
        do
            echo "$s"
            index=`expr $index + 1`
            echo UMENG_CHANNEL +$s $s >> $jiagu_channel_list
        done
        IFS="$OLD_IFS"
    fi
}

function rename_jiagu_apk()
{
    echo "rename_jiagu_apk"
    last_path=`pwd`
    cd $output_with_jiagu

    #文件数量为0则加固失败
    file_num=`ls -l | grep "^-" | wc -l`
    if [ ${file_num} -eq 0 ];then
        exit 1
    fi

    for fname in *.apk
    do
        echo "fname=${fname}"
        if [[ "$fname" =~ "jiagu" ]];then
            rm $fname
            echo "remove jiagu unuse apk"
            continue
        fi
        #去掉_sign
        new_name=${fname/_sign/}
        #去掉编号
        new_name=${new_name/_[0-9]_+/_}
        new_name=${new_name/_[0-9][0-9]_+/_}
        #开关状态
        switch=`cat $switch_file`
        echo "switch=${switch}"
        new_name=${new_name/__/_${switch}_}
        echo "new_name=${new_name}"
        mv $fname $new_name
    done
    cd last_path
}

function jiagu_apk()
{
    write_jiagu_channel
    choose_jiagu_program
    echo "jiagu_apk"
    echo "加固"
    apk_path=`find $output_without_jiagu -name "*.apk" -print`

    if [ -d ${apk_path} ]
    then
        echo "找不到未加固的apk"
        exit 1
    fi

    echo ${apk_path}

    java -jar $jiagu_path/jiagu/jiagu.jar -login 18680371624 as1234
    java -jar $jiagu_path/jiagu/jiagu.jar -importsign $sign_path 87542701 android 87542701
    java -jar $jiagu_path/jiagu/jiagu.jar -config -crashlog
    java -jar $jiagu_path/jiagu/jiagu.jar -showconfig
    java -jar $jiagu_path/jiagu/jiagu.jar -showsign
    java -jar $jiagu_path/jiagu/jiagu.jar -version
    sleep 10
    java -jar $jiagu_path/jiagu/jiagu.jar -jiagu $apk_path $output_with_jiagu -autosign -automulpkg -pkgparam $jiagu_channel_list
    rename_jiagu_apk
}

function copy_and_rename()
{
    echo "copy_and_rename"
    DATE=$(date +%Y%m%d%H%M)
    apk_path=`find $project_path -name "*.apk" -print|grep 'outputs/apk'`
    if [ -d ${apk_path} ]
    then
        echo "找不到打包完成的apk"
        exit 1
    fi

    apk_file_name=${apk_path##*/}
    echo $apk_file_name
    arr=(${apk_file_name//_/ })
    version_name=${arr[1]}
    version_code=${arr[2]}
    if [ $PROJECT == "xroom" ];then
        name="xroom_release_${version_code}_${switch}_${DATE}.apk"
    elif [ $PROJECT == "chatting" ];then
        name="chatting_release_${version_code}_${switch}_${DATE}.apk"
    elif [ $PROJECT == "huiwan" ];then
        name="huiwan_release_${version_code}_${switch}_${DATE}.apk"
    else
        name="wepie_release_${version_code}_${switch}_${DATE}.apk"
    fi
    echo "cp ${apk_path} ${output_without_jiagu}/${name}"
    cp ${apk_path} ${output_without_jiagu}/${name}
}

function clear()
{
    if [ ! -d $output_with_jiagu  ];then
        mkdir $output_with_jiagu
    fi

    if [ ! -d $output_without_jiagu  ];then
        mkdir $output_without_jiagu
    fi

    rm $output_with_jiagu/*
    rm $output_without_jiagu/*
}


if [ $# -lt  5 ]
then
    echo "传入正确的5个参数"
    echo "1:渠道"
    echo "2:审核开关(off | on)"
    echo "3:是否加固(true | false)"
    echo "4:环境(local | jenkins)"
    echo "5:项目(wespy | huiwan)"
    echo "6:代码分支(选填)"
    echo "7:包名(选填)"
    echo "bash jenkins/jenkins.sh official off true local wespy dev com.wepie.wespy"
    exit 1
fi

echo "渠道：$1"
CHANNEL=$1
echo "审核开关：$2 (off | on)"
SWITCH=$2
echo "是否加固：$3 (true | false)"
JIAGU=$3
echo "环境：$4 (local | jenkins)"
ENV=$4
echo "项目：$5 (wespy | huiwan)"
BRANCH=$6
echo "代码分支：$6"
PROJECT=$5
echo "包名"
PKG_NAME=$7

#项目地址（jenkins或本地打包调整这里）
project_path_local=/Users/<USER>/work/wepie/wespy-android
project_path_jenkins=$WORKSPACE/wespy-android
project_path=$project_path_jenkins
if [ $ENV = "local" ];then
    project_path=$project_path_local
fi
echo $project_path

#配置 360 加固所在目录
jiagu_path=$project_path/jenkins/jiagu_linux
jiagu_path_jenkins_old=$WORKSPACE/channelBuild/360jiagubao_linux_64_1536
jiagu_path_jenkins=$project_path/jenkins/jiagu_linux
jiagu_path_local=$project_path/jenkins/jiagu_mac
#配置应用签名所在目录
sign_path=$project_path/wepie/wespy-android.keystore
sign_path_vivo=$project_path/wepie/wespy-android3.keystore
sign_path_chatting=$project_path/wepie/wespy-android-chatting.keystore
sign_path_xroom=$project_path/wepie/wespy-android-xroom.keystore
#配置gradle打包输出目录
gradle_apk_output=$project_path/wepie/build/outputs/apk
#配置未加固输出目录
output_without_jiagu=$project_path/jenkins/output_without_jiagu
#配置加固输出目录
output_with_jiagu=$project_path/jenkins/output_with_jiagu
#审核开关目录
switch_file=$project_path/wepie/assets/switch.txt
#加固传入的渠道list
jiagu_channel_list=$project_path/jenkins/channel.txt
#全渠道list
all_channel_list=$project_path/jenkins/mulpkg_all.txt
#市场渠道
market_channel_list=$project_path/jenkins/market_all.txt
#市场渠道-头条快手
market_channel_list_ttks=$project_path/jenkins/market_all_ttks.txt

if [ "$BRANCH" = "wespy-vivo" ];then
    sign_path=$sign_path_vivo
fi

if [ "$BRANCH" = "chatting" ];then
    sign_path=$sign_path_chatting
fi

if [ "$BRANCH" = "xroom" ];then
    sign_path=$sign_path_xroom
fi

if [ "com.woyou.spy" = "$PKG_NAME" ];then
  sign_path=$sign_path_vivo
fi

if [ "com.woyou.chatting" = "$PKG_NAME" ];then
  sign_path=$sign_path_chattings
fi

if [ "com.huiyou.xroom" = "$PKG_NAME" ];then
  sign_path=$sign_path_xroom
fi

#gradle clean
#gradle assembleOfficialRelease
clear
copy_and_rename
if [ $JIAGU = "true" ];then
    jiagu_apk
fi
