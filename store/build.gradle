apply from: "../base_lib.gradle"

android {
    useLibrary 'org.apache.http.legacy'
    buildFeatures {
        buildConfig true
    }
    defaultConfig {
        buildConfigField "String", "BASE_FILE_DIR", "\"${rootProject.ext.appConfig.BASE_FILE_DIR}\""
        buildConfigField "String", "PHOTO_FILE_DIR", "\"${rootProject.ext.appConfig.PHOTO_FILE_DIR}\""
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    implementation project(path: ':lib:baseutil')
    implementation project(path: ':lib:liblog')

    api libs.gson

    implementation libs.mmkv
    implementation libs.okio
}