package com.huiwan.anim;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;

public class ByteBufferInputStream extends InputStream {
    private final ByteBuffer buffer;

    public ByteBufferInputStream(ByteBuffer buffer) {
        this.buffer = buffer;
    }

    @Override
    public int read() throws IOException {
        if (buffer.hasRemaining()) {
            return buffer.get();
        } else {
            return -1;
        }
    }

    @Override
    public int read(byte[] b, int off, int len) throws IOException {
        int copyLen = buffer.remaining();
        if (copyLen > len) {
            copyLen = len;
        }
        buffer.get(b, off, copyLen);
        return copyLen;
    }

    @Override
    public int available() throws IOException {
        return buffer.remaining();
    }
}
