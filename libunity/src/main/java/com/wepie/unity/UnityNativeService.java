package com.wepie.unity;

import com.huiwan.ipc.child.IPCChildManager;
import com.huiwan.ipc.model.IPCMsg;
import com.huiwan.platform.ThreadUtil;
import com.unity3d.player.UnityPlayer;
import com.wepie.unity.avatar.BaseUnityInMainUriProcessor;
import com.wepie.unity.unity.BaseRemoteUriProcessor;

/**
 * native与unity直接交互的类，unity通过反射调用该类的UnityCallNative传入一个json，后续解析成具体任务
 * 同样地，native通过nativeCallUnity将json传入unity
 * 目前项目中分别在主进程接入avatar，子进程接入xroom游戏，因此unity传过来的消息也需要分别处理。
 *
 * created by sa<PERSON><PERSON> on 2020/10/26
 */
public class UnityNativeService {

    private static final String OBJECT = "UnityNativeBridgeObject";
    private static final String METHOD = "NativeCallUnity";
    private static final String IMAGE_OBJECT = "CaptureActions";
    private static final String IMAGE_METHOD = "PickImageCallBackBase64";

    private static boolean isXRoomProcesses = false;

    private static BaseRemoteUriProcessor remoteProcessor;
    private static BaseUnityInMainUriProcessor avatarProcessor;

    public static byte[] UnityCallNative(String json) {
        UnityUtil.print("UnityCallNative: " + json);  //todo: for test
        final IPCMsg msg = UnityUtil.getGson().fromJson(json, IPCMsg.class);
        ThreadUtil.runOnUiThread(() -> {
            //如果在子进程，则为太空狼人的通信，否则则为avatar的通信
            if(isXRoomProcesses) {
                //unity请求native的req，任务在unity进程处理
                if(UnityUriConfig.getBlockList().contains(msg.getUri()) && msg.getType() == IPCMsg.TYPE_REQ) {
                    if(remoteProcessor != null) {
                        remoteProcessor.invoke(msg);
                    }
                } else if (UnityUriConfig.getPassList().contains(msg.getUri())) {
                    //unity请求native的req，发送到主进程由MainProcessor处理
                    IPCChildManager.getInstance().sendMsg(msg);
                } else if (UnityUriConfig.getUnityList().contains(msg.getUri()) && msg.getType() == IPCMsg.TYPE_RSP) {
                    //属于之前native请求unity收到的rsp
                    NativeCallUnityManager.getInstance().receiveRsp(msg);
                } else {
                    UnityUtil.print("unexpected msg");
                }
            } else {
                //如果是rsp，则交给NativeCallUnityManager处理
                if(UnityUriConfig.getUnityList().contains(msg.getUri()) && msg.getType() == IPCMsg.TYPE_RSP) {
                    NativeCallUnityManager.getInstance().receiveRsp(msg);
                } else {
                    if(avatarProcessor != null) avatarProcessor.invoke(msg);
                }

            }
        });
        return null;
    }

    public static void nativeCallUnity(IPCMsg unityMsg) {
        String json = UnityUtil.getGson().toJson(unityMsg);
        UnityUtil.print("NativeCallUnity: " + json);
        UnityPlayer.UnitySendMessage(OBJECT, METHOD, json);
    }

    public static void setRemoteProcessor(BaseRemoteUriProcessor processor) {
        remoteProcessor = processor;
    }

    public static void setAvatarProcessor(BaseUnityInMainUriProcessor avatarProcessor) {
        UnityNativeService.avatarProcessor = avatarProcessor;
    }

    public static BaseRemoteUriProcessor getRemoteProcessor() {
        return remoteProcessor;
    }

    public static void init(boolean isUnityProcesses) {
        isXRoomProcesses = isUnityProcesses;
    }

    //avatar商城自动捏脸上传照片接口，不同于其他交互流程。目前只在子进程使用。
    public static byte[] openAlbum() {
        if(isXRoomProcesses && remoteProcessor != null) {
            remoteProcessor.openAlbum();
        }
        return null;
    }

    public static void sendImage2Unity(String encodedImage) {
        UnityPlayer.UnitySendMessage(IMAGE_OBJECT, IMAGE_METHOD, encodedImage);
    }
}
