package com.wepie.unity.main;

import com.huiwan.ipc.model.IPCMsg;
import com.wepie.unity.ProcessCallBack;

public interface IRoomLocal {
    void gotoFixRoomSetting(IPCMsg msg);
    void sendFixRoomPacket(IPCMsg msg, ProcessCallBack callBack);
    void inviteFixRoom(IPCMsg msg);
    void sendFixRoomChatMsg(IPCMsg msg);
    void onSceneReady(IPCMsg msg);
    void showFixRoomUserDialog(IPCMsg msg);
    void reportFixRoomChatMsg(IPCMsg msg);
    void sendFixRoomSpeechGift(IPCMsg msg);
    void changeFixRoomLockState(IPCMsg msg);
    void showFixRoomMagicFace(IPCMsg msg);
    void syncFixRoomInfo(IPCMsg msg);
    void requestBuyGoods(IPCMsg msg);
    void setFixRoomActivityVisible();
}
