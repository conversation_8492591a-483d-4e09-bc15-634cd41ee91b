package com.huiwan.widget;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class SimplePagerViewHolder extends RecyclerView.ViewHolder {
    private final FrameLayout frameLayout;

    private SimplePagerViewHolder(@NonNull View itemView) {
        super(itemView);
        frameLayout = (FrameLayout) itemView;
    }

    public static SimplePagerViewHolder create(Context context) {
        FrameLayout frameLayout = new FrameLayout(context);
        frameLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        return new SimplePagerViewHolder(frameLayout);
    }

    public void removeAllViews() {
        frameLayout.removeAllViews();
    }

    public void addView(View view) {
        ViewParent parent = view.getParent();
        if (parent != null) {
            ((ViewGroup) parent).removeView(view);
        }
        addView(view, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    }

    public void addView(View view, ViewGroup.LayoutParams params) {
        frameLayout.addView(view, params);
    }
}
