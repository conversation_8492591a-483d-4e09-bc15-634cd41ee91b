package com.huiwan.widget

import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import android.util.TypedValue

class AutoResizeTextView : CustomTextView {
    private var defaultTextSize = 0f // 默认字体大小
    var minTextSize = 0f // 最小字体大小

    private val mPaint: Paint by lazy {
        Paint()
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        defaultTextSize = textSize
        minTextSize = defaultTextSize / 2
    }

    override fun setText(text: CharSequence?, type: BufferType?) {
        super.setText(text, type)
        resizeText()
        requestLayout()
    }

    private fun resizeText() {
        if (measuredWidth <= 0) {
            return
        }
        var textSize = defaultTextSize
        var textWidth = getTextWidth(text.toString(), textSize)
        while (textWidth > measuredWidth && textSize > minTextSize) {
            textSize--
            textWidth = getTextWidth(text.toString(), textSize)
        }
        setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
    }

    private fun getTextWidth(text: String, textSize: Float): Float {
        mPaint.set(paint)
        mPaint.textSize = textSize
        return mPaint.measureText(text)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        resizeText()
    }

}