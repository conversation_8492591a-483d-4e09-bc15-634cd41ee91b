package com.huiwan.widget.treeView.layout;

import android.content.Context;
import android.view.View;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.widget.treeView.TreeViewContainer;
import com.huiwan.widget.treeView.adapter.TreeViewHolder;
import com.huiwan.widget.treeView.line.BaseLine;
import com.huiwan.widget.treeView.model.ITraversal;
import com.huiwan.widget.treeView.model.NodeModel;
import com.huiwan.widget.treeView.model.TreeModel;
import com.huiwan.widget.treeView.util.ViewBox;

public class BoxLeftTreeLayoutManager extends BoxRightTreeLayoutManager {
    private static final String TAG = BoxLeftTreeLayoutManager.class.getSimpleName();
    private boolean isJustCalculate;

    public BoxLeftTreeLayoutManager(Context context, int spaceParentToChild, int spacePeerToPeer, BaseLine baseline) {
        super(context, spaceParentToChild, spacePeerToPeer, baseline);
    }

    @Override
    public int getTreeLayoutType() {
        return LAYOUT_TYPE_HORIZON_LEFT;
    }

    @Override
    public void performLayout(final TreeViewContainer treeViewContainer) {
        isJustCalculate = true;
        super.performLayout(treeViewContainer);
        isJustCalculate = false;
        final TreeModel<?> mTreeModel = treeViewContainer.getTreeModel();
        if (mTreeModel != null) {
            final int cx = fixedViewBox.getWidth() / 2;
            mTreeModel.doTraversalNodes(new ITraversal<NodeModel<?>>() {
                @Override
                public void next(NodeModel<?> next) {
                    mirrorByCx(next, treeViewContainer, cx);
                }

                @Override
                public void finish() {
                    // 计算最小的顶部位置
                    final int[] minTop = {Integer.MAX_VALUE};
                    mTreeModel.doTraversalNodes(new ITraversal<>() {
                        @Override
                        public void next(NodeModel<?> next) {
                            TreeViewHolder<?> currentHolder = treeViewContainer.getTreeViewHolder(next);
                            View currentNodeView = currentHolder == null ? null : currentHolder.getView();
                            if (currentNodeView != null) {
                                minTop[0] = Math.min(minTop[0], currentNodeView.getTop());
                            }
                        }

                        @Override
                        public void finish() {
                            // 计算偏移量
                            int offset = minTop[0] - ScreenUtil.dip2px(10f);
                            // 调整所有节点的垂直位置
                            mTreeModel.doTraversalNodes(new ITraversal<>() {
                                @Override
                                public void next(NodeModel<?> next) {
                                    TreeViewHolder<?> currentHolder = treeViewContainer.getTreeViewHolder(next);
                                    View currentNodeView = currentHolder == null ? null : currentHolder.getView();
                                    if (currentNodeView != null) {
                                        int left = currentNodeView.getLeft();
                                        int top = currentNodeView.getTop() - offset;
                                        int right = currentNodeView.getRight();
                                        int bottom = currentNodeView.getBottom() - offset;
                                        ViewBox finalLocation = new ViewBox(top, left, bottom, right);
                                        onManagerLayoutNode(next, currentNodeView, finalLocation, treeViewContainer);
                                    }
                                }

                                @Override
                                public void finish() {
                                    onManagerFinishLayoutAllNodes(treeViewContainer);
                                }
                            });
                        }
                    });
                }
            });
        }
    }

    private void mirrorByCx(NodeModel<?> currentNode, TreeViewContainer treeViewContainer, int centerX) {
        TreeViewHolder<?> currentHolder = treeViewContainer.getTreeViewHolder(currentNode);
        View currentNodeView = currentHolder == null ? null : currentHolder.getView();
        if (currentNodeView == null) {
            throw new NullPointerException(" currentNodeView can not be null");
        }
        int left = centerX * 2 - currentNodeView.getRight();
        int right = centerX * 2 - currentNodeView.getLeft();
        int top = currentNodeView.getTop();
        int bottom = currentNodeView.getBottom();
        ViewBox finalLocation = new ViewBox(top, left, bottom, right);
        onManagerLayoutNode(currentNode, currentNodeView, finalLocation, treeViewContainer);
    }

    @Override
    public void onManagerLayoutNode(NodeModel<?> currentNode, View currentNodeView, ViewBox finalLocation, TreeViewContainer treeViewContainer) {
        if (isJustCalculate) {
            currentNodeView.layout(finalLocation.left, finalLocation.top, finalLocation.right, finalLocation.bottom);
            return;
        }
        if (!layoutAnimatePrepare(currentNode, currentNodeView, finalLocation, treeViewContainer)) {
            currentNodeView.layout(finalLocation.left, finalLocation.top, finalLocation.right, finalLocation.bottom);
        }
    }

    @Override
    public void onManagerFinishLayoutAllNodes(TreeViewContainer treeViewContainer) {
        if (!isJustCalculate) {
            super.onManagerFinishLayoutAllNodes(treeViewContainer);
        }
    }
}
