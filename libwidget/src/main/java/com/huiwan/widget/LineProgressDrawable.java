package com.huiwan.widget;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.SystemClock;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.util.ScreenUtil;

public class LineProgressDrawable extends Drawable {
    private final Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private float mProgress = 0;
    private float mTargetProgress = 0;

    private int bgColor = Color.GRAY;
    private int fgColor = Color.GREEN;
    private GradientDrawable gradientDrawable;

    private void setProgress(int cur, int total) {
        if (total == 0) {
            setProgress(0);
        } else {
            setProgress((float)cur / total);
        }
    }

    public void setBgColor(int bgColor) {
        this.bgColor = bgColor;
    }

    public void setFgColor(int fgColor) {
        this.fgColor = fgColor;
        gradientDrawable = null;
        invalidateSelf();
    }

    public void setFgColors(int[] fgColors) {
        gradientDrawable = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, fgColors);
        gradientDrawable.setDither(true);
        gradientDrawable.setCornerRadius(ScreenUtil.dip2px(2));
        invalidateSelf();
    }

    public int getBgColor() {
        return bgColor;
    }

    public int getFgColor() {
        return fgColor;
    }

    public void setProgress(float progress) {
        mProgress = progress;
        if (mProgress < 0) {
            mProgress = 0;
        } else if (mProgress > 1) {
            mProgress = 1;
        }
        invalidateSelf();
    }


    public void animToProgress(float progress) {
        this.mTargetProgress = progress;
        if (mTargetProgress > 1) {
            mTargetProgress = 1;
        }
        if (mTargetProgress < 0) {
            mTargetProgress = 0;
        }
        invalidateSelf();
        unscheduleSelf(changeAnimRunner);
        scheduleSelf(changeAnimRunner, SystemClock.uptimeMillis() + 16);
    }

    private final Runnable changeAnimRunner = new Runnable() {
        @Override
        public void run() {
            float diff = mTargetProgress - mProgress;
            if (Math.abs(mTargetProgress - mProgress) > 0.001) {
                float change = diff;
                float changeFa = Math.min(Math.abs(diff)/8, 0.03f);
                do{
                    change = change / 2;
                } while (Math.abs(change) > changeFa);
                mProgress = mProgress + change;
                scheduleSelf(changeAnimRunner, SystemClock.uptimeMillis() + 16);
                invalidateSelf();
            }
        }
    };


    @Override
    public void draw(@NonNull Canvas canvas) {
        Rect rect = getBounds();
        int w = rect.width();
        int h = rect.height();
        if ( w <= 0 || h <= 0) {
            return;
        }

        float r = (float) Math.min(w, h) / 2;
        mPaint.setColor(bgColor);
        // 3 and -3 for draw edge
        canvas.drawRoundRect(3, 0, w - 3, h, r, r, mPaint);


        if (gradientDrawable != null) {
            if (ScreenUtil.isRtl()) {
                gradientDrawable.setBounds(w - (int) (w * mProgress), 0, w, h);
            } else {
                gradientDrawable.setBounds(0, 0, (int) (w * mProgress), h);
            }
            gradientDrawable.draw(canvas);
        } else {
            mPaint.setColor(fgColor);
            if (ScreenUtil.isRtl()) {
                canvas.drawRoundRect(w - (int) (w * mProgress), 0, w, h, r, r, mPaint);
            } else {
                canvas.drawRoundRect(0, 0, w * mProgress, h, r, r, mPaint);
            }
        }
    }

    public Paint getPaint() {
        return mPaint;
    }

    @Override
    public void setAlpha(int alpha) {
        mPaint.setAlpha(alpha);
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {
        mPaint.setColorFilter(colorFilter);
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSPARENT;
    }
}
