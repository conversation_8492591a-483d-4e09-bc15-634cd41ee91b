package com.huiwan.widget

import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.Property
import android.view.View
import android.widget.Checkable
import androidx.core.view.ViewCompat

/**
 * 自定义 SwitchView
 * android:thumb  标签
 * android:track  背景
 */
class WpSwitch(context: Context, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) :
    View(context, attrs, defStyleAttr, defStyleRes), Checkable {

    /**
     * 是否 check 状态集
     */
    private val checkedState = intArrayOf(
        android.R.attr.state_checked
    )

    /**
     * 内部 check
     * 使用 isChecked 来进行更新
     */
    private var mCheckInternal = false

    /**
     * 标签图
     */
    var thumbDrawable: Drawable? = null
        set(value) {
            val pre = field
            field = value
            value?.callback = this
            checkRefreshView(pre, value)
        }

    /**
     * 底图
     */
    var trackDrawable: Drawable? = null
        set(value) {
            field = value
            value?.callback = this
            invalidate()
        }

    /**
     * 状态更新回调
     */
    var onCheckedChangeListener: ((wpSwitch: WpSwitch, checked: Boolean) -> Unit)? = null

    /**
     * 当前动效进度
     */
    private var thumbPosition = 0f
        set(value) {
            field = value
            invalidate()
        }

    constructor(context: Context, attributeSet: AttributeSet?) :
            this(context, attributeSet, 0, 0)

    constructor(context: Context) : this(context, null)

    init {
        isClickable = true
        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.WpSwitch)
            mCheckInternal = typedArray.getBoolean(R.styleable.WpSwitch_android_checked, false)
            thumbDrawable = typedArray.getDrawable(R.styleable.WpSwitch_android_thumb)
            trackDrawable = typedArray.getDrawable(R.styleable.WpSwitch_android_track)
            typedArray.recycle()
        }
    }


    /**
     * 对比并确定刷新
     */
    private fun checkRefreshView(pre: Drawable?, cur: Drawable?) {
        if (pre == cur) {
            return
        }
        if (pre == null || cur == null) {
            requestLayout()
        } else {
            if (pre.intrinsicWidth != cur.intrinsicWidth || pre.intrinsicHeight != cur.intrinsicWidth) {
                requestLayout()
            } else {
                invalidate()
            }
        }
    }

    /**
     * 默认使用 thumb 的 1.5 倍的宽度做宽度
     */
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val wMode = MeasureSpec.getMode(widthMeasureSpec)
        val hMode = MeasureSpec.getMode(heightMeasureSpec)
        val wSize = MeasureSpec.getSize(widthMeasureSpec)
        val hSize = MeasureSpec.getSize(heightMeasureSpec)
        val d = thumbDrawable
        val w = if (wMode != MeasureSpec.EXACTLY) {
            val base = if (d != null && d.intrinsicWidth > 0) {
                d.intrinsicWidth * 1.5
            } else {
                0
            }
            base.toInt() + paddingLeft + paddingRight
        } else {
            wSize
        }
        val h = if (hMode != MeasureSpec.EXACTLY) {
            if (d != null && d.intrinsicHeight > 0) {
                d.intrinsicHeight
            } else {
                0
            } + paddingTop + paddingBottom
        } else {
            hSize
        }
        setMeasuredDimension(w, h)
    }

    override fun setChecked(checked: Boolean) {
        if (mCheckInternal != checked) {
            mCheckInternal = checked
            refreshDrawableState()
            onCheckedChangeListener?.invoke(this, checked)
            if (windowToken != null && ViewCompat.isLaidOut(this)) {
                animateThumbToCheckedState(checked)
            } else {
                // Immediately move the thumb to the new position.
                cancelPositionAnimator()
                thumbPosition = if (checked) 1f else 0f
            }
        }
    }

    override fun isChecked() = mCheckInternal

    override fun toggle() {
        isChecked = !isChecked
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawTrack(canvas)
        drawThumb(canvas)
    }


    /**
     * 支持 CHECK_STATE
     */
    override fun onCreateDrawableState(extraSpace: Int): IntArray? {
        val drawableState = super.onCreateDrawableState(extraSpace + 1)
        if (isChecked) {
            mergeDrawableStates(drawableState, checkedState)
        }
        return drawableState
    }


    /**
     * 更新 thumb 和 track 的状态
     */
    override fun drawableStateChanged() {
        super.drawableStateChanged()
        val state = drawableState
        var changed = false
        val t = thumbDrawable
        if (t != null && t.isStateful) {
            changed = changed or t.setState(state)
        }
        val track = trackDrawable
        if (track != null && track.isStateful) {
            changed = changed or track.setState(state)
        }
        if (changed) {
            invalidate()
        }
    }

    override fun performClick(): Boolean {
        toggle()
        return super.performClick()
    }

    /**
     * 绘制 标签
     */
    private fun drawThumb(canvas: Canvas) {
        val d = thumbDrawable ?: return
        val isRtl = ViewCompat.getLayoutDirection(this) == ViewCompat.LAYOUT_DIRECTION_RTL
        val pLeft = if (isRtl) {
            (width - d.intrinsicWidth) * (1 - thumbPosition)
        } else {
            (width - d.intrinsicWidth) * thumbPosition
        } + leftPaddingOffset
        val top = (height - d.intrinsicHeight) / 2 + paddingTop
        val bottom = top + d.intrinsicHeight
        val right = pLeft + d.intrinsicWidth
        d.setBounds(pLeft.toInt(), top, right.toInt(), bottom)
        d.draw(canvas)
    }

    /**
     * 绘制底条
     */
    private fun drawTrack(canvas: Canvas) {
        val d = trackDrawable ?: return
        d.setBounds(0, 0, width, height)
        d.draw(canvas)
    }


    /**
     * 支持 ObjectAnimator
     */
    private val thumbPosProperty: Property<WpSwitch, Float> = object : Property<WpSwitch, Float>(
        Float::class.java, "thumbPos"
    ) {
        override fun get(v: WpSwitch): Float {
            return v.thumbPosition
        }

        override fun set(v: WpSwitch, value: Float) {
            v.thumbPosition = value
        }
    }


    override fun jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState()
        thumbDrawable?.jumpToCurrentState()
        trackDrawable?.jumpToCurrentState()
        mPositionAnimator?.let {
            if (it.isStarted) {
                it.end()
            }
        }
        mPositionAnimator = null
    }

    private var mPositionAnimator: ObjectAnimator? = null

    private fun animateThumbToCheckedState(newCheckedState: Boolean) {
        val targetPosition: Float = if (newCheckedState) 1f else 0.toFloat()
        val animator = ObjectAnimator.ofFloat(this, thumbPosProperty, targetPosition)
        animator.duration = 250L
        animator.setAutoCancel(true)
        animator.start()
        mPositionAnimator = animator
    }

    private fun cancelPositionAnimator() {
        mPositionAnimator?.cancel()
    }
}