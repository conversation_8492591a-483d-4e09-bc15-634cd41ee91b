<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center"
            android:text="@string/game_chip_exchange_title"
            android:textColor="#333"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/close_iv"
            android:layout_width="52dp"
            android:layout_height="44dp"
            android:background="@color/transparent"
            android:src="@drawable/action_bar_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/my_chip_lay"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/shape_f4f4f4_corner100"
            android:paddingHorizontal="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_tv">

            <TextView
                android:id="@+id/chip_tv"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:minWidth="32dp"
                android:textColor="#757575"
                android:textFontWeight="500"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="0123456" />

            <ImageView
                android:id="@+id/chip_iv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="6dp"
                android:src="@drawable/game_chip"
                app:layout_constraintBottom_toBottomOf="@id/chip_tv"
                app:layout_constraintEnd_toStartOf="@id/chip_tv"
                app:layout_constraintTop_toTopOf="@id/chip_tv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/balance_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="@string/game_chip_exchange_balance"
            android:textColor="#757575"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@id/my_chip_lay"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/my_chip_lay" />

        <ImageView
            android:id="@+id/tip_bg_iv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            android:src="@drawable/game_chip_ex_dialog_tip"
            app:layout_constraintBottom_toBottomOf="@id/tip_tv"
            app:layout_constraintEnd_toEndOf="@id/tip_tv"
            app:layout_constraintStart_toStartOf="@id/tip_tv"
            app:layout_constraintTop_toBottomOf="@id/my_chip_lay"
            app:layout_constraintTop_toTopOf="@id/tip_tv" />

        <TextView
            android:id="@+id/tip_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:minHeight="32dp"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:text="@string/game_chip_exchange_tip"
            android:textColor="@color/white"
            android:textFontWeight="500"
            android:textSize="14dp"
            app:layout_constraintTop_toBottomOf="@id/my_chip_lay" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/baloot_chip_exchange_margin"
            android:layout_marginTop="6dp"
            android:overScrollMode="never"
            app:layout_constraintTop_toBottomOf="@id/tip_tv"
            tools:itemCount="5"
            tools:layoutManager="GridLayoutManager"
            tools:listitem="@layout/game_dialog_exchange_chip_item"
            tools:spanCount="3" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>