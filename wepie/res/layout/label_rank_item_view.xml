<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:clipChildren="false">

    <TextView
        android:id="@+id/label_rank_tv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="#999CB4"
        android:textFontWeight="700"
        android:textSize="14sp"
        android:textStyle="bold"
        app:autoSizeMaxTextSize="14sp"
        app:autoSizeMinTextSize="8dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/label_rank_iv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/label_rank_barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="end"
        app:constraint_referenced_ids="label_rank_iv,label_rank_tv" />

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/label_head_iv"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:clipChildren="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/label_rank_barrier"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/default_head_icon" />

    <com.huiwan.decorate.NameTextView
        android:id="@+id/label_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_text_accent_dark"
        android:textSize="14dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/label_rank_member_level_iv"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/label_head_iv"
        app:layout_constraintTop_toTopOf="parent"
        app:ntv_scene="light"
        tools:text="I'm name" />

    <ImageView
        android:id="@+id/label_rank_member_level_iv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/label_rank_member_point_tv"
        app:layout_constraintStart_toEndOf="@id/label_name_tv"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/family_level_icon1" />

    <TextView
        android:id="@+id/label_rank_member_point_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/color_text_accent_dark"
        android:textFontWeight="700"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="999999" />

</androidx.constraintlayout.widget.ConstraintLayout>