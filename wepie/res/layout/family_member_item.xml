<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:background="#ffffff"
    android:clipChildren="false">

    <LinearLayout
        android:id="@+id/avatar_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:clipChildren="false"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/choose_iv"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:src="@drawable/ic_un_select" />

        <com.huiwan.decorate.DecorHeadImgView
            android:id="@+id/family_avatar_iv"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:src="@drawable/default_head_icon" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/active_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/week_coin_tv"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:textAlignment="center"
            android:textColor="@color/color_primary"
            android:textSize="12dp"
            tools:text="12345" />

        <LinearLayout
            android:id="@+id/active_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:paddingStart="2dp"
                android:paddingEnd="2dp"
                android:src="@drawable/family_rank_fire_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/family_select_object_select_players_item_activeness"
                android:textColor="#999999"
                android:textSize="12dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/week_active_tv"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textAlignment="center"
            android:textColor="@color/color_primary"
            android:textSize="12dp"
            tools:text="1000000" />

        <ImageView
            android:id="@+id/delete_tv"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:scaleType="centerInside"
            android:src="@drawable/propose_close_icon"
            tools:visibility="gone" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:layout_toStartOf="@id/active_lay"
        android:layout_toEndOf="@id/avatar_lay"
        android:orientation="vertical">

        <com.huiwan.decorate.NameTextView
            android:id="@+id/name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:singleLine="true"
            android:textColor="#ff333333"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="望月阁1111111111" />

        <TextView
            android:id="@+id/family_role_tv"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="10sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/name_tv"
            tools:background="@drawable/family_role_leader_icon_bg"
            tools:text="@string/family_leader_str"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_family_title"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_marginStart="4dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/family_title_common_icon_bg"
            android:gravity="center"
            android:paddingHorizontal="8dp"
            android:textAlignment="center"
            android:textColor="#282d45"
            android:textSize="10dp"
            app:layout_constraintStart_toEndOf="@+id/family_role_tv"
            app:layout_constraintTop_toBottomOf="@+id/name_tv"
            app:layout_goneMarginStart="0dp"
            tools:text="@string/family_leader_str"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        style="@style/list_divider_style"
        android:layout_alignParentBottom="true" />

</RelativeLayout>