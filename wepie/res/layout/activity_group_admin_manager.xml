<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/admin_manager_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.huiwan.base.ui.empty.HWUIEmptyView
        android:id="@+id/empty_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white_color"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:icon_type="base_empty_no_friend_request"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/admin_manager_title"
        app:text="@string/voice_room_members_admin_none"
        app:text_color="@color/color_text_tertiary" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/admin_manager_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/admin_manager_title" />

</androidx.constraintlayout.widget.ConstraintLayout>