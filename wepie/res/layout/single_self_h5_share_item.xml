<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/game_item_self_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:orientation="vertical">

    <Space
        android:id="@+id/game_view_status_temp"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:visibility="gone" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false">

        <include
            android:id="@+id/game_item_self_time_lay"
            layout="@layout/single_self_time_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"/>

        <Space
            android:id="@+id/game_item_self_tip_padding"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_below="@id/game_item_self_time_lay" />

        <Space
            android:id="@+id/game_item_self_icon_padding"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_below="@id/game_item_self_time_lay" />

        <RelativeLayout
            android:id="@+id/game_item_self_msg_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/game_item_self_icon_padding"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="0dp"
            android:clipChildren="false"
            android:paddingStart="20dp">

            <com.huiwan.decorate.DecorHeadImgView
                android:id="@+id/game_item_self_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="14dp"
                android:layout_marginBottom="6dp"
                android:scaleType="centerCrop" />

            <ImageView
                android:id="@+id/game_item_self_singer_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignStart="@id/game_item_self_icon"
                android:layout_alignTop="@id/game_item_self_icon"
                android:layout_marginStart="-6dp"
                android:layout_marginTop="-6dp"
                android:background="@drawable/singer_identity_icon" />

            <include
                android:id="@+id/game_item_self_name_lay"
                layout="@layout/single_self_name_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/game_item_self_icon"
                android:layout_marginTop="0dp"
                android:layout_marginEnd="10dp"
                android:layout_toStartOf="@id/game_item_self_icon"/>

            <com.wepie.wespy.module.chat.gamemodel.MsgH5LinkModel
                android:id="@+id/game_item_h5_link_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/game_item_self_name_lay"
                android:layout_marginEnd="10dp"
                android:layout_toStartOf="@id/game_item_self_icon" />

        </RelativeLayout>

    </RelativeLayout>

    <Space
        android:id="@+id/game_item_self_padding_image"
        android:layout_width="match_parent"
        android:layout_height="10dp" />

    <Space
        android:id="@+id/last_item_padding"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:visibility="gone" />
</LinearLayout>