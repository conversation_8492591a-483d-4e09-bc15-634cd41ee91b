<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white_color">

    <com.wepie.wespy.module.marry.lover_home.view.NotRingView
        android:id="@+id/single_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible" />

    <com.wepie.wespy.module.marry.lover_home.view.UserNestView
        android:id="@+id/nest_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="invisible" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <com.huiwan.widget.actionbar.BaseWpActionBar
            android:id="@+id/action_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:show_status_view="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/info_tv"
            style="@style/action_bar_right_text_white"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/abc_action_bar_default_height_material"
            android:layout_gravity="bottom|end"
            android:gravity="center" />
    </FrameLayout>

    <com.huiwan.component.gift.show.GiftContentView
        android:id="@+id/gift_content_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wepie.wespy.module.gift.GiftComboView
        android:id="@+id/gift_combo_view"
        android:layout_width="match_parent"
        android:layout_height="360dp"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>