<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/member_label_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:includeFontPadding="false"
        android:textColor="@color/color_text_primary_ex"
        android:textSize="12dp"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="房间成员：1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/member_label_point"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:includeFontPadding="false"
        android:text="@string/voice_room_members_rank_point"
        android:textColor="@color/color_text_primary_ex"
        android:textSize="12dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/member_label_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/member_label_self_rank_lay"
        app:layout_constraintTop_toBottomOf="@id/member_label_count">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/member_label_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <com.huiwan.base.ui.empty.HWUIEmptyView
        android:id="@+id/member_empty_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:icon_type="base_empty_no_data"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:text="@string/voice_room_members_empty_tip"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/member_label_self_rank_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_fafafa_tl_tr_corner12"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible">

        <include
            android:id="@+id/member_label_self_rank"
            layout="@layout/label_rank_item_view"
            android:layout_width="match_parent"
            android:layout_height="64dp" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>