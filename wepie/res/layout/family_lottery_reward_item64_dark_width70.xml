<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="70dp"
    android:layout_height="wrap_content"
    tools:background="#ffffff">

    <com.huiwan.component.prop.PropItemImageView
        android:id="@+id/icon_iv"
        android:layout_width="62dp"
        android:layout_height="62dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp" />

    <TextView
        android:id="@+id/type_name_tv"
        android:layout_width="match_parent"
        android:layout_height="14dp"
        android:layout_alignStart="@id/icon_iv"
        android:layout_alignEnd="@id/icon_iv"
        android:layout_alignBottom="@id/icon_iv"
        android:gravity="center"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="9dp"
        tools:text="玩友圈卡片" />

    <TextView
        android:id="@+id/num_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignEnd="@id/icon_iv"
        android:layout_alignBottom="@id/icon_iv"
        android:layout_marginEnd="2dp"
        android:layout_marginBottom="2dp"
        android:background="@drawable/shape_33000000_corner24"
        android:paddingHorizontal="6dp"
        android:paddingVertical="2dp"
        android:textColor="#ffffff"
        android:textSize="10dp"
        tools:text="x1" />

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/ios32"
        android:layout_below="@id/icon_iv"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="@dimen/ios4"
        android:layout_marginTop="@dimen/ios4"
        android:layout_marginEnd="@dimen/ios4"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:textAlignment="center"
        android:textColor="#333333"
        android:textSize="@dimen/ios12"
        tools:text="环球\n旅行111111111111111" />

</RelativeLayout>