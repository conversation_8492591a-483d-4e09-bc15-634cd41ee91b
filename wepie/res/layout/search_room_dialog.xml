<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_transparent70">

    <LinearLayout
        android:layout_width="303dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_ffffff_corner8"
        android:orientation="vertical">

        <TextView
            android:id="@+id/search_room_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="24dp"
            android:text="@string/search_room_dialog_1"
            android:textColor="#333333"
            android:textSize="18dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp">

            <LinearLayout
                android:id="@+id/input_lay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginEnd="24dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/shape_f5f5f5_corner4_stroke_ebebeb"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/search_iv"
                    android:layout_width="29dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="-6dp"
                    android:src="@drawable/family_search_dialog_icon" />

                <EditText
                    android:id="@+id/search_room_edit_text"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@color/color_transparent"
                    android:hint="@string/game_room_input_number"
                    android:singleLine="true"
                    android:paddingStart="8dp"
                    android:paddingTop="6dp"
                    android:paddingEnd="6dp"
                    android:paddingBottom="6dp"
                    android:text=""
                    android:textAlignment="viewStart"
                    android:textColor="#333333"
                    android:textColorHint="#CCCCCC"
                    android:textCursorDrawable="@drawable/cursor_accent_ex"
                    android:textSize="14dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/input_num_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/input_lay"
                android:layout_alignEnd="@id/input_lay"
                android:layout_marginTop="-20dp"
                android:textColor="@color/color_primary"
                android:textSize="@dimen/ios12"
                android:visibility="gone"
                tools:text="0/150"
                tools:visibility="visible" />

        </RelativeLayout>

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="@dimen/line_height" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/search_room_cancel_tx"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:layout_weight="1"
                android:background="@drawable/sel_00000000_corner20_strock_24c572"
                android:backgroundTint="@color/sel_accent_ex_accent_gray"
                android:gravity="center"
                android:text="@string/search_room_dialog_4"
                android:textAlignment="center"
                android:textColor="@color/color_accent_ex"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/search_room_enter_tx"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/sel_24c572_corner20"
                android:backgroundTint="@color/sel_accent_ex_accent_gray"
                android:gravity="center"
                android:text="@string/search_room_dialog_5"
                android:textAlignment="center"
                android:textColor="#ffffff"
                android:textSize="16dp" />

        </LinearLayout>

        <Space
            android:layout_width="wrap_content"
            android:layout_height="24dp" />

    </LinearLayout>

</RelativeLayout>