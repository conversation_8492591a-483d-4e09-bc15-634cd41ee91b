<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="235dp"
    android:layout_height="wrap_content"
    android:background="@drawable/chat_vip_present_msg_left_bg"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/content_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp">

        <ImageView
            android:id="@+id/icon_iv"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/chat_vip_present_icon" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="8dp"
            android:layout_toEndOf="@id/icon_iv"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="2"
                android:text="@string/chat_vip_present_model_1"
                android:textColor="#ff000000"
                android:textSize="@dimen/wp14" />

            <TextView
                android:id="@+id/content_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="#ffa0a0a0"
                android:textSize="@dimen/wp10"
                tools:text="Here\'s a free VIP Card for you." />

        </LinearLayout>

    </RelativeLayout>

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="8dp"
        android:src="#EEEEEE" />

    <TextView
        android:id="@+id/click_detail_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:text="@string/chat_vip_present_model_3"
        android:textColor="#ff24c572"
        android:textSize="12dp" />

</LinearLayout>