<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F7F7"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <Space
        android:layout_width="wrap_content"
        android:layout_height="12dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white_color"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:text="@string/activity_lover_update_couple_ring_1"
            android:textColor="#ff000000"
            android:textSize="16dp" />

        <TextView
            android:layout_width="343dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="23dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:text="@string/add_couple_ring"
            android:textColor="#ff2f2f2f"
            android:textSize="12dp" />

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="9dp"
        android:text="@string/activity_lover_update_couple_ring_2"
        android:textColor="#999999"
        android:textSize="14dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="72dp"
        android:background="#ffffff"
        android:orientation="horizontal">

        <com.wepie.wespy.module.marry.lover_home.RingBgView
            android:id="@+id/ring_iv"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp" />

        <TextView
            android:id="@+id/ring_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="12dp"
            android:text="@string/activity_lover_update_couple_ring_3"
            android:textColor="#4a4a4a"
            android:textSize="16dp" />

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="9dp"
        android:text="@string/activity_lover_update_couple_ring_4"
        android:textColor="#999999"
        android:textSize="14dp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:id="@+id/empty_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white_color"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/wp54"
                android:gravity="center"
                android:text="@string/activity_lover_update_couple_ring_5"
                android:textAlignment="center"
                android:textColor="#999999"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/goto_shop_tv"
                android:layout_width="126dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="32dp"
                android:layout_marginTop="24dp"
                android:background="@drawable/sel_accent_corner100"
                android:gravity="center"
                android:text="@string/activity_lover_update_couple_ring_6"
                android:textAlignment="center"
                android:textColor="#ffffff"
                android:textSize="16dp" />

        </LinearLayout>

        <ListView
            android:id="@+id/my_ring_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:divider="@color/list_divider_color"
            android:dividerHeight="1px"
            android:listSelector="@color/transparent"
            android:visibility="gone" />

    </FrameLayout>

    <TextView
        android:id="@+id/add_ring_tv"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/sel_accent_corner100"
        android:gravity="center"
        android:text="@string/activity_lover_update_couple_ring_7"
        android:textAlignment="center"
        android:textColor="#ffffff"
        android:textSize="16dp"
        android:visibility="gone" />

</LinearLayout>