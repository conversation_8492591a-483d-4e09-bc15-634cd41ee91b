<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <FrameLayout
        android:id="@+id/honor_seat_num_1_lay"
        android:layout_width="48dp"
        android:layout_height="48dp">

        <TextView
            android:id="@+id/honor_seat_num_1"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/voice_mic_select_empty_icon"
            android:gravity="center"
            android:text="7"
            android:textColor="@color/color_accent_ex"
            android:textSize="16dp"
            android:textStyle="bold" />
    </FrameLayout>

    <Space
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1" />

    <FrameLayout
        android:id="@+id/honor_seat_num_2_lay"
        android:layout_width="48dp"
        android:layout_height="48dp">

        <TextView
            android:id="@+id/honor_seat_num_2"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/voice_mic_select_empty_icon"
            android:gravity="center"
            android:text="7"
            android:textColor="@color/color_accent_ex"
            android:textSize="16dp"
            android:textStyle="bold" />

        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />
    </FrameLayout>

    <Space
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1" />

    <FrameLayout
        android:id="@+id/honor_seat_num_3_lay"
        android:layout_width="48dp"
        android:layout_height="48dp">

        <TextView
            android:id="@+id/honor_seat_num_3"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/voice_mic_select_empty_icon"
            android:gravity="center"
            android:text="7"
            android:textColor="@color/color_accent_ex"
            android:textSize="16dp"
            android:textStyle="bold" />

        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />
    </FrameLayout>

    <Space
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1" />

    <FrameLayout
        android:id="@+id/honor_seat_num_4_lay"
        android:layout_width="48dp"
        android:layout_height="48dp">

        <TextView
            android:id="@+id/honor_seat_num_4"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/voice_mic_select_empty_icon"
            android:gravity="center"
            android:text="7"
            android:textColor="@color/color_accent_ex"
            android:textSize="16dp"
            android:textStyle="bold" />

        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />
    </FrameLayout>

    <Space
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1" />

    <FrameLayout
        android:id="@+id/honor_seat_num_5_lay"
        android:layout_width="48dp"
        android:layout_height="48dp">

        <TextView
            android:id="@+id/honor_seat_num_5"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/voice_mic_select_empty_icon"
            android:gravity="center"
            android:text="7"
            android:textColor="@color/color_accent_ex"
            android:textSize="16dp"
            android:textStyle="bold" />

        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />
    </FrameLayout>

    <Space
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="1" />

    <FrameLayout
        android:id="@+id/honor_seat_num_6_lay"
        android:layout_width="48dp"
        android:layout_height="48dp">

        <TextView
            android:id="@+id/honor_seat_num_6"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/voice_mic_select_empty_icon"
            android:gravity="center"
            android:text="7"
            android:textColor="@color/color_accent_ex"
            android:textSize="16dp"
            android:textStyle="bold" />
    </FrameLayout>


</LinearLayout>