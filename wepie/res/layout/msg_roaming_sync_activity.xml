<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_background"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tip_tv"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:background="#fad787"
        android:drawableEnd="@drawable/icon_forward_16"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:singleLine="true"
        android:text="@string/msg_roaming_sync_activity_1"
        android:textColor="#68440e" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:srlEnableAutoLoadMore="false"
        app:srlEnableOverScrollBounce="false"
        app:srlEnableOverScrollDrag="false">

        <com.wepie.wespy.module.game.game.activity.PullListView
            android:id="@+id/pull_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:cacheColorHint="#00000000"
            android:divider="@null"
            android:fadingEdge="none"
            android:listSelector="#00000000"
            style="@style/scrollbar"
            android:overScrollMode="never" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:layout_marginTop="4dp"
        android:text="@string/msg_roaming_sync_activity_2"
        android:textColor="@color/color_primary"
        android:textSize="12dp" />

</LinearLayout>