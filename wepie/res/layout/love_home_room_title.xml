<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="13dp"
    android:paddingEnd="16dp"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/room_back_iv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/action_bar_left_back_img" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/room_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:paddingEnd="16dp"
                android:singleLine="true"
                android:textColor="#ffffff"
                android:textSize="16dp"
                android:textStyle="bold"
                tools:text="12345678920" />

        </LinearLayout>

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/room_more_iv"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:scaleType="centerInside"
        app:srcCompat="@drawable/user_info_more_img" />

</LinearLayout>