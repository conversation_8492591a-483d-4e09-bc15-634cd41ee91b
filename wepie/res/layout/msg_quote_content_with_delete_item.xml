<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/quote_guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="3dp" />

    <LinearLayout
        android:id="@+id/quote_item_lay"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toStartOf="@id/quote_delete"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@id/quote_guideline"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/red">

        <TextView
            android:id="@+id/quote_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:paddingBottom="2dp"
            android:textAlignment="viewStart"
            android:textColor="#999cb4"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="I'm name"
            tools:visibility="visible" />

        <com.wepie.wespy.helper.view.SimpleTextView
            android:id="@+id/quote_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#999cb4"
            android:textSize="12dp"
            tools:text="contentcontentcontentcontentcontentcontentcontentcontentcontentcontent" />
    </LinearLayout>

    <ImageView
        android:id="@+id/quote_indicator"
        android:layout_width="3dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_ecedef_r2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/quote_delete"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/quote_item_lay"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/quote_close_icon" />
</androidx.constraintlayout.widget.ConstraintLayout>