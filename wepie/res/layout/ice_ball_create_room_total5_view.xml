<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/ice_ball_match_vs_iv"
        android:layout_width="147dp"
        android:layout_height="147dp"
        android:background="@drawable/little_game_team_inner_circle"
        android:scaleType="centerInside"
        android:src="@drawable/little_game_team_inner_vs"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView
        android:id="@+id/ice_ball_create_room_user0"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="-43dp"
        app:layout_constraintBottom_toTopOf="@+id/ice_ball_match_vs_iv"
        app:layout_constraintEnd_toEndOf="@+id/ice_ball_match_vs_iv"
        app:layout_constraintStart_toStartOf="@+id/ice_ball_match_vs_iv" />

    <com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView
        android:id="@+id/ice_ball_create_room_user1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:layout_marginEnd="-74dp"
        app:layout_constraintEnd_toEndOf="@+id/ice_ball_match_vs_iv"
        app:layout_constraintTop_toTopOf="@+id/ice_ball_match_vs_iv" />

    <com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView
        android:id="@+id/ice_ball_create_room_user2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="-37dp"
        android:layout_marginBottom="-80dp"
        app:layout_constraintBottom_toBottomOf="@+id/ice_ball_match_vs_iv"
        app:layout_constraintEnd_toEndOf="@+id/ice_ball_match_vs_iv" />

    <com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView
        android:id="@+id/ice_ball_create_room_user3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-37dp"
        android:layout_marginBottom="-80dp"
        app:layout_constraintBottom_toBottomOf="@+id/ice_ball_match_vs_iv"
        app:layout_constraintStart_toStartOf="@+id/ice_ball_match_vs_iv" />

    <com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView
        android:id="@+id/ice_ball_create_room_user4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-74dp"
        android:layout_marginTop="11dp"
        app:layout_constraintStart_toStartOf="@+id/ice_ball_match_vs_iv"
        app:layout_constraintTop_toTopOf="@+id/ice_ball_match_vs_iv" />

</androidx.constraintlayout.widget.ConstraintLayout>