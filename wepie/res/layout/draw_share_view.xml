<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#20C08E">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/draw_share_bg" />

    <RelativeLayout
        android:id="@+id/root_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="10dp">

        <com.wepie.wespy.module.draw.core.DrawImageView
            android:id="@+id/draw_iv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:id="@+id/draw_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/draw_iv"
            android:orientation="vertical"
            android:src="#ffffff">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:src="#E2E2E2" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#FCFCFC"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/word_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/wp16"
                    android:textColor="#333333"
                    android:textSize="@dimen/wp18"
                    android:textStyle="bold"
                    tools:text="Word：--" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/wp80"
                    android:layout_gravity="center_horizontal">

                    <LinearLayout
                        android:id="@+id/one_user_lay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="horizontal">

                        <com.huiwan.widget.CustomCircleImageView
                            android:id="@+id/head_iv"
                            android:layout_width="@dimen/wp48"
                            android:layout_height="@dimen/wp48"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/default_head_icon" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/name_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/draw_share_view_2"
                                android:textColor="#333333"
                                android:textSize="16dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/time_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="#7B7F82"
                                android:textSize="14dp"
                                tools:text="Time：1 hour" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/multi_user_lay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/multi_user_time_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#7B7F82"
                            android:textSize="14dp"
                            tools:text="Time：1 hour" />

                        <com.wepie.wespy.module.draw.core.share.ShareUserView
                            android:id="@+id/user_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginTop="4dp"
                            android:visibility="visible" />

                    </LinearLayout>

                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/draw_scan_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/root_lay"
        android:background="@drawable/draw_scan_background"
        android:minHeight="10dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/draw_scan_code_iv"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:src="@drawable/draw_scan_code" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/draw_scan_weplay_iv"
                android:layout_width="wrap_content"
                android:layout_height="27dp"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                app:srcCompat="@drawable/jackaroo_app_name"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/draw_scan_tx1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="4dp"
                android:textColor="#ffffff"
                android:textSize="13dp"
                tools:text="線上桌遊吧 認識新朋友" />

            <TextView
                android:id="@+id/draw_scan_tx2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="6dp"
                android:text="@string/final_Interpretation_key"
                android:textColor="#ffffff"
                android:textSize="10dp" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>