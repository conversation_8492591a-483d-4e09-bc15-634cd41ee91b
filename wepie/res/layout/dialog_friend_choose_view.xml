<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/share_ffffff_corner16"
    android:paddingTop="6dp">

    <TextView
        android:id="@+id/friend_choose_title"
        android:layout_width="wrap_content"
        android:layout_height="?attr/actionBarSize"
        android:gravity="center_vertical"
        android:text="@string/select_player"
        android:textColor="@color/color_text_accent_dark"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/friend_choose_sure"
        android:layout_width="wrap_content"
        android:layout_height="?attr/actionBarSize"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:text="@string/confirm"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@id/friend_choose_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/friend_choose_title" />

    <LinearLayout
        android:id="@+id/search_lay"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_below="@id/friend_choose_title"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/friend_choose_title">

        <EditText
            android:id="@+id/search_bar"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="18dp"
            android:layout_marginEnd="18dp"
            android:background="@drawable/shape_fafafa_corner8"
            android:drawableStart="@drawable/ic_search_gray"
            android:drawablePadding="8dp"
            android:gravity="center_vertical"
            android:hint="@string/activity_friend_choose_search_hint"
            android:imeOptions="actionSearch"
            android:paddingStart="11dp"
            android:paddingEnd="11dp"
            android:singleLine="true"
            android:textColor="@color/color_text_accent_dark"
            android:textColorHint="#babbc2"
            android:textCursorDrawable="@drawable/cursor_24c572"
            android:textSize="14dp" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        app:layout_constraintTop_toBottomOf="@id/search_lay" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.wepie.wespy.module.game.util.RecyclerViewSideBar
            android:id="@+id/slide_bar"
            android:layout_width="20dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>