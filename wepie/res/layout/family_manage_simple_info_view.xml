<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_background"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:clipChildren="false"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:clipChildren="false"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:drawablePadding="8dp"
                android:text="@string/family_manage_activity_base_info_icon"
                android:textColor="#333"
                android:textSize="14dp"
                android:textStyle="bold"
                app:drawableStartCompat="@drawable/shape_accent_4_12" />

            <Space
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <com.huiwan.decorate.DecorHeadImgView
                android:id="@+id/head_iv"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="18dp"
                android:layout_marginEnd="18dp"
                android:clipChildren="false" />

        </LinearLayout>

        <View style="@style/LineHorizontal_333_0_5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:drawablePadding="8dp"
            android:text="@string/family_manage_activity_base_info_name"
            android:textColor="#333"
            android:textSize="14dp"
            android:textStyle="bold"
            app:drawableStartCompat="@drawable/shape_accent_4_12" />

        <TextView
            android:id="@+id/name_tv"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginStart="26dp"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:textAlignment="center"
            android:textColor="@color/color_primary"
            android:textSize="14dp"
            tools:text="葬爱家族" />

        <View
            style="@style/LineHorizontal_333_0_5dp"
            android:layout_marginTop="12dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:drawablePadding="8dp"
            android:text="@string/family_manage_activity_base_info_desc"
            android:textColor="#333"
            android:textSize="14dp"
            android:textStyle="bold"
            app:drawableStartCompat="@drawable/shape_accent_4_12" />

        <TextView
            android:id="@+id/desc_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="26dp"
            android:layout_marginTop="8dp"
            android:textColor="@color/color_primary"
            android:textSize="14dp"
            tools:text="大锅很帅，谁敢不同意，踢出去" />

        <View
            style="@style/LineHorizontal_333_0_5dp"
            android:layout_marginTop="12dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/family_light_lay"
            android:layout_width="match_parent"
            android:layout_height="72dp">

            <TextView
                android:id="@+id/family_light_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="12dp"
                android:drawablePadding="8dp"
                android:text="@string/prop_item_name_family_light"
                android:textColor="#333"
                android:textSize="14dp"
                android:textStyle="bold"
                app:drawableStartCompat="@drawable/shape_accent_4_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.huiwan.widget.FamilyLightView
                android:id="@+id/family_light_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="26dp"
                android:layout_marginTop="8dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/family_light_tv" />

            <ImageView
                style="@style/list_right_arrow"
                android:contentDescription="@string/iv_cd"
                android:padding="2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View style="@style/LineHorizontal_333_0_5dp" />

        <RelativeLayout
            android:id="@+id/family_title_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="48dp">

            <TextView
                android:id="@+id/family_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:drawablePadding="8dp"
                android:text="@string/family_title_details"
                android:textColor="#333"
                android:textSize="14dp"
                android:textStyle="bold"
                app:drawableStartCompat="@drawable/shape_accent_4_12" />

            <ImageView
                android:id="@+id/family_title_arrow_iv"
                style="@style/list_right_arrow"
                android:contentDescription="@string/iv_cd"
                android:padding="2dp" />

        </RelativeLayout>

        <View style="@style/LineHorizontal_333_0_5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:drawablePadding="8dp"
            android:gravity="center_vertical"
            android:text="@string/family_manage_activity_base_info_apply_title"
            android:textColor="#333"
            android:textSize="14dp"
            android:textStyle="bold"
            app:drawableStartCompat="@drawable/shape_accent_4_12" />

        <View
            style="@style/LineHorizontal_333_0_5dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="26dp"
                android:text="@string/family_manage_activity_base_info_apply_tips_1"
                android:textColor="#2f2f2f"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/apply_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="16dp"
                android:text="@string/family_manage_simple_info_view_6"
                android:textColor="#757575" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/condition_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                style="@style/LineHorizontal_333_0_5dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp" />

            <RelativeLayout
                android:id="@+id/no_verify_lay"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="26dp"
                    android:text="@string/family_manage_activity_base_info_apply_tips_2"
                    android:textColor="#2f2f2f"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/verify_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:text="@string/family_manage_simple_info_view_8"
                    android:textColor="#757575" />

            </RelativeLayout>

            <View
                style="@style/LineHorizontal_333_0_5dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp" />

            <LinearLayout
                android:id="@+id/verify_level_lay"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="26dp"
                    android:text="@string/family_join_require_title"
                    android:textColor="#2f2f2f"
                    android:textSize="14dp" />

                <Space
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <ImageView
                    android:id="@+id/charm_iv"
                    android:layout_width="34dp"
                    android:layout_height="18dp"
                    tools:src="@drawable/user_charm_level_7" />

                <TextView
                    android:id="@+id/level_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/family_manage_activity_base_info_apply_tips_3_common"
                    android:textColor="#757575" />
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>