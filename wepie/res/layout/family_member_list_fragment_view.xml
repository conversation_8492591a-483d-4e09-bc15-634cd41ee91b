<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/sort_lay"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="2dp"
        android:paddingEnd="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/coin_rank"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:layout_marginStart="16dp"
            android:gravity="center"
            android:text="@string/family_diamond_tips"
            android:textAlignment="center"
            android:textColor="@color/color_accent"
            android:textSize="12sp"
            android:textStyle="bold" />

        <View
            android:layout_width="1dp"
            android:layout_height="12dp"
            android:layout_marginHorizontal="8dp"
            android:background="#ecedef" />

        <TextView
            android:id="@+id/active_rank"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:gravity="center"
            android:text="@string/family_active_tips"
            android:textAlignment="center"
            android:textColor="#999cb4"
            android:textSize="12sp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/week_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/week_active_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_vertical"
                android:text="@string/family_week_donate"
                android:textColor="@color/color_primary"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/week_active_sort_iv"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_sort_none"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/week_active_tv"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/total_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp">

            <TextView
                android:id="@+id/total_active_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical|end"
                android:text="@string/family_total_donate"
                android:textColor="@color/color_primary"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/total_active_sort_iv"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_sort_none"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/total_active_tv"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tv_rank_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:paddingHorizontal="20dp"
        android:textColor="@color/color_text_primary_ex"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sort_lay"
        tools:text="按照本周收取礼物增加的魅力值进行排名，按照本周收取礼物增加的魅力值进行排名" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_rank_tips">
        <!-- 成员列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_members"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
</androidx.constraintlayout.widget.ConstraintLayout>