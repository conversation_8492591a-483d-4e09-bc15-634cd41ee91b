<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/wp56"
        android:paddingStart="@dimen/wp8"
        android:background="@color/white"
        android:paddingEnd="@dimen/wp8">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/wp36"
            android:layout_centerInParent="true"
            android:layout_toStartOf="@+id/edit_text_cancle"
            android:background="@drawable/shape_f4f4f4_corner100"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/wp12"
                android:layout_height="@dimen/wp12"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/wp8"
                android:src="@drawable/family_member_search_icon" />

            <EditText
                android:id="@+id/edit_text"
                android:layout_width="match_parent"
                android:layout_height="@dimen/wp30"
                android:layout_marginTop="@dimen/wp3"
                android:background="@color/transparent"
                android:hint="@string/family_member_search_hint"
                android:paddingStart="@dimen/wp8"
                android:paddingEnd="@dimen/wp8"
                android:singleLine="true"
                android:textAlignment="viewStart"
                android:textColor="#333333"
                android:textColorHint="#CACACA"
                android:textSize="@dimen/wp15" />

        </LinearLayout>

        <TextView
            android:id="@+id/edit_text_cancle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="15dp"
            android:gravity="center"
            android:text="@string/cancel"
            android:textAlignment="center"
            android:textColor="@color/color_text_secondary"
            android:textSize="15sp"
            android:visibility="gone"
            tools:visibility="visible" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/content_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        android:background="@color/white"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/sort_lay"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center_vertical"
            android:paddingStart="2dp"
            android:paddingEnd="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/rank_type_tv"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:layout_marginStart="16dp"
                android:gravity="center"
                android:paddingTop="4dp"
                android:text="@string/family_diamond_tips"
                android:textAlignment="center"
                android:textColor="@color/color_text_primary_ex"
                android:textSize="12sp"
                android:textStyle="bold" />

            <Space
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/week_active_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginEnd="26dp"
                android:gravity="center_vertical"
                android:text="@string/family_week_donate"
                android:textColor="@color/color_primary"
                android:textSize="12dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/total_active_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:gravity="center_vertical|end"
                android:text="@string/family_total_donate"
                android:textColor="@color/color_primary"
                android:textSize="12dp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/member_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:itemCount="3"
            tools:layout_height="300dp"
            tools:listitem="@layout/family_tab_member_item_view" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/empty_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f4f4f4"
        android:visibility="gone">

        <com.huiwan.base.ui.empty.HWUIEmptyView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="140dp"
            app:icon_type="base_empty_find_empty"
            app:text="@string/search_empty_result"
            app:text_color="@color/color_text_tertiary" />

    </FrameLayout>

    <View
        android:id="@+id/mask_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>