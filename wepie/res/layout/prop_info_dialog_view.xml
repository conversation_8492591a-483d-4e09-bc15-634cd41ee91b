<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.huiwan.widget.CircleFrameLayout
        android:id="@+id/prop_corner_container"
        android:layout_width="303dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        app:cf_radius="12dp">

        <LinearLayout
            android:id="@+id/prop_container"
            android:layout_width="303dp"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.wepie.wespy.module.shop.dialogs.PacketSkinView
                android:id="@+id/packet_skin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible" />


            <View
                android:id="@+id/prop_preview_bg_view"
                android:layout_width="match_parent"
                android:layout_height="144dp"
                android:background="@color/white_color" />

            <LinearLayout
                android:id="@+id/prop_ctrl_lay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/prop_preview_bg_view"
                android:background="@color/white_color"
                android:orientation="vertical"
                android:paddingBottom="24dp">

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/scroller"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="@color/white_color"
                    android:orientation="vertical"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/prop_home_sec_kill_tip"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/shape_33000000_corner19"
                            android:drawableStart="@drawable/time_left"
                            android:drawablePadding="4dp"
                            android:gravity="center"
                            android:paddingStart="8dp"
                            android:paddingEnd="8dp"
                            android:textAlignment="center"
                            android:textColor="@color/white_color"
                            android:textSize="12dp"
                            android:visibility="gone"
                            tools:text="秒杀预告:1日23分" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/prop_name_lay"
                            android:layout_width="match_parent"
                            android:minHeight="24dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:orientation="horizontal"
                            android:paddingStart="20dp"
                            android:paddingEnd="20dp">

                            <TextView
                                android:id="@+id/prop_name_tv"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:gravity="start"
                                android:textColor="#333"
                                android:textSize="16dp"
                                android:textStyle="bold"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/prop_coin_cost_lay"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="I Do戒指" />

                            <LinearLayout
                                android:id="@+id/prop_coin_cost_lay"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:orientation="horizontal"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/prop_rmb_cost_lay"
                                app:layout_constraintTop_toTopOf="parent">

                                <ImageView
                                    android:id="@+id/coin_iv"
                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    android:layout_gravity="center_vertical"
                                    android:padding="2dp"
                                    android:src="@drawable/wejoy_coin_icon" />

                                <TextView
                                    android:id="@+id/prop_coin_cost_tv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginStart="3dp"
                                    android:gravity="center"
                                    android:includeFontPadding="false"
                                    android:textAlignment="center"
                                    android:textColor="#FFA60E"
                                    android:textSize="16dp"
                                    android:textStyle="bold"
                                    tools:text="334420" />

                                <TextView
                                    android:id="@+id/prop_coin_discount_tv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="bottom"
                                    android:layout_marginStart="4dp"
                                    android:layout_marginBottom="2dp"
                                    android:textColor="#979797"
                                    android:textSize="8dp"
                                    tools:text="12000" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/prop_rmb_cost_lay"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:visibility="gone"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:visibility="visible">

                                <TextView
                                    android:id="@+id/prop_rmb_cost_tv"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:gravity="center_vertical"
                                    android:padding="0dp"
                                    android:textColor="#FFA60E"
                                    android:textSize="16dp"
                                    android:textStyle="bold"
                                    tools:text="RMB 10" />

                            </LinearLayout>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <com.wepie.wespy.module.shop.dialogs.GiftExtraPropView
                            android:id="@+id/gift_extra_prop_view"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:layout_marginHorizontal="20dp"
                            android:layout_marginVertical="12dp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/prop_price_list_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="18dp"
                            android:orientation="vertical"
                            android:paddingStart="20dp"
                            android:paddingEnd="20dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/prop_info_dialog_view_1"
                                android:textColor="#333"
                                android:textSize="12dp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/prop_price_lv"
                                android:layout_width="265dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                tools:itemCount="3"
                                tools:layoutManager="GridLayoutManager"
                                tools:listitem="@layout/prop_info_dialog_price_item_view"
                                tools:spanCount="3" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/prop_reward_list_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="18dp"
                            android:orientation="vertical"
                            android:paddingStart="20dp"
                            android:paddingEnd="20dp"
                            android:visibility="gone">

                            <TextView
                                android:id="@+id/prop_reward_tip_tv"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333"
                                android:textSize="12dp"
                                tools:text="Select 1 Free Gift" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/prop_reward_lv"
                                android:layout_width="228dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                tools:layoutManager="GridLayoutManager"
                                tools:spanCount="3" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/prop_desc_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="18dp"
                            android:orientation="vertical"
                            android:paddingStart="20dp"
                            android:paddingEnd="20dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/prop_info_dialog_view_3"
                                android:textColor="#333"
                                android:textSize="12dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/prop_desc_tv"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:ellipsize="end"
                                android:lineSpacingExtra="6dp"
                                android:textAlignment="viewStart"
                                android:textColor="#979797"
                                android:textSize="12dp"
                                tools:text="购买保险并签订保单后，若一年内有一方强制离婚，需支付指定金额才可离婚。保险生效满1年，可申请99朵玫瑰花。" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/prop_home_anim_tip_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/shape_33000000_corner19"
                            android:drawableStart="@drawable/time_left"
                            android:drawablePadding="4dp"
                            android:gravity="center"
                            android:paddingStart="8dp"
                            android:paddingEnd="8dp"
                            android:textAlignment="center"
                            android:textColor="@color/white_color"
                            android:textSize="12dp"
                            android:visibility="gone"
                            tools:text="剩12天"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/prop_gain_desc_tv"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:gravity="center"
                            android:paddingStart="20dp"
                            android:paddingEnd="20dp"
                            android:textAlignment="center"
                            android:textColor="@color/color_primary"
                            android:textSize="12dp"
                            tools:text="收到秘密花园礼物后获得" />

                        <com.wepie.wespy.helper.indicators.LineIndicatorView
                            android:id="@+id/prop_line_indicator"
                            android:layout_width="match_parent"
                            android:layout_height="8dp"
                            android:layout_marginTop="12dp" />
                    </LinearLayout>

                </androidx.core.widget.NestedScrollView>

                <TextView
                    android:id="@+id/prop_purchase_btn"
                    android:layout_width="255dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="24dp"
                    android:background="@drawable/sel_prop_purchase_btn_bg"
                    android:gravity="center"
                    android:textColor="@color/white_color"
                    android:textStyle="bold"
                    tools:text="购买" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:id="@+id/prop_desc_mask_view"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="84dp"
            android:background="@drawable/prop_desc_mask"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/prop_info_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:background="@drawable/shape_33000000_corner100"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1dp"
            android:textColor="@color/white"
            android:textSize="11sp"
            android:visibility="gone" />

    </com.huiwan.widget.CircleFrameLayout>

    <RelativeLayout
        android:id="@+id/prop_preview_container"
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:layout_centerHorizontal="true"
        android:clipChildren="false">

        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/prop_preview_iv"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerInParent="true" />
        <com.huiwan.anim.LifecycleAnimView
                android:id="@+id/anim_view"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:visibility="gone"
                android:layout_centerInParent="true"/>

        <FrameLayout
            android:id="@+id/empty_frame"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/prop_preview_tip_tv"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="78dp"
            android:background="@drawable/shape_33000000_corner19"
            android:drawablePadding="4dp"
            android:gravity="center"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:textAlignment="center"
            android:textColor="@color/white_color"
            android:textSize="12dp"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/time_left"
            tools:text="剩12天"
            tools:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>