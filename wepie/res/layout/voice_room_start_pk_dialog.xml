<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="538dp">

    <TextView
        android:id="@+id/voice_room_pk_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:text="@string/voice_room_pk_new_title"
        android:textColor="#FF1B1D38"
        android:textSize="14dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/voice_room_pk_help_iv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        android:scaleType="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/voice_room_pk_title"
        app:layout_constraintBottom_toBottomOf="@+id/voice_room_pk_title"
        app:srcCompat="@drawable/ic_help_dragon"
        app:tint="#282D45" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@+id/voice_room_pk_start_tv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/voice_room_pk_title"
        android:layout_marginBottom="21dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/voice_room_pk_mode_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="27dp"
                    android:text="@string/voice_room_pk_new_select"
                    android:textColor="#FF1B1D38"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/voice_room_pk_one_lay"
                    android:layout_width="0dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/voice_room_pk_select_bg"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toStartOf="@+id/voice_room_pk_multi_lay"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/voice_room_pk_mode_tv">

                    <ImageView
                        android:id="@+id/voice_room_pk_one_iv"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginTop="8dp"
                        android:src="@drawable/voice_room_pk_one_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/voice_room_pk_one_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="@string/voice_room_pk_new_one"
                        android:textColor="@color/color_accent"
                        android:textStyle="bold" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/voice_room_pk_multi_lay"
                    android:layout_width="0dp"
                    android:layout_height="72dp"
                    android:layout_marginStart="9dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/voice_room_pk_unselect_bg"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/voice_room_pk_one_lay"
                    app:layout_constraintTop_toTopOf="@+id/voice_room_pk_one_lay">

                    <ImageView
                        android:id="@+id/voice_room_pk_multi_iv"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginTop="8dp"
                        android:src="@drawable/voice_room_pk_multi_icon"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/voice_room_pk_multi_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="@string/voice_room_pk_new_multi"
                        android:textColor="@color/text_secondary" />
                </LinearLayout>


                <TextView
                    android:id="@+id/voice_room_pk_rule_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="32dp"
                    android:text="@string/voice_room_pk_new_rules"
                    android:textColor="#FF1B1D38"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/voice_room_pk_one_lay" />

                <TextView
                    android:id="@+id/voice_room_pk_tips_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="4dp"
                    android:text="@string/voice_room_pk_new_rules_tips"
                    android:textColor="#999CB4"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/voice_room_pk_rule_tv" />

                <TextView
                    android:id="@+id/voice_room_pk_gift_tv"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/voice_room_pk_select_bg"
                    android:gravity="center"
                    android:text="@string/voice_room_pk_new_by_gift"
                    android:textColor="@color/color_accent"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/voice_room_pk_people_tv"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/voice_room_pk_tips_tv"
                    app:layout_goneMarginEnd="16dp" />

                <TextView
                    android:id="@+id/voice_room_pk_people_tv"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="9dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/voice_room_pk_unselect_bg"
                    android:gravity="center"
                    android:text="@string/voice_room_pk_new_by_num"
                    android:textColor="@color/text_secondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/voice_room_pk_gift_tv"
                    app:layout_constraintTop_toTopOf="@+id/voice_room_pk_gift_tv" />


                <TextView
                    android:id="@+id/voice_room_pk_duration_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="32dp"
                    android:text="@string/voice_room_pk_new_time_set"
                    android:textColor="#FF1B1D38"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/voice_room_pk_gift_tv" />

                <TextView
                    android:id="@+id/voice_room_pk_time1"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/voice_room_pk_select_bg"
                    android:gravity="center"
                    android:text="@string/voice_room_pk_new_minute1"
                    android:textColor="@color/color_accent"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@+id/voice_room_pk_time2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/voice_room_pk_duration_tv" />

                <TextView
                    android:id="@+id/voice_room_pk_time2"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/voice_room_pk_unselect_bg"
                    android:gravity="center"
                    android:text="@string/voice_room_pk_new_minute2"
                    android:textColor="@color/text_secondary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/voice_room_pk_time3"
                    app:layout_constraintStart_toEndOf="@+id/voice_room_pk_time1"
                    app:layout_constraintTop_toTopOf="@+id/voice_room_pk_time1" />

                <TextView
                    android:id="@+id/voice_room_pk_time3"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/voice_room_pk_unselect_bg"
                    android:gravity="center"
                    android:text="@string/voice_room_pk_new_minute3"
                    android:textColor="@color/text_secondary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/voice_room_pk_time2"
                    app:layout_constraintTop_toTopOf="@+id/voice_room_pk_time1" />


                <TextView
                    android:id="@+id/voice_room_pk_time4"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/voice_room_pk_unselect_bg"
                    android:gravity="center"
                    android:text="@string/voice_room_pk_new_minute4"
                    android:textColor="@color/color_text_secondary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/voice_room_pk_time5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/voice_room_pk_time1" />

                <TextView
                    android:id="@+id/voice_room_pk_time5"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/voice_room_pk_unselect_bg"
                    android:gravity="center"
                    android:text="@string/voice_room_pk_new_minute5"
                    android:textColor="@color/color_text_secondary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@+id/voice_room_pk_time6"
                    app:layout_constraintStart_toEndOf="@+id/voice_room_pk_time4"
                    app:layout_constraintTop_toTopOf="@+id/voice_room_pk_time4" />

                <TextView
                    android:id="@+id/voice_room_pk_time6"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/voice_room_pk_unselect_bg"
                    android:gravity="center"
                    android:text="@string/voice_room_pk_new_minute6"
                    android:textColor="@color/color_text_secondary"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/voice_room_pk_time5"
                    app:layout_constraintTop_toTopOf="@+id/voice_room_pk_time4" />

            </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/voice_room_pk_start_tv"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/color_accent_selector_corner24"
        android:gravity="center"
        android:text="@string/voice_room_pk_new_start"
        android:textColor="@color/color_white"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>