<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/friend_txt_lay"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.huiwan.component.prop.ChatMsgBubbleItem
        android:id="@+id/bubble_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignStart="@id/chat_invite_card_layout"
        android:layout_alignTop="@id/chat_invite_card_layout"
        android:layout_alignEnd="@id/chat_invite_card_layout"
        android:layout_alignBottom="@+id/chat_invite_card_layout" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/chat_invite_card_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="2dp"
        android:orientation="vertical"
        android:paddingLeft="21dp"
        android:paddingTop="14dp"
        android:paddingRight="16dp"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:maxWidth="225dp"
            android:textColor="#1B1D38"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="What you care about, dg is cool xxxxxxxxxxxx,Invite you into the voice room" />

        <com.huiwan.widget.CustomCircleImageView
            android:id="@+id/head_iv"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/default_head_icon"
            app:civ_corner_radius="8dp"
            app:layout_constraintStart_toStartOf="@+id/name_tv"
            app:layout_constraintTop_toBottomOf="@+id/name_tv" />

        <TextView
            android:id="@+id/room_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="10dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxWidth="170dp"
            android:maxLines="1"
            android:textColor="#1B1D38"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@+id/head_iv"
            app:layout_constraintTop_toBottomOf="@+id/name_tv"
            app:layout_goneMarginTop="8dp"
            tools:text="wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww" />

        <TextView
            android:id="@+id/label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#999CB4"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@+id/room_name_tv"
            app:layout_constraintTop_toBottomOf="@+id/room_name_tv"
            tools:text="音乐 · C140772" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>