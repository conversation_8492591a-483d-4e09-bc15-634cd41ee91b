<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/choose_propose_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:gravity="center"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:text="@string/propse_reach_xx"
        android:textAlignment="center"
        android:textColor="#999999"
        android:textSize="12dp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/list_lay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:visibility="gone">

            <ListView
                android:id="@+id/choose_propose_list"
                style="@style/scrollbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:cacheColorHint="#00000000"
                android:fadingEdge="none"
                android:listSelector="#00000000" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="3dp">

                <com.wepie.wespy.module.game.util.SideBar
                    android:id="@+id/choose_propose_bar"
                    android:layout_width="20dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical" />

            </LinearLayout>

        </RelativeLayout>

        <com.huiwan.base.ui.empty.HWUIEmptyView
            android:id="@+id/empty_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#ffffff"
            android:gravity="center"
            android:paddingHorizontal="20dp"
            android:visibility="gone"
            app:button_text="@string/activity_propose_choose_user_3"
            app:icon_type="base_empty_no_friend_request"
            app:text="@string/activity_propose_choose_user_2"
            app:text_color="@color/color_text_tertiary" />

    </FrameLayout>

</LinearLayout>