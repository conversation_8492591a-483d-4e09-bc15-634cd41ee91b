<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/game_item_room_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:orientation="vertical">

    <Space
        android:id="@+id/game_item_self_top_image"
        android:layout_width="match_parent"
        android:layout_height="10dp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false">

        <include
            android:id="@+id/game_item_room_time_lay"
            layout="@layout/single_room_time_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"/>

        <Space
            android:id="@+id/game_item_room_icon_padding"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_below="@id/game_item_room_time_lay" />

        <com.huiwan.decorate.DecorHeadImgView
            android:id="@+id/game_item_room_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_below="@id/game_item_room_icon_padding"
            android:layout_alignParentStart="true"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="8dp"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/game_item_room_singer_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignStart="@id/game_item_room_icon"
            android:layout_alignTop="@id/game_item_room_icon"
            android:layout_marginStart="-6dp"
            android:layout_marginTop="-6dp"
            android:background="@drawable/singer_identity_icon" />

        <Space
            android:id="@+id/game_item_room_tip_padding"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_below="@id/game_item_room_time_lay" />

        <include
            android:id="@+id/game_item_room_name_lay"
            layout="@layout/single_room_name_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/game_item_room_icon"
            android:layout_marginStart="10dp"
            android:layout_marginTop="0dp"
            android:layout_toEndOf="@id/game_item_room_icon"/>

        <com.wepie.wespy.module.chat.gamemodel.MsgAllGameInviteModel
            android:id="@+id/game_item_all_game_invite_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/game_item_room_name_lay"
            android:layout_marginStart="5dp"
            android:layout_toEndOf="@id/game_item_room_icon" />

    </RelativeLayout>

    <Space
        android:id="@+id/game_item_room_padding_image"
        android:layout_width="match_parent"
        android:layout_height="10dp" />

    <Space
        android:id="@+id/last_item_padding"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:visibility="gone" />
</LinearLayout>