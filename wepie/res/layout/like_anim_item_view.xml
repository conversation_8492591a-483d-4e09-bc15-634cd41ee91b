<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="30dp"
    android:background="@drawable/shape_33000000_corner100">

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/head_iv"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="6dp"
        app:civ_border_color="@color/white_color"
        app:civ_border_width="1dp" />

    <TextView
        android:id="@+id/tip_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="6dp"
        android:layout_toEndOf="@id/head_iv"
        android:singleLine="true"
        android:textColor="@color/white_color"
        android:textSize="10dp"
        android:textStyle="bold"
        tools:text="大锅很帅赞了这幅画" />

</RelativeLayout>