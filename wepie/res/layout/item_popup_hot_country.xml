<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:id="@+id/country_item"
        app:layout_constraintDimensionRatio="w,1:3"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center"
        android:textSize="12sp"
        android:background="@drawable/bg_item_popup_hot_country"
        tools:text="阿拉伯"/>

</androidx.constraintlayout.widget.ConstraintLayout>