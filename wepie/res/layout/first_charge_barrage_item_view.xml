<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="566dp">

    <ImageView
        android:id="@+id/first_charge_bg_iv"
        android:layout_width="303dp"
        android:layout_height="510dp"
        android:layout_centerHorizontal="true"
        android:src="@drawable/gift_packet_win_bg_long" />

    <TextView
        android:id="@+id/theme_tv"
        android:layout_width="180dp"
        android:layout_height="74dp"
        android:layout_alignStart="@id/first_charge_bg_iv"
        android:layout_marginStart="20dp"
        android:layout_marginTop="30dp"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/ios3"
        android:paddingStart="@dimen/ios9"
        android:paddingEnd="@dimen/ios9"
        android:textColor="#ffbe4a00"
        android:textSize="@dimen/ios13" />

    <include
        layout="@layout/first_charge_time_layout"
        android:layout_width="303dp"
        android:layout_height="108dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="115dp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/ios5"
        android:layout_marginTop="202dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/item_rv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="@dimen/ios16"
                android:layout_marginTop="@dimen/ios14"
                android:layout_marginEnd="@dimen/ios16"
                android:clipChildren="false"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="3"
                tools:listitem="@layout/first_charge_gift_item_view" />

        </RelativeLayout>

    </RelativeLayout>

    <TextView
        android:id="@+id/barrage_one_iv"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/ios18"
        android:layout_marginStart="@dimen/ios10"
        android:layout_marginTop="@dimen/ios198"
        android:background="@drawable/gift_packet_barrage_bg"
        android:gravity="center_vertical"
        android:paddingStart="8dp"
        android:paddingEnd="26dp"
        android:text="@string/gift_packet_barrage_2"
        android:textColor="@color/white"
        android:textSize="10dp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/barrage_two_iv"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/ios18"
        android:layout_marginStart="@dimen/ios10"
        android:layout_marginTop="@dimen/ios302"
        android:background="@drawable/gift_packet_barrage_bg"
        android:gravity="center_vertical"
        android:paddingStart="8dp"
        android:paddingEnd="26dp"
        android:text="@string/gift_packet_barrage_3"
        android:textColor="@color/white"
        android:textSize="10dp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/barrage_three_iv"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/ios18"
        android:layout_marginStart="@dimen/ios10"
        android:layout_marginTop="@dimen/ios302"
        android:background="@drawable/gift_packet_barrage_bg"
        android:gravity="center_vertical"
        android:paddingStart="8dp"
        android:paddingEnd="26dp"
        android:text="@string/gift_packet_barrage_1"
        android:textColor="@color/white"
        android:textSize="10dp"
        android:textStyle="bold" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/ios5"
        android:layout_marginTop="328dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/extra_item_rv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="@dimen/ios16"
                android:layout_marginTop="@dimen/ios14"
                android:layout_marginEnd="@dimen/ios16"
                android:clipChildren="false"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="3"
                tools:listitem="@layout/first_charge_gift_item_view" />

        </RelativeLayout>

    </RelativeLayout>

    <TextView
        android:id="@+id/submit_tv"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_alignStart="@id/first_charge_bg_iv"
        android:layout_alignEnd="@id/first_charge_bg_iv"
        android:layout_marginStart="40dp"
        android:layout_marginTop="438dp"
        android:layout_marginEnd="40dp"
        android:background="@drawable/sel_29c472_corner24_stroke2"
        android:gravity="center"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/ios16"
        tools:text="$5" />

    <ImageView
        android:id="@+id/close_iv"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:scaleType="center"
        android:src="@drawable/first_charge_close" />

</RelativeLayout>