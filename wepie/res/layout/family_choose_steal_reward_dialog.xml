<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/pocket_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/pocket_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/pocket_view1"
                android:layout_width="@dimen/wp90"
                android:layout_height="@dimen/wp120"
                android:src="@drawable/family_steal_choose_pocket_icon" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/pocket_view2"
                android:layout_width="@dimen/wp90"
                android:layout_height="@dimen/wp120"
                android:src="@drawable/family_steal_choose_pocket_icon" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/pocket_view3"
                android:layout_width="@dimen/wp90"
                android:layout_height="@dimen/wp120"
                android:src="@drawable/family_steal_choose_pocket_icon" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

        </LinearLayout>

        <ImageView
            android:id="@+id/close_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/pocket_lay"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="60dp"
            android:src="@drawable/family_steal_close_icon" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/pocket_view"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="40dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="@string/family_steal_activity_steal_choose_tips1"
            android:textColor="#ffffff"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/steal_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="7dp"
            android:text="@string/family_steal_activity_steal_choose_tips2"
            android:textColor="#CCffffff"
            android:textSize="14dp" />

    </LinearLayout>

</RelativeLayout>