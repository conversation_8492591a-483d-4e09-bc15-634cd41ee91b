<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_fff_corner16_tl_tr">


    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_1"
        android:layout_width="60dp"
        android:layout_height="70dp"
        app:layout_constraintEnd_toStartOf="@id/seat_10"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:seat_num="1"
        tools:background="#30f0" />


    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_10"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginStart="30dp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/seat_1"
        app:layout_constraintTop_toTopOf="parent"
        app:seat_num="10"
        app:seat_type="1"
        />


    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_2"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toStartOf="@id/seat_3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/seat_1"
        app:seat_num="2"
        tools:background="#30f0" />

    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_3"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toStartOf="@id/seat_4"
        app:layout_constraintStart_toEndOf="@id/seat_2"
        app:layout_constraintTop_toBottomOf="@id/seat_1"
        app:seat_num="3"
        tools:background="#30f0" />

    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_4"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toStartOf="@id/seat_5"
        app:layout_constraintStart_toEndOf="@id/seat_3"
        app:layout_constraintTop_toBottomOf="@id/seat_1"
        app:seat_num="4"
        tools:background="#30f0" />

    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_5"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/seat_4"
        app:layout_constraintTop_toBottomOf="@id/seat_1"
        app:seat_num="5"
        tools:background="#30f0" />


    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_6"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toStartOf="@id/seat_3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/seat_2"
        app:seat_num="6"
        tools:background="#30f0" />

    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_7"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toStartOf="@id/seat_4"
        app:layout_constraintStart_toEndOf="@id/seat_2"
        app:layout_constraintTop_toBottomOf="@id/seat_2"
        app:seat_num="7"
        tools:background="#30f0" />

    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_8"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toStartOf="@id/seat_5"
        app:layout_constraintStart_toEndOf="@id/seat_3"
        app:layout_constraintTop_toBottomOf="@id/seat_2"
        app:seat_num="8"
        tools:background="#30f0" />

    <com.wepie.wespy.module.voiceroom.scoreboard.ScoreSeatView
        android:id="@+id/seat_9"
        android:layout_width="60dp"
        android:layout_height="70dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/seat_4"
        app:layout_constraintTop_toBottomOf="@id/seat_2"
        app:seat_num="9"
        tools:background="#30f0" />

    <include
        layout="@layout/voice_score_honor_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@+id/seat_9"
        app:layout_constraintStart_toStartOf="@+id/seat_6"
        app:layout_constraintTop_toBottomOf="@+id/seat_6" />

</androidx.constraintlayout.widget.ConstraintLayout>