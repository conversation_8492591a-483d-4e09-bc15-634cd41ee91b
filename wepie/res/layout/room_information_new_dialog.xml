<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:background="@color/black">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/room_info_root_view"
        android:layout_width="match_parent"
        android:layout_height="560dp"
        android:layout_gravity="bottom">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="30dp"
            android:background="@drawable/user_detail_info_layout_bg" />

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginStart="20dp"
            android:src="@drawable/circle_shadow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.huiwan.widget.CustomCircleImageView
            android:id="@+id/room_info_picture"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginStart="20dp"
            android:scaleType="centerCrop"
            android:src="@drawable/default_head_icon"
            app:civ_border_color="#ffffff"
            app:civ_corner_radius="24dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/room_info_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="44dp"
            android:layout_marginEnd="68dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/voice_room_info_setting" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/room_info_jump"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="44dp"
            android:layout_marginEnd="12dp"
            android:layout_toEndOf="@+id/room_info_setting"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/voice_room_invite_friends" />

        <RelativeLayout
            android:id="@+id/room_info_relativeLayout_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@+id/room_info_picture">

            <TextView
                android:id="@+id/room_info_room_name"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_alignParentStart="true"
                android:layout_marginEnd="10dp"
                android:layout_toStartOf="@+id/room_level_iv"
                android:ellipsize="end"
                android:singleLine="true"
                android:textAlignment="viewStart"
                android:textColor="@color/color_text_accent_dark"
                android:textSize="20sp"
                tools:text="房间名称最多10个字房间名称最多10个字" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/room_info_room_name"
                android:layout_alignParentStart="true"
                android:layout_marginTop="2.5dp"
                android:layout_marginEnd="10dp"
                android:layout_toStartOf="@+id/room_level_iv"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/room_info_room_label_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="18dp"
                    android:background="@drawable/room_label_bg"
                    android:minWidth="30dp"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="1dp"
                    android:text="@string/wedding"
                    android:textColor="#FF59A9"
                    android:textSize="11dp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/room_info_room_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:background="@drawable/bg_fafafa_corner9"
                    android:gravity="center"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:textAlignment="center"
                    android:textColor="#999CB4"
                    android:textSize="11dp"
                    tools:text="高级语音房" />

                <com.wepie.wespy.voiceroom.RoomIdView
                    android:id="@+id/room_info_room_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:gravity="start"
                    android:textColor="#999CB4"
                    android:textSize="12sp"
                    app:riv_scene="userinfo"
                    tools:text="ID:12345" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/room_id_copy"
                    android:layout_width="18dp"
                    android:layout_height="match_parent"
                    android:paddingHorizontal="4dp"
                    android:scaleType="center"
                    app:srcCompat="@drawable/ic_id_copy" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/today_exp_lay"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="@drawable/advance_room_today_exp_bg"
                android:minWidth="96dp"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="12dp"
                    android:layout_marginStart="36dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="20dp"
                    android:text="@string/voice_room_today_experience_title"
                    android:textColor="#1B1D38"
                    android:textSize="10dp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/today_exp_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="36dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="#9D25E6"
                    android:textSize="10dp"
                    tools:text="3568" />

                <ProgressBar
                    android:id="@+id/today_exp_pb"
                    style="@android:style/Widget.ProgressBar.Horizontal"
                    android:layout_width="40dp"
                    android:layout_height="2dp"
                    android:layout_marginStart="36dp"
                    android:layout_marginTop="2dp"
                    android:max="100"
                    android:progress="50" />

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/room_level_iv"
                android:layout_width="40dp"
                android:layout_height="32dp"
                android:layout_alignStart="@+id/today_exp_lay"
                android:layout_centerVertical="true"
                android:layout_marginStart="-6dp"
                android:scaleType="fitXY"
                android:visibility="gone"
                tools:visibility="visible" />

        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:scrollbars="none"
            app:layout_constraintBottom_toTopOf="@+id/room_info_layout_bottom"
            app:layout_constraintTop_toBottomOf="@+id/room_info_relativeLayout_2">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/family_room_info_lay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="28dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.huiwan.decorate.DecorHeadImgView
                        android:id="@+id/family_avatar_iv"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="20dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/family_avatar_iv"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.huiwan.decorate.NameTextView
                            android:id="@+id/name_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:maxWidth="125dp"
                            android:maxEms="5"
                            android:singleLine="true"
                            android:textAlignment="center"
                            android:textColor="#1B1D38"
                            android:textSize="16dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="AAAAAAAAAAAAAAAAAAAA" />

                        <com.wepie.wespy.module.family.FamilyLevelIconView
                            android:id="@+id/family_level_view"
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_marginStart="4dp"
                            app:layout_constraintBottom_toBottomOf="@+id/name_tv"
                            app:layout_constraintStart_toEndOf="@+id/name_tv"
                            app:layout_constraintTop_toTopOf="@+id/name_tv" />

                        <com.huiwan.widget.FamilyLightView
                            android:id="@+id/family_light_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="@+id/name_tv"
                            app:layout_constraintStart_toEndOf="@+id/family_level_view"
                            app:layout_constraintTop_toTopOf="@+id/name_tv" />

                        <TextView
                            android:id="@+id/family_leader_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="7dp"
                            android:ellipsize="end"
                            android:maxWidth="200dp"
                            android:singleLine="true"
                            android:textColor="#999999"
                            android:textSize="12dp"
                            app:layout_constraintStart_toStartOf="@id/name_tv"
                            app:layout_constraintTop_toBottomOf="@+id/name_tv"
                            tools:text="族长：望月一哥" />


                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_goneMarginEnd="16dp"
                        app:srcCompat="@drawable/ic_arrow_right"
                        app:tint="#E6E7EC" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:orientation="horizontal">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/room_manager_info_recycle"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:clipChildren="false"
                        android:overScrollMode="never"
                        tools:itemCount="1"
                        tools:listitem="@layout/room_manager_info_item" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/room_info_forward_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="20dp"
                        android:scaleType="center"
                        app:srcCompat="@drawable/ic_arrow_right"
                        app:tint="#E6E7EC" />

                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/room_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="36dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/room_info_layout_online"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="8dp"
                        app:layout_constraintEnd_toStartOf="@+id/room_info_layout_member"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/room_info_online"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/online_text"
                            android:textColor="#FF25AC7B"
                            android:textSize="14dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/room_info_online_num"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:text=""
                            android:textColor="#FF25AC7B"
                            android:textSize="16dp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/room_info_online"
                            tools:text="123456" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_arrow_right"
                            app:tint="#25AC7B" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/room_info_layout_member"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_marginStart="15dp"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/room_info_layout_online"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/room_info_member_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/member_text"
                            android:textColor="#FFD27D2E"
                            android:textSize="14dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/room_info_member_num"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:text=""
                            android:textColor="#FFD27D2E"
                            android:textSize="16dp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/room_info_member_value"
                            tools:text="123456" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_arrow_right"
                            app:tint="#D27D2E" />

                    </androidx.constraintlayout.widget.ConstraintLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>

                <FrameLayout
                    android:id="@+id/room_info_layout_fans"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|start"
                        android:text="@string/fans"
                        android:textColor="#999CB4"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/room_info_fan_number"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|end"
                        android:textColor="#1B1D38"
                        android:textSize="14sp"
                        tools:text="3698" />

                </FrameLayout>

                <FrameLayout
                    android:id="@+id/room_info_layout_area"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|start"
                        android:text="@string/activity_edit_userinfo_10"
                        android:textColor="#999CB4"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/room_info_owner_area_name_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|end"
                        android:textColor="#1B1D38"
                        android:textSize="14sp"
                        tools:text="沙特阿拉伯" />

                </FrameLayout>

                <LinearLayout
                    android:id="@+id/room_info_layout_notice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="36dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/voice_room_title_note"
                        android:textColor="#999CB4"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/room_bulletin_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textColor="#1B1D38"
                        android:textSize="14sp"
                        tools:text="这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦 这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦 这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦这里是公告哦" />

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/room_info_layout_bottom"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_alignParentBottom="true"
            android:layout_marginHorizontal="16dp"
            android:paddingVertical="8dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/room_info_layout_follow"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="15dp"
                android:background="@drawable/room_info_follow_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/room_info_layout_join"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/room_info_img_follow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/room_info_tv_follow"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/room_info_follow_icon" />

                <TextView
                    android:id="@+id/room_info_tv_follow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:text="@string/follow_text"
                    android:textColor="@color/color_accent"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/room_info_img_follow"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/room_info_layout_join"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:background="@drawable/room_info_join_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/room_info_layout_follow"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/room_info_img_join"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/room_info_tv_join"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/room_info_join_icon" />

                <TextView
                    android:id="@+id/room_info_tv_join"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:text="@string/join_text"
                    android:textColor="#FFFFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/room_info_img_join"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>