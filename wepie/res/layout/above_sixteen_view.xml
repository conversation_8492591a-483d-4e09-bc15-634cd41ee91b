<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="android.widget.FrameLayout">

    <LinearLayout
        android:id="@+id/content_lay"
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_ffffff_corner12"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="15dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:text="@string/above_sixteen_view_1"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:paddingStart="23dp"
            android:paddingEnd="23dp"
            android:text="@string/above_sixteen_view_2"
            android:textColor="#757575"
            android:textSize="12sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/below_sixteen_tv"
            android:layout_width="252dp"
            android:layout_height="90dp"
            android:layout_marginTop="14dp"
            android:background="@drawable/icon_below_sixteen_bg"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:text="@string/below_sixteen_text"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/above_sixteen_tv"
            android:layout_width="252dp"
            android:layout_height="90dp"
            android:layout_marginTop="14dp"
            android:background="@drawable/icon_above_sixteen_bg"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:text="@string/above_sixteen_text"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

    </LinearLayout>

</merge>