<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/voice_room_create_bg"
        android:orientation="vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <LinearLayout
            android:id="@+id/popup_add_friend_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/sel_f8fafc"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="4dp"
                app:srcCompat="@drawable/friend_add_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/popup_friend_lay_1"
                android:textAlignment="center"
                android:textColor="#1B1D38"
                android:textSize="16dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/popup_add_group_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:background="@drawable/sel_f8fafc"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="4dp"
                app:srcCompat="@drawable/add_group_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/popup_friend_lay_4"
                android:textAlignment="center"
                android:textColor="#1B1D38"
                android:textSize="16dp"
                tools:text="wwwwwwww" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/popup_group_chat_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:background="@drawable/sel_f8fafc"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="4dp"
                app:srcCompat="@drawable/create_group_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/popup_friend_lay_2"
                android:textAlignment="center"
                android:textColor="#1B1D38"
                android:textSize="16dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/popup_sweep_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/sel_f8fafc"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="4dp"
                app:srcCompat="@drawable/scan_qrcode_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/popup_friend_lay_3"
                android:textAlignment="center"
                android:textColor="#1B1D38"
                android:textSize="16dp" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>