<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="303dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_ffffff_corner8"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/select_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="24dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/cancel_tv"
                android:layout_width="120dp"
                android:layout_height="40dp"
                android:background="@drawable/sel_00000000_corner20_strock_24c572"
                android:backgroundTint="@color/sel_accent_common_selector"
                android:gravity="center"
                android:text="@string/update_ring_select_view_1"
                android:textAlignment="center"
                android:textColor="@color/color_accent_ex"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/enter_tv"
                android:layout_width="120dp"
                android:layout_height="40dp"
                android:layout_marginStart="12dp"
                android:background="@drawable/sel_24c572_corner20"
                android:backgroundTint="@color/sel_accent_common_selector"
                android:gravity="center"
                android:text="@string/update_ring_select_view_2"
                android:textAlignment="center"
                android:textColor="#ffffff"
                android:textSize="16dp" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>