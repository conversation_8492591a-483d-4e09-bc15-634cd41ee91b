<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/side_bar_tv"
        android:layout_width="61dp"
        android:layout_height="51dp"
        android:layout_gravity="end"
        android:layout_marginEnd="30dp"
        android:background="@drawable/ic_water_drops_bg"
        android:gravity="center"
        android:paddingEnd="10dp"
        android:textAlignment="center"
        android:textColor="@android:color/white"
        android:textSize="26dp"
        tools:text="C" />

</FrameLayout>