<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F4F4F4"
    android:orientation="vertical">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/setting_total_send_lay"
        android:layout_width="match_parent"
        android:layout_height="53dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/setting_item_bg">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:text="@string/activity_lover_send_gift_setting_1"
            android:textColor="@color/setting_item_text_color"
            android:textSize="16dp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/setting_total_send_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            app:srcCompat="@drawable/ic_un_select" />

    </RelativeLayout>

    <ImageView style="@style/list_divider_style" />

    <RelativeLayout
        android:id="@+id/setting_friend_send_lay"
        android:layout_width="match_parent"
        android:layout_height="53dp"
        android:background="@drawable/setting_item_bg">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:text="@string/activity_lover_send_gift_setting_2"
            android:textColor="@color/setting_item_text_color"
            android:textSize="16dp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/setting_friend_send_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            app:srcCompat="@drawable/ic_un_select" />

    </RelativeLayout>

</LinearLayout>