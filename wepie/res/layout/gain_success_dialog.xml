<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/content_lay"
        android:layout_width="303dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_ffffff_corner8"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="42dp"
            android:text="@string/gain_success_dialog_1"
            android:textColor="#ff2f2f2f"
            android:textSize="24dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/content_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:text="@string/gain_success_dialog_2"
            android:textAlignment="center"
            android:textColor="#ff1e1e1e"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/next_tv"
            android:layout_width="255dp"
            android:layout_height="42dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/sel_fad787_corner21"
            android:gravity="center"
            android:text="@string/gain_success_dialog_3"
            android:textAlignment="center"
            android:textColor="#ff68440e"
            android:textSize="16dp" />

    </LinearLayout>

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/svga_view"
        android:layout_width="match_parent"
        android:layout_height="350dp"
        android:layout_alignTop="@id/content_lay"
        android:layout_marginTop="-200dp"
        android:scaleType="centerCrop"
        app:loopCount="1" />

</RelativeLayout>