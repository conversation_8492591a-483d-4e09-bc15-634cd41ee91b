<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.wepie.wespy.module.discover.CircleHeaderDecor
        android:id="@+id/discover_decor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:orientation="vertical">

        <View
            android:id="@+id/discover_square_post_item_head_space"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="#f4f4f4" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="12dp"
            android:clipChildren="false">

            <com.huiwan.decorate.DecorHeadImgView
                android:id="@+id/discover_square_post_item_head_img"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="6dp"
                android:layout_marginTop="6dp"
                android:clipChildren="false"
                android:src="@drawable/default_head_icon" />

            <ImageView
                android:id="@+id/discover_square_post_item_menu"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_alignParentEnd="true"
                android:scaleType="centerInside"
                android:src="@drawable/menu_grey_dot" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="46dp"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="8dp"
                android:layout_marginTop="7dp"
                android:layout_marginEnd="54dp"
                android:layout_toEndOf="@id/discover_square_post_item_head_img">

                <com.huiwan.decorate.NameTextView
                    android:id="@+id/discover_square_post_item_user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_press_corner"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxWidth="180dp"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="#4a4a4a"
                    android:textSize="16dp"
                    android:textStyle="bold"
                    tools:text="小明一二三四五六七八九11111" />

                <com.huiwan.decorate.vip.VipLabelView
                    android:id="@+id/discover_square_post_item_vip_label"
                    android:layout_width="@dimen/vip_icon_width"
                    android:layout_height="@dimen/vip_icon_height"
                    android:layout_alignParentTop="true"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="-3dp"
                    android:layout_toEndOf="@id/discover_square_post_item_user_name"
                    tools:src="@drawable/vip_label_1" />

                <TextView
                    android:id="@+id/discover_square_post_item_time_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/discover_square_post_item_user_name"
                    android:layout_marginTop="8dp"
                    android:gravity="center_vertical"
                    android:textColor="#999999"
                    android:textSize="12dp"
                    tools:text="1分钟前" />

            </RelativeLayout>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <TextView
                android:id="@+id/discover_square_post_item_topic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_press_corner"
                android:gravity="center_vertical|start"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#27c33b"
                android:textSize="16dp"
                tools:text="#话题终结者在此#" />

            <TextView
                android:id="@+id/discover_square_post_item_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="6"
                android:textColor="#4a4a4a"
                android:textSize="16dp"
                tools:text="一二三四五" />

            <TextView
                android:id="@+id/discover_square_post_item_content_more_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/bg_press_corner"
                android:text="@string/discover_square_post_item_view_1"
                android:textColor="#3763a6"
                android:textSize="16dp"
                android:visibility="gone" />

            <com.wepie.wespy.module.discover.views.ItemImgViews
                android:id="@+id/discover_square_post_item_four_img_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone" />

            <com.wepie.wespy.module.discover.views.ItemImgViews
                android:id="@+id/discover_square_post_item_nine_img_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone" />

            <com.wepie.wespy.module.fdiscover.view.CircleImageView
                android:id="@+id/discover_square_post_item_content_circle_iv"
                android:layout_width="150dp"
                android:layout_height="200dp"
                android:layout_marginTop="10dp"
                android:visibility="gone" />

            <com.wepie.wespy.module.fdiscover.item.detailcontent.ContentDraw
                android:id="@+id/discover_square_post_item_draw_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/discover_square_post_item_like_lay"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp">

                <ImageView
                    android:id="@+id/discover_square_post_item_like_icon_iv"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:src="@drawable/icon_discover_item_dislike" />

                <TextView
                    android:id="@+id/discover_square_post_item_like_num_tv"
                    android:layout_width="60dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="4dp"
                    android:gravity="center_vertical"
                    android:textColor="#999999"
                    android:textSize="12dp"
                    tools:text="1" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/discover_square_post_item_comment_lay"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/discover_square_post_item_comment_icon_iv"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:src="@drawable/comment_grey" />

                <TextView
                    android:id="@+id/discover_square_post_item_comment_num_tv"
                    android:layout_width="70dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="4dp"
                    android:gravity="center_vertical"
                    android:textColor="#999999"
                    android:textSize="12dp"
                    tools:text="2" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>