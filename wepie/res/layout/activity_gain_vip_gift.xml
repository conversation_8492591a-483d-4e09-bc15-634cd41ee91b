<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/vip_gain_present_bg"
        android:orientation="vertical">

        <com.huiwan.widget.actionbar.BaseWpActionBar
            android:id="@+id/action_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="24dp">

            <ImageView
                android:layout_width="@dimen/wp320"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:src="@drawable/vip_gain_present_left_top" />

            <ImageView
                android:layout_width="@dimen/wp47"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="@dimen/wp36"
                android:layout_marginEnd="@dimen/wp8"
                android:adjustViewBounds="true"
                android:src="@drawable/vip_gain_present_right_top" />

            <ImageView
                android:layout_width="@dimen/wp83"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="@dimen/wp183"
                android:adjustViewBounds="true"
                android:src="@drawable/vip_gain_present_left_bottom" />

            <ImageView
                android:layout_width="@dimen/wp100"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="@dimen/wp61"
                android:adjustViewBounds="true"
                android:src="@drawable/vip_gain_present_right_bottom" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="52dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/vip_gain_present_conent_bg"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="80dp">

                    <ImageView
                        android:id="@+id/mark_left"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:scaleX="@integer/image_scale_x"
                        android:src="@drawable/quotation_mark_left" />

                    <ImageView
                        android:id="@+id/mark_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignBottom="@+id/content_tv"
                        android:layout_alignParentEnd="true"
                        android:layout_marginEnd="20dp"
                        android:scaleX="@integer/image_scale_x"
                        android:src="@drawable/quotation_mark_right" />

                    <TextView
                        android:id="@+id/content_tv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignTop="@id/mark_left"
                        android:layout_marginStart="6dp"
                        android:layout_marginEnd="6dp"
                        android:layout_toStartOf="@id/mark_right"
                        android:layout_toEndOf="@id/mark_left" />

                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="20dp">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:src="@drawable/vip_board_bg_default" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/activity_gain_vip_gift_2"
                            android:textColor="#ff806242"
                            android:textSize="@dimen/wp24" />

                        <TextView
                            android:id="@+id/use_time_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#ff806242"
                            android:textSize="@dimen/wp10"
                            tools:text="Duration：0 month" />

                    </LinearLayout>

                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/transfer_lay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="24dp"
                        android:text="@string/activity_gain_vip_gift_4"
                        android:textColor="#ff333333"
                        android:textSize="@dimen/wp14" />

                    <TextView
                        android:id="@+id/vip_level_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="8dp"
                        android:text="@string/gain_vip_level_privileges"
                        android:textColor="#ffcaa061"
                        android:textSize="@dimen/wp14" />

                </LinearLayout>

                <TextView
                    android:id="@+id/gain_tv"
                    android:layout_width="@dimen/wp279"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/wp24"
                    android:background="@drawable/shape_f5f5f5_corner21"
                    android:gravity="center"
                    android:text="@string/vip_received"
                    android:textAlignment="center"
                    android:textColor="#ff333333"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/overdue_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="12dp"
                    android:textColor="#ff999999"
                    android:textSize="12dp"
                    tools:text="00天00时00分后失效" />

                <Space
                    android:layout_width="wrap_content"
                    android:layout_height="20dp" />

            </LinearLayout>

            <com.huiwan.widget.CustomCircleImageView
                android:id="@+id/avatar_iv"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="36dp"
                android:src="@drawable/default_head_icon" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/avatar_iv"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="9dp"
                android:textColor="#ff333333"
                android:textSize="16dp" />

        </RelativeLayout>

    </LinearLayout>

</ScrollView>