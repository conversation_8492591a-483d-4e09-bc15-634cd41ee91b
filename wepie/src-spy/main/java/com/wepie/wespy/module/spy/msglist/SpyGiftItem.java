package com.wepie.wespy.module.spy.msglist;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.fixroom.FixRoomMsg;
import com.wepie.wespy.module.chat.gamemodel.MsgTextHelper;
import com.wepie.wespy.module.game.game.view.LinkLongClickMovementMethod;

/**
 * Created by bigwen on 2017/10/27.
 */
public class SpyGiftItem extends SpyMsgAdapter.AbsMsgViewHolder {

    private final Context mContext;
    private TextView contentTv;
    private SpyMsgLongClickListener longClickListener;

    public SpyGiftItem(View itemView) {
        super(itemView);
        mContext = itemView.getContext();
        init();
    }

    private void init() {
        contentTv = itemView.findViewById(R.id.gift_item_content_tv);

        itemView.setBackgroundResource(SpyRoomMsgConst.ITEM_BG);
        contentTv.setTextColor(SpyRoomMsgConst.CONTENT_COLOR);
        contentTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, SpyRoomMsgConst.NORMAL_TEXT_SIZE);
        contentTv.setLineSpacing(SpyRoomMsgConst.TEXT_LINE_SPACE, 1);
    }

    public void update(final FixRoomMsg msg) {
        FixRoomMsgUtil.refreshBg(itemView, msg);
        UserService.get().getCacheSimpleUser(msg.getSend_uid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(final UserSimpleInfo sender) {
                UserService.get().getCacheSimpleUser(msg.getGiftRecvUid(), new UserSimpleInfoCallback() {
                    @Override
                    public void onUserInfoSuccess(UserSimpleInfo receiver) {
                        refreshContent(sender.getRemarkName(), receiver.getRemarkName(), msg);
                    }

                    @Override
                    public void onUserInfoFailed(String description) {
                        refreshContent(sender.getRemarkName(), "xxx", msg);
                    }
                });
            }

            @Override
            public void onUserInfoFailed(String description) {
                refreshContent("xxx", "xxx", msg);
            }
        });
    }

    private void refreshContent(String nickname, String receiver, final FixRoomMsg msg) {
        longClickListener = new SpyMsgLongClickListener(itemView, msg);

        try {
            SpannableStringBuilder ssb = MsgTextHelper.getFixRoomGiftContent(nickname, receiver, msg);
            int start = ssb.toString().indexOf(nickname);
            float startPos = contentTv.getPaint().measureText(ssb, 0, start);
            ssb.setSpan(new SpyUserNameSpan(contentTv, msg.getSend_uid(), nickname, startPos, msg.getRid()), start, start + nickname.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            contentTv.setText(ssb);
            contentTv.setMovementMethod(new LinkLongClickMovementMethod(mContext, () -> {
                if (longClickListener != null) longClickListener.onLongClick(null);
            }));
            contentTv.setLongClickable(false);
        } catch (Exception e) {
            e.printStackTrace();
            contentTv.setText(nickname + ResUtil.getString(R.string.char_colon) + msg.getContent());
        }
    }

    public static SpyMsgAdapter.AbsMsgViewHolder create(ViewGroup parent) {
        return new SpyGiftItem(LayoutInflater.from(parent.getContext()).inflate(R.layout.spy_gift_item, parent, false));
    }
}