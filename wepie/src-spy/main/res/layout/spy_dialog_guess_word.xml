<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        app:srcCompat="@drawable/spy_dialog_alert" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:background="@drawable/spy_dialog_white_bg">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp">

            <TextView
                android:id="@+id/spy_boom_word_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:text="@string/game_room_spy_room_boom_word_title"
                android:textColor="#165ecb"
                android:textSize="22dp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginTop="24dp" />

            <TextView
                android:id="@+id/spy_dialog_time_down_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="#ff945b"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="@+id/spy_boom_word_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/spy_boom_word_title"
                tools:text="15s" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/spy_boom_words_rv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/spy_boom_word_title"
                tools:itemCount="8"
                tools:listitem="@layout/spy_word_item_lay" />

            <TextView
                android:id="@+id/spy_dialog_ok_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:background="@drawable/orange_btn_selector"
                android:gravity="center"
                android:includeFontPadding="false"
                android:minWidth="120dp"
                android:paddingVertical="13dp"
                android:text="@string/sure"
                android:textColor="@color/white"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/spy_boom_words_rv" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            android:id="@+id/spy_dialog_mask"
            layout="@layout/spy_dialog_guess_word_mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

    </FrameLayout>


</LinearLayout>