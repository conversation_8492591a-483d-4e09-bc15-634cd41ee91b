<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:layout_height="46dp">

    <include
        android:id="@+id/spy_word_lay"
        layout="@layout/spy_word_item_lay"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <FrameLayout
        android:id="@+id/spy_select_head_lay"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_width="100dp" />

</androidx.constraintlayout.widget.ConstraintLayout>