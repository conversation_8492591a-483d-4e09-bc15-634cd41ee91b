<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="android.widget.RelativeLayout">

    <com.wepie.wespy.module.voiceroom.voicegame.views.VoiceRoomCocosView
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/room_action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:action_bar_color="@color/transparent"
        app:force_white_status="true"
        app:has_action_bar="false"
        app:show_status_view="true">

    </com.huiwan.widget.actionbar.BaseWpActionBar>

    <com.wepie.wespy.module.voiceroom.main.title.RoomTitleView
        android:id="@+id/room_title_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/room_action_bar" />

    <LinearLayout
        android:id="@+id/room_state_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/room_title_view"
        android:layout_marginStart="36dp"
        android:layout_marginTop="4dp">

        <com.wepie.wespy.module.voiceroom.main.VoiceRoomActivityStateView
            android:id="@+id/room_activity_state_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="6dp" />

        <com.wepie.wespy.module.voiceroom.ViewPluginStub
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:tag="OwnerGroup" />

    </LinearLayout>

    <include
        android:id="@+id/room_mic_lay"
        layout="@layout/voice_game_all_mic_seat_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/room_state_lay"
        android:layout_marginTop="8dp" />

    <com.wepie.wespy.module.voiceroom.face.trickface.TrickFaceAnimView
        android:id="@+id/room_trick_anim_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wepie.wespy.module.voiceroom.voicegame.views.VoiceGamePrepareView
        android:id="@+id/room_prepare_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/short_room_msg_lay"
        android:layout_below="@id/room_mic_lay"
        android:layout_marginTop="12dp" />

    <com.wepie.wespy.module.voiceroom.voicegame.views.ShortRoomMsg
        android:id="@+id/short_room_msg_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="52dp" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/short_room_msg_lay"
        android:layout_alignTop="@id/room_mic_lay"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        android:tag="RoomLeave" />

    <com.wepie.wespy.module.voiceroom.voicegame.views.RoomMsgFullScreenView
        android:id="@+id/room_msg_fullscreen_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentBottom="true"
        android:visibility="invisible" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/room_mic_lay"
        android:tag="MusicRunner" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="DrawGuess" />

<!--    <com.wepie.wespy.module.voiceroom.ViewPluginStub-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:tag="PuzzleRunner" />-->

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="Slot" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="BigWinner" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/vip_enter_guide_line"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.47" />

        <com.wepie.wespy.module.voiceroom.main.enter.VoiceEnterNameView
            android:id="@+id/room_user_enter_tx"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:isVip="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vip_enter_guide_line" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="57dp"
        android:gravity="end">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/mic_rp_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.wepie.wespy.module.voiceroom.mic.VoiceMicRunner
                android:id="@+id/room_mic_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <com.wepie.wespy.module.voiceroom.rain.DelayRpSmallView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/room_mic_view"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.wepie.wespy.module.voiceroom.galaWeb.GalaWebSmallView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/mic_rp_lay"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="Bingo" />

    <com.wepie.wespy.module.voiceroom.rain.DelayRpFullView
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wepie.wespy.module.voiceroom.barrage.VoiceRoomBarrageView
        android:id="@+id/room_barrage_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/room_user_enter_tx" />

<!--    <com.wepie.wespy.module.voiceroom.rocket.VoiceStarRocketView-->
<!--        android:id="@+id/star_rocket_view"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:layout_centerHorizontal="true"-->
<!--        android:layout_marginBottom="52dp" />-->

    <com.wepie.wespy.module.voiceroom.main.bottom.RoomBottomView
        android:id="@+id/room_bottom_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="RoomGuide" />

    <com.wepie.wespy.module.voiceroom.rocket.VoiceStarRocketAnimView
        android:id="@+id/star_rocket_anim_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="GalaH5" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="RoomUpdate" />

    <com.wepie.wespy.module.voiceroom.main.enter.car.CarAnimHolder
        android:id="@+id/car_holder"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wepie.wespy.module.voiceroom.main.gift.VoiceGiftContentView
        android:id="@+id/room_gift_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_line"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.47" />

        <com.wepie.wespy.module.voiceroom.main.gift.VoiceGiftComboView
            android:id="@+id/combo_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipChildren="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/guide_line" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.wepie.wespy.module.voiceroom.sgift.BroadcastGiftView
        android:id="@+id/server_gift_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="RoomSettings" />

    <com.wepie.wespy.module.voiceroom.main.VoiceRoomSendView
        android:id="@+id/room_send_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:visibility="gone" />

    <com.wepie.wespy.module.voiceroom.ViewPluginStub
        android:id="@+id/room_rain_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="RoomRain" />

</merge>