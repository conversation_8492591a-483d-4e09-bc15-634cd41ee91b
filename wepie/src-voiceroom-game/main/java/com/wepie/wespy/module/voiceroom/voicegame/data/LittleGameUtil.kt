package com.wepie.wespy.module.voiceroom.voicegame.data

import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.editionentity.GameConfig.MatchInfo
import com.huiwan.constants.Common
import com.huiwan.constants.GameType

object LittleGameUtil {

    //1v1 双人游戏
    fun isMateOneGamerTwo(mode: Int): Boolean {
        return mode == Common.MATCH_MATE_ONE_GAMER_TWO || mode == Common.ROOM_MATE_ONE_GAMER_TWO || mode == Common.MATCH_MATE_TWO_GAMER_TWO || mode == Common.ROOM_MATE_TWO_GAMER_TWO
    }

    //4人游戏，无同伴
    fun isMateOneGamerFour(mode: Int): Boolean {
        return mode == Common.MATCH_MATE_ONE_GAMER_FOUR || mode == Common.ROOM_MATE_ONE_GAMER_FOUR
    }

    //4人游戏，有同伴
    fun isMateTwoGamerFour(mode: Int): Boolean {
        return mode == Common.MATCH_MATE_TWO_GAMER_FOUR || mode == Common.ROOM_MATE_TWO_GAMER_FOUR
    }

    //3人游戏
    fun isMateOneGameThree(mode: Int): Boolean {
        return mode == Common.MATCH_MATE_ONE_GAMER_THREE || mode == Common.ROOM_MATE_ONE_GAMER_THREE
    }

    //5人游戏
    fun isMateOneGameFive(mode: Int): Boolean {
        return mode == Common.MATCH_MATE_ONE_GAMER_FIVE || mode == Common.ROOM_MATE_ONE_GAMER_FIVE
    }

    //6人游戏
    fun isMateOneGameSix(mode: Int): Boolean {
        return mode == Common.MATCH_MATE_ONE_GAMER_SIX || mode == Common.ROOM_MATE_ONE_GAMER_SIX
    }

    //2v2v2 6人游戏
    fun isMateTwoGamerSix(mode: Int): Boolean {
        return mode == Common.MATCH_MATE_TWO_GAMER_SIX || mode == Common.ROOM_MATE_TWO_GAMER_SIX
    }

    fun getMatchInfo(gameType: Int, gameMode: Int, mode: Int, betLevel: Int) = Sequence {
        getSupportedGameModes(gameType).listIterator()
    }.firstOrNull {
        (it.roomMode == mode || it.matchMode == mode) && it.betLevel == betLevel && gameMode == it.gameMode
    }

    fun getSupportedGameModes(gameType: Int): List<MatchInfo> {
        return when (gameType) {
            GameType.GAME_TYPE_EXPLODING_KITTENS -> {
                Sequence { ConfigHelper.getInstance().gameConfig.getGameConfig(gameType).matchGroupList.listIterator() }
                    .fold(mutableListOf<MatchInfo>()) { acc, i ->
                        acc.addAll(i.matchInfoList)
                        acc
                    }.filter {
                        it.supportMode.isEmpty() || it.supportMode.contains(GameConfig.MODEL_SUPPORT_TYPE_CREATE)
                    }.toList()
            }

            else -> ConfigHelper.getInstance().gameConfig.getGameConfig(gameType).matchInfoList
        }
    }

    fun getGamerCount(mode: Int): Int {
        return if (isMateOneGamerTwo(mode)) {
            2
        } else if (isMateOneGamerFour(mode) || isMateTwoGamerFour(mode)) {
            4
        } else if (isMateOneGameThree(mode)) {
            3
        } else if (isMateOneGameFive(mode)) {
            5
        } else if (isMateOneGameSix(mode) || isMateTwoGamerSix(mode)) {
            6
        } else {
            2
        }
    }

}