package com.wepie.wespy.module.voiceroom.voicegame.views

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.ktx.dp
import com.huiwan.base.ktx.gone
import com.huiwan.base.ktx.updateVisible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.bottomsheet.IDragView
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginConstraintsLayout
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IFullScreenRoomMsg
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IShortRoomMsg
import com.wepie.wespy.module.voiceroom.msg.RoomMsgView

class RoomMsgFullScreenView(
    context: Context, attributeSet: AttributeSet? = null,
) : PluginConstraintsLayout(context, attributeSet), IFullScreenRoomMsg, IDragView {
    private val msgView: RoomMsgView
    override var rv: RecyclerView? = null
    override var rootLay: View? = null
    override var startY: Float = 0f
    override var startTime: Long = 0
    override var hasMoved: Boolean = false
    override var needScroll: Boolean = false
    override var canDrag: Boolean = true

    init {
        LayoutInflater.from(context).inflate(R.layout.room_msg_fullscreen_view, this)
        msgView = findViewById(R.id.msg_view)
        R.color.color_gray_icon_tertiary
        rv = msgView.recyclerView
        val dp12 = 12.dp.toFloat()
        rootLay = findViewById<View>(R.id.content_lay).apply {
            background = GradientDrawable().apply {
                cornerRadii = floatArrayOf(
                    dp12, dp12, dp12, dp12,
                    0f, 0f, 0f, 0f
                )
                setColor(ResUtil.getColor(R.color.black_alpha90))
            }
        }
        setOnClickListener {
            moveLayWithAnim(rootLay!!.measuredHeight.toFloat(), true)
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev ?: return super.dispatchTouchEvent(ev)
        if (ScreenUtil.touchEventInView(rootLay, ev.rawX, ev.rawY)) {
            return super.dealDispatchTouchEvent(ev)
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun initView() {

    }

    override fun initData() {

    }


    override fun addNewMsg(newMsg: VoiceRoomMsg?) {
        msgView.addNewMsg(newMsg)
    }

    override fun scroll2Bottom(forceScroll: Boolean) {
        msgView.scroll2Bottom(forceScroll)
    }

    override fun updateAllMsg() {
        msgView.updateAllMsg()
    }

    override fun checkMsgCache() {
        msgView.checkMsgCache()
    }

    override fun showSoftInputDialog() {
        TODO("Not yet implemented")
    }

    override fun showSoftInputDialog(atUserName: String?) {
        TODO("Not yet implemented")
    }

    override fun callViewDispatchTouchEvent(ev: MotionEvent?): Boolean {
        return super.dispatchTouchEvent(ev)
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (changedView == this) {
            VoicePluginService.getPlugin(IShortRoomMsg::class.java)
                .updateVisibility(visibility != View.VISIBLE)
            if (visibility == View.VISIBLE) {
                exeAnimator()
            }
        }
    }

    override fun show() {
        updateVisible(true)
    }

    override fun onClose() {
        gone()
    }

    override fun isVisible() = visibility == View.VISIBLE

    private fun exeAnimator() {
        rootLay?.let {
            it.translationY = it.measuredHeight.toFloat()
            it.animate().setDuration(ANIM_DURATION).translationYBy(-it.measuredHeight.toFloat())
                .setListener(null).start()
        }
    }

}