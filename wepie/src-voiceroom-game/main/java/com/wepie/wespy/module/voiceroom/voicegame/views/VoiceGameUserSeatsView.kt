package com.wepie.wespy.module.voiceroom.voicegame.views

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.PressUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.lib.api.impl
import com.huiwan.user.LoginHelper
import com.huiwan.widget.setExOnClickListener
import com.wejoy.littlegame.EXCHANGE_TYPE_ENTER
import com.wejoy.littlegame.GAME_SCENE_VOICE_MAIN
import com.wejoy.littlegame.ILittleGameApi
import com.wejoy.weplay.ex.view.viewScope
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil
import com.wepie.wespy.module.voiceroom.dataservice.VoiceGameModel
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.voicegame.VoiceGameRoomViewModel
import com.wepie.wespy.module.voiceroom.voicegame.VoiceGameTrackUtil
import com.wepie.wespy.module.voiceroom.voicegame.VoieGameDialogUtil
import com.wepie.wespy.module.voiceroom.voicegame.data.LittleGameUtil
import com.wepie.wespy.module.voiceroom.voicegame.data.VoiceGameSeatEventListener
import com.wepie.wespy.net.tcp.sender.VoiceGameRoomPacketSender
import kotlinx.coroutines.launch

class VoiceGameUserSeatsView(context: Context, attributeSet: AttributeSet? = null) :
    ConstraintLayout(context, attributeSet) {
    private var matchView: AbsVoiceGameMatchView = VoiceGameMatch1V1View(context)
    private val topIv: ImageView
    private val bottomIv: ImageView
    private val matchContainer: FrameLayout
    private var mode: Int = -1
    private var gameType: Int = -1

    init {
        LayoutInflater.from(context).inflate(R.layout.voice_game_user_game_seats_view, this)
        topIv = findViewById(R.id.invite_iv)
        bottomIv = findViewById(R.id.ready_iv)
        matchContainer = findViewById(R.id.match_container)
        PressUtil.addPressEffect(listOf(topIv, bottomIv))
        initEvent()
    }

    private fun initSeatViews(mode: Int, gameType: Int) {
        this.mode = mode
        this.gameType = gameType
        matchView = if (LittleGameUtil.isMateOneGamerTwo(mode)) {
            VoiceGameMatch1V1View(context)
        } else if (LittleGameUtil.isMateOneGamerFour(mode) || LittleGameUtil.isMateTwoGamerFour(mode)) {
            VoiceGameMatch2V2View(context)
        } else if (LittleGameUtil.isMateOneGameThree(mode)) {
            VoiceGameMatchTotal3View(context)
        } else if (LittleGameUtil.isMateOneGameFive(mode)) {
            VoiceGameMatchTotal5View(context)
        } else if (LittleGameUtil.isMateOneGameSix(mode)) {
            VoiceGameMatchTotal6View(context, is3V3 = false)
        } else if (LittleGameUtil.isMateTwoGamerSix(mode)) {
            VoiceGameMatch2V2V2View(context)
        } else {
            VoiceGameMatch1V1View(context)
        }
        matchContainer.removeAllViews()
        matchContainer.addView(
            matchView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
        )
    }

    fun initEvent() {
        topIv.setExOnClickListener {
            VoiceGameTrackUtil.appClick(TrackSource.INVITE)
            VoieGameDialogUtil.showInviteFriendDialog(it.context)
        }

        bottomIv.setExOnClickListener {
            val vm = VoiceGameRoomViewModel.get() ?: return@setExOnClickListener
            if (vm.littleGameInfo.isOnlySeatAllowGame &&
                !VoiceRoomService.getInstance().roomInfo.isInSeat(LoginHelper.getLoginUid())
            ) {
                VoieGameDialogUtil.showTextDialog(
                    context, ResUtil.getStr(R.string.mic_seat_can_join_game)
                )
                return@setExOnClickListener
            }
            if (!vm.isSelfInSeat()) {
                //展示开始按钮 上游戏座位/麦位
                sitOnGameSeat(vm)
            } else {
                if (vm.selfSeatId > 0) {
                    if (vm.isSelfReady()) {
                        VoiceGameTrackUtil.appClick(TrackButtonName.CANCEL_PREPARE)
                    } else {
                        VoiceGameTrackUtil.appClick(TrackButtonName.PREPARE)
                    }
                    readyOnGameSeat(vm)
                } else {
                    HLog.d("VoiceGameUserSeatsView", "can`t find selfSeatId when click ready")
                }
            }
        }
    }

    private fun sitOnGameSeat(vm: VoiceGameRoomViewModel, isTrack: Boolean = true) {
        val littleGameInfo = vm.littleGameInfo
        viewScope.launch {
            val head = VoiceGameRoomPacketSender.sitOnGameSeat(littleGameInfo.rid)
            if (head.codeOk()) {
                VoiceGameTrackUtil.onTrackSitSuccess()
                return@launch
            }
            VoiceGameTrackUtil.onTrackSitFailed()
            if (isTrack) {
                val basicInfo = littleGameInfo.gameBasicInfo
                com.huiwan.littlegame.util.LittleGameUtil.trackGameError(
                    head.code, basicInfo.littleGameType, basicInfo.gameMode,
                    basicInfo.mode, basicInfo.betLevel
                )
            }
            val ret = handleEnterError(head.code, littleGameInfo) { sitOnGameSeat(vm, false) }
            if (!ret) {
                ToastUtil.show(head.desc)
            }
        }
    }

    private fun readyOnGameSeat(vm: VoiceGameRoomViewModel) {
        viewScope.launch {
            val head = VoiceGameRoomPacketSender.readyOnGameSeat(
                vm.littleGameInfo.rid, !vm.isSelfReady(), vm.selfSeatId
            )
            if (!head.codeOk()) {
                handleEnterError(head.code, vm.littleGameInfo) { readyOnGameSeat(vm) }
            }
        }
    }

    private fun handleEnterError(
        code: Int, littleGameInfo: VoiceGameModel.LittleGameInfo, next: () -> Unit
    ): Boolean {
        val info = littleGameInfo.getLittleGameSimpleInfo()
        return ILittleGameApi::class.impl().handleEnterError(
            context, code, EXCHANGE_TYPE_ENTER or GAME_SCENE_VOICE_MAIN, info, next
        )
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        return super.dispatchTouchEvent(ev)
    }

    fun refreshIvFromCocosZipRes(gameType: Int) {
        checkRefreshBottomIv(gameType)
        checkAndRefreshTopIv(gameType)
        matchView.updateEmptyView(gameType)
    }

    private fun checkRefreshBottomIv(gameType: Int) {
        val vm = VoiceGameRoomViewModel.get() ?: return
        val bottomPicName = if (!vm.isSelfInSeat()) {
            //展示开始按钮
            CocosGameConfigUtil.GAME_SIT_DOWN
        } else if (vm.isSelfReady()) {
            //展示 取消准备
            CocosGameConfigUtil.GAME_UNREADY
        } else {
            CocosGameConfigUtil.GAME_READY
        }
        CocosGameConfigUtil.loadResAsync(
            bottomIv, gameType, bottomPicName,
            if (bottomPicName == CocosGameConfigUtil.GAME_SIT_DOWN) {
                ResUtil.getDrawable(R.drawable.cocos_game_ready_default)
            } else {
                ColorDrawable(Color.TRANSPARENT)
            }
        )
    }


    fun refreshSeats(
        seatsInfo: List<VoiceGameModel.LittleGameInfo.GameSeat>,
        mode: Int,
        gameType: Int,
        listener: VoiceGameSeatEventListener?
    ) {
        if (mode != this.mode || gameType != this.gameType) {
            initSeatViews(mode, gameType)
            matchView.updateEmptyView(gameType)
        }
        matchView.updateSeatInfo(seatsInfo, listener)
        checkRefreshBottomIv(gameType)
        checkAndRefreshTopIv(gameType)
    }

    private fun checkAndRefreshTopIv(gameType: Int) {
        CocosGameConfigUtil.loadResAsync(
            topIv,
            gameType,
            CocosGameConfigUtil.GAME_INVITE
        )
    }

}