package com.wepie.module.musichum.result;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.decorate.CharmManager;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.decorate.NameTextView;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.module.musichum.model.MusicHumGameInfo;
import com.wepie.module.musichum.model.MusicHumGameResult;
import com.wepie.module.musichum.model.MusicHumGamer;
import com.wepie.wespy.R;
import com.wepie.wespy.module.gift.NumberUtil;
import com.wepie.wespy.module.vip.main.VipUiConfig;
import com.wepie.wespy.utils.QRCodeEncodingUtils;

public class MusicHumResultView extends RelativeLayout {

    private ResultAdapter adapter = new ResultAdapter();
    private ViewGroup resultLay, gameInfoLay;
    private ConstraintLayout userInfoLay;
    private DecorHeadImgView headIv;
    private NameTextView nameTv;
    private LinearLayout lightNumLay;
    private ImageView firstWinIv;
    private TextView rankTv, snatchTv, percentTv;
    private ImageView firstIv;
    private TextView noFirstTv, isFirstTv;
    private ImageView lightIv;
    private TextView noticeTv;
    private ImageView evaluateIv, charmIv, vipIv;
    private RecyclerView rv;
    private MusicHumGameInfo gameInfo;
    public MusicHumResultView(Context context) {
        super(context);
        initView();
    }

    public MusicHumResultView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        inflate(getContext(), R.layout.snatch_song_result_view, this);
        rv = findViewById(R.id.rv);
        rv.setLayoutManager(new LinearLayoutManager(getContext()));
        rv.setAdapter(adapter);
        resultLay = findViewById(R.id.result_lay);
        userInfoLay = findViewById(R.id.user_info_lay);
        gameInfoLay = findViewById(R.id.game_info_lay);
        headIv = findViewById(R.id.head_iv);
        firstWinIv = findViewById(R.id.is_first_win_iv);
        nameTv = findViewById(R.id.name_tv);
        lightNumLay = findViewById(R.id.light_num_lay);
        rankTv = findViewById(R.id.rank_tv);
        noFirstTv = findViewById(R.id.no_first_tv);
        firstIv = findViewById(R.id.first_iv);
        isFirstTv = findViewById(R.id.is_first_tv);
        snatchTv = findViewById(R.id.snatch_tv);
        percentTv = findViewById(R.id.percent_tv);
        evaluateIv = findViewById(R.id.evaluate_iv);
        lightIv = findViewById(R.id.light_iv);
        noticeTv = findViewById(R.id.notice_tv);
        vipIv = findViewById(R.id.vip_level_iv);
        charmIv = findViewById(R.id.charm_level_iv);
    }

    public void refresh(MusicHumGameInfo gameInfo) {
        if (gameInfo == null) {
            ToastUtil.show(ResUtil.getStr(R.string.common_data_error));
            return;
        }
        if (gameInfo.selfInGame()) {
            int selfUid = LoginHelper.getLoginUid();
            MusicHumGameResult gamer = gameInfo.getGameResult(selfUid);
            MusicHumGamer musicHumGamer = gameInfo.getGamer(selfUid);
            if (gamer == null || musicHumGamer == null) {
                ToastUtil.show(ResUtil.getStr(R.string.music_hum_no_result_for_date_error));
                return;
            }
            gamer.setReceiveGiftCount(musicHumGamer.getTotalLightUpCount());
            userInfoLay.setVisibility(VISIBLE);
            gameInfoLay.setVisibility(VISIBLE);
            addUserAndGameView(gamer);
        } else {
            userInfoLay.setVisibility(GONE);
            gameInfoLay.setVisibility(GONE);
        }
        adapter.setRid(gameInfo.getRid());
        this.gameInfo = gameInfo;
        adapter.refresh(gameInfo.getGameResult());
    }

    private void addUserAndGameView(MusicHumGameResult gamer) {
        UserService.get().getCacheSimpleUser(gamer.getUid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                headIv.showUserHead(gamer.getUid());
                nameTv.setUserName(simpleInfo);
                if (simpleInfo.vip > 0) {
                    VipUiConfig.displayBoardLevel(vipIv, simpleInfo.vip, true);
                } else {
                    vipIv.setVisibility(GONE);
                }

                if (CharmManager.getInstance().getCharmLevel(simpleInfo.flower) > 0) {
                    charmIv.setImageResource(CharmManager.getInstance().getCharmShortRes(simpleInfo.flower));
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
                vipIv.setVisibility(GONE);
            }
        });

        if(gamer.getRank() == 1 && gamer.getWinInRow() > 1) {
            rankTv.setVisibility(INVISIBLE);
            noFirstTv.setVisibility(INVISIBLE);
            firstIv.setVisibility(VISIBLE);
            isFirstTv.setVisibility(VISIBLE);
            isFirstTv.setText(gamer.getWinInRow() + getContext().getString(R.string.snatch_song_result_view_3));
        } else {
            rankTv.setVisibility(VISIBLE);
            noFirstTv.setVisibility(VISIBLE);
            firstIv.setVisibility(INVISIBLE);
            isFirstTv.setVisibility(INVISIBLE);
            rankTv.setText("" + gamer.getRank());
        }
        noticeTv.setText(gamer.getTitle());
        evaluateIv.setImageResource(EvaluateUtil.getEvaluateImg(gamer.getLevel()));
        if(gamer.getRank() == 1) {
            firstWinIv.setVisibility(VISIBLE);
        } else {
            firstWinIv.setVisibility(GONE);
        }
        snatchTv.setText(String.valueOf(gamer.getGrabCount()));
        percentTv.setText(gamer.getSuccRate() + "%");
        if (gamer.getReceiveGiftCount() > 0) {
            lightIv.setVisibility(VISIBLE);
            lightNumLay.setVisibility(VISIBLE);
            NumberUtil.showSmallNumber(lightNumLay, gamer.getReceiveGiftCount(), true);
        } else {
            lightIv.setVisibility(INVISIBLE);
            lightNumLay.setVisibility(INVISIBLE);
        }
    }
    private void hideAddFriendIcon() {
        if (adapter == null) {
            return;
        }
        for (int i = 0; i < adapter.getItemCount(); ++i) {
            RecyclerView.ViewHolder vh =  rv.findViewHolderForAdapterPosition(i);
            if (vh instanceof ResultAdapter.Holder) {
                ResultAdapter.Holder  holder = ((ResultAdapter.Holder) vh);
                holder.addIv.setVisibility(GONE);
            }
        }
    }

    private void reLoadRecyclerView() {
        if (adapter == null) {
            return;
        }
        adapter.setMode(ResultAdapter.TYPE_NORMAL);
        adapter.refresh(gameInfo.getGameResult());
    }

    public Bitmap getDrawCacheImg() {
        //创建保存截图的bitmap
        Bitmap bigBitmap = null;
        if (adapter == null) return bigBitmap;
        //获取item的数量
        int size = adapter.getItemCount();
        //recycler的完整高度 用于创建bitmap时使用
        int height = 0;
        //先算其他view高度
        height += resultLay.getMeasuredHeight();
        for (int i = 0; i < size; i++) {
            //获取itemView的实际高度并累加
            height += ScreenUtil.dip2px(62);
        }
        //二维码的view的高度
        int qrcodeHeight = ScreenUtil.dip2px(110);
        //根据计算出的recyclerView高度创建bitmap
        bigBitmap = Bitmap.createBitmap(getMeasuredWidth(),
                height + qrcodeHeight, Bitmap.Config.ARGB_8888);
        //创建一个canvas画板
        Canvas canvas = new Canvas(bigBitmap);
        //截图时去掉添加好友的加号
        hideAddFriendIcon();
        this.draw(canvas);
        //重新加载rv的各个item，以添加回来应该显示的添加好友的按钮
        reLoadRecyclerView();
        int left = 0;
        //画笔
        Paint paint = new Paint();
        View qrcodeView = inflate(getContext(), R.layout.snatch_song_qrcode_item_view, null);
        ImageView qrcodeIv = qrcodeView.findViewById(R.id.qrcode_iv);
        String url = ConfigHelper.getInstance().getConstV3Info().musicHumShareUrl;
        Bitmap qrcodeBitmap = QRCodeEncodingUtils.createQRCode(url, ScreenUtil.dip2px(66), ScreenUtil.dip2px(66), null);

        qrcodeIv.setImageBitmap(getRoundBitmapByShader(qrcodeBitmap, ScreenUtil.dip2px(3), ScreenUtil.dip2px(1), 0xffa445ff));
        qrcodeView.measure(MeasureSpec.makeMeasureSpec(rv.getWidth(), MeasureSpec.EXACTLY),
                MeasureSpec.makeMeasureSpec(ScreenUtil.dip2px(110), MeasureSpec.EXACTLY));
        qrcodeView.layout(0, 0,
                qrcodeView.getMeasuredWidth(),
                qrcodeView.getMeasuredHeight());
        qrcodeView.setDrawingCacheEnabled(true);
        qrcodeView.buildDrawingCache();
        Bitmap qrcodeCache = qrcodeView.getDrawingCache();
        canvas.drawBitmap(qrcodeCache, left, height, paint);
        qrcodeCache.recycle();
        return bigBitmap;
    }

    public Bitmap getShowCacheImg(Bitmap bigBitmap) {
        //首先判断 剩余空间 和 图片的0.8大小 之间的大小关系,得到最终获得的高度和宽度
        int maxHeight = ScreenUtil.getScreenHeight() - (int)getResources().getDimension(R.dimen.ios148);
        int resHeight = 0;
        int resWidth = 0;
        if((int)(0.8f*bigBitmap.getHeight()) > maxHeight) {
            resHeight += maxHeight;
            resWidth += ScreenUtil.getScreenWidth()*maxHeight/ScreenUtil.getScreenHeight();
        } else {
            resHeight += (int)(0.8f*bigBitmap.getHeight()) + ScreenUtil.dip2px(20);
            resWidth += (int)(0.8f*bigBitmap.getWidth()) + ScreenUtil.dip2px(20);
        }
        //创建图片
        Bitmap resBitMap = Bitmap.createBitmap(resWidth, resHeight, Bitmap.Config.ARGB_8888);
        //画笔
        Paint paint = new Paint();
        Canvas resCanvas = new Canvas(resBitMap);
        //绘制背景
        resCanvas.drawColor(0xff671aa7);
        // 计算缩放比例
        float scaleWidth = ((float) resWidth - ScreenUtil.dip2px(20)) / bigBitmap.getWidth();
        float scaleHeight = ((float) resHeight - ScreenUtil.dip2px(20)) / bigBitmap.getHeight();
        // 取得想要缩放的matrix参数
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        // 得到新的图片
        Bitmap newbm = Bitmap.createBitmap(bigBitmap, 0, 0, bigBitmap.getWidth(), bigBitmap.getHeight(), matrix,
                true);
        //绘制新图片
        resCanvas.drawBitmap(newbm, ScreenUtil.dip2px(10), ScreenUtil.dip2px(10), paint);
        return resBitMap;
    }


    /**
     * 通过BitmapShader 圆角边框
     * @param bitmap
     * @param radius
     * @param boarder
     * @return
     */
    private Bitmap getRoundBitmapByShader(Bitmap bitmap, int radius, int boarder, int color) {
        if (bitmap == null) {
            return null;
        }
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        Matrix matrix = new Matrix();
        //创建输出的bitmap
        Bitmap desBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        //创建canvas并传入desBitmap，这样绘制的内容都会在desBitmap上
        Canvas canvas = new Canvas(desBitmap);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        //创建着色器
        BitmapShader bitmapShader = new BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP);
        //给着色器配置matrix
        bitmapShader.setLocalMatrix(matrix);
        paint.setShader(bitmapShader);
        //创建矩形区域并且预留出border
        RectF rect = new RectF(boarder, boarder, width - boarder, height - boarder);
        //把传入的bitmap绘制到圆角矩形区域内
        canvas.drawRoundRect(rect, radius, radius, paint);

        if (boarder > 0) {
            //绘制boarder
            Paint boarderPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            boarderPaint.setColor(color);
            boarderPaint.setStyle(Paint.Style.STROKE);
            boarderPaint.setStrokeWidth(boarder);
            canvas.drawRoundRect(rect, radius, radius, boarderPaint);
        }
        return desBitmap;
    }


}
