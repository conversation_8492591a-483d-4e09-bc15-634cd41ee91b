package com.wepie.module.musichum.stage;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.wepie.module.musichum.MusicHumAnimUtil;
import com.wepie.wespy.R;

/**
 * Created by bigwen on 2020-03-25.
 *
 *  游戏开始
 *  下一首
 *
 */
public class MusicHumStageStartHolder {

    private View rootView;
    private ImageView iconIv, nextIv;
    private boolean isInPlay = false;
    private Handler handler = new Handler(Looper.getMainLooper());

    public MusicHumStageStartHolder(Context context) {
        rootView = LayoutInflater.from(context).inflate(R.layout.music_hum_stage_start_view, null);
        iconIv = rootView.findViewById(R.id.icon_iv);
        nextIv = rootView.findViewById(R.id.next_iv);
    }

    public void showReadyGo() {
        iconIv.setVisibility(View.VISIBLE);
        nextIv.setVisibility(View.GONE);

        if (isInPlay) return;
        isInPlay = true;
        iconIv.setImageResource(R.drawable.music_hum_game_start3_icon);
        int interval = 800;
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
//                SoundUtil.getInstance().playSound(SoundUtil.TYPE_MUSIC_HUM_READY);
                iconIv.setImageResource(R.drawable.music_hum_game_start2_icon);
                MusicHumAnimUtil.showTimeAnim(iconIv);
            }
        }, interval);
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
//                SoundUtil.getInstance().playSound(SoundUtil.TYPE_MUSIC_HUM_READY);
                iconIv.setImageResource(R.drawable.music_hum_game_start1_icon);
                MusicHumAnimUtil.showTimeAnim(iconIv);
            }
        }, interval * 2);
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
//                SoundUtil.getInstance().playSound(SoundUtil.TYPE_MUSIC_HUM_GO);
                iconIv.setImageResource(R.drawable.music_hum_game_start_go_icon);
                MusicHumAnimUtil.showTimeAnim(iconIv);
                isInPlay = false;
            }
        }, interval * 3);
    }

    public void showNext() {
        isInPlay = false;
        handler.removeCallbacksAndMessages(null);
        iconIv.setVisibility(View.GONE);
        nextIv.setVisibility(View.VISIBLE);
    }

    public View getRootView() {
        return rootView;
    }

}
