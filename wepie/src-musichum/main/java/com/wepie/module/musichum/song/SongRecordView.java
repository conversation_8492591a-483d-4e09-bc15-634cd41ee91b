package com.wepie.module.musichum.song;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.media.MediaRecorder;
import android.os.CountDownTimer;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.libpermission.WPPermission;
import com.wepie.wespy.R;
import com.huiwan.store.file.FileConfig;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.FileUtil;
import com.wepie.wespy.helper.dialog.PermissionDialog;

import java.util.List;

/**
 * date 2018/8/3
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class SongRecordView extends FrameLayout {
    private static final String TAG = SongRecordView.class.getSimpleName();

    private ViewGroup progressLay;
    private ProgressBar progressBar;
    private TextView progressMarker;
    private TextView limitTv;
    private TextView maxTv;
    private ImageView recordBtn;
    private TextView recordInfoTv;

    private long recordStartTime = 0;
    private boolean recording = false;
    private MediaRecorder recorder = null;
    private String path = "";
    private IRecordListener listener = null;

    private int maxTime = 180;
    private int limitTime = 30;

    private CountDownTimer timer;

    public SongRecordView(@NonNull Context context) {
        this(context, null);
    }

    public SongRecordView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        View v = LayoutInflater.from(context).inflate(R.layout.try_song_record_view, this);
        recordBtn = v.findViewById(R.id.record_btn);
        recordInfoTv = v.findViewById(R.id.record_info_tv);
        progressBar = v.findViewById(R.id.record_progress_bar);
        progressMarker = v.findViewById(R.id.record_progress_marker);
        progressLay = v.findViewById(R.id.record_progress_lay);
        limitTv = v.findViewById(R.id.record_limit_time_tv);
        maxTv = v.findViewById(R.id.record_max_time_tv);
        initEvent();
    }

    private long lastOpTime = 0;
    
    private void initEvent() {
        recordBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (System.currentTimeMillis() - lastOpTime < 800) {
                    ToastUtil.show(R.string.too_fast);
                    return;
                }
                lastOpTime = System.currentTimeMillis();
                if (recording) {
                    stopRecord();
                } else {
                    WPPermission.with((Activity)getContext())
                            .permission(Manifest.permission.RECORD_AUDIO)
                            .request(new PermissionCallback() {
                                @Override
                                public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                                    startRecord();
                                    if (!alreadyHas) {
                                        ToastUtil.show(ResUtil.getStr(R.string.common_access_granted));
                                    }
                                }

                                @Override
                                public void noPermission(List<String> denied, boolean quick) {
                                    if (quick) {
                                        PermissionDialog.showJumpPermissionDialog(getContext(), ResUtil.getStr(R.string.permission_denied_no_record));
                                    } else {
                                        ToastUtil.show(R.string.permission_denied_no_record);
                                    }
                                }
                            });
                }
            }
        });
    }

    public void setLimitTime(int limitTime) {
        this.limitTime = limitTime;
        if (limitTime > maxTime) {
            maxTime = limitTime;
        }
        updateTimeProgress();
    }

    public void setMaxTime(int maxTime) {
        this.maxTime = maxTime;
        if (maxTime < 1) {
            maxTime = 1;
        }
        if (limitTime > maxTime) {
            limitTime = maxTime;
        }
        updateTimeProgress();
    }

    private void updateTimeProgress() {
        progressBar.setMax(maxTime * 1000);
        limitTv.setText(String.valueOf(limitTime));
        maxTv.setText(String.valueOf(maxTime));
        int limitTvTranslationX = ScreenUtil.dip2px(240 * limitTime / maxTime + 2);
        int progressMarkerTranslationX = ScreenUtil.dip2px(240 * limitTime / maxTime) + ScreenUtil.dip2px(9);
        if (ScreenUtil.isRtl()) {
            limitTv.setTranslationX(-limitTvTranslationX);
            progressMarker.setTranslationX(-progressMarkerTranslationX);
        } else {
            limitTv.setTranslationX(limitTvTranslationX);
            progressMarker.setTranslationX(progressMarkerTranslationX);
        }
    }

    private void startAnimator() {
        stopAnimator();
        progressBar.setMax(maxTime * 1000);
        if (maxTime > 0) {
            timer = new CountDownTimer(maxTime * 1000, 60) {
                @Override
                public void onTick(long millisUntilFinished) {
                    if (recording) {
                        progressBar.setProgress(maxTime * 1000 - (int)millisUntilFinished);
                    }
                }

                @Override
                public void onFinish() {
                    if (recording) {
                        stopRecord();
                    }
                }
            };
            timer.start();
        }
    }

    private void stopAnimator() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    private void startRecord() {
        if (recording){
            return;
        }

        path = FileConfig.AUDIO_BASE_FOLDER +"record_"+System.currentTimeMillis()+ ".aac";
        try {
            FileUtil.createFile(path);
            recorder = new MediaRecorder();
            recorder.setAudioSource(MediaRecorder.AudioSource.MIC);
            recorder.setOutputFormat(MediaRecorder.OutputFormat.DEFAULT);
            recorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);
            recorder.setAudioEncodingBitRate(1000 * 32);
            recorder.setOutputFile(path);
            recorder.prepare();
            recorder.start();
            recording = true;
            recordStartTime = System.currentTimeMillis();
            recordInfoTv.setVisibility(GONE);
            recordBtn.setImageResource(R.drawable.try_song_stop);
            progressLay.setVisibility(VISIBLE);
            startAnimator();
            if (listener != null) {
                listener.onRecordStart();
            }
        }catch (Exception e) {
            ToastUtil.show(ResUtil.getStr(R.string.record_song_fail_plase_check_permission));
            HLog.e(TAG, HLog.CLR,"{}", e);
            FileUtil.safeDeleteFile(path);
            stopRecord();
        }
    }

    private void stopRecord() {
        if (!recording) {
            return;
        }
        try {
            if (recorder != null){
                recorder.stop();
                recorder.release();
                int time = (int)((System.currentTimeMillis() - recordStartTime) / 1000);
                if (listener != null) {
                    listener.onRecordEnd(path, time);
                }
            } else {
                if (listener != null) {
                    listener.onRecordEnd("", -1);
                }
            }
        } catch (Exception e) {
            HLog.e(TAG,HLog.CLR,"{}", e);
            ToastUtil.show(R.string.record_failed);
            FileUtil.safeDeleteFile(path);
        } finally {
            recording = false;
            recorder = null;
            recordInfoTv.setVisibility(VISIBLE);
            progressLay.setVisibility(INVISIBLE);
            progressBar.setProgress(0);
            recordBtn.setImageResource(R.drawable.try_song_record);
            stopAnimator();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopRecord();
        stopAnimator();
    }

    public void setListener(IRecordListener listener) {
        this.listener = listener;
    }

    public interface IRecordListener {
        void onRecordStart();
        void onRecordEnd(String path, int second);
    }
}
