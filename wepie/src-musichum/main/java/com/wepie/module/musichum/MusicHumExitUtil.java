package com.wepie.module.musichum;

import android.app.Activity;

import com.huiwan.base.str.ResUtil;
import com.huiwan.user.LoginHelper;
import com.wepie.module.musichum.model.MusicHumGameInfo;
import com.wepie.module.musichum.net.MusicHumPacketSender;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFragCb;
import com.wepie.wespy.helper.dialog.FragDialogDoubleBtnTip;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.wepie.wespy.model.entity.fixroom.IFixGameInfo;
import com.wepie.wespy.module.fixroom.FixRoomExitCallback;
import com.wepie.wespy.module.fixroom.FixRoomExitUtil;

public class MusicHumExitUtil {
    public static void handleExitMusicHumExit(final Activity activity, final FixRoomInfo fixRoomInfo, IFixGameInfo gameInfo, final FixRoomExitCallback callback) {
        boolean hasDialog = true;
        int selfUid = LoginHelper.getLoginUid();
        if (gameInfo instanceof MusicHumGameInfo && gameInfo.getRid() == fixRoomInfo.getRid()) {
            hasDialog = !gameInfo.exitIgnoreState();
        }
        if (fixRoomInfo.isGameing()) {
            if (fixRoomInfo.isInSit(selfUid) && hasDialog) {
                FragDialogDoubleBtnTip.showDialog(activity, "",
                        ResUtil.getStr(R.string.game_room_draw_room_force_exit),
                        ResUtil.getStr(R.string.game_room_draw_room_force_exit_dialog_btn_right),
                        ResUtil.getStr(R.string.game_room_draw_room_force_exit_dialog_btn_left),
                        true, new BaseFragCb<Object>() {
                            @Override
                            public void onClickCancel() {
                                MusicHumPacketSender.musicHumForceQuitReq(fixRoomInfo.getRid(), null);
                                FixRoomExitUtil.handlerGaming(activity, fixRoomInfo, callback);
                            }
                        });
            } else {
                //不是强退，也发退出游戏的包
                MusicHumPacketSender.musicHumForceQuitReq(fixRoomInfo.getRid(), null);
                FixRoomExitUtil.handlerGaming(activity, fixRoomInfo, callback);
            }
        } else {
            FixRoomExitUtil.handlerNotGaming(activity, fixRoomInfo, callback);
        }
    }
}
