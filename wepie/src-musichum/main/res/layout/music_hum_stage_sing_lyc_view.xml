<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:clipChildren="false"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/musichum_lyric_fly"
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:background="@drawable/musichum_lyric_shadow" />

    <LinearLayout
        android:id="@+id/lyc_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginStart="60dp"
        android:layout_marginEnd="60dp"
        android:orientation="vertical">

        <com.wepie.module.musichum.stage.MusicHumLyricTextView
            android:id="@+id/contribute_lyc_tv1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/ios22"
            android:layout_marginTop="5dp"
            android:layout_marginStart="@dimen/ios8"
            android:layout_marginEnd="@dimen/ios8"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#B2ffffff"
            android:textStyle="bold"
            app:max_size="12"
            tools:text="爱是一道光爱是一道光爱是一道光爱是一道光爱是一道光爱是一道光" />

        <com.wepie.module.musichum.stage.MusicHumLyricTextView
            android:id="@+id/contribute_lyc_tv2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/ios22"
            android:layout_marginStart="@dimen/ios8"
            android:layout_marginTop="@dimen/ios4"
            android:layout_marginEnd="@dimen/ios8"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#B2ffffff"
            android:textStyle="bold"
            app:max_size="12"
            tools:text="如此美妙" />

        <com.wepie.module.musichum.stage.MusicHumLyricTextView
            android:id="@+id/sing_lyc_tv1"
            android:layout_width="match_parent"
            android:layout_height="22dp"
            android:minHeight="@dimen/ios22"
            android:layout_marginStart="@dimen/ios8"
            android:layout_marginTop="@dimen/ios4"
            android:layout_marginEnd="@dimen/ios8"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#ffffffff"
            app:max_size="14"
            android:textStyle="bold"
            android:shadowColor="#FF39F8"
            android:shadowRadius="7"
            android:shadowDx="0"
            tools:text="照亮我们想要的未来照亮我们想要的未来照亮我们想要的未来照亮我们想要的未来" />

        <com.wepie.module.musichum.stage.MusicHumLyricTextView
            android:id="@+id/sing_lyc_tv2"
            android:layout_width="match_parent"
            android:layout_height="22dp"
            android:minHeight="@dimen/ios22"
            android:layout_marginStart="@dimen/ios8"
            android:layout_marginTop="@dimen/ios4"
            android:layout_marginEnd="@dimen/ios8"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="#ffffffff"
            app:max_size="14"
            android:textStyle="bold"
            android:shadowColor="#FF39F8"
            android:shadowRadius="7"
            android:shadowDx="0"
            tools:text="魔力北极光" />
    </LinearLayout>

    <TextView
        android:id="@+id/card_tips_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/lyc_lay"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/ios8"
        android:layout_marginEnd="@dimen/ios17"
        android:background="@drawable/music_hum_card_background_tips"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:padding="4dp"
        android:text="@string/sing_behind_lyrics"
        android:textColor="#ffffff"
        android:textSize="12dp"
        android:visibility="visible" />

</RelativeLayout>