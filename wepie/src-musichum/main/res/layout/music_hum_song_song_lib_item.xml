<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="12dp"
        android:textSize="13dp"
        android:textColor="@color/color_accent"
        android:layout_marginEnd="8dp"
        android:gravity="center"
        tools:text="新歌速递"
        android:background="@drawable/shape_00000000_corner4_stroke_6090ff"/>

</LinearLayout>