<vector android:height="15dp" android:viewportHeight="30"
    android:viewportWidth="58" android:width="29dp"
    xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillType="evenOdd"
        android:pathData="M15,0L43,0A15,15 0,0 1,58 15L58,15A15,15 0,0 1,43 30L15,30A15,15 0,0 1,0 15L0,15A15,15 0,0 1,15 0z"
        android:strokeColor="#00000000" android:strokeWidth="1">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="29" android:endY="17.68"
                android:startX="11.692" android:startY="13.325" android:type="linear">
                <item android:color="#FF43D879" android:offset="0"/>
                <item android:color="#FF2EC157" android:offset="1"/>
            </gradient>
        </aapt:attr>
    </path>
    <path android:fillColor="#FFFFFF" android:fillType="nonZero"
        android:pathData="M43,0C51.284,0 58,6.716 58,15C58,23.284 51.284,30 43,30L15,30C6.716,30 0,23.284 0,15C0,6.716 6.716,0 15,0L43,0ZM43,2L15,2C7.82,2 2,7.82 2,15C2,22.18 7.82,28 15,28L43,28C50.18,28 56,22.18 56,15C56,7.82 50.18,2 43,2Z"
        android:strokeColor="#00000000" android:strokeWidth="1"/>
    <path android:fillColor="#FFFFFF" android:fillType="nonZero"
        android:pathData="M21.025,10.366C21.502,9.867 22.293,9.85 22.792,10.328C23.29,10.806 23.307,11.597 22.83,12.095L14.911,20.361C14.431,20.862 13.635,20.877 13.138,20.394L8.421,15.816C7.925,15.335 7.913,14.544 8.394,14.049C8.875,13.553 9.666,13.541 10.162,14.022L13.976,17.723L21.025,10.366Z"
        android:strokeColor="#00000000" android:strokeWidth="1"/>
</vector>
