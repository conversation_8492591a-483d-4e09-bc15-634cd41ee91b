package com.wejoy.weplay.helper.view;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.anim.schedule.FrameAnimateDrawable;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.widget.CustomTextView;
import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.simple.SimpleComponent;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;

public class WejoyRefreshHeader extends SimpleComponent implements RefreshHeader {
    //默认下拉刷新图片资源
    private static final int DEFAULT_DOWN_IMAGE_RES = R.drawable.jk_refresh_down_default_icon;
    //默认加载中图片资源
    private static final int DEFAULT_LOADING_IMAGE_RES = R.drawable.jk_refresh_loading_default_icon;
    //默认加载动画资源
    private static final int DEFAULT_LOADING_ANIM_RES = R.anim.anim_wejoy_pull_loading;

    private final Drawable mDownImageDrawable;
    private final Drawable mLoadingImageDrawable;
    private FrameAnimateDrawable animationDrawable;

    private final ImageView mHeaderImage;
    private final TextView mHeaderText;

    public WejoyRefreshHeader(Context context) {
        this(context, null);
    }

    public WejoyRefreshHeader(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WejoyRefreshHeader(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));

        mHeaderImage = new ImageView(context);
        mHeaderImage.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        mHeaderImage.setId(View.generateViewId());
        LayoutParams imgLp = new LayoutParams(ScreenUtil.dip2px(76), ScreenUtil.dip2px(32));
        imgLp.addRule(CENTER_IN_PARENT);
        addView(mHeaderImage, imgLp);

        mHeaderText = new CustomTextView(context);
        mHeaderText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10);
        mHeaderText.setSingleLine();
        mHeaderText.setMaxLines(1);
        mHeaderText.setEllipsize(TextUtils.TruncateAt.END);
        mHeaderText.setTextColor(ResUtil.getColor(R.color.color_primary));
        LayoutParams tvLp = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        tvLp.addRule(CENTER_HORIZONTAL);
        tvLp.addRule(BELOW, mHeaderImage.getId());
        tvLp.setMargins(0, ScreenUtil.dip2px(15), 0, 0);
        addView(mHeaderText, tvLp);
        WpImageLoader.load(WpImageLoader.getAssetUri("svga/jk_loading.svga"), null, ImageLoadInfo.newInfo().owner(this), new WpImageLoadListener<String>() {
            @Override
            public boolean onComplete(String model, Drawable drawable) {
                if (drawable instanceof FrameAnimateDrawable) {
                    animationDrawable = (FrameAnimateDrawable) drawable;
                }
                return true;
            }

            @Override
            public boolean onFailed(String model, Exception e) {
                return true;
            }
        });
        Drawable drawable = ResUtil.getDrawable(DEFAULT_DOWN_IMAGE_RES);
        mDownImageDrawable = drawable.mutate();
        mDownImageDrawable.setTint(ResUtil.getColor(R.color.color_accent));
        mLoadingImageDrawable = ResUtil.getDrawable(DEFAULT_LOADING_IMAGE_RES);
    }

    /**
     * 【仅限框架内调用】开始动画
     *
     * @param refreshLayout RefreshLayout
     * @param height        HeaderHeight or FooterHeight
     * @param maxDragHeight 最大拖动高度
     */
    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        if (animationDrawable != null) {
            animationDrawable.stop();
            mHeaderImage.setImageDrawable(animationDrawable);
            animationDrawable.start();
        }
    }

    /**
     * 【仅限框架内调用】动画结束
     *
     * @param refreshLayout RefreshLayout
     * @param success       数据是否成功刷新或加载
     * @return 完成动画所需时间 如果返回 Integer.MAX_VALUE 将取消本次完成事件，继续保持原有状态
     */
    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        if (animationDrawable != null) {
            animationDrawable.stop();
        }
        mHeaderImage.setImageDrawable(mDownImageDrawable);
        return 0;
    }

    /**
     * 【仅限框架内调用】状态改变事件 {@link RefreshState}
     *
     * @param refreshLayout RefreshLayout
     * @param oldState      改变之前的状态
     * @param newState      改变之后的状态
     */
    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        switch (newState) {
            case None:
            case PullDownToRefresh:
                mHeaderImage.setImageDrawable(mDownImageDrawable);
                mHeaderText.setText(ResUtil.getStr(R.string.common_text_pull_to_refresh));
                break;
            case ReleaseToRefresh:
                mHeaderImage.setImageDrawable(mLoadingImageDrawable);
                mHeaderText.setText(ResUtil.getStr(R.string.common_text_pull_to_refresh));
                break;
            case RefreshReleased:
            case Refreshing:
                mHeaderImage.setImageDrawable(mLoadingImageDrawable);
                mHeaderText.setText(ResUtil.getStr(R.string.common_text_loading_refresh));
                break;
        }
    }

}
