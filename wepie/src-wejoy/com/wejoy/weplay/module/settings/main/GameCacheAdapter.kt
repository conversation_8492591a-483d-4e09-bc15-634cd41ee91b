package com.wejoy.weplay.module.settings.main

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.huiwan.base.str.ResUtil
import com.huiwan.store.file.FileManager
import com.huiwan.widget.image.RoundedImageView
import com.huiwan.widget.rv.BaseRvAdapter
import com.huiwan.widget.rv.RVHolder
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R

class GameCacheAdapter(private val checkGameListener: CheckGameListener) :
    BaseRvAdapter<GameCacheData, GameCacheAdapter.Holder>() {
    private val selectGame = mutableMapOf<String, GameCacheData>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        return Holder(LayoutInflater.from(parent.context).inflate(
            R.layout.item_game_cache_layout,
            parent, false
        ), object : CheckGameListener {
            override fun selectGame(gamePref: String, cacheData: GameCacheData) {
                selectGame[gamePref] = cacheData
                checkGameListener.selectGame(gamePref, cacheData)
            }

            override fun unselectGame(gamePref: String) {
                selectGame.remove(gamePref)
                checkGameListener.unselectGame(gamePref)
            }
        })
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val data = getItem(position)
        holder.onBind(data)
        holder.updateSelectGame(selectGame.containsKey(data.gamePref))
    }

    fun getSelectGames(): MutableMap<String, GameCacheData> {
        return selectGame
    }

    fun setAllSelect() {
        for (cacheData in getDataList()) {
            if (cacheData.status == 0) {
                selectGame[cacheData.gamePref] = cacheData
            }
        }
        notifyDataSetChanged()
    }

    fun cancelAllSelect() {
        selectGame.clear()
        notifyDataSetChanged()
    }

    fun isAllSelect(): Boolean {
        val allSize = getDataList().filter {
            it.status == 0
        }.size
        return selectGame.size >= allSize
    }

    class Holder(view: View, private val checkGameListener: CheckGameListener) : RVHolder(view) {
        private val iconIv: RoundedImageView by lazy {
            view.findViewById(R.id.game_icon_iv)
        }
        private val nameTv: AppCompatTextView by lazy {
            view.findViewById(R.id.game_name_tv)
        }
        private val cacheSizeTv: AppCompatTextView by lazy {
            view.findViewById(R.id.game_cache_size_tv)
        }
        private val checkBoc: CheckBox by lazy {
            view.findViewById(R.id.game_check_box)
        }
        private val clearStateTv: TextView by lazy {
            view.findViewById(R.id.operate_state_tv)
        }

        fun onBind(data: GameCacheData) {
            if (data.gameIcon.isNullOrEmpty()) {
                iconIv.setImageResource(R.drawable.default_head_icon)
            } else {
                WpImageLoader.load(data.gameIcon, iconIv)
            }

            nameTv.text = if (data.gameName.isNullOrEmpty()) {
                ResUtil.getStr(R.string.unkonw_game)
            } else {
                data.gameName
            }
            cacheSizeTv.text = FileManager.getUnit(data.cacheSize)
            checkBoc.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    checkGameListener.selectGame(data.gamePref, data)
                } else {
                    checkGameListener.unselectGame(data.gamePref)
                }
            }

            when (data.status) {
                2 -> {
                    nameTv.alpha = 0.3F
                    cacheSizeTv.alpha = 0.3F
                    checkBoc.alpha = 0.3F
                    checkBoc.isEnabled = false
                    clearStateTv.text = ResUtil.getStr(R.string.clear_cache_game_down_loading)
                }

                1 -> {
                    nameTv.alpha = 0.3F
                    cacheSizeTv.alpha = 0.3F
                    checkBoc.alpha = 0.3F
                    checkBoc.isEnabled = false
                    clearStateTv.text = ResUtil.getStr(R.string.clear_cache_game_playing)
                }

                else -> {
                    nameTv.alpha = 1F
                    cacheSizeTv.alpha = 1F
                    checkBoc.alpha = 1F
                    checkBoc.isEnabled = true
                    clearStateTv.text = ""
                }
            }
        }

        fun updateSelectGame(select: Boolean) {
            checkBoc.isChecked = select
        }
    }

    interface CheckGameListener {
        fun selectGame(gamePref: String, cacheData: GameCacheData)
        fun unselectGame(gamePref: String)
    }
}