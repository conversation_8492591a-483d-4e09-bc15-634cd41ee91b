package com.wejoy.weplay.module.settings.main.manageRes

import com.huiwan.base.cahceManager.IManualMangeRes
import com.huiwan.base.cahceManager.ManualResType
import com.huiwan.store.file.FileManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class NormalClearManger(
    override val type: ManualResType,
    override val managedFilePaths: List<String>
) : IManualMangeRes {
    override suspend fun statisticSize(): Long {
        return withContext(Dispatchers.IO) {
            FileManager.getFileSizeAsync(*managedFilePaths.toTypedArray())
        }
    }

    override suspend fun cleanRes() {
        withContext(Dispatchers.IO) {
            managedFilePaths.forEach {
                FileManager.deleteFileOrDir(it)
            }
        }
    }
}