package com.wejoy.weplay.module.settings.main

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Process
import android.text.TextUtils
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.wepie.wespy.module.login.start.StartActivity
import kotlinx.coroutines.launch

class RestartActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val mActivityManager = getSystemService(ACTIVITY_SERVICE) as ActivityManager
        for (appProcess in mActivityManager.runningAppProcesses) {
            val name = appProcess.processName
            if (TextUtils.isEmpty(name)) continue
            if (name == packageName) {
                Process.killProcess(appProcess.pid)
            }
        }
        startActivity(Intent(this, StartActivity::class.java))
        overridePendingTransition(0, 0)
    }

    override fun onPause() {
        super.onPause()
        lifecycleScope.launch {
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        RestartService.stop(this)
    }

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val intent = Intent(context, RestartActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        }
    }

}