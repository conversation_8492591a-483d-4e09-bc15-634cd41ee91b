package com.wejoy.weplay.module.makefriend.related

import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo
import com.huiwan.store.PrefUtil
import com.wejoy.weplay.module.makefriend.WejoyVoiceRoomListFragment
import com.wejoy.weplay.module.makefriend.WejoyVoiceRoomTypeFragment

class WejoyRelatedVoiceRoomFragment : WejoyVoiceRoomTypeFragment<WejoyRelatedRoomViewModel>() {

    private var bannerFragment: Fragment? = null

    private var isFirstIn = PrefUtil.getInstance().getBoolean(KEY_IS_FIRST_IN_RELATE, true)

    override fun getViewModelClass(): Class<WejoyRelatedRoomViewModel> =
        WejoyRelatedRoomViewModel::class.java

    override fun initData() {
        super.initData()
        model.getSelfVoiceRoomInfoListLiveData().observe(viewLifecycleOwner) {
            if (it.isNullOrEmpty()) {
                if (bannerFragment !is NoSelfVoiceRoomFragment) {
                    val fragment = NoSelfVoiceRoomFragment()
                    showBanner(fragment)
                }
            } else {
                if (bannerFragment is SelfAdvanceVoiceRoomFragment) {
                    (bannerFragment as SelfAdvanceVoiceRoomFragment).setData(it)
                } else {
                    val fragment = SelfAdvanceVoiceRoomFragment()
                    fragment.setData(it)
                    showBanner(fragment)
                }
            }
        }
        model.getTitleListLiveData().observe(viewLifecycleOwner) {
            if (viewLifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                checkFirstIn()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        model.requestMyVoiceRoom()
        checkFirstIn()
    }

    private fun checkFirstIn() {
        if (!isFirstIn) {
            return
        }
        if ((roomViewPager.adapter?.itemCount ?: 0) >= 2) {
            roomViewPager.currentItem = 1
            isFirstIn = false
            PrefUtil.getInstance().setBoolean(KEY_IS_FIRST_IN_RELATE, false)
        }
    }

    private fun showBanner(fragment: Fragment) {
        val transaction = childFragmentManager.beginTransaction()
        transaction.replace(headLay.id, fragment)
        transaction.commitAllowingStateLoss()
        bannerFragment = fragment
    }

    override fun createRoomListFragment(info: VoiceLabelInfo): Fragment {
        if (info.id == VoiceLabelInfo.LABEL_RECENT) {
            val fragment = WejoyVoiceRoomListFragment()
            fragment.setData(
                info, WejoyRecentVoiceRoomListViewModel::class.java, getViewModelClass()
            )
            return fragment
        }
        return super.createRoomListFragment(info)
    }

    companion object {
        private const val KEY_IS_FIRST_IN_RELATE = "isFirstInRelate"
    }
}

