package com.wejoy.weplay.module.makefriend.label

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import com.huiwan.widget.ITextViewHolder
import com.huiwan.widget.TabAdapter
import com.huiwan.widget.TabViewHolder
import com.wepie.wespy.R

class WejoyRoomTypeViewAdapter(private val context: Context) :
    TabAdapter<WejoyRoomTypeViewHolder>() {

    private val data: MutableList<String> by lazy {
        ArrayList()
    }

    override fun onCreateTabView(tab: com.huiwan.widget.TabLayout.Tab): WejoyRoomTypeViewHolder {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.wejoy_type_label_item_view, tab.view, false)
        return WejoyRoomTypeViewHolder(view)
    }

    override fun onBindTabView(holder: WejoyRoomTypeViewHolder, position: Int) {
        val s = data[position]
        holder.labelTv.text = s
    }

    override fun getItemCount(): Int = data.size

    fun refresh(list: List<String>) {
        data.clear()
        data.addAll(list)
    }
}

class WejoyRoomTypeViewHolder(view: View) : TabViewHolder(view), ITextViewHolder {
    val labelTv = itemView.findViewById<TextView>(R.id.label_tv)

    override fun findTextView(): TextView = labelTv
}