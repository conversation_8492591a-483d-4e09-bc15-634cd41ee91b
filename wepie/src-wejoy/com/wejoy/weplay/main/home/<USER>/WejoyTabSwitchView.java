package com.wejoy.weplay.main.home.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.configservice.editionentity.HomeFootConfig;
import com.huiwan.store.PrefUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.module.abtest.AbTestManager;
import com.wepie.wespy.module.home.HomeGameConfigUtil;
import com.wepie.wespy.module.main.HomeConst;
import com.wepie.wespy.module.main.tab.OnTabSelectListener;
import com.wepie.wespy.module.main.view.TabSwitchInterface;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by three on 2018/3/28.
 */

public class WejoyTabSwitchView extends LinearLayout implements TabSwitchInterface {
    private static final String KEY_LOVELY_NEW_SHOW_DATE = "lovely_new_show_data";
    private static final String KEY_LOVELY_NEW_SHOW_COUNT = "lovely_new_show_count";

    private final Context mContext;
    private WejoyTabSwitchCell[] wejoyTabSwitchCells;
    private OnTabSelectListener tabSelectListener;
    private int colorNormal;
    private int colorSelected;
    private int[] resNormal;
    private int[] resSelected;
    private String[] trackBtnName;
    private boolean isCurrentShowReturnTop;

    private TabSwitchInterface.Callback callback;
    private static final String TAG = "WejoyTabSwitchView";

    public WejoyTabSwitchView(Context context) {
        this(context, null);
    }

    public WejoyTabSwitchView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        setBackgroundResource(R.drawable.tab_switch_bg);
        setElevation(ScreenUtil.dip2px(8));
    }

    public void initColor(int colorNormal, int colorSelected) {
        this.colorNormal = colorNormal;
        this.colorSelected = colorSelected;
    }

    public void initRes(int[] tabResNormal, int[] tabResSelected, String[] assertAnim, String[] trackBtnName) {
        this.resNormal = tabResNormal;
        this.resSelected = tabResSelected;
        this.trackBtnName = trackBtnName;
    }

    public void initView(String[] titleArray, int visibleCount) {
        wejoyTabSwitchCells = new WejoyTabSwitchCell[titleArray.length];
        int dp61 = ScreenUtil.dip2px(61F);
        int dp1 = ScreenUtil.dip2px(1.5F);
        for (int i = 0; i < titleArray.length; i++) {
            WejoyTabSwitchCell cell = new WejoyTabSwitchCell(mContext);
            cell.initTitleTx(titleArray[i]);
            wejoyTabSwitchCells[i] = cell;

            LayoutParams params = new LayoutParams(0, dp61);
            params.weight = 1;
            cell.setPaddingRelative(0, dp1, 0, 0);
            this.addView(cell, params);

            final int index = i;
            cell.setOnClickListener(v -> {
                if (index == 0 && isCurrentShowReturnTop) {
                    EventDispatcher.postHomeReturnToTop();
                }
                setSelectView(index);
                if (tabSelectListener != null) tabSelectListener.onSelected(index);

                reportEvent(index);
                hideTabTips(index);
            });
        }
    }

    public void setSelectView(int index) {
        int len = wejoyTabSwitchCells.length;
        for (int i = 0; i < len; i++) {
            WejoyTabSwitchCell cell = wejoyTabSwitchCells[i];
            boolean isSelect = index == i;
            if (AbTestManager.getInstance().isHomeRecommend()) {
                // 当前已经选中第一个tab且此时状态为"回到顶部"时
                if (isSelect && index == 0 && isCurrentShowReturnTop) {
                    setHomeTab(true);
                } else {
                    updateSelectStatus(isSelect, i, cell);
                }
                if (i == 0 && !isSelect) {
                    cell.initTitleTx(ResUtil.getStr(R.string.tab_weplay));
                }
            } else {
                updateSelectStatus(isSelect, i, cell);
            }
        }
    }

    private void updateSelectStatus(boolean isSelected, int i, WejoyTabSwitchCell cell) {
        int tx_color = isSelected ? colorSelected : colorNormal;
        int image_res = isSelected ? resSelected[i] : resNormal[i];
        HomeFootConfig.FootIconConfig.IconConfig iconConfig = HomeGameConfigUtil.getHomeTabIcon(i, isSelected, false);
        if (null == iconConfig) {
            iconConfig = new HomeFootConfig.FootIconConfig.IconConfig();
            String msg = "updateSelectStatus error! iconConfig is null!";
            HLog.e(TAG, HLog.USR, msg);
        }
        iconConfig.defaultTitleColor = tx_color;
        iconConfig.defaultImage = image_res;
        cell.updateSelectStatus(iconConfig, isSelected);
    }

    public void setTabTitle(int index, String title) {
        if (index < wejoyTabSwitchCells.length) {
            wejoyTabSwitchCells[index].initTitleTx(title);
        }
    }

    public void showSmallDot(int index, boolean isShow) {
        WejoyTabSwitchCell cell = wejoyTabSwitchCells[index];
        cell.showSmallDot(isShow);
        cell.showBigDot(0);
    }

    public void showBigDot(int index, int num) {
        WejoyTabSwitchCell cell = wejoyTabSwitchCells[index];
        cell.showSmallDot(false);
        cell.showBigDot(num);
    }

    public void registerOnTabSelectListener(OnTabSelectListener listener) {
        this.tabSelectListener = listener;
    }

    public void setHomeTab(boolean showReturnTop) {
        // jackaroo 忽略 showReturnTop
        WejoyTabSwitchCell cell = wejoyTabSwitchCells[0];

        HomeFootConfig.FootIconConfig.IconConfig iconConfig = HomeGameConfigUtil.getHomeTabIcon(0, true, showReturnTop);
        int res = resSelected[0];
        if (null == iconConfig) {
            iconConfig = new HomeFootConfig.FootIconConfig.IconConfig();
            String msg = "updateSelectStatus error! iconConfig is null!";
            HLog.e(TAG, HLog.USR, msg);
        }
        iconConfig.defaultTitleColor = colorSelected;
        iconConfig.defaultImage = res;

        String title = ResUtil.getStr(R.string.tab_weplay);
        cell.initTitleTx(title);
//        isCurrentShowReturnTop = showReturnTop;
        cell.updateSelectStatus(iconConfig, true);
    }

    public void setCellVisible(int index, int visible) {
        wejoyTabSwitchCells[index].setVisibility(visible);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void showTabTips(int index) {
        if (index >= wejoyTabSwitchCells.length || wejoyTabSwitchCells[index].getVisibility() != VISIBLE) {
            if (callback != null) callback.onHide();
        } else if (checkShowTips()) {
            WejoyTabSwitchCell cell = wejoyTabSwitchCells[index];
            Map<String, Object> map = new HashMap<>();
            map.put("screen_sub_name", TrackSource.LOVELY_NEW_POPUP);
            ShenceEvent.appViewScreen(TrackScreenName.HOME_PAGE, map);
            cell.showTips(index, true);
            cell.showSmallDot(true);
            cell.post(() -> {
                int[] location = new int[2];
                cell.getLocationOnScreen(location);
                int x = location[0] + cell.getMeasuredWidth() / 2;
                if (callback != null) callback.onShow(x);
            });
        }
    }

    @Override
    public void hideTabTips(int index) {
        if (index == HomeConst.TAB_FRIEND) {
            wejoyTabSwitchCells[index].showTips(index, false);
            wejoyTabSwitchCells[index].showSmallDot(false);
            if (callback != null) callback.onHide();
        }
    }

    private boolean checkShowTips() {
        PrefUtil prefUtil = PrefUtil.getInstance();
        String currentData = TimeUtil.longToYearMonthAndDay(TimeUtil.getServerTime());
        String lastData = prefUtil.getString(KEY_LOVELY_NEW_SHOW_DATE, "");
        if (currentData.equals(lastData)) {
            int showCount = prefUtil.getInt(KEY_LOVELY_NEW_SHOW_COUNT, 0);
            if (showCount >= 3) {
                return false;
            }
            prefUtil.setInt(KEY_LOVELY_NEW_SHOW_COUNT, showCount + 1);
        } else {
            prefUtil.setString(KEY_LOVELY_NEW_SHOW_DATE, currentData);
            prefUtil.setInt(KEY_LOVELY_NEW_SHOW_COUNT, 1);
        }
        return true;
    }

    private void reportEvent(int index) {
        String trackBtnName = "";
        if (index >= 0 && index < this.trackBtnName.length) {
            trackBtnName = this.trackBtnName[index];
        }
        Map<String, Object> map = new HashMap<>();
        map.put("btn_area", "tab");
        map.put("btn_name", trackBtnName);
        map.put("is_lovely_new_pop", index == HomeConst.TAB_FRIEND && wejoyTabSwitchCells[index].hasTips());
        ShenceEvent.appClick(TrackScreenName.HOME_PAGE, map);
    }

    @Override
    public void setCallback(TabSwitchInterface.Callback callback) {
        this.callback = callback;
    }
}
