package com.wepie.wespy.model.entity.family;

import com.google.gson.annotations.SerializedName;
import com.wepie.wespy.module.family.main.mine.msg.FamilyMsgInterface;

/**
 * Created by <PERSON>wen on 2019-11-04.
 */
public class FamilyMessage implements FamilyMsgInterface {

    @SerializedName("icon")
    private String icon = "";
    @SerializedName("content")
    private String content = "";
    @SerializedName("timestamp")
    private long timestamp;
    @SerializedName("data")
    private FamilyMessageData data = new FamilyMessageData();
    @SerializedName("type")
    private int type;
    @SerializedName("uid")
    private int uid;

    public FamilyMessage() {
    }

    public FamilyMessage(int type, String content) {
        this.type = type;
        this.content = content;
    }

    public static class FamilyMessageData {
        @SerializedName("join_limit_type")
        private int joinLimitType;

        @SerializedName("join_limit_value")
        private int joinLimitValue;

        public int getJoinLimitType() {
            return joinLimitType;
        }

        public int getJoinLimitValue() {
            return joinLimitValue;
        }
    }

    @Override
    public String getMsgIcon() {
        return icon;
    }

    @Override
    public String getMsgText() {
        return content;
    }

    @Override
    public int getMsgType() {
        return type;
    }

    @Override
    public FamilyMessageData getExtData() {
        return data;
    }

    @Override
    public long getTimeStampInSec() {
        return timestamp;
    }

    @Override
    public int getUid() {
        return uid;
    }
}
