package com.wepie.wespy.model.entity.family;

import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON>wen on 2019-10-18.
 */
public class FamilyBaseInfo {

    @SerializedName("id")
    public int id;
    @SerializedName("name")
    public String name = "";
    @SerializedName("avatar")
    public String avatar = "";
    @SerializedName("level")
    public int level;
    @SerializedName("leader_uid")
    public int leaderUid;
    @SerializedName("leader_name")
    public String leaderName = "";
    @SerializedName("announcement")
    public String announcement = "";
    @SerializedName("introduction")
    public String introduction = "";
    @SerializedName("member_count")
    public int memberCount;
    @SerializedName("member_count_limit")
    public int memberCountLimit;
    @SerializedName("rank")
    public int rank;
    @SerializedName("join_limit_type")
    public int joinLimitType = 0; //0: 魅力值  1:游戏等级
    @SerializedName("join_limit_value")
    public int joinLimitValue;
    @SerializedName("active_score")
    public long activeScore;
    @SerializedName("avatar_frame_id")
    public int avatar_frame_id;
    @SerializedName("week_rank")
    public int week_rank;
    @SerializedName("family_light_board")
    public FamilyLightInfo familyLightInfo = new FamilyLightInfo();
    @SerializedName("join_week_game")
    public int joinWeekGame = 1;
    @SerializedName("week_game_info")
    public FamilyWeekGameInfo weekGameInfo = new FamilyWeekGameInfo();

}
