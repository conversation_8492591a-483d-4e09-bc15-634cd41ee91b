package com.wepie.wespy.model.entity;

/**
 * Created by geeksammao on 24/01/2018.
 */

public class SingleChatMsg extends ChatMsg{
    @Override
    public String getMid() {
        return "";
    }

    @Override
    public String getContent() {
        return null;
    }

    @Override
    public void setContent(String content) {

    }

    @Override
    public void setSend_uid(int uid) {

    }

    @Override
    public int getSend_uid() {
        return 0;
    }

    @Override
    public void setRecv_uid(int uid) {

    }

    @Override
    public int getRecv_uid() {
        return 0;
    }

    @Override
    public int getStatus() {
        return 0;
    }

    @Override
    public void setStatus(int status) {

    }

    @Override
    public int getMediaType() {
        return 0;
    }

    @Override
    public void setMediaType(int mediaType) {

    }

    @Override
    public int getSubType() {
        return 0;
    }

    @Override
    public void setSubType(int subType) {

    }

    @Override
    public long getTime() {
        return 0;
    }

    @Override
    public void setSequence(int sequence) {
//        this.sequence = sequence;
    }

    @Override
    public int getSequence() {
        return 0;
    }

    @Override
    public void setTime(long time) {

    }

    @Override
    public void setMid(String mid) {

    }

    @Override
    public void setBlocked(boolean isBlocked) {

    }

    @Override
    public boolean isBlocked() {
        return false;
    }

    @Override
    public void setExt(String ext) {

    }

    @Override
    public String getExt() {
        return null;
    }

    @Override
    public String getRedPacketMsg() {
        return null;
    }

    @Override
    public boolean isPasswordPacket() {
        return false;
    }

    @Override
    public void setContentWithRecall() {

    }

    @Override
    public int getBubbleId() {
        return 0;
    }

    @Override
    public String getRefMid() {
        return "";
    }

    @Override
    public void setRefMid(String refMid) {

    }

    @Override
    public String getQuoteType() {
        return "";
    }

    @Override
    public void setQuoteType(String quoteType) {

    }

    @Override
    public String getQuoteId() {
        return "";
    }

    @Override
    public void setBubbleId(int bubbleId) {
    }
}
