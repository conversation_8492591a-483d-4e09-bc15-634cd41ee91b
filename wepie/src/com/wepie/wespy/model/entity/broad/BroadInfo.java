package com.wepie.wespy.model.entity.broad;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.huiwan.base.util.JsonUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.model.entity.VocSongInfoShare;

/**
 * Created by three on 16/2/2.
 */
public class BroadInfo {
    private static final String TAG = BroadInfo.class.getSimpleName();

    @Expose(serialize = true, deserialize = true)
    @SerializedName("broadcast_id")
    public int broadcast_id;

    @SerializedName("uid")
    @Expose(serialize = true, deserialize = true)
    public int uid;

    @SerializedName("nickname")
    @Expose(serialize = true, deserialize = true)
    public String nickname;

    @SerializedName("headimgurl")
    @Expose(serialize = true, deserialize = true)
    public String headimgurl;

    @SerializedName("message")
    @Expose(serialize = true, deserialize = true)
    public String message;

    @SerializedName("appointment_time")
    @Expose(serialize = true, deserialize = true)
    public long appointment_time;

    @SerializedName("appointment_end_time")
    @Expose(serialize = true, deserialize = true)
    public long appointment_end_time;

    @SerializedName("gender")
    @Expose(serialize = true, deserialize = true)
    public long gender;

    @SerializedName("vow_song_content")
    @Expose(serialize = true, deserialize = true)
    public String vow_song_content;


    @SerializedName("songShareInfo")
    private VocSongInfoShare songShareInfo = null;

    @SerializedName("translateContent")
    public String translateContent = "";

    @Nullable
    public VocSongInfoShare getSongShareInfo() {
        if (TextUtils.isEmpty(vow_song_content)){
            return null;
        }
        if (songShareInfo == null){
            try {
                songShareInfo = JsonUtil.fromJson(vow_song_content, VocSongInfoShare.class);
            } catch (Exception e){
                HLog.e(TAG, "getSongShareInfo: ", e);
            }
        }
        return songShareInfo;
    }

}
