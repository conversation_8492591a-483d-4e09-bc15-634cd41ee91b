package com.wepie.wespy.model.entity.task;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class TaskListInfo {

    public static final int EVERY_DAY_TASK = 1;
    public static final int GROW_TASK = 2;
    public static final int FAMILY_TASK = 3;//加了一个参数task_type. 3表示家族任务，不填就默认的。领取任务奖励的接口和之前的一样
    public static final int FAMILY_ACTIVE_TASK = 4;//参数task_type 4 获取家族周活跃值任务
    public static final int FAMILY_NEWCOMER_BENEFIT_TASK = 16;//新人福利任务
    @SerializedName("active")
    public int active;
    @SerializedName("active_stage_list")
    public List<ActiveStageInfo> activeStageInfos = new ArrayList<>();
    @SerializedName("task_type_list")
    public List<TaskTypeInfo> taskTypeInfos = new ArrayList<>();
    @SerializedName("note")
    public String note = "";

    @SerializedName("family_newbie_gift_task")
    @Nullable
    public FamilyNewcomerGiftTask familyNewcomerGiftTask = null;

    /**
     * 新人见面礼任务
     */
    public static class FamilyNewcomerGiftTask {
        @SerializedName("total")
        public int total = 0;
        @SerializedName("finish_count")
        public int finishedCount = 0;
        @SerializedName("meeting_gift_task")
        public TaskInfo meetingGiftTask = new TaskInfo();
        @SerializedName("newbie_gift_desc")
        public String meetingBenefitIntroDesc = "";
    }

    public static class ActiveStageInfo {
        @SerializedName("stage_id")
        public int stageId;
        @SerializedName("need_active")
        public int needActive;
        @SerializedName("reward_status")
        public int rewardStatus;
        @SerializedName("reward_ids")
        public List<Integer> rewardIds = new ArrayList<>();
        @SerializedName("reward_values")
        public List<Integer> rewardValues = new ArrayList<>();
        @SerializedName("name")
        public String name = "";
        @SerializedName("desc")
        public String desc = "";
        @SerializedName("media_url")
        public String mediaUrl = "";
        @SerializedName("open_media_url")
        public String openMediaUrl = "";

        public boolean isOpen() {
            return rewardStatus == 2;
        }

        public boolean canReceive() {
            return rewardStatus == 1;
        }

        public boolean isNotReach() {
            return rewardStatus == 0;
        }
    }

    public static class TaskTypeInfo {
        @SerializedName("task_type")
        public int taskType;
        @SerializedName("task_list")
        public List<TaskInfo> taskList = new ArrayList<>();
    }

    public static class TaskInfo {
        @SerializedName("task_id")
        public int taskId;
        @SerializedName("title")
        public String title = "";
        @SerializedName("desc")
        public String desc = "";
        @SerializedName("current")
        public int current;
        @SerializedName("condition")
        public int condition;
        @SerializedName("reward_ids")
        public List<Integer> rewardIds = new ArrayList<>();
        @SerializedName("reward_values")
        public List<Integer> rewardValues = new ArrayList<>();
        @SerializedName("jump_url")
        public String jumpUrl = "";
        @SerializedName("show_progress")
        public int showProgress;
        @SerializedName("reward_status")
        public int rewardStatus;
        @SerializedName("remain_reward_num")
        public int remainRewardNum;

        public boolean isShowProgress() {
            return showProgress == 1;
        }

        public boolean isReceived() {
            return rewardStatus == 2;
        }

        public boolean canReceive() {
            return rewardStatus == 1;
        }
    }

    public int getMaxActive() {
        if (activeStageInfos != null && activeStageInfos.size() > 0) {
            return activeStageInfos.get(activeStageInfos.size() - 1).needActive;
        }
        // 异常情况返回，避免当前进度大于最大进度
        return active;
    }

    public List<TaskInfo> getEverydayTaskInfo() {
        if (taskTypeInfos == null) return new ArrayList<>();

        for (TaskTypeInfo taskTypeInfo : taskTypeInfos) {
            if (taskTypeInfo.taskType == EVERY_DAY_TASK) {
                if (taskTypeInfo.taskList != null) {
                    return taskTypeInfo.taskList;
                }
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }

    public List<TaskInfo> getGrowTaskInfo() {
        if (taskTypeInfos == null) return new ArrayList<>();

        for (TaskTypeInfo taskTypeInfo : taskTypeInfos) {
            if (taskTypeInfo.taskType == GROW_TASK) {
                if (taskTypeInfo.taskList != null) {
                    return taskTypeInfo.taskList;
                }
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }

    public List<TaskInfo> getFamilyTaskInfo() {
        if (taskTypeInfos == null) return new ArrayList<>();

        for (TaskTypeInfo taskTypeInfo : taskTypeInfos) {
            if (taskTypeInfo.taskType == FAMILY_TASK || taskTypeInfo.taskType == FAMILY_ACTIVE_TASK) {
                if (taskTypeInfo.taskList != null) {
                    return taskTypeInfo.taskList;
                }
            }
        }
        return new ArrayList<>();
    }

    public boolean hasEveryDayOrGrowTaskRedDot() {
        return hasEveryDayTaskReddot() || hasGrowTaskReddot();
    }

    public boolean hasEveryDayTaskReddot() {
        List<TaskInfo> taskInfos = getEverydayTaskInfo();
        for (TaskInfo taskInfo : taskInfos) {
            if (taskInfo.rewardStatus == 1) {
                return true;
            }
        }
        return false;
    }

    public boolean hasGrowTaskReddot() {
        List<TaskInfo> taskInfos = getGrowTaskInfo();
        for (TaskInfo taskInfo : taskInfos) {
            if (taskInfo.rewardStatus == 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 家族新人礼包任务 （一组2个的任务）是否有红点
     */
    private boolean hasWelcomeBenefitTaskRedDot() {
        List<TaskInfo> welcomeBenefitTasks = new ArrayList<>();
        for (TaskTypeInfo taskTypeInfo : taskTypeInfos) {
            if (taskTypeInfo.taskType == FAMILY_NEWCOMER_BENEFIT_TASK) {
                welcomeBenefitTasks.addAll(taskTypeInfo.taskList);
                break;
            }
        }
        for (TaskInfo taskInfo : welcomeBenefitTasks) {
            if (taskInfo.canReceive()) {
                return true;
            }
        }
        return false;
    }

    public boolean hasFamilyTaskRedDot() {
        TaskInfo familyNewcomerGiftTaskInfo = familyNewcomerGiftTask != null ? familyNewcomerGiftTask.meetingGiftTask : null;
        if (familyNewcomerGiftTaskInfo != null && familyNewcomerGiftTaskInfo.canReceive()) {
            return true;
        }
        boolean showRedDot = false;
        for (TaskListInfo.TaskInfo taskInfo : getFamilyTaskInfo()) {
            if (taskInfo.canReceive()) {
                showRedDot = true;
                break;
            }
        }
        showRedDot = showRedDot || hasWelcomeBenefitTaskRedDot();
        if (!showRedDot && activeStageInfos != null) {
            for (TaskListInfo.ActiveStageInfo activeStageInfo : activeStageInfos) {
                if (activeStageInfo.canReceive()) {
                    showRedDot = true;
                    break;
                }
            }
        }
        return showRedDot;
    }
}
