package com.wepie.wespy.model.entity.marry;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * Created by <PERSON>wen on 2019/1/22.
 */
public class WeddingDay {

    private long startTimeMs;//第一个时间, 作为这个实体的主键
    private List<WeddingTime> weddingTimeList = new ArrayList<>();

    public long getStartTimeMs() {
        return startTimeMs;
    }

    public WeddingDay(long startTimeMs) {
        this.startTimeMs = startTimeMs;
    }

    public void addTime(WeddingTime weddingTime2) {
        weddingTimeList.add(weddingTime2);
    }

    public boolean isFull() {
        for (WeddingTime weddingTime2:weddingTimeList) {
            if (!weddingTime2.isBooked()) {
                return false;
            }
        }
        return true;
    }

    public boolean isValid() {
        return weddingTimeList.size() != 0;
    }

    public int getDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startTimeMs);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    public String getTipTx() {
        return isToday() ? ResUtil.getString(R.string.today) : isTomorrow() ? ResUtil.getString(R.string.tomorrow) : "";
    }

    private boolean isTomorrow() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        int tomorrow = calendar.get(Calendar.DAY_OF_YEAR);
        calendar.setTimeInMillis(startTimeMs);
        int thisDay = calendar.get(Calendar.DAY_OF_YEAR);
        return tomorrow == thisDay;
    }

    private boolean isToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        int today = calendar.get(Calendar.DAY_OF_YEAR);
        calendar.setTimeInMillis(startTimeMs);
        int thisDay = calendar.get(Calendar.DAY_OF_YEAR);
        return today == thisDay;
    }

    public List<WeddingTime> getWeddingTimeList() {
        return weddingTimeList;
    }
}
