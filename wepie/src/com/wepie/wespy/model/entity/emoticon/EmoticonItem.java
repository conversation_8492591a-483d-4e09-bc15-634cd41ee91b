package com.wepie.wespy.model.entity.emoticon;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.huiwan.store.database.SmartModel;

/**
 * date 2020/9/7
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class EmoticonItem extends SmartModel implements Comparable<EmoticonItem> {
    public static final int TYPE_ITEM_ADD = -1;

    @DBPrimaryKey
    @DBField
    @SerializedName("url")
    public String url = "";

    @DBField
    @SerializedName("sort")
    public int sort = 0;

    @DBField
    @SerializedName("type")
    public int type = 0;

    @DBField
    @SerializedName("md5")
    public String md5 = "";

    @DBField
    @SerializedName("name")
    public String name = "";

    public EmoticonItem() {
    }

    public EmoticonItem(String url, int sort, int type, String md5) {
        this.url = url;
        this.sort = sort;
        this.type = type;
        this.md5 = md5;
    }

    public boolean isAdd() {
        return type == TYPE_ITEM_ADD;
    }

    public boolean isEmoticon() {
        return type >= 0;
    }

    // sort 值较大的在前面
    @Override
    public int compareTo(EmoticonItem o) {
        return Integer.compare(o.sort, sort);
    }

    public static EmoticonItem makeAdd() {
        EmoticonItem item = new EmoticonItem();
        item.type = TYPE_ITEM_ADD;
        return item;
    }

    @NonNull
    @Override
    public String toString() {
        return "EmoticonItem{" +
                "sort=" + sort +
                '}';
    }
}
