package com.wepie.wespy.model.event.other;

import androidx.annotation.Nullable;

// Created by bigwen on 2018/4/26.
public class ChangeTabEvent {
    public int tabNum;
    public int subIndex;
    public int roomScene;
    private JumpDetail jumpDetail;

    public interface JumpDetail {
    }


    public ChangeTabEvent(int tabNum, int subIndex) {
        this(tabNum, subIndex, -1, null);
    }

    public ChangeTabEvent(int tabNum, int subIndex, int roomScene) {
        this(tabNum, subIndex, roomScene, null);
    }

    public ChangeTabEvent(int tabNum, int subIndex, int roomScene, @Nullable JumpDetail jumpDetail) {
        this.tabNum = tabNum;
        this.subIndex = subIndex;
        this.roomScene = roomScene;
        this.jumpDetail = jumpDetail;
    }

    public JumpDetail getJumpDetail() {
        return jumpDetail;
    }
}
