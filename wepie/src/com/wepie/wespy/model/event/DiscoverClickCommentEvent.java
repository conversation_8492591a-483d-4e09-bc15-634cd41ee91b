package com.wepie.wespy.model.event;

/**
 * date 2018/4/21
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class DiscoverClickCommentEvent {
    public static final int DISCOVER_SQUARE_TAB = 1;
    public static final int DISCOVER_SQUARE_TOPIC = 2;
    public int from;
    public int discoverId;
    public int discoverOwnerId;
    public int toUid;
    public String toNickname;
    public String content;

    public DiscoverClickCommentEvent(int from, int discoverId, int discoverOwnerId, int toUid, String toNickname, String content) {
        this.from = from;
        this.discoverId = discoverId;
        this.discoverOwnerId = discoverOwnerId;
        this.toUid = toUid;
        this.toNickname = toNickname;
        this.content = content;
    }
}
