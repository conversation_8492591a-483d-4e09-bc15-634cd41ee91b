package com.wepie.wespy.consts;

import androidx.work.Constraints;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;
import androidx.work.WorkRequest;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.component.gift.res.GiftResDownloader;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.platform.ThreadUtil;
import com.wejoy.bingo.common.util.BingoResourceLoad;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.module.abtest.AbTestManager;
import com.wepie.wespy.module.care.main.CareUtilHelper;
import com.wepie.wespy.module.redpacket.RedPacketDownloadUtil;

import java.util.concurrent.TimeUnit;

/**
 * Created by bigwen on 2020-03-20.
 *
 *
 * 结论：
 * 下载资源的分为3个阶段：
 * 1、登录前：一些必须的接口数据；
 * 2、服务器登录完成->下一个页面：首页的配置等；
 * 3、登录完成后：其他（按照优先级排）；
 * 完成上面3点后，再来排查一些比较细节的体验。
 *
 * todo项：
 * 1、统计目前各个需要下载的资源的体积大小，方便列优先级；@jimmy？
 * 2、调研其他产品的技术手段？@何国文
 *
 * 目前的问题
 * 1、登录页下载资源过多，影响登录速度，增加登录失败的概率。一键登录按钮也很大概率不会出现。
 * 2、进入首页后才下载首页的配置，网速慢的情况下，可能会有较长时间的空白，用户点击某个游戏可能无法进入（之前某个KOL出现过）。
 *
 * 希望能做的事情：
 * 1、新用户进入登录页后，优先请求一键登录，保证一键登录的请求成功率；
 * 2、用户登录成功后，再开始下载资源（可以有一个转菊花的过程），防止下载资源占用网速影响登录速度；（我新下载马上用QQ登录，经常出现登录失败的情况）；
 * 3、下载资源可以有一个先后顺序：最优先下载首页的资源，如首页的游戏图片、游戏配置、首页活动等，保证用户进入到首页后的游戏体验；
 * 4、除开首页的资源，其他资源的优先级：
 * • 语音房标签、语音房背景、礼物静态资源；
 * • 画板、item表所有商品静态图片；
 *
 * • 礼物动画、音效，商品动画资源；
 * • 语音房动画标签和撩一下；
 * • 魅力值说明图片；
 * 整体的原则：文件小且影响核心体验的先下，文件体积大且不影响核心体验的后下；
 * 5、如果用户点开了未加载资源的页面or弹窗，则优先下载此处的资源。且保证所有带资源的页面or弹窗，要有默认状态，防止网络差的情况下显示出现问题；
 * 6、所有的资源确认一下是否压缩过；
 */
public class DownloadSourceUtil {
    public static final String TAG = "DownloadSourceUtil";

    /**
     * ConstV3,GiftConfig,PropConfig,UserTagConfig,VoiceRoomConfig接口拉取成功后调用
     */
    public static void downloadConfigData() {
        ThreadUtil.runInOtherThread(() -> {
            RedPacketDownloadUtil.preLoadImgs(ConfigHelper.getInstance().getPropConfig().getRedPacketSkinList());
            //确保可以回测礼物下载策略
            boolean giftPreDownload = !AbTestManager.getInstance().isDisableResPreDownload();
            CareUtilHelper.checkMoveOldResFile(giftPreDownload);
            if (giftPreDownload) {
                GiftResDownloader.check2LoadGiftImage(ConfigHelper.getInstance().getGiftConfig().getPreDownloadGifts());
            }
        });
    }

    public static void startWorkRequest() {
        WorkManager workManager = null;
        try {
            workManager = WorkManager.getInstance(LibBaseUtil.getApplication());
        } catch (Exception e) {
            FLog.e(new Throwable("DownloadSourceUtil.startWorkRequest WorkManager self upload", e));
        }
        if (workManager == null) {
            HLog.d(TAG, HLog.USR, "startWorkRequest workManager is null, return");
            return;
        }

        Constraints constraints = new Constraints.Builder()
                .setRequiresBatteryNotLow(true)
                .setRequiresStorageNotLow(true)
                .setRequiredNetworkType(NetworkType.UNMETERED)
                .build();
        WorkRequest uploadWorkRequest = new OneTimeWorkRequest.Builder(DownloadSourceTask.class)
                .setConstraints(constraints)
                .setInitialDelay(5, TimeUnit.SECONDS)
                .build();
        workManager.enqueue(uploadWorkRequest);
    }

}
