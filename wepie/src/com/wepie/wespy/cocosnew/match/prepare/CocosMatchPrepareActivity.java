package com.wepie.wespy.cocosnew.match.prepare;


import static com.wepie.wespy.cocosnew.match.main.ar285.LittleGameTrackUtilsKt.SCENE_LITTLE_GAME_MAIN;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.barrage.BarrageAnimView;
import com.huiwan.base.util.InitializerManagerUtils;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.SingleClickListener;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.show.GiftContentView;
import com.huiwan.component.gift.show.GiftSendScene;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.constants.IntentConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.lib.api.plugins.ICompetitionApi;
import com.huiwan.lib.api.plugins.friend.AddFriendCallback;
import com.huiwan.lib.api.plugins.friend.AddFriendInfo;
import com.huiwan.littlegame.view.WejoyNativeUserDialog;
import com.huiwan.user.LoginHelper;
import com.huiwan.voiceservice.VoiceManager;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.libpermission.PermissionUtil;
import com.wepie.startup.InitializerManager;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.CocosTrackKt;
import com.wepie.wespy.cocosnew.match.MatchPacketPresenter;
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener;
import com.wepie.wespy.cocosnew.match.invite.CocosInvite;
import com.wepie.wespy.cocosnew.match.main.ar285.loader.LittleGameResUtil;
import com.wepie.wespy.cocosnew.match.matching.CocosMatchUtil;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.dialog.progress.ProgressDialog;
import com.wepie.wespy.model.entity.match.SeatInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.contact.manager.AddFriendManager;
import com.wepie.wespy.module.gift.GiftShowConfigHelper;
import com.wespy.component.suspend.SuspendManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> Yuu
 * email <EMAIL>
 * date 2017/11/13.
 * 冰球 2 V 2 组队
 */
public class CocosMatchPrepareActivity extends BaseActivity implements ICocosMatchPrepareView, OnGameUserEventListener {

    private ProgressDialog progressDialog;
    private CocosMatchPreparePresenter preparePresenter;

    private CocosPrepareView prepareView;
    private ImageView inviteBtn;
    private ImageView matchBtn;
    private ImageView readyBtn;
    private ImageView micBtn;
    private ImageView voiceBtn;
    private TextView titleTv;
    private ImageView prepareBgIv;
    private ImageView prepareFrontIv;
    private TextView suspendTv;
    private TextView rematchTv;
    private boolean fromGameMain = false;

    private GiftContentView giftContentView;
    private BarrageAnimView barrageAnimView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarUtil.initStatusBar(this, false);
        setContentView(R.layout.acticity_cocos_ice_ball_match_pre);
        int gameType = getIntent().getIntExtra(IntentConfig.GAME_TYPE, -1);
        fromGameMain = getIntent().getBooleanExtra(IntentConfig.FROM_GAME_MAIN, false);
        preparePresenter = new CocosMatchPreparePresenter(this);
        preparePresenter.setGameType(gameType);
        initView();
        initEvent();
        preparePresenter.init();
        preparePresenter.registerEventBus();
        ApiService.of(ILittleGameApi.class).preload(this);
        SuspendManager.INSTANCE.closeSuspend();
        CocosTrackKt.cocosTrackViewPrepare(getIntent());
        ApiService.of(ICompetitionApi.class).registerEnterCompetitionCallback(this);
    }

    private void initView() {
        inviteBtn = findViewById(R.id.ice_ball_match_pre_invite_btn);
        readyBtn = findViewById(R.id.ice_ball_match_pre_ready_btn);
        matchBtn = findViewById(R.id.ice_ball_match_pre_start_match_btn);
        micBtn = findViewById(R.id.cocos_mic_btn);
        voiceBtn = findViewById(R.id.cocos_voice_btn);
        titleTv = findViewById(R.id.cocos_title_tv);
        prepareBgIv = findViewById(R.id.prepare_bg_iv);
        prepareFrontIv = findViewById(R.id.prepare_front_iv);
        prepareView = findViewById(R.id.ice_ball_match_prepare_view);
        suspendTv = findViewById(R.id.cocos_suspend_tv);
        rematchTv = findViewById(R.id.rematch_tv);
        giftContentView = findViewById(R.id.gift_content_view);
        barrageAnimView = findViewById(R.id.barrage_view);
        progressDialog = new ProgressDialog(this);

        prepareView.setListener(this);

        new CocosMatchBetInfoView(findViewById(R.id.bet_info_lay));

        CocosGameConfigUtil.loadResAsync(inviteBtn, preparePresenter.getGameType(), CocosGameConfigUtil.GAME_INVITE, true);
        CocosGameConfigUtil.loadResAsync(readyBtn, preparePresenter.getGameType(), CocosGameConfigUtil.GAME_READY, true);
        CocosGameConfigUtil.loadResAsync(matchBtn, preparePresenter.getGameType(), CocosGameConfigUtil.GAME_START, true);
        CocosGameConfigUtil.load(prepareView, preparePresenter.getGameType(), CocosGameConfigUtil.ROOM_EMPTY, new WpImageLoadListener<>() {
            @Override
            public boolean onComplete(String model, Drawable drawable) {
                prepareView.updateUserEmpty(drawable);
                return true;
            }

            @Override
            public boolean onFailed(String model, Exception e) {
                return false;
            }
        });

        CocosGameConfigUtil.loadResBgAsyncWithRecycle(prepareBgIv, preparePresenter.getGameType(), CocosGameConfigUtil.ROOM_BACKGROUND);
        CocosGameConfigUtil.loadResBgAsyncWithRecycle(prepareFrontIv, preparePresenter.getGameType(), CocosGameConfigUtil.ROOM_FRONT);
    }

    private void initEvent() {
        VoiceManager manager = VoiceManager.getInstance();
        updateVoiceIcon(manager.isAgoraOn());
        updateMicIcon(manager.isMicOn(), manager.isAgoraOn());
        readyBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosTrackKt.cocosTrackPrepareChangeReady();
                preparePresenter.changeReadyState();
            }
        });

        matchBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosTrackKt.cocosTrackPrepareAppClick(TrackButtonName.START, null);
                preparePresenter.startMatch();
            }
        });

        inviteBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosTrackKt.cocosTrackPrepareAppClick(TrackButtonName.INVITE, null);
                preparePresenter.showInviteFriend();
            }
        });

        findViewById(R.id.cocos_room_back_img).setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosTrackKt.cocosTrackPrepareAppClick(TrackButtonName.EXIT_GAME, null);
                preparePresenter.leave();
            }
        });

        micBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosMatchUtil.requirePermission(CocosMatchPrepareActivity.this, new PermissionCallback() {
                    @Override
                    public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                        VoiceManager manager = VoiceManager.getInstance();

                        if (alreadyHas) {
                            // 原本有权限时，直接按原本逻辑调整
                            manager.setMicOn(!manager.isMicOn());
                            manager.muteMic();
                        } else {
                            // 原本没有权限时，此时展示应该是关的。 结果状态应该是要保持是开的。
                            manager.setMicOn(true);
                            manager.openMic();
                            manager.rejoinChannel();
                        }
                        updateMicIcon(manager.isMicOn(), manager.isAgoraOn());
                    }
                });
            }
        });

        voiceBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                VoiceManager manager = VoiceManager.getInstance();
                manager.setAgroaOn(!manager.isAgoraOn());
                updateVoiceIcon(manager.isAgoraOn());
                updateMicIcon(manager.isMicOn(), manager.isAgoraOn());
                preparePresenter.clickSpeaker();
            }
        });
        suspendTv.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                suspend();
            }
        });
        PressUtil.addPressEffect(suspendTv);

        rematchTv.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                preparePresenter.rematch();
            }
        });
    }

    private void suspend() {
        LittleGame.touchCreateSuspend();
        if (fromGameMain) {
            JumpUtil.gotoMainActivity(CocosMatchPrepareActivity.this);
        } else {
            finish();
        }
    }

    private BaseFullScreenDialog userDialog;

    public void showUserDialog(int uid) {
        final Activity activity = this;
        final int gameType = preparePresenter.getGameType();
        final WejoyNativeUserDialog nativeUserDialog = new WejoyNativeUserDialog(this);
        nativeUserDialog.update(uid, gameType, 0);
        userDialog = new BaseFullScreenDialog(this, R.style.dialog_style_custom);
        userDialog.setContentView(nativeUserDialog);
        userDialog.setCanceledOnTouchOutside(true);
        userDialog.show();
        nativeUserDialog.setJumpUserClickListener(v -> {
            Map<String, Object> map = new HashMap<>();
            LittleGame.getTeamInfo().addTrackInfo(map);
            ApiService.of(HwApi.class).gotoUserInfoDetailActivityFromGame(v.getContext(), uid, gameType, JsonUtil.toJsonString(map));
        });

        nativeUserDialog.setCallback(new WejoyNativeUserDialog.Callback() {
            @Override
            public void onSendGift(int uid) {
                GiftAnimUtil.showGiftView(activity, GiftShowConfigHelper.sceneSend(GiftSendScene.SceneCocosTeam, uid), info -> {
                    info.subSource = TrackSource.PREPARE_PAGE;
                    preparePresenter.sendGift(info);
                });
                userDialog.dismiss();
            }

            @Override
            public void onAddFriend(final int uid, int addPrice) {
                AddFriendInfo addFriendInfo = new AddFriendInfo(uid, gameType).setSubSource(TrackSource.PREPARE_PAGE);
                LittleGame.getTeamInfo().addTrackInfo(addFriendInfo.getTrackExtData());
                AddFriendManager.getInstance().addFriend(activity, addFriendInfo, new AddFriendCallback() {
                    @Override
                    public void onSuccess(String msg) {
                        nativeUserDialog.refreshAddFriendSuccess();
                        DialogUtil.showAddFriendSuccessDialog(activity, null);
                    }

                    @Override
                    public void onFail(int code, String msg) {
                        ToastUtil.show(msg);
                    }
                });
            }

            @Override
            public void onDismiss() {
                userDialog.dismiss();
            }
        });
    }

    @Override
    protected void onResume() {
        preparePresenter.onResume();
        VoiceManager manager = VoiceManager.getInstance();
        updateVoiceIcon(manager.isAgoraOn());
        updateMicIcon(manager.isMicOn(), manager.isAgoraOn());
        super.onResume();
    }

    @Override
    public void showInviteFriendDialog(int tid, int gameType) {
        CocosInvite.INSTANCE.invite(this, tid, gameType, TrackString.SCENE_GAME_SUB_MATCH_PREPARE);
    }

    @Override
    public void showLoading() {
        progressDialog.show();
    }

    @Override
    public void hideLoading() {
        progressDialog.dismiss();
    }

    @Override
    public void showErrMsg(String msg) {
        ToastUtil.show(msg);
    }

    @Override
    public void updateViews(TeamInfo teamInfo) {
        List<SeatInfo> seatInfoList = teamInfo.getSeats();
        int owner = teamInfo.getOwner();
        boolean isReady = teamInfo.selfIsReady();
        boolean canInvite = !teamInfo.isTeamFull();
        prepareView.updateSeatInfo(seatInfoList, owner);
        if (owner == LoginHelper.getLoginUid()) {
            readyBtn.setVisibility(View.GONE);
            matchBtn.setVisibility(View.VISIBLE);
        } else {
            readyBtn.setVisibility(View.VISIBLE);
            String res = isReady ? CocosGameConfigUtil.GAME_UNREADY : CocosGameConfigUtil.GAME_READY;
            CocosGameConfigUtil.loadResAsync(readyBtn, preparePresenter.getGameType(), res, true);
            matchBtn.setVisibility(View.GONE);
        }
        if (canInvite) {
            inviteBtn.setEnabled(true);
            CocosGameConfigUtil.loadResAsync(inviteBtn, preparePresenter.getGameType(), CocosGameConfigUtil.GAME_INVITE);
        } else {
            inviteBtn.setEnabled(false);
            CocosGameConfigUtil.loadResAsync(inviteBtn, preparePresenter.getGameType(), CocosGameConfigUtil.GAME_INVITE_DISABLE);
        }

        if (teamInfo.getPlayers().size() >= 2) {
            rematchTv.setVisibility(View.VISIBLE);
        } else {
            rematchTv.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
    }

    @Override
    public void onBackPressed() {
//        preparePresenter.leave();
        suspend();
    }

    @Override
    public void leave() {
        finish();
    }

    @Override
    public void jumpToMatch() {
        JumpUtil.gotoIceBallMatchActivity(this, preparePresenter.getGameType(), fromGameMain);
    }

    @Override
    protected void onDestroy() {
        preparePresenter.unRegisterEventBus();
        super.onDestroy();
        VoiceManager.getInstance().clearCallback();
//        VoiceManager.getInstance().leaveAndRelease();
    }

    public void updateMicIcon(boolean micOn, boolean voiceOn) {
        if (micOn && voiceOn && PermissionUtil.hasAudioPermission(this)) {
            micBtn.setImageResource(R.drawable.ic_team_mic_on);
        } else {
            micBtn.setImageResource(R.drawable.ic_team_mic_off);
        }
    }

    public void updateVoiceIcon(boolean voiceOn) {
        if (voiceOn) {
            voiceBtn.setImageResource(R.drawable.ic_team_voice_on);
        } else {
            voiceBtn.setImageResource(R.drawable.ic_team_voice_off);
        }
    }

    @Override
    public void updateTitle(String titleText) {
        titleTv.setText(titleText);
    }

    @Override
    public void showUpdateDialog(int gameType) {
        LittleGameResUtil.INSTANCE.checkLoadRes(this, gameType, true, SCENE_LITTLE_GAME_MAIN, null);
    }

    @Override
    public void onReqChange(int seatId) {
//        MatchPacketPresenter.reqSwitchSeat(seatId);//暂时没有换位功能
    }

    @Override
    public void onKick(int uid) {
        MatchPacketPresenter.onKickUser(uid);
    }

    @Override
    public void onUserClick(int uid) {
        if (uid > 0) {
            showUserDialog(uid);
        }
    }

    @Override
    public void showGiftAnim(GiftShowInfo showInfo) {
        giftContentView.showGiftAnim(showInfo);
        if (!TextUtils.isEmpty(showInfo.barrageAnimStr)) {
            barrageAnimView.startAnim(showInfo.barrageAnimStr, BarrageAnimView.TYPE_GIFT);
        }
    }

    @Override
    public Context getViewContext() {
        return this;
    }

    @Override
    public int supportFloatView() {
        return 0;
    }

    @Override
    protected void filterStartup() {
        super.filterStartup();
        InitializerManagerUtils.wait(InitializerManager.FILTER_FOUR);
    }

    @Override
    public ILife getLife() {
        return ILifeUtil.toLife(this);
    }

    @Override
    public void onSpeak(List<SpeakerInfo> speakerInfos) {
        prepareView.refreshAnim(speakerInfos);
    }
}
