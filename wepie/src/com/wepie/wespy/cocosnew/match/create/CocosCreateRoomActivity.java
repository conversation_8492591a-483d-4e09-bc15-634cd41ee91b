package com.wepie.wespy.cocosnew.match.create;


import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.barrage.BarrageAnimView;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ClipBoardUtil;
import com.huiwan.base.util.InitializerManagerUtils;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.SingleClickListener;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.component.gift.show.GiftContentView;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.IntentConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.ICompetitionApi;
import com.huiwan.littlegame.view.WejoyNativeUserDialog;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.voiceservice.VoiceManager;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.littlegame.LittleGame;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.liblog.main.HLog;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.libpermission.PermissionUtil;
import com.wepie.startup.InitializerManager;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.CocosTrackKt;
import com.wepie.wespy.cocosnew.match.MatchPacketPresenter;
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener;
import com.wepie.wespy.cocosnew.match.invite.CocosInvite;
import com.wepie.wespy.cocosnew.match.matching.CocosMatchUtil;
import com.wepie.wespy.cocosnew.match.prepare.CocosMatchBetInfoView;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.cocosnew.util.CommonUtil;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.model.entity.iceball.IceGameBeforeStart;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.home.InviteSocialFriendDialog;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import java.util.List;


/**
 * <AUTHOR> Yuu
 * email <EMAIL>
 * date 2017/11/13.
 * 冰球 开房间
 */
public class CocosCreateRoomActivity extends BaseActivity implements ICocosCreateRoomView,
        OnGameUserEventListener {

    private CocosCreateRoomPresenter presenter;

    private ImageView inviteBtn;
    private ImageView readyBtn;
    private ImageView startBtn;
    private ImageView micBtn;
    private ImageView voiceBtn;
    private ViewGroup centerUserLay;
    private AbsCocosCreateRoomView centerUserView;

    private GiftContentView giftContentView;
    private BarrageAnimView barrageAnimView;

    private BaseFullScreenDialog switchSeatDialog;
    private TextView switchSeatTimeTv;

    private TextView roomIdTv;
    private ImageView canFollowIv;

    private ImageView roomBgIv;
    private ImageView roomFrontIV;
    private TextView suspendTv;
    private boolean fromGameMain = false;
    private boolean needShowInviteDialog = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarUtil.initStatusBar(this, false);
        setContentView(R.layout.acticity_cocos_ice_ball_create_room);
        presenter = new CocosCreateRoomPresenter(this);
        int gameType = getIntent().getIntExtra(IntentConfig.GAME_TYPE, -1);
        fromGameMain = getIntent().getBooleanExtra(IntentConfig.FROM_GAME_MAIN, false);
        needShowInviteDialog = getIntent().getBooleanExtra(IntentConfig.NEED_SHOW_INVITE_DIALOG, false);
        presenter.setGameType(gameType);
        initView();
        initEvent();
        presenter.init();
        presenter.registerEventBus();
        initData();

        if (needShowInviteDialog) {
            InviteSocialFriendDialog.show(this, gameType, LittleGame.getTeamInfo().getTid(), getTrackSubScene());
        }
        ApiService.of(ILittleGameApi.class).preload(this);
        CocosTrackKt.cocosTrackViewPrepare(getIntent());
        ApiService.of(ICompetitionApi.class).registerEnterCompetitionCallback(this);
    }

    private void initData() {
        ((TextView) findViewById(R.id.cocos_title_tv)).setText(presenter.getTitleText());
    }

    private void initView() {
        centerUserLay = findViewById(R.id.center_user_view_lay);
        inviteBtn = findViewById(R.id.ice_ball_create_room_invite_btn);
        readyBtn = findViewById(R.id.ice_ball_create_room__ready_btn);
        startBtn = findViewById(R.id.ice_ball_create_room_start_game_btn);
        micBtn = findViewById(R.id.cocos_mic_btn);
        voiceBtn = findViewById(R.id.cocos_voice_btn);
        roomBgIv = findViewById(R.id.room_bg_iv);
        roomFrontIV = findViewById(R.id.room_front_iv);
        giftContentView = findViewById(R.id.gift_content_view);
        barrageAnimView = findViewById(R.id.barrage_view);
        suspendTv = findViewById(R.id.cocos_suspend_tv);
        roomIdTv = findViewById(R.id.cocos_room_id_tv);
        new CocosMatchBetInfoView(findViewById(R.id.ice_ball_create_room_entry_lay));
        canFollowIv = findViewById(R.id.follow_iv);
        StatusBarUtil.fitNavigationBar(findViewById(R.id.ice_ball_create_room_bottom_lay));

        CocosGameConfigUtil.loadResAsync(inviteBtn, presenter.getGameType(), CocosGameConfigUtil.GAME_INVITE, true);
        CocosGameConfigUtil.loadResAsync(readyBtn, presenter.getGameType(), CocosGameConfigUtil.GAME_READY, true);
        CocosGameConfigUtil.loadResAsync(startBtn, presenter.getGameType(), CocosGameConfigUtil.GAME_START, true);

        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (GameConfig.isJackarooVip(teamInfo.game_type, teamInfo.gameMode)) {
            // 只有在创建房间的时候，才会存在使用 vip 背景的情况
            CocosGameConfigUtil.loadResBgAsyncWithRecycle(roomBgIv, presenter.getGameType(), CocosGameConfigUtil.ROOM_VIP_BACKGROUND);
        } else {
            CocosGameConfigUtil.loadResBgAsyncWithRecycle(roomBgIv, presenter.getGameType(), CocosGameConfigUtil.ROOM_BACKGROUND);
        }
        CocosGameConfigUtil.loadResBgAsyncWithRecycle(roomFrontIV, presenter.getGameType(), CocosGameConfigUtil.ROOM_FRONT);
        canFollowIv.setImageResource(teamInfo.isPrivate ? R.drawable.jackaroo_eye_blind : R.drawable.jackaroo_eye);
    }

    private void initEvent() {
        VoiceManager manager = VoiceManager.getInstance();
        updateVoiceIcon(manager.isAgoraOn());
        updateMicIcon(manager.isMicOn(), manager.isAgoraOn());
        readyBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosTrackKt.cocosTrackPrepareChangeReady();
                presenter.changeReadyState();
            }
        });
        inviteBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosTrackKt.cocosTrackPrepareAppClick(TrackButtonName.INVITE, null);
                presenter.showInviteFriend();
            }
        });
        startBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosTrackKt.cocosTrackPrepareAppClick(TrackButtonName.START, null);
                presenter.startGame();
            }
        });
        micBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosMatchUtil.requirePermission(CocosCreateRoomActivity.this, new PermissionCallback() {
                    @Override
                    public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                        VoiceManager manager = VoiceManager.getInstance();
                        if (alreadyHas) {
                            // 原本有权限时，直接按原本逻辑调整
                            manager.setMicOn(!manager.isMicOn());
                            manager.muteMic();
                        } else {
                            // 原本没有权限时，此时展示应该是关的。 结果状态应该是要保持是开的。
                            manager.setMicOn(true);
                            manager.openMic();
                            manager.rejoinChannel();
                        }
                        updateMicIcon(manager.isMicOn(), manager.isAgoraOn());
                    }
                });
            }
        });
        voiceBtn.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                VoiceManager manager = VoiceManager.getInstance();
                manager.setAgroaOn(!manager.isAgoraOn());
                updateVoiceIcon(manager.isAgoraOn());
                updateMicIcon(manager.isMicOn(), manager.isAgoraOn());
                presenter.clickSpeaker();
            }
        });
        findViewById(R.id.cocos_room_back_img).setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                CocosTrackKt.cocosTrackPrepareAppClick(TrackButtonName.EXIT_GAME, null);
                presenter.leaveTeam();
            }
        });

        suspendTv.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                suspend();
            }
        });
        PressUtil.addPressEffect(suspendTv);
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.onResume();
        VoiceManager manager = VoiceManager.getInstance();
        updateVoiceIcon(manager.isAgoraOn());
        updateMicIcon(manager.isMicOn(), manager.isAgoraOn());
    }

    @Override
    public void showInviteFriendDialog(int tid, int gameType) {
        CocosInvite.INSTANCE.invite(this, tid, gameType, getTrackSubScene());
    }

    private String getTrackSubScene() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        String trackSubScene = TrackString.SCENE_GAME_SUB_SELF_CREATE;
        if (GameConfig.isJackarooVip(teamInfo.game_type, teamInfo.gameMode)) {
            trackSubScene = TrackString.SCENE_GAME_SUB_VIP_CREATE;
        }
        return trackSubScene;
    }

    @Override
    public void showLoading() {
        showProgressDialog(null, true);
    }

    @Override
    public void hideLoading() {
        hideProgressDialog();
    }

    @Override
    public void showTipMsg(String msg) {
        ToastUtil.show(msg);
    }

    @Override
    protected void onDestroy() {
        presenter.onDestroy();
        super.onDestroy();
        VoiceManager.getInstance().clearCallback();
    }

    private void initSwitchSeatDialog(int uid) {
        switchSeatDialog = new BaseFullScreenDialog(this, R.style.dialog_style);
        switchSeatDialog.setContentView(R.layout.ice_ball_switch_seat_dialog_view);
        final TextView switchSeatUserInfo = switchSeatDialog.findViewById(R.id.ice_ball_switch_info_tv);
        switchSeatTimeTv = switchSeatDialog.findViewById(R.id.ice_ball_switch_time_tv);
        switchSeatDialog.setCanceledOnTouchOutside(true);
        switchSeatDialog.init();

        TextView cancelBt = switchSeatDialog.findViewById(R.id.ice_ball_switch_cancel_btn);
        TextView sureBt = switchSeatDialog.findViewById(R.id.ice_ball_switch_sure_btn);
        cancelBt.setOnClickListener(v -> {
            presenter.disagreeSwitchSeat();
            switchSeatDialog.dismiss();
            switchSeatDialog = null;
            switchSeatTimeTv = null;
        });
        sureBt.setOnClickListener(v -> {
            switchSeatDialog.dismiss();
            switchSeatDialog = null;
            switchSeatTimeTv = null;
            presenter.agreeSwitchSeat();
        });

        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                String nickName = userInfo.getNickname();
                if (nickName.length() > 6) {
                    nickName = nickName.substring(0, 6) + "...";
                }
                String src = ResUtil.getResource().getString(R.string.cocos_req_change_seat_tip_s, nickName);
                Spanned result;
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                    result = Html.fromHtml(src, Html.FROM_HTML_MODE_LEGACY);
                } else {
                    result = Html.fromHtml(src);
                }
                switchSeatUserInfo.setText(result);
                if (!switchSeatDialog.isShowing()) {
                    switchSeatDialog.show();
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
                showTipMsg(description);
            }
        });
    }

    private void updateSwitchSeatTipContent(final int second) {
        if (switchSeatTimeTv != null) {
            switchSeatTimeTv.setText(String.format(LibBaseUtil.getLocale(), "(%d)", second));
        }
    }

    @Override
    public void hideSwitchSeatDialog() {
        if (switchSeatDialog != null) {
            switchSeatDialog.dismiss();
        }
        switchSeatDialog = null;
        switchSeatTimeTv = null;
    }

    @Override
    public void showNewSwitchSeatDialog(final int fromUser, int second) {
        if (switchSeatDialog != null) {
            switchSeatDialog.dismiss();
        }
        switchSeatTimeTv = null;
        switchSeatDialog = null;

        initSwitchSeatDialog(fromUser);
        updateSwitchSeatTipContent(second);
    }

    @Override
    public void updateSwitchSeatDialog(int fromUser, int second) {
        if (switchSeatTimeTv == null) {
            HLog.e("CreateRoom", "error switch seat content should not be null");
        } else {
            updateSwitchSeatTipContent(second);
        }
    }

    private void updateReadyView(TeamInfo teamInfo) {
        if (teamInfo.getOwner() == LoginHelper.getLoginUid()) {
            startBtn.setVisibility(View.VISIBLE);
            readyBtn.setVisibility(View.GONE);
        } else {
            startBtn.setVisibility(View.GONE);
            readyBtn.setVisibility(View.VISIBLE);
            if (teamInfo.selfIsReady()) {
                CocosGameConfigUtil.loadResAsync(readyBtn, presenter.getGameType(), CocosGameConfigUtil.GAME_UNREADY);
            } else {
                CocosGameConfigUtil.loadResAsync(readyBtn, presenter.getGameType(), CocosGameConfigUtil.GAME_READY);
            }
        }
    }

    @Override
    public void updateTeamView(TeamInfo teamInfo) {
        updateReadyView(teamInfo);
        if (centerUserView == null) {
            if (teamInfo.isMateOneGamerTwo()) {
                centerUserView = new CocosCreateRoom1V1View(this);
            } else if (teamInfo.isMateOneGamerFour() || teamInfo.isMateTwoGamerFour()) {
                centerUserView = new CocosCreateRoom2V2View(this);
            } else if (teamInfo.isMateOneGameThree()) {
                centerUserView = new CocosCreateRoomTotal3View(this);
            } else if (teamInfo.isMateOneGameFive()) {
                centerUserView = new CocosCreateRoomTotal5View(this);
            } else if (teamInfo.isMateOneGameSix()) {
                centerUserView = new CocosCreateRoomTotal6View(this, false);
            } else if (teamInfo.isMateOneGameEight()) {
                centerUserView = new CocosCreateRoomTotal8View(this, false);
            } else if (teamInfo.isMateTwoGamerSix()) {
                centerUserView = new CocosCreateRoom2V2V2View(this);
            } else {
                centerUserView = new CocosCreateRoom1V1View(this);
            }
            centerUserLay.addView(centerUserView, new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
            CocosGameConfigUtil.load(centerUserView, presenter.getGameType(), CocosGameConfigUtil.ROOM_EMPTY, new WpImageLoadListener<>() {
                @Override
                public boolean onComplete(String model, Drawable drawable) {
                    centerUserView.setEmptyImg(drawable);
                    return false;
                }

                @Override
                public boolean onFailed(String model, Exception e) {
                    return false;
                }
            });
        }
        centerUserView.updateSeatInfo(teamInfo, this);
        String tid = String.valueOf(teamInfo.tid);
        roomIdTv.setText(tid);
        findViewById(R.id.ice_ball_create_room_id_lay).setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                ClipBoardUtil.copy(CocosCreateRoomActivity.this, tid);
                ToastUtil.show(R.string.copied);
            }
        });
    }

    @Override
    public void updateInvite(boolean canInvite) {
        if (canInvite) {
            inviteBtn.setEnabled(true);
            CocosGameConfigUtil.loadResAsync(inviteBtn, presenter.getGameType(), CocosGameConfigUtil.GAME_INVITE);

        } else {
            inviteBtn.setEnabled(false);
            CocosGameConfigUtil.loadResAsync(inviteBtn, presenter.getGameType(), CocosGameConfigUtil.GAME_INVITE_DISABLE);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
    }


    @Override
    public void leave() {
        ToastUtil.show(R.string.little_game_team_already_exit);
        finish();
    }

    @Override
    public void onBackPressed() {
//        presenter.leaveTeam();
        suspend();
    }

    @Override
    public void jumpToGame(IceGameBeforeStart iceGameBeforeStart) {
        JumpUtil.gotoLittleGameActivity(this, false, 0, iceGameBeforeStart.roomInfo, iceGameBeforeStart.beforeStartData, false);
    }


    public void updateMicIcon(boolean micOn, boolean voiceOn) {
        if (micOn && voiceOn && PermissionUtil.hasAudioPermission(this)) {
            micBtn.setImageResource(R.drawable.ic_team_mic_on);
        } else {
            micBtn.setImageResource(R.drawable.ic_team_mic_off);
        }
    }

    public void updateVoiceIcon(boolean voiceOn) {
        if (voiceOn) {
            voiceBtn.setImageResource(R.drawable.ic_team_voice_on);
        } else {
            voiceBtn.setImageResource(R.drawable.ic_team_voice_off);
        }
    }

    @Override
    public void onReqChange(int seatId) {
        MatchPacketPresenter.reqSwitchSeat(seatId);
    }

    @Override
    public void onKick(int uid) {
        MatchPacketPresenter.onKickUser(uid);
    }

    @Override
    public void onUserClick(int uid) {
        if (uid > 0) {
            CommonUtil.showUserDialog(uid, CocosCreateRoomActivity.this, presenter.getGameType(), WejoyNativeUserDialog.SCENE_CREATE_ROOM_PAGE);
        }
    }

    @Override
    public void showGiftAnim(GiftShowInfo showInfo) {
        giftContentView.showGiftAnim(showInfo);
        if (!TextUtils.isEmpty(showInfo.barrageAnimStr)) {
            barrageAnimView.startAnim(showInfo.barrageAnimStr, BarrageAnimView.TYPE_GIFT);
        }
    }

    @Override
    public void onSpeak(List<SpeakerInfo> speakerInfos) {
        if (centerUserView != null && centerUserView.getVisibility() == View.VISIBLE) {
            centerUserView.refreshAnim(speakerInfos);
        }
    }

    private void suspend() {
        LittleGame.touchCreateSuspend();
        EventDispatcher.postLittleGameSuspend(true);
        if (fromGameMain) {
            JumpUtil.gotoMainActivity(CocosCreateRoomActivity.this);
        } else {
            finish();
        }
    }

    @Override
    public int supportFloatView() {
        return 0;
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.clear();
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        savedInstanceState.clear();
        super.onRestoreInstanceState(savedInstanceState);
    }

    @Override
    protected void filterStartup() {
        super.filterStartup();
        InitializerManagerUtils.wait(InitializerManager.FILTER_FOUR);
    }
}
