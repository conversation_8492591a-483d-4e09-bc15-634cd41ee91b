package com.wepie.wespy.cocosnew.util;

import static com.huiwan.lib.api.plugins.friend.AddFriendInfo.SCENE_VOICE_ROOM;

import android.content.Context;
import android.text.TextUtils;

import androidx.collection.ArrayMap;

import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.component.gift.send.OnGiftSendCb;
import com.huiwan.component.gift.show.GiftSendScene;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.constants.GameType;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.lib.api.plugins.friend.AddFriendCallback;
import com.huiwan.lib.api.plugins.friend.AddFriendInfo;
import com.huiwan.littlegame.view.WejoyNativeUserDialog;
import com.huiwan.media.VolumeUtil;
import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.LoginHelper;
import com.huiwan.voiceservice.VoiceManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.littlegame.LittleGameSimpleInfo;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.lib.api.plugins.track.ITrackExt;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.lib.api.plugins.voice.VoiceConfig;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.shence.ShenceGameTypeSource;
import com.wepie.wespy.helper.shence.util.ShenceGiftUtil;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.contact.manager.AddFriendManager;
import com.wepie.wespy.module.gift.GiftShowConfigHelper;
import com.wepie.wespy.module.media.VoiceConfigHelper;
import com.wepie.wespy.module.voiceroom.dataservice.RoomMsgSendUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.util.VoiceRoomHelper;
import com.wepie.wespy.net.http.api.GameApi;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by bigwen on 2017/11/30.
 */
public class CommonUtil {

    private static final String TAG = "CommonUtil";
    private static boolean isInGame = false;//标记在游戏中，冰球，和其他cocos游戏都是这个字段

    public static int getLoginUid() {
        return LoginHelper.getLoginUid();
    }

    public static boolean isFriend(int uid) {
        return FriendInfoCacheManager.getInstance().isFriend(uid);
    }

    public static Gift getGift(int id) {
        return ConfigHelper.getInstance().getGiftConfig().getGift(id);
    }

    public static String getSid() {
        return LoginHelper.getSid();
    }

    public static void sendRoomMsg(VoiceRoomMsg roomMsg) {
        RoomMsgSendUtil.sendRoomMsg(roomMsg);
    }

    public static int changeChannel(final int type, final String rid, final boolean openMic, boolean isWatcher) {
        if (TextUtils.isEmpty(rid)) return -1;
        VoiceConfig config = VoiceConfig.newBuilder()
                .setVoiceType(type)
                .setChannelName(rid)
                .setOpenMic(openMic)
                .setBroadcaster(!isWatcher)
                .setLittleGame(true)
                .setVolume(VolumeUtil.getVoiceChatVolume())
                .setConfigSelector(new VoiceConfigHelper.ConfigSelector())
                .build();
        return VoiceManager.getInstance().joinChannel(config) ? 0 : -1;
    }

    public static int openMicWithName(String channelName) {
        int code = VoiceManager.getInstance().openMic(channelName) ? 0 : -1;
        HLog.i(TAG, "openMicWithName: " + code);
        return code;
    }

    public static int closeMicWithName(String channelName) {
        int code = VoiceManager.getInstance().closeMic(channelName) ? 0 : -1;
        HLog.i(TAG, "closeMicWithName: " + code);
        return code;
    }

    public static int openMic() {
        return VoiceManager.getInstance().openMic() ? 0 : -1;
    }

    public static int closeMic() {
        return VoiceManager.getInstance().closeMic() ? 0 : -1;
    }

    public static void leaveChannel() {
        VoiceManager.getInstance().leaveChannel();
    }

    public static void leaveAndRelease() {
        VoiceManager.getInstance().leaveAndRelease();
    }

    public static void setAgroaOn(boolean isOn) {
        VoiceManager.getInstance().setAgroaOn(isOn);
    }

    public static boolean isAgoraOn() {
        return VoiceManager.getInstance().isAgoraOn();
    }

    public static void setMicOn(boolean isOn) {
        VoiceManager.getInstance().setMicOn(isOn);
    }

    public static boolean isMicOn() {
        return VoiceManager.getInstance().isMicOn();
    }

    public static int muteMic(String channelName) {
        return VoiceManager.getInstance().muteMic(channelName) ? 0 : -1;
    }

    public static boolean isOnlineStrict(String channelName) {
        return VoiceManager.getInstance().isOnlineStrict(channelName);
    }

    public static int setAudioVolumeIndication(int interval, int smooth) {
        return VoiceManager.getInstance().setAudioVolumeIndication(interval, smooth);
    }

    public static int muteRemoteAudioStream(int uid, boolean isMute) {
        return VoiceManager.getInstance().muteRemoteAudioStream(uid, isMute) ? 0 : -1;
    }

    public static void setInGame(boolean param) {
        isInGame = param;
    }

    public static boolean isInGame() {
        return isInGame;
    }

    /**
     * 小游戏房专用
     */
    public static void showUserDialogInVoiceRoom(int uid, Context context, int gameType, String source, String subSource) {
        WejoyNativeUserDialog nativeUserDialog = new WejoyNativeUserDialog(context);
        nativeUserDialog.update(uid, gameType, VoiceRoomService.getInstance().getRid(), true);
        nativeUserDialog.setJumpUserClickListener(v -> {
            int rid = VoiceRoomService.getInstance().getRoomInfo().rid;
            JumpUtil.enterUserInfoDetailFromVoiceRoom(context, uid, rid);
        });
        BaseFullScreenDialog userDialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        userDialog.setContentView(nativeUserDialog);
        userDialog.setCanceledOnTouchOutside(true);
//        userDialog.initFullScreenDialogWithoutStatusBar();
        userDialog.show();
        nativeUserDialog.setCallback(new WejoyNativeUserDialog.Callback() {
            @Override
            public void onSendGift(int uid) {
                VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
                VoiceRoomHelper.showGiftDialog(context, roomInfo, roomInfo.rid, uid, true, subSource);
                userDialog.dismiss();
            }

            @Override
            public void onAddFriend(final int uid, int addPrice) {
                AddFriendInfo addFriendInfo = AddFriendInfo.newBuilder()
                        .setTargetUid(uid)
                        .setGameType(GameType.GAME_TYPE_VOICE_GAME_ROOM)
                        .setSource(source)
                        .setSubSource(subSource)
                        .setScene(SCENE_VOICE_ROOM)
                        .addExtData("rid", String.valueOf(VoiceRoomService.getInstance().getRid()));

                LittleGameSimpleInfo simpleInfo = VoiceRoomService.getInstance().getVoiceGameViewModel().getLittleGameSimpleInfo();
                if (simpleInfo instanceof ITrackExt) {
                    addFriendInfo.putExtData(((ITrackExt) simpleInfo).getTrackExt());
                }
                AddFriendManager.getInstance().addFriend(context, addFriendInfo, new AddFriendCallback() {
                    @Override
                    public void onSuccess(String msg) {
                        nativeUserDialog.refreshAddFriendSuccess();
                        DialogUtil.showAddFriendSuccessDialog(context, null);
                    }

                    @Override
                    public void onFail(int code, String msg) {
                        ToastUtil.show(msg);
                    }
                });
            }

            @Override
            public void onDismiss() {
                userDialog.dismiss();
            }
        });
    }

    public static void showUserDialog(int uid, Context context, int gameType, int scene) {
        WejoyNativeUserDialog nativeUserDialog = new WejoyNativeUserDialog(context);
        nativeUserDialog.setScene(scene);
        nativeUserDialog.update(uid, gameType, 0);
        BaseFullScreenDialog userDialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        userDialog.setContentView(nativeUserDialog);
        userDialog.setCanceledOnTouchOutside(true);
        userDialog.show();

        nativeUserDialog.setJumpUserClickListener(v -> {
            Map<String, Object> map = new HashMap<>();
            LittleGame.getTeamInfo().addTrackInfo(map);
            ApiService.of(HwApi.class).gotoUserInfoDetailActivityFromGame(v.getContext(), uid, gameType, JsonUtil.toJsonString(map));
        });

        nativeUserDialog.setCallback(new WejoyNativeUserDialog.Callback() {
            @Override
            public void onSendGift(int uid) {
                GiftAnimUtil.showGiftView(context, GiftShowConfigHelper.sceneSend(GiftSendScene.SceneCocosTeam, uid), new OnGiftSendCb() {
                    @Override
                    public void onGiftSend(GiftSendInfo info) {
                        if (scene == WejoyNativeUserDialog.SCENE_CREATE_ROOM_PAGE) {
                            info.subSource = TrackSource.PREPARE_PAGE;
                        }
                        sendGift(info, gameType);
                    }
                });
                userDialog.dismiss();
            }

            @Override
            public void onAddFriend(final int uid, int addPrice) {
                AddFriendInfo addFriendInfo = com.huiwan.littlegame.util.CommonUtil.createAddFriendInfo(uid, gameType);
                if (scene == WejoyNativeUserDialog.SCENE_CREATE_ROOM_PAGE) {
                    addFriendInfo.setSubSource(TrackSource.PREPARE_PAGE);
                }
                if (LittleGame.getTeamInfo().game_type == gameType) {
                    LittleGame.getTeamInfo().addTrackInfo(addFriendInfo.getTrackExtData());
                }
                AddFriendManager.getInstance().addFriend(context, addFriendInfo, new AddFriendCallback() {
                    @Override
                    public void onSuccess(String msg) {
                        nativeUserDialog.refreshAddFriendSuccess();
                        DialogUtil.showAddFriendSuccessDialog(context, null);
                    }

                    @Override
                    public void onFail(int code, String msg) {
                        ToastUtil.show(msg);
                    }
                });
            }

            @Override
            public void onDismiss() {
                userDialog.dismiss();
            }
        });
    }

    private static void sendGift(GiftSendInfo sendInfo, int gameType) {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (teamInfo != null) {
            for (int i = 0; i < teamInfo.players.size(); i++) {
                sendInfo.notifyUidList.add(teamInfo.players.get(i).uid);
            }
            sendInfo.gameType = gameType;
            GameApi.sendGiftCommon(sendInfo, new LifeDataCallback<>(GlobalLife.INSTANCE) {
                @Override
                public void onSuccess(Result<Object> result) {
                    reportCocosSendGift(sendInfo, ShenceGameTypeSource.getGameTypeShortSource(gameType));
                }

                @Override
                public void onFail(int code, String msg) {
                    ToastUtil.show(msg);
                }
            });
        }
    }

    public static void reportCocosSendGift(GiftSendInfo sendInfo, String source) {
        Map<String, Object> map = new ArrayMap<>();
        LittleGame.getTeamInfo().addTrackInfo(map);
        ShenceGiftUtil.reportSendGift(sendInfo, source, "", map);
    }

    public static void trackAutoGuide(int gameType, int mode) {
        Map<String, Object> map = new ArrayMap<>();
        map.put("game_type", gameType);
        map.put("mode_type", mode);
        TrackUtil.appViewScreen(TrackScreenName.NEW_USER_INTRODUCE, map);
    }

    public static void trackQuickStart(int gameType, int mode) {
        Map<String, Object> map = new ArrayMap<>();
        map.put("game_type", gameType);
        map.put("mode_type", mode);
        TrackUtil.appClick(TrackScreenName.NEW_USER_INTRODUCE, TrackButtonName.GAME_START, map);
    }

}
