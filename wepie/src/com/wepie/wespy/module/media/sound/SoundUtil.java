package com.wepie.wespy.module.media.sound;

import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.SoundPool;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.interfaces.SingleCallback;
import com.huiwan.base.str.ResUtil;
import com.huiwan.baseservice.HwServiceManager;
import com.huiwan.baseservice.IHwService;
import com.huiwan.platform.ThreadUtil;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;

import java.util.HashMap;

/**
 * Created by three on 16/4/23.
 */
public class SoundUtil implements IHwService {

    private static final String TAG = "SoundUtil";
    private static SoundUtil instance = null;
    private SoundPool mSoundPool;
    private final HashMap<String, Integer> mSourceMap;
    private final HashMap<String, Integer> sourceTimeMap = new HashMap<>();

    private final HashMap<String, Integer> typeRawMap = new HashMap<>();
    public static final long PLAY_READY_VOICE_GAP = 3000;

    public static final String TYPE_SEAT_FULL_GIRL = "seat_full_girl";
    public static final String TYPE_TICK = "tick";
    public static final String TYPE_GUESS_RIGHT = "guess_right";
    public static final String TYPE_GUESS_WRONG = "guess_wrong";
    public static final String TYPE_MATCH_SUCCESS = "match_success";

    public static final String TYPE_RECEIVE_REWARD = "receive_reward";
    public static final String TYPE_FAMILY_CALL = "family_call";

    public static final String TYPE_CP_GAME_OVER1 = "cp_game_over1";
    public static final String TYPE_CP_GAME_OVER2 = "cp_game_over2";
    public static final String TYPE_CP_GAME_OVER3 = "cp_game_over3";
    public static final String TYPE_CP_MATCH_SUCCESS = "cp_match_success";
    public static final String TYPE_CP_MATCH_FAIL = "cp_match_fail";
    public static final String TYPE_CP_ROOM_TIMER = "cp_room_timer";

    public static final String TYPE_AUCTION_FAIL = "auction_fail";
    public static final String TYPE_AUCTION_HAMMER = "auction_hammer";
    public static final String TYPE_AUCTION_SUCCESS = "auction_success";
    public static final String TYPE_AUCTION_TIME = "auction_time";

    public static final String TYPE_FAMILY_STEAL = "family_steal";

    public static final String TYPE_SPY_GAME_END_SUCCESS = "spy_game_end_success";
    public static final String TYPE_SPY_GAME_END_FALI = "spy_game_end_fail";
    public static final String TYPE_BIG_WINNER_CLICK = "big_winner_click";
    public static final String TYPE_BIG_WINNER_3_2_1_GO = "big_winner_3_2_1";
    public static final String TYPE_BIG_WINNER_WIN = "big_winner_win";
    public static final String TYPE_BIG_WINNER_FAILED = "big_winner_failed";
    public static final String TYPE_BIG_WINNER_SEND_COUNT_DOWN = "big_winner_send_count_down";
    public static final String TYPE_BIG_WINNER_TURNTABLE_START = "big_winner_turntable_start";


    public static final String TYPE_MENTOR_SHIP_GRADUATED = "mentor_ship_graduated";
    public static final String TYPE_MUSIC_HUM_SEAT_FULL = "music_hum_seat_full";

    private SoundUtil() {
        mSoundPool = new SoundPool(300, AudioManager.STREAM_MUSIC, 10);
        mSourceMap = new HashMap<>();

        typeRawMap.put(TYPE_MATCH_SUCCESS, R.raw.match_success);
        typeRawMap.put(TYPE_RECEIVE_REWARD, R.raw.box_puzzle);

        typeRawMap.put(TYPE_GUESS_RIGHT, R.raw.guess_right);
        typeRawMap.put(TYPE_GUESS_WRONG, R.raw.guess_wrong);
        typeRawMap.put(TYPE_TICK, R.raw.tick);
        typeRawMap.put(TYPE_SEAT_FULL_GIRL, R.raw.seat_full_girl);
        typeRawMap.put(TYPE_CP_GAME_OVER1, R.raw.cp_game_over_1);
        typeRawMap.put(TYPE_CP_GAME_OVER2, R.raw.cp_game_over_2);
        typeRawMap.put(TYPE_CP_GAME_OVER3, R.raw.cp_game_over_3);
        typeRawMap.put(TYPE_CP_MATCH_SUCCESS, R.raw.cp_match_success);
        typeRawMap.put(TYPE_CP_MATCH_FAIL, R.raw.cp_match_fail);
        typeRawMap.put(TYPE_CP_ROOM_TIMER, R.raw.cp_room_timer);

        typeRawMap.put(TYPE_AUCTION_FAIL, R.raw.auction_fail);
        typeRawMap.put(TYPE_AUCTION_HAMMER, R.raw.auction_hammer);
        typeRawMap.put(TYPE_AUCTION_SUCCESS, R.raw.auction_success);
        typeRawMap.put(TYPE_AUCTION_TIME, R.raw.auction_time);

        typeRawMap.put(TYPE_FAMILY_STEAL, R.raw.family_steal);
        typeRawMap.put(TYPE_FAMILY_CALL, R.raw.family_call);

//        typeRawMap.put(TYPE_SPY_GAME_END_SUCCESS, R.raw.spy_game_end_success);
//        typeRawMap.put(TYPE_SPY_GAME_END_FALI, R.raw.spy_game_end_fail);

        typeRawMap.put(TYPE_MENTOR_SHIP_GRADUATED, R.raw.mentor_ship_graduated);

        typeRawMap.put(TYPE_BIG_WINNER_3_2_1_GO, R.raw.big_winner_3_2_1_go);
        typeRawMap.put(TYPE_BIG_WINNER_CLICK, R.raw.big_winner_click);
        typeRawMap.put(TYPE_BIG_WINNER_SEND_COUNT_DOWN, R.raw.big_winner_count_down);
        typeRawMap.put(TYPE_BIG_WINNER_TURNTABLE_START, R.raw.big_winner_turntable_start);
        typeRawMap.put(TYPE_BIG_WINNER_FAILED, R.raw.big_winner_failed);
        typeRawMap.put(TYPE_BIG_WINNER_WIN, R.raw.big_winner_win);
//        typeRawMap.put(TYPE_MUSIC_HUM_SEAT_FULL, R.raw.music_hum_seat_full);

        HwServiceManager.getServiceManager().unregisterService(this);
    }

    public static SoundUtil getInstance() {
        if (instance == null) instance = new SoundUtil();
        return instance;
    }

    public int playSound(String type) {
        if (ActivityTaskManager.isBackground()) return 0;
        return playAndLoadLocalFile(type, null);
    }

    public int playSoundCheckMuteRoom(String type, int rid) {
        if (VoiceRoomService.getInstance().isMuteRoom(rid) || ActivityTaskManager.isBackground())
            return 0;
        return playAndLoadLocalFile(type, null);
    }

    public void playSound(String type, SoundPoolCallback callback) {
        if (ActivityTaskManager.isBackground()) {
            if (callback != null) callback.onPlay(0);
            return;
        }
        playAndLoadLocalFile(type, callback);
    }

    public void load(final String type) {
        if (mSourceMap.containsKey(type)) {
            return;
        }
        mSoundPool.load(ResUtil.getApplicationLangContext(), typeRawMap.get(type), 0);
        mSoundPool.setOnLoadCompleteListener((soundPool, sampleId, status) -> mSourceMap.put(type, sampleId));
    }

    public void getSourceTime(String sourceName, SingleCallback<Integer> callback) {
        Integer time = sourceTimeMap.get(sourceName);
        if (time != null) {
            if (callback != null) {
                callback.onCall(time);
            }
            return;
        }
        ThreadUtil.runInOtherThread(() -> {
            int sourceTime;
            Integer raw = typeRawMap.get(sourceName);
            if (raw == null) {
                return;
            }
            MediaPlayer mediaPlayer = MediaPlayer.create(LibBaseUtil.getApplication(), raw);
            if (mediaPlayer == null) {
                return;
            }
            sourceTime = mediaPlayer.getDuration();
            sourceTimeMap.put(sourceName, sourceTime);
            mediaPlayer.release();
            ThreadUtil.runOnUiThread(() -> callback.onCall(sourceTime));
        });
    }

    private int playAndLoadLocalFile(final String type, SoundPoolCallback callback) {
        Integer soundId = mSourceMap.get(type);
        if (soundId != null) {
            int streamId = mSoundPool.play(soundId, 1, 1, 1, 0, 1);
            if (callback != null) callback.onPlay(streamId);
            return streamId;
        }

        mSoundPool.setOnLoadCompleteListener((soundPool, sampleId, status) -> {
            mSourceMap.put(type, sampleId);
            try {
                int streamId = mSoundPool.play(sampleId, 1, 1, 1, 0, 1);
                if (callback != null) callback.onPlay(streamId);
            } catch (Exception e) {
                FLog.e(e);
            }
        });
        mSoundPool.load(ResUtil.getApplicationLangContext(), typeRawMap.get(type), 0);
        return 0;
    }

    public void cancelSoundStream(int streamId) {
        try {
            if (mSoundPool != null) {
                mSoundPool.stop(streamId);
            }
        } catch (Exception e) {
            HLog.e(TAG, "cancelLastPlayOne: ", e);
        }
    }

    public void releaseSoundPool() {
        try {
            if (mSoundPool != null) {
                for (String id : mSourceMap.keySet()) {
                    mSoundPool.stop(mSourceMap.get(id));
                }
                mSoundPool.release();
                mSoundPool = null;
            }
            HwServiceManager.getServiceManager().unregisterService(this);
            instance = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void resumePlay(int streamId) {
        if (mSoundPool != null) {
            mSoundPool.resume(streamId);
        }
    }

    @Override
    public void languageChangeClear() {
        releaseSoundPool();
    }
}
