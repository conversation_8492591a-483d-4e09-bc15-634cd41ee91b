package com.wepie.wespy.module.abtest;

import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.configservice.editionentity.AbTestNormalConfig;
import com.huiwan.configservice.model.AbTestNormal;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.user.LoginHelper;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.wespy.helper.shence.ShenceUtil;
import com.wepie.wespy.net.http.api.ConstApi;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;

/**
 * 和账户无关
 */
public class AbTestNormalManager {
    private AbTestNormalConfig abTestInfo = new AbTestNormalConfig();
    private static AbTestNormalManager ourInstance;
    private ArrayList<String> configString;

    public static AbTestNormalManager getInstance() {
        if (ourInstance == null) ourInstance = new AbTestNormalManager();
        return ourInstance;
    }

    private AbTestNormalManager() {
    }

    private void setAbTestInfo(AbTestNormalConfig abTestInfo) {
        this.abTestInfo = abTestInfo;
        inflateConfigString();
    }

    public static void clear() {
        ourInstance = null;
    }

    public void requestAbTestData() {
        requestAbTestData(true);
    }

    public void requestAbTestData(boolean useCache) {
        if (useCache) {
            if (abTestInfo.version > 0) {
                //do nothing
            } else {
                //第一次加载，load本地，并请求接口
                ThreadUtil.runInOtherThread(() -> {
                    loadCache();
                    if (abTestInfo.version > 0) {
                        //do nothing
                    }
                    requestNormal(abTestInfo.version, abTestInfo.versionMD5);
                });
            }
        } else {
            requestNormal(0, "");
        }
    }

    private void requestNormal(int version, String versionMD5) {
        ConstApi.getNormalAbTest(version, versionMD5, new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<AbTestNormalConfig> result) {
                setAbTestInfo(result.data);
                saveFile(result.data);

                ShenceUtil.registerSuperProperties(false); // 上报abtest配置
            }

            @Override
            public void onFail(int i, String s) {
            }
        });
    }

    private void loadCache() {
        String data = FileUtil.readFileSync(getCacheName());
        try {
            final AbTestNormalConfig testInfo = JsonUtil.fromJson(data, AbTestNormalConfig.class);
            if (testInfo != null) {
                setAbTestInfo(testInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void saveFile(AbTestNormalConfig config) {
        String json = JsonUtil.toJson(config);
        FileUtil.writeFileAsync(getCacheName(), json);
    }

    private String getCacheName() {
        return FileCacheName.NORMAL_AB_TEST + "_" + LoginHelper.getLoginUid();
    }

    public ArrayList<String> getAbtestGroupJsonString() {
        if (abTestInfo.version <= 0 || configString == null) return new ArrayList<>();
        return configString;
    }

    private void inflateConfigString() {
        configString = new ArrayList<>();

        try {
            JSONObject jsonObject = new JSONObject(JsonUtil.toJson(abTestInfo.abTest));
            Iterator<String> keys = jsonObject.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                configString.add(key + ": " + jsonObject.getString(key));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean needEncrypt() {
        return true;
    }

    public AbTestNormal getAbTest() {
        return abTestInfo.abTest;
    }

    public interface Callback {
        void onLocalSuccess(AbTestNormalConfig abTestInfo);
        void onNetSuccess(AbTestNormalConfig abTestInfo);
        void onFail(String msg);
    }
}
