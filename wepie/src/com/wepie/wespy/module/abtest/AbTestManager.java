package com.wepie.wespy.module.abtest;

import static com.huiwan.configservice.modelAbTest.AbTest.MusicHumAAC;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.huiwan.base.common.ChunkConfig;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.configservice.editionentity.AbTestConfig;
import com.huiwan.configservice.modelAbTest.AbTest;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.wespy.helper.shence.ShenceUtil;
import com.wepie.wespy.net.http.api.ConstApi;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * 和账户相关
 */
public class AbTestManager {
    private static final String KEY_AB_TEST = "ab_test_";
    private final AbTestConfig empty = new AbTestConfig();
    private final MutableLiveData<AbTestConfig> liveData = new MutableLiveData<>();
    private static volatile AbTestManager ourInstance;

    private static final List<Observer<AbTestConfig>> observerList = new ArrayList<>();

    public static AbTestManager getInstance() {
        if (ourInstance == null) {
            synchronized (AbTestManager.class) {
                if (ourInstance == null) {
                    ourInstance = new AbTestManager();
                }
            }
        }
        return ourInstance;
    }

    private AbTestManager() {
        loadCache();
        ThreadUtil.runOnUiThread(() -> {
            synchronized (AbTestManager.class) {
                for (Observer<AbTestConfig> observer : observerList) {
                    liveData.observeForever(observer);
                }
            }
        });
    }

    private void setAbTestInfo(AbTestConfig abTestInfo) {
        liveData.postValue(abTestInfo);
    }

    @NonNull
    private AbTestConfig getAbTestInfo() {
        AbTestConfig value = liveData.getValue();
        if (value == null) {
            return empty;
        }
        return value;
    }

    public AbTest getAbTest() {
        return getAbTestInfo().abTest;
    }

    public static void addObserver(Observer<AbTestConfig> observer) {
        synchronized (AbTestManager.class) {
            observerList.add(observer);
        }
        ThreadUtil.runOnUiThread(() -> {
            if (ourInstance != null) {
                ourInstance.liveData.observeForever(observer);
            }
        });
    }

    public static void removeObserver(Observer<AbTestConfig> observer) {
        synchronized (AbTestManager.class) {
            observerList.remove(observer);
        }
        ThreadUtil.runOnUiThread(() -> {
            if (ourInstance != null) {
                ourInstance.liveData.removeObserver(observer);
            }
        });
    }

    public static void clear() {
        synchronized (AbTestManager.class) {
            if (ourInstance != null) {
                for (Observer<AbTestConfig> observer : observerList) {
                    ourInstance.liveData.removeObserver(observer);
                }
            }
            ourInstance = null;
        }
    }

    public void requestAbTestData(final Callback callback) {
        AbTestConfig abTestInfo = getAbTestInfo();
        ConstApi.getAbTest(abTestInfo.getVersion(), abTestInfo.getVersionMD5(), new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<AbTestConfig> result) {
                setAbTestInfo(result.data);
                saveFile(result.data);

                ShenceUtil.registerSuperProperties(false); // 上报abtest配置

                if (callback != null) callback.onSuccess();
            }

            @Override
            public void onFail(int i, String s) {
                if (callback != null) callback.onFail(s);
            }
        });
    }

    private void loadCache() {
        String data = PrefUserUtil.getInstance().getString(KEY_AB_TEST, "");
        if (TextUtil.isEmpty(data)) {
            // 读取已有旧数据
            data = FileUtil.readFileSync(getCacheName());
        }
        try {
            final AbTestConfig testInfo = JsonUtil.fromJson(data, AbTestConfig.class);
            if (testInfo != null) {
                setAbTestInfo(testInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void saveFile(AbTestConfig config) {
        String json = JsonUtil.toJson(config);
        PrefUserUtil.getInstance().setString(KEY_AB_TEST, json);
    }

    private String getCacheName() {
        return FileCacheName.AB_TEST + "_" + LoginHelper.getLoginUid();
    }

    public boolean showPlayComboWithAnim() {
        return getAbTestInfo().abTest.isShowAnim();
    }

    public boolean isHomeRecommend() {
        return true;
    }

    public boolean canVisitNotFriend() {
        return getAbTestInfo().abTest.canVisitNotFriend();
    }

    public boolean isDisableResPreDownload() {
        if (getAbTestInfo().abTest == null) return false;
        return getAbTestInfo().abTest.disable_res_pre_download;
    }

    public boolean useCocos243(int gameType) {
        return getAbTestInfo().abTest.useCocos244List.contains(gameType);
    }

    public boolean isAvatarWolfActivated() {
        return getAbTestInfo().abTest.isAvatarWolfActivated();
    }

    public boolean isAvatarSpyActivated() {
        return getAbTestInfo().abTest.isAvatarSpyActivated();
    }

    public boolean isNewUser() {
        return UserService.get().getLoginUser().getRegisterTime() > TIME_DIVIDER;
    }

    private static final long TIME_DIVIDER = Timestamp.valueOf("2019-08-24 00:00:00").getTime();

    public boolean useNewText() {
        return getAbTestInfo().abTest.useNewTextView();
    }

    public boolean useWebp() {
        return getAbTestInfo().abTest.useWebp;
    }

    public boolean headUseWebp() {
        return getAbTestInfo().abTest.headUseWebp;
    }

    public boolean zoomImage() {
        return getAbTestInfo().abTest.zoomImage;
    }

    public int compressLimitSize() {
        return getAbTestInfo().abTest.compressLimitSize;
    }

    public boolean isShowNewFriendList() {
        return getAbTestInfo().abTest.showNewFriendList();
    }

    public boolean useVap() {
        return getAbTestInfo().abTest.useVap();
    }

    public boolean isTeenMode() {
        return getAbTestInfo().abTest.isTeenMode();
    }

    public boolean proxyH5Request() {
        return getAbTestInfo().abTest.proxyH5Request;
    }

    public List<Integer> getConnectorSupport() {
        return getAbTestInfo().abTest.proxyCocosWebSocket;
    }

    public boolean isCocosUseWeb() {
        return getAbTestInfo().abTest.isCocosUseWeb;
    }

    public int getAudioSec() {
        return getAbTestInfo().abTest.audioRecordCount;
    }

    public ChunkConfig getChunkConfig() {
        return getAbTestInfo().abTest.chunkConfig;
    }

    public int getAudioValidSec() {
        return getAbTestInfo().abTest.audioValidSec;
    }

    public int getBackupAddrCount() {
        return getAbTestInfo().abTest.backupAddrConfig.getCount();
    }

    public int getBackupAddrSecond() {
        return getAbTestInfo().abTest.backupAddrConfig.getSecond();
    }

    public boolean getOpenVoiceAge() {
        return getAbTestInfo().abTest.openVoiceAge;
    }

    public boolean isOpenHomeAge() {
        return getAbTestInfo().abTest.openHomeAge;
    }

    public boolean musicHumUseAAC() {
        return getAbTestInfo().abTest.music_hum_recognize_category == MusicHumAAC;
    }

    public boolean musicHunConUserAAC() {
        return getAbTestInfo().abTest.musicHumConRecognizeCategory == MusicHumAAC;
    }

    public boolean useHttpDns() {
        return getAbTestInfo().abTest.isUseHttpDns;
    }

    public AbTest.QAClient qaClient() {
        return getAbTestInfo().abTest.qaClient;
    }

    public boolean canShowGuessSong() {
        return false;
    }

    public boolean isDynamicTest() {
        return getAbTestInfo().abTest.isDynamicTest;
    }

    public boolean isForceUpdateByInc() {
        return getAbTestInfo().abTest.isForceUpdateByInc();
    }

    /***
     * 增量更新文件数量占总文件数量的百分比
     */
    public int getIncUpdateItemRatioLimit() {
        String key = "inc_update_item_ratio_limit";
        return getAbTestInfo().abTest.getIntFromSwitch(key, 100);
    }

    /**
     * 增量更新文件大小站总文件大小的百分比
     */
    public int getIncUpdateSizeRatioLimit() {
        String key = "inc_update_size_ratio_limit";
        return getAbTestInfo().abTest.getIntFromSwitch(key, 100);
    }

    public boolean isOpenFamilyWeekGame() {
        return getAbTestInfo().abTest.openFamilyWeekGame;
    }

    public interface Callback {
        void onSuccess();

        void onFail(String msg);
    }
}
