package com.wepie.wespy.module.report.module.message;

import com.google.gson.annotations.Expose;
import com.wepie.wespy.model.entity.GroupChatMsg;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.entity.hwroom.HWRoomMsg;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.module.chat.model.ChatImage;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by <PERSON>wen on 2020-05-19.
 */
public class ReportChatMsg implements ReportChatInterface{

    @Expose(serialize = false, deserialize = false)
    private String mid;
    @Expose(serialize = false, deserialize = false)
    private int itemType;
    @Expose(serialize = false, deserialize = false)
    private int reportScene; //本地字段
    private int status;
    public static final int SINGLE_CHAT = 1;
    public static final int GROUP_CHAT = 2;
    public static final int FIX_ROOM = 3;
    public static final int VOICE_ROOM = 4;
    public static final int HW_ROOM = 5;

    private int msgType;//单聊，群聊，语音房，新桌游房对应各自的msgtype
    private int uid;
    private long time;
    private String msg = "";
    private boolean cked;//选中

    public JSONObject getJSONObject() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("msg_type", getContentType());
            jsonObject.put("uid", uid);
            jsonObject.put("time", time);
            jsonObject.put("msg", msg);
            jsonObject.put("cked", cked);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    @Override
    public String getMid() {
        return mid;
    }

    @Override
    public int getItemType() {
        return itemType;
    }

    @Override
    public int getContentType() {
        int contentType = ReportChatInterface.CONTENT_TYPE_TEXT;
        if (reportScene == SINGLE_CHAT) {
            if (msgType == WPMessage.MEDIA_TYPE_NORMAL_PHOTO) {
                contentType = ReportChatInterface.CONTENT_TYPE_IMAGE;
            } else if (msgType == WPMessage.MEDIA_TYPE_AUDIO) {
                contentType = ReportChatInterface.CONTENT_TYPE_VOICE;
            } else if (msgType == WPMessage.MEDIA_TYPE_CHAT_VIDEO) {
                contentType = ReportChatInterface.CONTENT_TYPE_VIDEO;
            }
        } else if (reportScene == GROUP_CHAT) {
            if (msgType == GroupChatMsg.MEDIA_TYPE_IMAGE) {
                contentType = ReportChatInterface.CONTENT_TYPE_IMAGE;
            } else if (msgType == GroupChatMsg.MEDIA_TYPE_AUDIO) {
                contentType = ReportChatInterface.CONTENT_TYPE_VOICE;
            } else if (msgType == GroupChatMsg.MEDIA_TYPE_VIDEO) {
                contentType = ReportChatInterface.CONTENT_TYPE_VIDEO;
            }
        } else if (reportScene == FIX_ROOM) {
            if (msgType == GroupChatMsg.MEDIA_TYPE_IMAGE) {
                contentType = ReportChatInterface.CONTENT_TYPE_IMAGE;
            } else if (msgType == GroupChatMsg.MEDIA_TYPE_AUDIO) {
                contentType = ReportChatInterface.CONTENT_TYPE_VOICE;
            }
        } else if (reportScene == VOICE_ROOM) {
            if (msgType == VoiceRoomMsg.MEDIA_TYPE_PHOTO || msgType == VoiceRoomMsg.MEDIA_TYPE_EMOJI) {
                contentType = ReportChatInterface.CONTENT_TYPE_IMAGE;
            }
        }
        return contentType;
    }

    @Override
    public int getSenderUid() {
        return uid;
    }

    @Override
    public long getTime() {
        return time;
    }

    @Override
    public String getContent() {
        return msg;
    }

    @Override
    public ChatImage getImage() {
        return ChatImage.fromString(msg);
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    public void setOriginalMsgType(int msgType) {
        this.msgType = msgType;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setChecked(boolean cked) {
        this.cked = cked;
    }

    public boolean isChecked() {
        return cked;
    }

    public void setReportScene(int reportScene) {
        this.reportScene = reportScene;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getReportScene() {
        return reportScene;
    }
}
