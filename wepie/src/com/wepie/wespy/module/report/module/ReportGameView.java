package com.wepie.wespy.module.report.module;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.ReportGameList;
import com.wepie.wespy.net.http.api.ConstApi;

/**
 * Created by bigwen on 2020-05-19.
 */
public class ReportGameView extends ReportBaseModule {

    private Context mContext;
    private ReportGameAdapter reportGameAdapter;

    public ReportGameView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public ReportGameView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.report_game_view, this);
        RecyclerView recyclerView = findViewById(R.id.recycle_view);
        final int itemWidth = ScreenUtil.getScreenWidth() / 3;
        recyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                //不是第一个的格子都设一个左边和底部的间距
                //由于每行都只有3个，所以第一个都是3的倍数，把左边距设为0
                if (parent.getChildLayoutPosition(view) % 3 == 0) {
                    outRect.left = (int) mContext.getResources().getDimension(R.dimen.wp16);
                } else if (parent.getChildLayoutPosition(view) % 3 == 1) {
                    outRect.left = (int) (itemWidth - mContext.getResources().getDimension(R.dimen.ios106)) / 2;
                } else if (parent.getChildLayoutPosition(view) % 3 == 2) {
                    outRect.left = itemWidth - (int) mContext.getResources().getDimension(R.dimen.ios106) - (int) mContext.getResources().getDimension(R.dimen.wp16);
                }
            }
        });
        reportGameAdapter = new ReportGameAdapter(mContext);
        recyclerView.setAdapter(reportGameAdapter);
        recyclerView.setNestedScrollingEnabled(false);
        initData();
    }

    private void initData() {
        ConstApi.getReportGameList(new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<ReportGameList> result) {
                reportGameAdapter.refreshDataList(result.data.getGameList());
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    public int getChooseGameType() {
        return reportGameAdapter.getChooseGameType();
    }

    @Override
    public void setModuleInterface(ReportModuleInterface moduleInterface) {
        reportGameAdapter.setModuleInterface(moduleInterface);
    }
}