package com.wepie.wespy.module.report.module;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.store.PrefUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.report.module.message.ReportChatMsg;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2020-05-21.
 */
public class ReportChatView extends ReportBaseModule {

    private Context mContext;
    private TextView mChatTypeTv;
    private TextView mChatNumTv;
    private TextView singleChatTipTv;
    private List<ReportChatMsg> dataList = new ArrayList<>();

    public ReportChatView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public ReportChatView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.report_chat_view, this);
        mChatTypeTv = findViewById(R.id.chat_type_tv);
        mChatNumTv = findViewById(R.id.chat_num_tv);
        singleChatTipTv = findViewById(R.id.single_chat_tips_tv);
        ReportUtil.setUpReportChatView(this);
    }

    public void refreshSingleChat(int otherUid) {
        mChatTypeTv.setText(R.string.dm_proof);
        refreshSingleChatReportTips();
        this.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                PrefUtil.getInstance().setBoolean(PrefUtil.KET_REPORT_SINGLE_CHAT_TIPS, true);
                refreshSingleChatReportTips();
                JumpUtil.gotoReportChatChooseActivity(mContext, otherUid, 0);
            }
        });
    }

    private void refreshSingleChatReportTips() {
        boolean showedTips = PrefUtil.getInstance().getBoolean(PrefUtil.KET_REPORT_SINGLE_CHAT_TIPS, false);//已经展示过
        singleChatTipTv.setVisibility(showedTips ? GONE: VISIBLE);
    }

    public void refreshGroupChat(int gid) {
        mChatTypeTv.setText(ResUtil.getStr(R.string.family_report_group_activity_group_evidence));
        this.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                JumpUtil.gotoReportChatChooseActivity(mContext, 0, gid);
            }
        });
    }

    @Override
    public void setModuleInterface(ReportModuleInterface moduleInterface) {

    }

    public void setChatList(List<ReportChatMsg> chatList) {
        dataList.clear();
        dataList.addAll(chatList);
        int chooseNum = 0;
        for (ReportChatMsg reportChatMsg: dataList) {
            if (reportChatMsg.isChecked()) chooseNum++;
        }
        mChatNumTv.setText(ResUtil.getStr(R.string.choos_x_proofs, chooseNum));
    }

    public List<ReportChatMsg> getChatData() {
        return dataList;
    }
}