package com.wepie.wespy.module.draw.core.draw;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.huiwan.base.util.BitmapUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.platform.ThreadUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.uploader.BucketType;
import com.wepie.lib.api.uploader.IUploadCallback;
import com.wepie.lib.api.uploader.SimpleFileUploader;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.libimageloader.WpSimpleImageLoadListener;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.helper.shence.ShenceGameTypeSource;
import com.wepie.wespy.model.entity.fixroom.DrawGuessGameInfo;
import com.wepie.wespy.model.entity.fixroom.DrawGuessGamerInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.draw.core.action.DrawActionCallback;
import com.wepie.wespy.module.draw.core.action.DrawActionView;
import com.wepie.wespy.module.draw.core.action.DrawToolCallback;
import com.wepie.wespy.module.draw.core.action.DrawToolView;
import com.wepie.wespy.module.draw.core.action.GuessToolCallback;
import com.wepie.wespy.module.draw.core.action.GuessToolView;
import com.wepie.wespy.module.draw.core.info.DrawInfo;
import com.wepie.wespy.module.draw.core.info.PlayInfo;
import com.wepie.wespy.module.draw.core.share.DrawShareUtil;
import com.wepie.wespy.module.draw.core.share.PlayToolCallback;
import com.wepie.wespy.module.draw.core.share.PlayToolView;
import com.wepie.wespy.module.draw.core.snapshot.ServerSnapShotLifeCallback;
import com.wepie.wespy.module.draw.core.snapshot.ServerSnapShotUtil;
import com.wepie.wespy.module.draw.core.snapshot.SnapShot;
import com.wepie.wespy.module.report.ReportBuilder;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.http.api.PropApi;
import com.wepie.wespy.net.tcp.sender.DrawGuessPacketSender;

import java.io.File;
import java.util.List;
import java.util.Stack;

/**
 * Created by bigwen on 2019/3/15.
 */
public class DrawMainView extends FrameLayout {

    private Context mContext;
    private DrawPathView drawPathSurView;
    private DrawToolView drawToolView;
    private GuessToolView guessToolView;
    private PlayToolView playToolView;
    private ImageView playIv;
    private String TAG = "DrawMainView";
    private DrawMainCallback callback;
    private DrawActionView drawActionView;
    private int drawBoardId = -1;
    private DrawGuessGameInfo mGuessGameInfo;
    private ProgressDialogUtil progressDialogUtil = new ProgressDialogUtil();
    private DrawUpload drawUploadUtil = new DrawUpload();

    public DrawMainView(@NonNull Context context) {
        super(context);
        mContext = context;
        init(context);
    }

    public DrawMainView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public DrawMainView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public DrawMainView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        LayoutInflater.from(mContext).inflate(R.layout.draw_main_view, this);
        drawPathSurView = findViewById(R.id.draw_sur_view);
        drawActionView = findViewById(R.id.draw_action_view);
        drawToolView = findViewById(R.id.draw_tool_view);
        guessToolView = findViewById(R.id.guess_tool_view);
        drawActionView.setToolInterface(drawToolView);
        playToolView = findViewById(R.id.play_tool_view);
        playIv = findViewById(R.id.draw_image_iv);

        drawActionView.setDrawActionCallback(new DrawActionCallback() {
            @Override
            public void drawAction(DrawInfo drawInfo) {
                DrawUtil.print(TAG, "drawAction: " + drawInfo.getSeq());
                drawPathSurView.submitSelfTraceInMain(drawInfo);
                refreshUndoAndRedo();
            }

            @Override
            public void drawSelfAction(DrawInfo drawInfo) {
//                DrawUtil.print(TAG, "drawSelfAction: " + drawInfo.getSeq());
                drawPathSurView.submitSelfTraceInMain(drawInfo);
            }

            @Override
            public int getNextSeq() {
                return getNextLocalSeq();
            }
        });

        drawToolView.setDrawToolCallback(new DrawToolCallback() {
            @Override
            public void onClear() {
                DrawInfo drawInfo = new DrawInfo();
                drawInfo.setDrawType(DrawInfo.TYPE_CLEAR_ALL);
                drawInfo.setSeq(getNextLocalSeq());

                drawPathSurView.submitSelfTraceInMain(drawInfo);
            }

            @Override
            public void onSelectDrawBoard(int propId) {
                if (propId != drawBoardId) {
                    drawBoardId = propId;
                    PropApi.useProp(propId, new LifeDataCallback<>(DrawMainView.this) {
                        @Override
                        public void onSuccess(Result<String> result) {
                            DrawGuessPacketSender.changeDrawBoard(drawBoardId, new LifeSeqCallback(DrawMainView.this) {
                                @Override
                                public void onSuccess(RspHeadInfo head) {
                                    refreshDrawBoard(drawBoardId);
                                }

                                @Override
                                public void onFail(RspHeadInfo head) {
                                    ToastUtil.show(head.desc);
                                }
                            });
                        }

                        @Override
                        public void onFail(int i, String s) {
                            ToastUtil.show(s);
                        }
                    });
                }
            }

            @Override
            public void onSave() {
                onSaveAction();
            }

            @Override
            public void redo() {
                DrawInfo drawInfo = new DrawInfo();
                drawInfo.setDrawType(DrawInfo.TYPE_REDO);
                drawInfo.setSeq(getNextLocalSeq());

                drawPathSurView.submitSelfTraceInMain(drawInfo);
                refreshUndoAndRedo();
            }

            @Override
            public void undo() {
                DrawInfo drawInfo = new DrawInfo();
                drawInfo.setDrawType(DrawInfo.TYPE_UNDO);
                drawInfo.setSeq(getNextLocalSeq());

                drawPathSurView.submitSelfTraceInMain(drawInfo);
                refreshUndoAndRedo();
            }

            @Override
            public boolean hasDraw() {
                return drawPathSurView.getTotalActionSize() > 0;
            }
        });

        guessToolView.setGuessToolInterface(new GuessToolCallback() {
            @Override
            public void onSave() {
                onSaveAction();
            }

            @Override
            public void onEgg() {
                if (callback != null) callback.sendEgg();
            }

            @Override
            public void onFlower() {
                if (callback != null) callback.sendFlower();
            }

            @Override
            public void uploadDraw() {
                uploadDrawToServer();
            }

            @Override
            public boolean hasDraw() {
                return drawPathSurView.getTotalActionSize() > 0;
            }
        });

        drawPathSurView.setCallback(new DrawPathViewCallback() {
            @Override
            public void onDrawBitmap(Bitmap bitmap) {

            }

            @Override
            public void showSnapShot(Stack<SnapShot> snapShots) {

            }

            @Override
            public void addAction(int seq) {
                if (playToolView.getVisibility() == View.VISIBLE) {
                    playToolView.refreshProgress(seq);
                }
                refreshUndoAndRedo();
            }

            @Override
            @WorkerThread
            public void uploadTrace(List<DrawInfo> drawInfoList, int localSeq) {
                drawUploadUtil.uploadDrawTrace(drawInfoList, localSeq);
            }
        });

        playToolView.setCallback(new PlayToolCallback() {
            @Override
            public void start(final List<DrawInfo> drawInfoList) {
                drawPathSurView.clearAll(new FinishCallback() {
                    @Override
                    public void onFinish() {
                        drawPathSurView.submitTraceListInMain(drawInfoList);
                        playIv.setVisibility(GONE);
                    }
                });
            }

            @Override
            public void setPlaySpeed(PlaySpeed speed) {
                drawPathSurView.setSpeed(speed);
            }

            @Override
            public void end() {
//                playIv.setVisibility(VISIBLE);
            }

            @Override
            public void pause() {
                drawPathSurView.onPause();
            }

            @Override
            public void resume(List<DrawInfo> drawInfoList) {
                drawPathSurView.submitTraceListInMain(drawInfoList);
                playIv.setVisibility(GONE);
            }
        });
    }

    private void onSaveAction() {
        if (mGuessGameInfo == null) {
            ToastUtil.show(R.string.draw_info_error);
            return;
        }
        getDrawBitmap(new DrawBitmapCallback() {
            @Override
            public void onFinish(Bitmap bitmap) {
                DrawShareUtil.showShareDialog(mContext, bitmap, false, mGuessGameInfo, getDrawPathMaxSeq());
            }
        });
    }

    private void uploadDrawToServer() {
        if (mGuessGameInfo == null) {
            HLog.e(TAG, HLog.USR, "uploadDraw: mGuessGameInfo is null");
            return;
        }
        Bitmap bitmap = getDrawBitmap();
        if (bitmap == null) {
            HLog.e(TAG, HLog.USR, "uploadDraw: draw bitmap is null!");
            return;
        }
        ThreadUtil.runInOtherThread(() -> {
            Bitmap targetBitmap = bitmap.copy(Bitmap.Config.ARGB_4444, true);
            Canvas canvas = new Canvas(targetBitmap);
            canvas.drawColor(Color.WHITE);
            canvas.drawBitmap(bitmap, 0, 0, null);
            String tmpFilePath = DrawReportUtil.getCacheDrawImgPath();
            BitmapUtil.saveBitmapWithCompress(BitmapUtil.zoomImage(targetBitmap, 560, 640), new File(tmpFilePath));
            ThreadUtil.runOnUiThread(() ->
                    SimpleFileUploader.upload(BucketType.drawImage, tmpFilePath, new IUploadCallback() {
                        @Override
                        public void onSuccess(String localPath, String url) {
                            final VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(mGuessGameInfo.getRid());
                            int gameType = roomInfo.game_type;
                            String source = ShenceGameTypeSource.getGameTypeSource(gameType);
                            JumpUtil.gotoReportGameUserActivity(mContext, ReportBuilder.newBuilder()
                                    .setTargetUid(mGuessGameInfo.getDrawUid())
                                    .setRid(mGuessGameInfo.getRid())
                                    .setGameType(gameType)
                                    .setGameImgUrl(url)
                                    .setSource(source));

                            FileUtil.safeDeleteFile(tmpFilePath);
                        }

                        @Override
                        public void onFailed(int code, String msg) {
                            ToastUtil.debugShow(msg);
                            HLog.e(TAG, HLog.USR, "uploadDraw: upload file failed! msg = {}", msg);
                            FileUtil.safeDeleteFile(tmpFilePath);
                        }
                    }));

        });
    }

    public void showDraw(DrawGuessGameInfo guessGamerInfo) {
        if (guessGamerInfo == null) return;
        this.mGuessGameInfo = guessGamerInfo;

        DrawGuessGamerInfo drawingGamer = guessGamerInfo.getDrawingGamer();
        if (drawingGamer != null) {
            refreshDrawBoard(drawingGamer.getDrawBoardId());
        }

        drawPathSurView.setVisibility(VISIBLE);
        drawPathSurView.setSpeed(PlaySpeed.SPEED_A);
        drawActionView.setVisibility(VISIBLE);
        guessToolView.setVisibility(GONE);
        drawToolView.setVisibility(VISIBLE);
        playToolView.setVisibility(GONE);
        playIv.setVisibility(GONE);
        drawUploadUtil.setNeedSendPacker(true);
    }

    public void showGuessViewReportIcon() {
        guessToolView.showReportIcon();
    }

    public void showGuess(DrawGuessGameInfo guessGamerInfo) {
        if (guessGamerInfo == null) return;
        this.mGuessGameInfo = guessGamerInfo;

        DrawGuessGamerInfo drawingGamer = guessGamerInfo.getDrawingGamer();
        if (drawingGamer != null) {
            refreshDrawBoard(drawingGamer.getDrawBoardId());
        }

        drawPathSurView.setVisibility(VISIBLE);
        drawPathSurView.setSpeed(PlaySpeed.SPEED_F);
        drawActionView.setVisibility(GONE);
        guessToolView.setVisibility(VISIBLE);
        drawToolView.setVisibility(GONE);
        playToolView.setVisibility(GONE);
        playIv.setVisibility(GONE);
        drawUploadUtil.setNeedSendPacker(false);
    }

    public void showPlay(final PlayInfo playInfo) {
        if (playInfo == null) return;

        refreshDrawBoard(playInfo.getDrawBoardId());

        drawPathSurView.setVisibility(VISIBLE);
        drawPathSurView.setSpeed(PlaySpeed.SPEED_E);
        drawActionView.setVisibility(GONE);
        guessToolView.setVisibility(GONE);
        drawToolView.setVisibility(GONE);
        playToolView.setVisibility(VISIBLE);
        playIv.setVisibility(VISIBLE);
        ImageLoaderUtil.loadNormalImage(playInfo.getDrawImageUrl(), playIv);
        drawUploadUtil.setNeedSendPacker(false);

        String traceUrl = playInfo.getTraceUrl();
        if (TextUtils.isEmpty(traceUrl)) {
            ToastUtil.show(R.string.draw_info_error);
        } else {
            progressDialogUtil.showLoadingDelay(mContext);
            ServerSnapShotUtil.downLoadSnapShot(playInfo.getTraceUrl(), new ServerSnapShotLifeCallback(this) {
                @Override
                public void onFinish(List<DrawInfo> drawInfos) {
                    progressDialogUtil.hideLoading();
                    playInfo.setDrawInfoList(drawInfos);
                    playToolView.refreshPlay(playInfo);
                }
            });
        }
    }

    //清除所有信息, 游戏结束、下一轮画画
    public void clearTotal() {
        DrawUtil.print(TAG, "clearTotal");
        drawActionView.clearAll();
        drawPathSurView.clearAll(new FinishCallback() {
            @Override
            public void onFinish() {
                drawUploadUtil.clearSeq();
            }
        });
        drawToolView.clearAll();
    }

    public int getNextLocalSeq() {
        return getDrawPathMaxSeq() + 1;
    }

    public int getDrawPathMaxSeq() {
        return drawPathSurView.getMaxSeq();
    }

    public void setCallback(DrawMainCallback callback) {
        this.callback = callback;
    }

    public void addTraceList(String snapShot, final List<DrawInfo> drawInfoList) {
        if (!TextUtils.isEmpty(snapShot)) {
            ServerSnapShotUtil.downLoadSnapShot(snapShot, new ServerSnapShotLifeCallback(this) {
                @Override
                public void onFinish(List<DrawInfo> drawInfos) {
                    drawInfos.addAll(drawInfoList);
                    drawPathSurView.submitTraceListInMain(drawInfos);
                    DrawUtil.print(TAG, "addTraceList size = " + drawInfos.size() + " seq = " + DrawUtil.getMaxSeq(drawInfos) + " size = " + drawInfoList.size() + " seq = " + DrawUtil.getMaxSeq(drawInfoList));
                }
            });
        } else {
            drawPathSurView.submitTraceListInMain(drawInfoList);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        clearTotal();
    }

    public DrawPathView getDrawPathSurView() {
        return drawPathSurView;
    }

    public void setDrawEnable(boolean enable) {
        if (enable) {
            drawActionView.setVisibility(VISIBLE);
        } else {
            drawActionView.setVisibility(GONE);
        }
    }

    @MainThread
    public Bitmap getDrawBitmap() {
        return drawPathSurView.getDrawBitmap();
    }

    public void getDrawBitmap(final DrawBitmapCallback callback) {
        drawPathSurView.getDrawBitmap(callback);
    }

    public void refreshUndoAndRedo() {
        drawToolView.setUndoEnable(drawPathSurView.canUndo());
        drawToolView.setRedoEnable(drawPathSurView.canRedo());
    }

    public void refreshDrawBoard(int propId) {
        PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(propId);
        if (propItem != null) {
            WpImageLoader.load(propItem.getMediaUrl(), null, ImageLoadInfo.newInfo().owner(drawPathSurView), new WpSimpleImageLoadListener() {
                @Override
                public boolean onComplete(String uri, Drawable data) {
                    drawPathSurView.setBackgroundBitmap(BitmapUtil.getBitmapFromDrawable(data));
                    return super.onComplete(uri, data);
                }
            });
            drawToolView.setDrawBoardId(propId);
        } else {
            drawPathSurView.setBackgroundBitmap(null);
        }
    }

    public void destroy() {
        drawPathSurView.destroy();
    }

    public void onResume() {
        drawPathSurView.onResume();
        DrawUtil.print(TAG, "onResume");
    }
}