package com.wepie.wespy.module.notify;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.Shader;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.wepie.wespy.R;
import com.huiwan.base.util.log.TimeLogger;

/**
 * date 2019-08-13
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class NotifyBackgroundDrawable extends Drawable {
    private Paint paint = new Paint();
    private Shader shader;
    private float flushPosPercent = 0;
    private Drawable flushDrawable;
    private boolean needFlush = false;

    NotifyBackgroundDrawable(Context context) {
        flushDrawable = ContextCompat.getDrawable(context, R.drawable.light_yellow);
        if (flushDrawable != null) {
            flushDrawable.setBounds(0, 0, flushDrawable.getIntrinsicWidth(), flushDrawable.getIntrinsicHeight());
        }
    }

    void flush() {
        flushPosPercent = 1;
        needFlush = true;
        invalidateSelf();
    }

    @Override
    protected void onBoundsChange(Rect bounds) {
        shader = new LinearGradient(0, 0, bounds.width(), 0, new int[]{0xffffd439, 0xfff9a837, 0xffff9837}, null, Shader.TileMode.CLAMP);
    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        paint.setShader(shader);
        canvas.drawRect(getBounds(), paint);
        if (flushDrawable != null && needFlush) {
            canvas.save();
            float flushPos = flushPosPercent * getBounds().width();
            canvas.translate(flushPos, 0);
            flushDrawable.draw(canvas);
            canvas.restore();
            flushPosPercent -= 0.015f;
            if (flushPosPercent > -1) {
                TimeLogger.msg("flush draw: " + flushPosPercent + "\t " + flushPos);
                invalidateSelf();
            } else {
                needFlush = false;
                TimeLogger.msg("flush draw no invalidate: " + flushPosPercent + "\t " + flushPos);
            }
        }
    }

    @Override
    public void setAlpha(int alpha) {
        paint.setAlpha(alpha);
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {
        paint.setColorFilter(colorFilter);
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSPARENT;
    }
}
