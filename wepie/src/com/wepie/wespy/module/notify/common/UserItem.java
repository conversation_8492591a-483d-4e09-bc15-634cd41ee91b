package com.wepie.wespy.module.notify.common;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.core.content.ContextCompat;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.SingleClickListener;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.decorate.CharmManager;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomTextView;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.notify.CommonNotifyInfo;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.lang.ref.SoftReference;

/**
 * date 2020-02-05
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class UserItem implements ViewItem {
    private int uid;
    private int textColor;
    private int bgColor;

    @Override
    public int getType() {
        return CommonNotifyInfo.TYPE_USER;
    }

    @Override
    public View genView(final Context context) {
        final LinearLayout linearLayout = new LinearLayout(context);
        linearLayout.setOrientation(LinearLayout.HORIZONTAL);
        linearLayout.setGravity(Gravity.CENTER_VERTICAL);
        linearLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ScreenUtil.dip2px(24)));
        UserService.get().getCacheSimpleUser(uid, null);
        linearLayout.addOnAttachStateChangeListener(new OnAttachStateChangeListener(this));

        if (bgColor != Color.TRANSPARENT) {
            linearLayout.setBackground(genBg(bgColor));
            linearLayout.setPaddingRelative(ScreenUtil.dip2px(4), 0, ScreenUtil.dip2px(4), 0);
        } else {
            linearLayout.setPaddingRelative(ScreenUtil.dip2px(3), 0, 0, 0);
        }
        linearLayout.setOnClickListener(new SingleClickListener() {

            @Override
            public void onClickInternal(@NotNull View v) {
                JumpUtil.enterUserInfoDetailActivity(v.getContext(), uid, TrackSource.APP_ALL_NOTIFY);
            }
        });
        return linearLayout;
    }

    private void loadUser(ViewGroup view) {
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(view) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                updateUserInfo(view.getContext(), simpleInfo, view);
            }

            @Override
            public void onUserInfoFailed(String description) {
                TimeLogger.err("error get user info: " + uid + " " + description);
            }
        });
    }

    private void updateUserInfo(Context context, UserSimpleInfo simpleInfo, ViewGroup layout) {
        Drawable charmDrawable = CharmManager.getShortDrawable(simpleInfo.flower);
        if (charmDrawable != null) {
            ImageView charmIv = new AppCompatImageView(context);
            charmIv.setImageDrawable(charmDrawable);
            LinearLayout.LayoutParams charmLp = new LinearLayout.LayoutParams(ScreenUtil.dip2px(34), ScreenUtil.dip2px(18));
            charmLp.setMarginEnd(ScreenUtil.dip2px(3));
            charmLp.bottomMargin = ScreenUtil.dip2px(1);
            layout.addView(charmIv, charmLp);
        }

        Drawable headDrawable = ContextCompat.getDrawable(context, R.drawable.circle_fffe54_stroke);
        if (headDrawable != null) {
            headDrawable.mutate();

            headDrawable.setColorFilter(textColor, PorterDuff.Mode.SRC_ATOP);
        }
        FrameLayout frameLayout = new FrameLayout(context);
        LinearLayout.LayoutParams frameParams = new LinearLayout.LayoutParams(ScreenUtil.dip2px(20), ScreenUtil.dip2px(20));
        if (ScreenUtil.isRtl()) {
            frameParams.leftMargin = ScreenUtil.dip2px(6);
        } else {
            frameParams.rightMargin = ScreenUtil.dip2px(6);
        }
        layout.addView(frameLayout, frameParams);
        DecorHeadImgView headImgView = new DecorHeadImgView(context);
        headImgView.showUserHead(simpleInfo.uid);
        frameLayout.addView(headImgView, LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        ImageView imageView = new ImageView(context);
        imageView.setImageDrawable(headDrawable);
        frameLayout.addView(imageView, LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);

        TextView nameTv = new CustomTextView(context);
        LinearLayout.LayoutParams nameLp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
        nameTv.setGravity(Gravity.CENTER_VERTICAL);
        nameTv.setTextColor(textColor);
        nameTv.setText(simpleInfo.getRemarkName());
        nameTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
        nameTv.setMaxWidth(ScreenUtil.dip2px(72));
        nameTv.setSingleLine(true);
        nameTv.setEllipsize(TextUtils.TruncateAt.END);
        layout.addView(nameTv, nameLp);
    }


    private Drawable genBg(int color) {
        float radius = ScreenUtil.dip2px(14);
        float[] radiusR = new float[]{radius, radius, radius, radius, radius, radius, radius, radius};
        RoundRectShape shape = new RoundRectShape(radiusR, null, null);
        ShapeDrawable shapeDrawable = new ShapeDrawable(shape);
        shapeDrawable.getPaint().setColor(color);
        return shapeDrawable;
    }

    public static UserItem from(JSONObject jo) {
        UserItem user = new UserItem();
        user.uid = jo.optInt("uid");
        user.textColor = jo.optInt("text_color");
        user.bgColor = jo.optInt("bg_color");
        return user;
    }

    private static class OnAttachStateChangeListener implements View.OnAttachStateChangeListener {
        private final SoftReference<UserItem> reference;

        public OnAttachStateChangeListener(UserItem item) {
            reference = new SoftReference<>(item);
        }

        @Override
        public void onViewAttachedToWindow(@NonNull View v) {
            UserItem item = reference.get();
            if (item == null) {
                return;
            }
            if (v instanceof ViewGroup) {
                item.loadUser((ViewGroup) v);
            }
        }

        @Override
        public void onViewDetachedFromWindow(@NonNull View v) {

        }
    }
}
