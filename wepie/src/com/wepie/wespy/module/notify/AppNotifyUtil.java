package com.wepie.wespy.module.notify;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.lib.api.plugins.IAppNotify;
import com.huiwan.store.PrefUtil;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.cocosnew.match.create.CocosCreateRoomActivity;
import com.wepie.wespy.cocosnew.match.matching.CocosMatchActivity;
import com.wepie.wespy.cocosnew.match.prepare.CocosMatchPrepareActivity;
import com.wepie.wespy.helper.ContentViewHelper;
import com.wepie.wespy.module.advertisement.AdvertisementActivity;
import com.wepie.wespy.module.fdiscover.broad.send.SendActivity;
import com.wepie.wespy.module.fdiscover.send.MultiSelectSendActivity;

import java.lang.ref.SoftReference;
import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingDeque;

import kotlin.Unit;

/**
 * date 2019-08-13
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class AppNotifyUtil implements Runnable, IAppNotify {
    private static final String TAG = "AppNotifyUtil";

    private static final AppNotifyUtil util = new AppNotifyUtil();
    private final Queue<BaseNotifyInfo<?>> notifyInfoQueue = new LinkedBlockingDeque<>();
    private final Handler handler = new Handler(Looper.getMainLooper());
    private SoftReference<ShowingItem<?>> showingRef = null;
    private boolean resumeZeroFlag = false;
    private final List<Class<?>> classList = Arrays.asList(
//            FixRoomActivity.class,
            SendActivity.class,
            MultiSelectSendActivity.class,
            AdvertisementActivity.class,
//            BalootActivity.class,
//            PartySpyEditActivity.class,
//            PartyRoomActivity.class,
            CocosMatchActivity.class,
            CocosMatchPrepareActivity.class,
            CocosCreateRoomActivity.class
    );

    static {
        ActivityTaskManager.getInstance().registerActivityTaskListener(new ActivityTaskManager.ActivityTaskListener() {
            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                super.onActivityStopped(activity);
                if (activity.isFinishing()) {
                    util.clearShowingInActivity(activity);
                }
            }
        });
    }

    public static IAppNotify getNotifyApiInst() {
        return util;
    }

    public static void notifyRingGift(RingGiftInfo gainRingInfo) {
        util.notify(new GainRingNotifyInfo(gainRingInfo));
    }

    public static void notifyRingFamilyWheel(RingFamilyRingInfo familyWheelInfo) {
        util.notify(new RingFamilyWheelNotifyInfo(familyWheelInfo));
    }

    public static void notifyAppGift(AppGiftInfo appGiftInfo) {
        util.notify(new AppGiftNotifyInfo(appGiftInfo));
    }

    public static void notifyVipUpGrade(VipUpgradeInfo vipUpgradeInfo) {
        util.notify(new VipUpgradeNotifyInfo(vipUpgradeInfo));
    }

    public static void notifyAllUser(CommonNotifyInfo commonNotifyInfo) {
        util.notify(new AllNotifyInfo(commonNotifyInfo));
    }

    public static void changeVisibilityIfExist(boolean visible) {
        if (util.showingRef != null) {
            ShowingItem<?> item = util.showingRef.get();
            if (item != null && item.isShowing) {
                item.hideOrShowOnly(visible);
            }
        }
    }

    private void notify(BaseNotifyInfo<?> notifyInfo) {
        notifyInfoQueue.offer(notifyInfo);
        HLog.d(TAG,"offer and queue size: " + notifyInfoQueue.size());
        if (showingRef != null) {
            ShowingItem<?> item = showingRef.get();
            if (item != null && item.isShowing) {
                HLog.d(TAG,"itemShowing");
                return;
            }
        }
        if (resumeZeroFlag) {
            return;
        }
        schedule();
    }

    private void schedule() {
        BaseNotifyInfo<?> info = notifyInfoQueue.poll();
        if (info == null) {
            return;
        }
        TimeLogger.msgNoStack("schedule item");
        if (!(info.data instanceof CommonNotifyInfo)) {
            check2show(info);
            return;
        }
        CommonNotifyInfo commonNotifyInfo = (CommonNotifyInfo) info.data;
        List<String> preferLangList = commonNotifyInfo.getPreferLangList();
        if (preferLangList.isEmpty() || preferLangList.contains(PrefUtil.getInstance().getString(PrefUtil.PREFER_LANG, ""))) {
            if (commonNotifyInfo.getNoticeType() != CommonNotifyInfo.NOTICE_TYPE_RED_PACKET) {
                check2show(info);
            } else {
                // 因为发送者的发送红包页面会finish导致弹幕不出现，所以红包弹幕需要延迟一会出现
                handler.postDelayed(() -> check2show(info), 200L);
            }
        }
    }

    @SuppressWarnings("unchecked")
    private void updateOrSchedule(ShowingItem item) {
        BaseNotifyInfo<?> info = notifyInfoQueue.poll();
        if (info != null) {
            Activity topActivity = ActivityTaskManager.getInstance().getTopActivity();
            if (item.view.getContext() == topActivity && item.view.shouldUpdate(info.data)) {
                item.view.update(info.data, true);
                handler.removeCallbacks(this);
                handler.postDelayed(this, info.showTime);
                TimeLogger.msgNoStack("update item: " + info.data);
            } else {
                TimeLogger.msgNoStack("show new item: " + info.data);
                item.hideAndRemove();
                check2show(info);
            }
        } else {
            item.hideAndRemove();
            TimeLogger.msgNoStack("no floating info");
        }
    }

    private void check2show(BaseNotifyInfo<?> info) {
        handler.removeCallbacks(this);
        handler.postDelayed(this, info.showTime);

        Activity topActivity = ActivityTaskManager.getInstance().getTopActivity();
        if (topActivity == null || ActivityTaskManager.isBackground()) {
            resumeZeroFlag = true;
            TimeLogger.msgNoStack("no activity resumed no showing， queue size: " + notifyInfoQueue.size());
            return;
        }
        if (noShowActivity(topActivity)) {
            clearAll();
            return;
        }
        if (topActivity instanceof IAppNotify.INotifyPage) {
            ((IAppNotify.INotifyPage) topActivity).canAppNotify(canNotify -> {
                if (canNotify && !topActivity.isFinishing()) {
                    showNotify(topActivity, info);
                } else {
                    clearAll();
                    HLog.d(TAG, HLog.USR, "activityCannotNotify");
                }
                return Unit.INSTANCE;
            });
        } else {
            showNotify(topActivity, info);
        }
    }

    @Override
    public void clearAll() {
        resumeZeroFlag = false;
        handler.removeCallbacks(this);
        notifyInfoQueue.clear();
        if (showingRef != null) {
            showingRef.clear();
        }
        ContentViewHelper.removeAllTopNotifyView();
    }


    @Override
    public void run() {
        resumeZeroFlag = false;
        if (showingRef != null) {
            ShowingItem<?> item = showingRef.get();
            if (item != null) {
                updateOrSchedule(item);
            } else {
                TimeLogger.msgNoStack("item null schedule");
                schedule();
            }
        } else {
            TimeLogger.msgNoStack("ref null schedule");
            schedule();
        }
    }

    private boolean noShowActivity(Activity activity) {
        Class<?> c = activity.getClass();
        for (Class<?> cItem : classList) {
            if (c.isAssignableFrom(cItem)) {
                return true;
            }
        }
        return false;
    }

    private void showNotify(Activity topActivity, BaseNotifyInfo<?> info) {
        HLog.d(TAG, "showNotify, queueSize={}", notifyInfoQueue.size());
        ContentViewHelper contentViewHelper;
        try {
            contentViewHelper = new ContentViewHelper(topActivity);
        } catch (Exception e) {
            FLog.e(e);
            return;
        }
        int h = 0;
        if (StatusBarUtil.isSupportFullScreen()) {
            h = ScreenUtil.getStatusBarHeight();
        }
        int x = 0;
        int y;
        y = ScreenUtil.dip2px(info.topMargin) + h;

        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT);
        lp.setMarginStart(x);
        lp.topMargin = y;
        ShowingItem<?> item = info.updateAndShowView(topActivity, contentViewHelper, lp);
        if (item == null) {
            showingRef = null;
        } else {
            showingRef = new SoftReference<>(item);
        }
    }

    private void clearShowingInActivity(Activity activity) {
        if (showingRef != null) {
            ShowingItem<?> item = showingRef.get();
            if (item != null && item.isShowing && item.inActivity(activity)) {
                item.hideAndRemove();
                showingRef = null;
                TimeLogger.msgNoStack("clear showing ref in top activity");
            }
        }
    }

    static abstract class BaseNotifyInfo<T> {
        final Class<? extends BaseNotifyView<T>> c;
        final T data;
        final long showTime;
        final int topMargin;

        BaseNotifyInfo(Class<? extends BaseNotifyView<T>> c, T data, long showTime) {
            this(c, data, showTime, 22);
        }

        BaseNotifyInfo(Class<? extends BaseNotifyView<T>> c, T data, long showTime, int topMargin) {
            this.c = c;
            this.data = data;
            this.showTime = showTime;
            this.topMargin = topMargin;
        }

        ShowingItem<?> updateAndShowView(Context context, ContentViewHelper helper, FrameLayout.LayoutParams lp) {
            try {
                Constructor<? extends BaseNotifyView<T>> constructor = c.getConstructor(Context.class);
                BaseNotifyView<T> view = constructor.newInstance(context);
                ShowingItem<T> showingItem = new ShowingItem<>(helper, view);
                view.update(data, false);
                // for weak ref not released
                view.setFloatingItem(showingItem);
                helper.showInContent(view, lp);
                view.touchListener(showingItem);
                return showingItem;
            } catch (Exception e) {
                TimeLogger.err("error show notify view");
            }
            return null;
        }

        @NonNull
        @Override
        public String toString() {
            return String.format(LibBaseUtil.getLocale(), "%s, %s", c.getSimpleName(), data);
        }
    }

    static class VipUpgradeNotifyInfo extends BaseNotifyInfo<VipUpgradeInfo> {
        VipUpgradeNotifyInfo(VipUpgradeInfo upgradeInfo) {
            super(VipUpgradeNotifyView.class, upgradeInfo, 8000);
        }
    }

    static class AllNotifyInfo extends BaseNotifyInfo<CommonNotifyInfo> {
        AllNotifyInfo(CommonNotifyInfo notifyInfo) {
            super(AppCommonNotifyView.class, notifyInfo, notifyInfo.getDurationInMill());
        }
    }

    static class GainRingNotifyInfo extends BaseNotifyInfo<RingGiftInfo> {
        GainRingNotifyInfo(RingGiftInfo notifyInfo) {
            super(RingGiftNotifyView.class, notifyInfo, 5000);
        }
    }

    static class RingFamilyWheelNotifyInfo extends BaseNotifyInfo<RingFamilyRingInfo> {
        RingFamilyWheelNotifyInfo(RingFamilyRingInfo ringFamilyRingInfo) {
            super(RingFamilyRingNotifyView.class, ringFamilyRingInfo, 5000);
        }
    }

    static class AppGiftNotifyInfo extends BaseNotifyInfo<AppGiftInfo> {
        AppGiftNotifyInfo(AppGiftInfo appGiftInfo) {
            super(AppGiftNotifyView.class, appGiftInfo, 8000);
        }
    }

    static class ShowingItem<T> {
        ContentViewHelper floatingHelper;
        BaseNotifyView<T> view;
        boolean isShowing = true;

        ShowingItem(ContentViewHelper floatingHelper, BaseNotifyView<T> view) {
            this.floatingHelper = floatingHelper;
            this.view = view;
        }

        void hideAndRemove() {
            floatingHelper.hide(view);
            isShowing = false;
        }

        void hideOrShowOnly(boolean show) {
            view.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
        }

        boolean inActivity(Activity activity) {
            return ContextUtil.getActivityFromContext(view.getContext()) == activity;
        }

        public void clear() {
            if (floatingHelper != null) floatingHelper.clear();
        }
    }
}
