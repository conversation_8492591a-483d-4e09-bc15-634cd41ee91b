package com.wepie.wespy.module.vip.open;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;

/**
 * Created by bigwen on 2019-07-23.
 */
@SuppressLint("SetTextI18n")
public class OpenVipGridItem extends FrameLayout {

    private Context mContext;
    private TextView priceTv, nameTv, currencyTv;
    private LinearLayout itemLay;
    private TextView originalPriceTv, monthPriceTv;
    private RelativeLayout originalPriceLay;
    private ImageView discountIv;
    private Typeface numberTypeface;

    public OpenVipGridItem(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public OpenVipGridItem(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.vip_grid_item, this);
        currencyTv = findViewById(R.id.currency_tv);
        priceTv = findViewById(R.id.price_tv);
        nameTv = findViewById(R.id.name_tv);
        itemLay = findViewById(R.id.item_lay);
        discountIv = findViewById(R.id.discount_iv);
        originalPriceLay = findViewById(R.id.original_price_lay);
        originalPriceTv = findViewById(R.id.original_price_tv);
        monthPriceTv = findViewById(R.id.month_price_tv);
        priceTv.setTypeface(numberTypeface);
    }

    public void refresh(OpenVipInterface openVipInterface, int selectPosition, int itemPosition) {
        currencyTv.setText(openVipInterface.getCurrency());
        if (openVipInterface.getCurrency() != null && openVipInterface.getCurrency().length() > 2) {
            currencyTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10);
        }
        priceTv.setText(openVipInterface.getGoodsPrice() + "");
        nameTv.setText(openVipInterface.getGoodsName());
        itemLay.setSelected(selectPosition == itemPosition);

        if (openVipInterface.getDiscountRate() > 0) {
            originalPriceLay.setVisibility(VISIBLE);
//            monthPriceTv.setVisibility(VISIBLE);
            discountIv.setVisibility(VISIBLE);
            CharSequence currency = openVipInterface.getCurrency();
            originalPriceTv.setText(ResUtil.getStr(R.string.vip_grid_item_was, currency, openVipInterface.getOriginalPrice()));
            if (openVipInterface.getMonthGoodsPrice() > 0) {
//                String price = StringUtil.formatInteger(openVipInterface.getMonthGoodsPrice());
//                String str = ResUtil.getResource().getString(R.string.only_per_month, currency, price);
//                monthPriceTv.setText(str);
            } else {
//                monthPriceTv.setVisibility(INVISIBLE);
            }
            ImageLoaderUtil.loadNormalImage(openVipInterface.getDiscountIcon(), discountIv);
        } else {
            originalPriceLay.setVisibility(GONE);
//            monthPriceTv.setVisibility(GONE);
            discountIv.setVisibility(GONE);
        }

    }
}