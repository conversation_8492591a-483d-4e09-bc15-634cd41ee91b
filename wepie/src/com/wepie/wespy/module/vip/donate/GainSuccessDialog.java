package com.wepie.wespy.module.vip.donate;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.opensource.svgaplayer.SVGAImageView;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.huiwan.anim.SVGAUtil;

/**
 * Created by <PERSON>wen on 2019-08-29.
 */
public class GainSuccessDialog extends FrameLayout {

    private Context mContext;
    private GainSuccessCallback callback;
    private TextView contentTv, nextTv;
    private SVGAImageView svgaImageView;

    public GainSuccessDialog(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public GainSuccessDialog(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.gain_success_dialog, this);
        contentTv = findViewById(R.id.content_tv);
        nextTv = findViewById(R.id.next_tv);
        svgaImageView = findViewById(R.id.svga_view);

        nextTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (callback != null) callback.onEnter();
            }
        });
    }

    public void setContent(String content) {
        contentTv.setText(content);
    }

    public void setCallback(GainSuccessCallback callback) {
        this.callback = callback;
    }

    public static void showDialog(Context context, String content, boolean showAnim, final GainSuccessCallback callback) {
        GainSuccessDialog vipDetailMainDialog =  new GainSuccessDialog(context);
        vipDetailMainDialog.setContent(content);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_background_80);
        vipDetailMainDialog.setCallback(new GainSuccessCallback() {
            @Override
            public void onEnter() {
                dialog.dismiss();
                if (callback != null) callback.onEnter();
            }
        });
        dialog.setContentView(vipDetailMainDialog);
        dialog.setCanceledOnTouchOutside(false);
        dialog.initFullWidth();
        dialog.show();
        if (showAnim) {
            vipDetailMainDialog.playAnim();
        }
    }

    private void playAnim() {
        SVGAUtil.playSvga("svga/open_vip_success.svga", svgaImageView);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        svgaImageView.clearAnimation();
    }

    public interface GainSuccessCallback {
        void onEnter();
    }
}