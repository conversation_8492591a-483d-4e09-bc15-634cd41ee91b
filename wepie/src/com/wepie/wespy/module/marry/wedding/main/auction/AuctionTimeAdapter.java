package com.wepie.wespy.module.marry.wedding.main.auction;

import android.content.Context;
import androidx.annotation.Nullable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import com.wepie.wespy.model.entity.marry.WeddingTime;

import java.util.ArrayList;
import java.util.List;

// Created by bigwen on 2019/1/11.
public class AuctionTimeAdapter extends BaseAdapter {

    private Context mContext;
    private List<WeddingTime> weddingTime = new ArrayList<>();
    private int selectPosition;

    AuctionTimeAdapter(Context mContext) {
        this.mContext = mContext;
    }

    void refreshWeddingTime(List<WeddingTime> weddingTime2) {
        this.weddingTime.clear();
        this.weddingTime.addAll(weddingTime2);
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return weddingTime.size();
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = new AuctionTimeItemView(mContext);
        }
        AuctionTimeItemView auctionItemView = (AuctionTimeItemView) convertView;
        auctionItemView.refresh(weddingTime.get(position), selectPosition == position);
        auctionItemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectPosition = position;
                notifyDataSetChanged();
            }
        });
        return auctionItemView;
    }

    @Nullable
    public WeddingTime getSelectData() {
        if (selectPosition < weddingTime.size()) {
            return weddingTime.get(selectPosition);
        } else {
            return null;
        }
    }
}
