package com.wepie.wespy.module.marry.wedding.main;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.user.LifeUserInfoLoadCallback;
import com.wepie.wespy.R;
import com.huiwan.configservice.ConfigHelper;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.huiwan.user.entity.User;
import com.wepie.wespy.model.entity.marry.AuctionRoom;
import com.wepie.wespy.model.entity.marry.WeddingDay;
import com.huiwan.user.UserService;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.module.marry.wedding.edit.time.WeddingDaySelectCallback;
import com.wepie.wespy.module.marry.wedding.edit.time.WeddingDurationAdapter;
import com.wepie.wespy.module.marry.wedding.edit.time.WeddingDurationCallback;
import com.wepie.wespy.module.marry.wedding.edit.time.WeddingSelectDayView;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.wespy.net.tcp.sender.MarryPacketSender;
import com.huiwan.base.util.ToastUtil;

import java.util.List;

// Created by bigwen on 2019/1/8.

public class AppointTimeView extends FrameLayout {

    private Context mContext;
    private long startTimeSec;
    private ProgressDialogUtil progressDialogUtil = new ProgressDialogUtil();
    private AppointTimeCallback callback;
    private AppointTimePresenter presenter;
    private WeddingDurationAdapter durationAdapter;
    private WeddingSelectDayView dayView;

    public AppointTimeView(@NonNull Context context) {
        super(context);
        mContext = context;
        init();
    }

    public AppointTimeView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.wedding_card_time_activity, this);
        BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        ListView listView = findViewById(R.id.list_view);
        TextView refreshTimeTv = findViewById(R.id.refresh_time_tv);
        dayView = findViewById(R.id.select_day_view);
        durationAdapter = new WeddingDurationAdapter(mContext);
        listView.setAdapter(durationAdapter);

        actionBar.addTitleRightTextWithBack(R.string.schedule_wedding, ResUtil.getStr(R.string.confirm), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (callback != null) callback.onBack();
            }
        }, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                appointTime();
            }
        });

        refreshTimeTv.setText(ConfigHelper.getInstance().marryRefreshTip());

        presenter = new AppointTimePresenter(this);
    }

    public void refreshTimeList() {
        presenter.refreshTimeList();
        presenter.refreshAuction();
    }

    private void appointTime() {
        if (startTimeSec <= 0) {
            ToastUtil.show(R.string.activity_send_select_time_tip);
            return;
        }
        UserService.get().getCacheUserFromServer(LoginHelper.getLoginUid(), new LifeUserInfoLoadCallback(this) {
            @Override
            public void onUserInfoSuccess(User userInfo) {
                if (userInfo.getMateUid() != 0) {
                    UserService.get().getCacheUserFromServer(userInfo.getMateUid(), new LifeUserInfoLoadCallback(AppointTimeView.this) {
                        @Override
                        public void onUserInfoSuccess(User userInfo) {
                            String content = "";
                            String sureBtn = "";
                            if (userInfo.isBan()) {
                                content = ResUtil.getString(R.string.wedding_time_introduction);
                                sureBtn = ResUtil.getStr(R.string.schedule_anyway);
                            } else {
                                content = ResUtil.getStr(R.string.wedding_time_confirm_tip);
                                sureBtn = ResUtil.getStr(R.string.confirm);
                            }

                            doAppointTime(content, sureBtn);
                        }

                        @Override
                        public void onUserInfoFailed(String description) {
                            ToastUtil.show(description);
                        }
                    });
                } else {
                    ToastUtil.show(R.string.not_get_cp_info);
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
                ToastUtil.show(description);
            }
        });
    }

    private void doAppointTime(String content, String sureBtn) {
        DialogBuild.newBuilder(mContext).setSingleBtn(false).setTitle(null).setContent(content).setSureTx(sureBtn).setCanCancel(false).setDialogCallback(new DialogBuild.DialogCallback() {
            @Override
            public void onClickSure() {
                progressDialogUtil.showLoadingDelay(mContext);
                MarryPacketSender.appointWeddingReq(startTimeSec, new LifeSeqCallback(AppointTimeView.this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        progressDialogUtil.hideLoading();
                        ToastUtil.show(R.string.schedule_success);
                        if (callback != null) callback.refreshData();
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        progressDialogUtil.hideLoading();
                        ToastUtil.show(head.desc);
                    }
                });
            }

            @Override
            public void onClickCancel() {

            }
        }).show();
    }

    void updateView(List<WeddingDay> weddingDay2List) {
        dayView.refresh(weddingDay2List);
        dayView.setCallback(new WeddingDaySelectCallback() {
            @Override
            public void onSelect(WeddingDay broadDayInfo) {
                durationAdapter.refresh(broadDayInfo.getWeddingTimeList());
                startTimeSec = durationAdapter.getSelectTimeSec();
            }
        });
        durationAdapter.refresh(weddingDay2List.get(0).getWeddingTimeList());
        durationAdapter.setCallback(new WeddingDurationCallback() {
            @Override
            public void onSelect(int positon) {
                startTimeSec = durationAdapter.getSelectTimeSec();
            }
        });
        dayView.setDefaultSelect();
    }

    void refreshAuctionTips(List<AuctionRoom> auctionRoom2List) {
        dayView.refreshAuctionTips(auctionRoom2List);
    }

    public void setCallback(AppointTimeCallback callback) {
        this.callback = callback;
    }
}