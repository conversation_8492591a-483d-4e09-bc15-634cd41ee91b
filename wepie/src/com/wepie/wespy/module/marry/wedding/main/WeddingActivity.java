package com.wepie.wespy.module.marry.wedding.main;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;

import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.ActivityResultCode;
import com.huiwan.constants.IntentConfig;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.model.entity.marry.InvitationInfo;
import com.wepie.wespy.model.entity.marry.MarryInfo;
import com.wepie.wespy.model.event.other.MarryRefreshEvent;
import com.wepie.wespy.module.marry.propose.main.ProposeSingleViewNew;
import com.wepie.wespy.module.marry.wedding.main.auction.AuctionTimeView;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

// Created by bigwen on 2018/12/25.
public class WeddingActivity extends BaseActivity {

    private WeddingPresenter presenter;
    private CreateWeddingView weddingView;
    private ProposeSingleViewNew singleViewNew;
    private AppointTimeView appointTimeView;
    private AuctionTimeView auctionTimeView;
    private ProgressDialogUtil progressDialogUtil = new ProgressDialogUtil();
    private boolean isFirstLaunch = true;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wedding_new);
        weddingView = findViewById(R.id.wedding_view);
        appointTimeView = findViewById(R.id.appoint_time_view);
        singleViewNew = findViewById(R.id.single_view);
        auctionTimeView = findViewById(R.id.auction_time_view);
        BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        actionBar.addTitleAndBack(R.string.wedding);
        presenter = new WeddingPresenter(this);

        InvitationInfo invitationInfo2 = (InvitationInfo) getIntent().getSerializableExtra(IntentConfig.INVITATION_DATA);
        MarryInfo marryInfo2 = (MarryInfo) getIntent().getSerializableExtra(IntentConfig.MARRY_DATA);
        refreshViewFromLocal(invitationInfo2, marryInfo2);

        presenter.getMarryInfo();
        presenter.clearAcceptRedDot();
    }

    @Override
    protected void onResume() {
        super.onResume();
        //被要求多刷新
        if (appointTimeView.getVisibility() == View.VISIBLE && !isFirstLaunch) {
            appointTimeView.refreshTimeList();
        }

        if (isFirstLaunch) {
            isFirstLaunch = false;
        }
    }

    void refreshViewFromLocal(InvitationInfo invitationInfo2, MarryInfo marryInfo2) {
        if (marryInfo2 != null) {
            if (marryInfo2.isEmptyMarry()) {
                showSingle();
            } else {
                if (invitationInfo2 != null) {
                    refreshInvitation(invitationInfo2, true);
                }
            }
        }
    }

    void refreshMarryState(MarryInfo marryInfo2) {
        if (marryInfo2.isEmptyMarry()) {
            showSingle();
        } else {
            presenter.getInvitation(marryInfo2);
        }
    }

    void refreshInvitation(InvitationInfo invitationInfo2, boolean localRefresh) {
        if (invitationInfo2.isEmptyData()) {
            showAppointTime(localRefresh);
        } else {
            int state = invitationInfo2.getInvitationState();
            if (state == InvitationInfo.INVITATION_INIT) {
                showAuctionChooseTime(invitationInfo2, localRefresh);
            } else if (state == InvitationInfo.INVITATION_TIME_SET) {
                showCreateWedding(invitationInfo2);
            } else if (state == InvitationInfo.INVITATION_CREATED) {
                showCreateWedding(invitationInfo2);
            }
        }
    }

    private void showSingle() {
        singleViewNew.setVisibility(View.VISIBLE);
        weddingView.setVisibility(View.GONE);
        appointTimeView.setVisibility(View.GONE);
        auctionTimeView.setVisibility(View.GONE);
        singleViewNew.showWeddingSingle();
    }

    private void showCreateWedding(InvitationInfo invitationInfo2) {
        singleViewNew.setVisibility(View.GONE);
        weddingView.setVisibility(View.VISIBLE);
        appointTimeView.setVisibility(View.GONE);
        auctionTimeView.setVisibility(View.GONE);
        weddingView.refresh(invitationInfo2);
    }

    private void showAppointTime(boolean localRefresh) {
        appointTimeView.setVisibility(View.VISIBLE);
        singleViewNew.setVisibility(View.GONE);
        weddingView.setVisibility(View.GONE);
        auctionTimeView.setVisibility(View.GONE);
        if (!localRefresh) {//本地刷新的时候，不刷接口，现在会show两次（本地数据一次，服务器数据一次），防止接口多次请求
            appointTimeView.refreshTimeList();
        }
        appointTimeView.setCallback(new AppointTimeCallback() {
            @Override
            public void onBack() {
                finish();
            }

            @Override
            public void refreshData() {
                presenter.getMarryInfo();
                //预订后刷新一次教堂列表
                EventDispatcher.postMarryEvent(MarryRefreshEvent.TYPE_APPOINT_WEDDING);
            }
        });
    }

    private void showAuctionChooseTime(InvitationInfo invitationInfo2, boolean localRefresh) {
        appointTimeView.setVisibility(View.GONE);
        singleViewNew.setVisibility(View.GONE);
        weddingView.setVisibility(View.GONE);
        auctionTimeView.setVisibility(View.VISIBLE);
        if (!localRefresh) {
            auctionTimeView.refresh(invitationInfo2);
        }
        auctionTimeView.setCallback(new AppointTimeCallback() {
            @Override
            public void onBack() {
                finish();
            }

            @Override
            public void refreshData() {
                presenter.getMarryInfo();
                //预订后刷新一次教堂列表
                EventDispatcher.postMarryEvent(MarryRefreshEvent.TYPE_APPOINT_WEDDING);
            }
        });
    }

    void showProcessDialog() {
        progressDialogUtil.showLoadingDelay(this);
    }

    void hideProcessDialog() {
        progressDialogUtil.hideLoading();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == ActivityResultCode.REQUEST_CODE_CREATE_INVITATION && resultCode == Activity.RESULT_OK && data != null) {
            if (data.getBooleanExtra(IntentConfig.UPDATE, false)) {
                presenter.getMarryInfo();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        presenter.clear();
    }
}
