package com.wepie.wespy.module.marry.wedding.edit.auction;

import android.app.Activity;
import android.os.Bundle;
import androidx.annotation.Nullable;

import android.widget.ImageView;
import android.widget.ListView;

import com.huiwan.base.str.ResUtil;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.model.entity.marry.AuctionRoom;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.tcp.callback.marry.AuctionLifeCallback;
import com.wepie.wespy.net.tcp.sender.MarryPacketSender;
import com.huiwan.base.util.ToastUtil;

import java.util.List;

// Created by bigwen on 2019/1/10.
public class AuctionActivity extends BaseActivity {

    private AuctionAdapter adapter;
    private Activity mActivity;
    private AuctionHeadView headView;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_auction);
        mActivity = this;
        BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        ListView listView = findViewById(R.id.list_view);
        adapter = new AuctionAdapter(this);
        adapter.setCallback(new AuctionAdapterCallback() {
            @Override
            public void updateData() {
                getData(false);
            }
            @Override
            public void userRefresh() {
                getData(true);
            }
        });
        headView = new AuctionHeadView(this);
        listView.addHeaderView(headView);
        listView.setAdapter(adapter);
        actionBar.addTitleAndBack(ResUtil.getStr(R.string.auction_hall_text));
        getData(false);
        ImageView bgIv = findViewById(R.id.auction_bg_iv);
        String url = "https://res.weplayapp.com/conf/BDF9B090-6286-4097-B4D0-4737E24C3B3E.webp?w=750&h=512";
        WpImageLoader.load(url, bgIv, ImageLoadInfo.newInfo().placeholder(R.drawable.auction_bg));
    }

    private void getData(final boolean showToast) {
        MarryPacketSender.getAuctionListReq(new AuctionLifeCallback(this) {
            @Override
            public void onSuccess(List<AuctionRoom> auctionRoomList) {
                EventDispatcher.postAuctionListEvent(auctionRoomList);
                refreshList(auctionRoomList);
                if (showToast) {
                    ToastUtil.show(R.string.refresh_success);
                }
            }

            @Override
            public void onFail(String msg) {
                ToastUtil.show(msg);
                headView.refresh(null);
            }
        });
    }

    private void refreshList(List<AuctionRoom> auctionRoomList) {
        adapter.refreshRoom2List(auctionRoomList);
        headView.refresh(auctionRoomList);
    }
}
