package com.wepie.wespy.module.marry.wedding.edit.time;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.marry.WeddingTime;

/**
 * Created by three on 15/10/10.
 */
public class WeddingDurationItemView extends LinearLayout{
    private Context mContext;
    private TextView durationTx;
    private TextView reserveTv;
    private ImageView selectImage;

    public WeddingDurationItemView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public WeddingDurationItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.wedding_duration_item, this);
        durationTx = findViewById(R.id.duration_item_tx);
        reserveTv = findViewById(R.id.duration_item_icon_reserve_tv);
        selectImage = findViewById(R.id.duration_item_icon_choose);
    }

    public void update(WeddingTime weddingTime2) {
        String duration = weddingTime2.getItemText();
        boolean reserved = weddingTime2.isBooked();
        durationTx.setText(duration);

        reserveTv.setVisibility(reserved ? View.VISIBLE : View.GONE);
        selectImage.setVisibility(reserved ? View.GONE : View.VISIBLE);
        selectImage.setImageResource(R.drawable.wedding_template_normal);
    }

    public void showSelect(boolean isSelect) {
        selectImage.setImageResource(isSelect ? R.drawable.wedding_template_select : R.drawable.wedding_template_normal);
    }
}
