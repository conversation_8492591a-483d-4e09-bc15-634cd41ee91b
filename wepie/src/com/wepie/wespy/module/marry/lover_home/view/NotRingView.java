package com.wepie.wespy.module.marry.lover_home.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.ui.empty.HWUIEmptyView;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.model.entity.marry.RecommendSingleFriendInfo;
import com.wepie.wespy.module.marry.lover_home.presenter.SinglePresenter;

import java.util.List;

/**
 * 单身情侣小窝界面
 */
public class NotRingView extends RelativeLayout {
    private ImageView userHead;
    private TextView nameTv;
    private RecyclerView recommendUserRv;
    private HWUIEmptyView emptyLay;
    private TextView careTipTv;
    private TextView recommendTitleTv;
    private RelativeLayout recommendTitleLayout;

    private RecommendAdapter adapter;
    private SinglePresenter presenter;
    private RelativeLayout alreadyProposeLay;
    private TextView proposeTv;

    public NotRingView(Context context) {
        super(context);
        init();
    }

    public NotRingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.lover_home_single_view, this);
        presenter = new SinglePresenter(this);
        initView();
    }

    public void update() {
        presenter.refreshUiAccordingMarryState();
    }

    private void initView() {
        userHead = findViewById(R.id.user_head);
        nameTv = findViewById(R.id.name_tv);
        recommendUserRv = findViewById(R.id.recommend_user_rv);
        emptyLay = findViewById(R.id.empty_view);
        careTipTv = findViewById(R.id.care_tip_tv);
        recommendTitleTv = findViewById(R.id.recommend_title_tv);
        recommendTitleLayout = findViewById(R.id.recommend_title_layout);
        alreadyProposeLay = findViewById(R.id.already_propose_lay);
        proposeTv = findViewById(R.id.propose_tv);

        HeadImageLoader.loadHeadImage(LoginHelper.getHeadUrl(), 45, userHead);
        nameTv.setText(LoginHelper.getNickname());
        adapter = new RecommendAdapter(getContext());
        recommendUserRv.setLayoutManager(new LinearLayoutManager(getContext()));
        recommendUserRv.setAdapter(adapter);

        emptyLay.setOnClickListener(() -> {
            presenter.gotoMakeFriendTab();
            return null;
        });

        TextView proposeTipsTv = findViewById(R.id.propose_tips);
        proposeTipsTv.setText(ResUtil.getStr(R.string.love_home_limit, ConfigHelper.getInstance().getProposeRequestCare()));
    }

    public void showEmptyView() {
        recommendTitleLayout.setVisibility(GONE);
        recommendTitleTv.setVisibility(GONE);
        careTipTv.setVisibility(GONE);
        recommendUserRv.setVisibility(GONE);
        alreadyProposeLay.setVisibility(GONE);

        emptyLay.setVisibility(VISIBLE);
    }

    public void refreshRecommendFriendList(List<RecommendSingleFriendInfo> infoList) {
        recommendTitleLayout.setVisibility(VISIBLE);
        recommendTitleTv.setVisibility(VISIBLE);
        careTipTv.setVisibility(VISIBLE);
        recommendUserRv.setVisibility(VISIBLE);
        emptyLay.setVisibility(GONE);
        alreadyProposeLay.setVisibility(GONE);

        adapter.update(infoList);
    }

    public void showAlreadyProposeView(String name) {
        alreadyProposeLay.setVisibility(VISIBLE);
        proposeTv.setText(ResUtil.getStr(R.string.love_home_request_wedding_tips, name));

        emptyLay.setVisibility(GONE);
        recommendTitleLayout.setVisibility(GONE);
        recommendTitleTv.setVisibility(GONE);
        careTipTv.setVisibility(GONE);
        recommendUserRv.setVisibility(GONE);
    }
}
