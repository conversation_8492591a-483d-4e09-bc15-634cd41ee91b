package com.wepie.wespy.module.marry.lover_home;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.model.entity.marry.NestPageInfo;
import com.huiwan.base.util.ToastUtil;

// Created by bigwen on 2018/6/12.
public class LoverSendGiftSettingActivity extends BaseActivity {

    private static final int SETTING_EMPTY = R.drawable.ic_un_select;
    private static final int SETTING_CHOOSE = R.drawable.ic_multi_select;

    private ImageView totalSendIv, friendSendIv;
    private RelativeLayout totalSendLay, friendSendLay;
    private BaseWpActionBar actionBar;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_lover_send_gift_setting);
        actionBar = findViewById(R.id.action_bar);
        totalSendLay = findViewById(R.id.setting_total_send_lay);
        friendSendLay = findViewById(R.id.setting_friend_send_lay);
        totalSendIv = findViewById(R.id.setting_total_send_iv);
        friendSendIv = findViewById(R.id.setting_friend_send_iv);
        actionBar.addTitleAndBack(R.string.set_send_gift_permission);
        friendSendLay.setOnClickListener(clickListener);
        totalSendLay.setOnClickListener(clickListener);
        refreshStateUi();
    }

    private View.OnClickListener clickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (totalSendLay == v) {
                if (LoverHomeManage.getGiftAuth() == NestPageInfo.GIFT_AUTH_TOTAL) return;
                LoverHomeManage.setGiftAuth(NestPageInfo.GIFT_AUTH_TOTAL, LoverSendGiftSettingActivity.this, new SetGiftAuthCallback() {
                    @Override
                    public void onSuccess() {
                        refreshStateUi();
                    }

                    @Override
                    public void onFail(String msg) {
                        ToastUtil.show(msg);
                    }
                });
            } else if (friendSendLay == v) {
                if (LoverHomeManage.getGiftAuth() == NestPageInfo.GIFT_AUTH_FRIEND) return;
                LoverHomeManage.setGiftAuth(NestPageInfo.GIFT_AUTH_FRIEND, LoverSendGiftSettingActivity.this, new SetGiftAuthCallback() {
                    @Override
                    public void onSuccess() {
                        refreshStateUi();
                    }

                    @Override
                    public void onFail(String msg) {
                        ToastUtil.show(msg);
                    }
                });
            }
        }
    };

    private void refreshStateUi() {
        boolean isChooseTotal = LoverHomeManage.getGiftAuth() == NestPageInfo.GIFT_AUTH_TOTAL;
        if (isChooseTotal) {
            totalSendIv.setImageResource(SETTING_CHOOSE);
            friendSendIv.setImageResource(SETTING_EMPTY);
        } else {
            totalSendIv.setImageResource(SETTING_EMPTY);
            friendSendIv.setImageResource(SETTING_CHOOSE);
        }
    }
}
