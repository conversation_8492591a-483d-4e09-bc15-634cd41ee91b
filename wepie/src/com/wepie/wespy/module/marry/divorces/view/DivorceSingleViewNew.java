package com.wepie.wespy.module.marry.divorces.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;

// Created by bigwen on 2018/12/25.
public class DivorceSingleViewNew extends RelativeLayout {
    private Context mContext;
    private TextView proposeBt;

    public DivorceSingleViewNew(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public DivorceSingleViewNew(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.divorce_single_view_new, this);
        proposeBt = (TextView) findViewById(R.id.divorce_single_propose_bt);

        proposeBt.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                JumpUtil.gotoPropose2Activity(mContext, -1);
            }
        });
    }
}
