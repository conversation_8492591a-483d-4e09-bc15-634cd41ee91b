package com.wepie.wespy.module.marry.propose.template;

import android.content.Context;
import android.text.method.ScrollingMovementMethod;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.ProposeTemplateExtra;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.model.entity.marry.ProposeInfo;
import com.wepie.wespy.module.marry.MarryUtil;
import com.wepie.wespy.module.marry.propose.process.ProposeParam;

// Created by bigwen on 2019/1/3.

public class ProposeTemplateView extends RelativeLayout {

    private final Context mContext;
    private TextView contentTv;
    private RelativeLayout surpriseLay;
    private ImageView giftIconIv;
    private TextView giftNumTv;
    private TextView senderTv;
    private ImageView ringIconIv;
    private TextView receiverNameTv;
    private ImageView pagerIv;
    private ImageView ringBoxIv;

    public ProposeTemplateView(@NonNull Context context) {
        super(context);
        mContext = context;
        init();
    }

    public ProposeTemplateView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.template_view, this);
        contentTv = findViewById(R.id.content_tv);
        surpriseLay = findViewById(R.id.surprise_lay);
        giftIconIv = findViewById(R.id.gift_icon);
        giftNumTv = findViewById(R.id.gift_num_tv);
        senderTv = findViewById(R.id.sender_name_tv);
        ringIconIv = findViewById(R.id.ring_icon_iv);
        ringBoxIv = findViewById(R.id.ring_box_iv);
        pagerIv = findViewById(R.id.pager_iv);
        receiverNameTv = findViewById(R.id.receiver_name_tv);
        contentTv.setMovementMethod(ScrollingMovementMethod.getInstance());
    }

    public void refresh(final ProposeParam proposeParam, PropItem propItem) {
        refreshData(proposeParam.getTargetUid(), LoginHelper.getLoginUid(), proposeParam.getProposeContent(),
                proposeParam.getGiftId(), proposeParam.getGiftNum(),
                proposeParam.getRingId());
        refreshTemplate(propItem);
    }

    public void refresh(final ProposeInfo proposeInfo2) {
        PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(proposeInfo2.getProposeTemplateId());
        refreshData(proposeInfo2.getRecvUid(), proposeInfo2.getSendUid(), proposeInfo2.getContent(),
                proposeInfo2.getSurpviseGift().getGiftId(), proposeInfo2.getSurpviseGift().getGiftNum(),
                proposeInfo2.getRingType());

        refreshTemplate(propItem);
    }

    private void refreshData(int receiverUid, int senderUid, final String text, int giftId, int giftNum, int ringType) {
        contentTv.setText(text);
        UserService.get().getCacheSimpleUser(receiverUid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                receiverNameTv.setText(ResUtil.getStr(R.string.dear, StringUtil.formatName(userInfo.getRemarkName())));
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
        UserService.get().getCacheSimpleUser(senderUid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                senderTv.setText(userInfo.getRemarkNameLimit6());
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });

        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftId);
        if (gift != null && giftNum > 0) {
            surpriseLay.setVisibility(VISIBLE);
            ImageLoaderUtil.loadNormalImage(gift.getMedia_url(), giftIconIv, ImageLoadInfo.getGiftInfo());
            String numText = "x" + giftNum;
            giftNumTv.setText(numText);
        } else {
            surpriseLay.setVisibility(INVISIBLE);
        }
        MarryUtil.loadRingImage(ringType, ringIconIv);
    }

    private void refreshTemplate(@Nullable PropItem propItem) {
        if (propItem == null) return;
        ProposeTemplateExtra extra = propItem.getProposeTemplate();
        if (extra == null) return;
        ImageLoaderUtil.loadNormalImage(extra.getRingBoxUrl(), ringBoxIv);
        ImageLoaderUtil.loadNormalImage(extra.getLetterUrl(), pagerIv);
        contentTv.setTextColor(extra.getFontColor());
        senderTv.setTextColor(extra.getFontColor());
        receiverNameTv.setTextColor(extra.getFontColor());
        giftNumTv.setTextColor(extra.getFontColor());
    }
}