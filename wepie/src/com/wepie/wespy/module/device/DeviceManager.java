package com.wepie.wespy.module.device;

import android.content.Context;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.huiwan.base.util.AUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.constants.HttpCode;
import com.huiwan.platform.ThreadUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.wespy.net.http.api.DeviceApi;
import com.wepie.wpdd.DeviceInfoUtil;
import com.wepie.wpdd.model.DeviceInfo;

import java.util.concurrent.ExecutorService;

public class DeviceManager {
    private volatile DeviceInfo deviceInfo;
    private final static String filename = "ADevice";

    private final ExecutorService service = ThreadUtil.newSingleThreadPool("DeviceManager");

    public static DeviceManager getInstance() {
        return Holder.INSTANCE;
    }

    public void initDeviceInfo() {
        String aDeviceInfo = loadDeviceInfo();
        if (!StringUtil.isNull(aDeviceInfo)) {
            try {
                JsonObject jsonObject = JsonParser.parseString(aDeviceInfo).getAsJsonObject();
                deviceInfo = JsonUtil.fromJson(jsonObject, DeviceInfo.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void refreshDeviceInfo(Context context) {
        DeviceInfo deviceInfo = DeviceInfoUtil.getDeviceInfo(context);
        deviceInfo.shumeiId = "";
        if (this.deviceInfo == null) {
            this.deviceInfo = deviceInfo;
            reportDeviceId();
            return;
        }
        if (!deviceInfo.equals(this.deviceInfo)) {
            if (this.deviceInfo.deviceId != null) {
                deviceInfo.deviceId = this.deviceInfo.deviceId;
            }
            this.deviceInfo = deviceInfo;
            reportDeviceId();
        }
    }

    private void reportDeviceId() {
        DeviceApi.sendDeviceInfo(DeviceInfoUtil.getADeviceString(deviceInfo), new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<DeviceInfo> result) {
                if (result.code == HttpCode.CODE_OK) {
                    deviceInfo.deviceId = result.data.deviceId;
                    saveDeviceInfo(deviceInfo);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.debugShow(msg);
            }
        });
    }

    private void saveDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
        FileUtil.writeFileAsync(filename, DeviceInfoUtil.getADeviceString(deviceInfo), service);
    }


    private String loadDeviceInfo() {
        String aDeviceInfo = FileUtil.readFile(filename);
        if (aDeviceInfo == null) {
            return null;
        } else {
            return AUtil.aOutTextDx9(aDeviceInfo);
        }
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    private static class Holder {
        private static final DeviceManager INSTANCE = new DeviceManager();
    }
}
