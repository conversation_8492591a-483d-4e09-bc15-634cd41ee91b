package com.wepie.wespy.module.discover;

import android.animation.ValueAnimator;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.component.util.PropResLoaderUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.DiscoverDecorItem;
import com.huiwan.configservice.constentity.propextra.DiscoverDecorItemSvga;
import com.huiwan.configservice.model.PropItem;
import com.welib.alinetlog.AliNetLogUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.HLog;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2019-08-06
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class CircleHeaderDecor extends RelativeLayout {
    private final List<ImageView> animViews = new ArrayList<>();
    private ImageView bgIv;
    private ImageView fgIv;
    private int lastDecor = 0;

    public CircleHeaderDecor(Context context) {
        this(context, null);
    }

    public CircleHeaderDecor(Context context, AttributeSet attrs) {
        super(context, attrs);
        initIv();
    }

    public void showDecorAnim(int animId) {
        if (animId != lastDecor) {
            if (animId == 0) {
                hideAnim();
            } else {
                lastDecor = animId;
                PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(animId);
                if (item != null && item.getType() == PropItem.TYPE_DISCOVER_DECOR) {
                    DiscoverDecorItem decorItem = item.getDiscoverDecorItem();
                    if (decorItem != null) {
                        showAnimInternal(animId, decorItem);
                    } else {
                        hideAnim();
                    }
                } else {
                    hideAnim();
                }
            }
        } else {
            if (animId != 0) {
                PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(animId);
                if (item != null && item.getType() == PropItem.TYPE_DISCOVER_DECOR && getVisibility() == View.GONE) {
                    DiscoverDecorItem decorItem = item.getDiscoverDecorItem();
                    if (decorItem != null) {
                        showAnimInternal(animId, decorItem);
                    }
                }
            }
        }
    }

    private void hideAnim() {
        setVisibility(GONE);
    }

    private void showAnimInternal(int animId, @NonNull DiscoverDecorItem decorItem) {
        setVisibility(VISIBLE);
        if (!TextUtils.isEmpty(decorItem.bgImgUrl)) {
            bgIv.setVisibility(VISIBLE);
            bgIv.setScaleX(decorItem.bgImgAutoMirror ? -1 : 1);
            PropResLoaderUtil.INSTANCE.loadResForDownload(decorItem.bgImgUrl,
                    decorItem.getLocalPropPath(decorItem.bgImgUrl), bgIv, null);
        } else {
            bgIv.setVisibility(GONE);
        }

        if (!TextUtils.isEmpty(decorItem.fgImgUrl)) {
            fgIv.setVisibility(VISIBLE);
            fgIv.setScaleX(decorItem.fgImgAutoMirror ? -1 : 1);
            PropResLoaderUtil.INSTANCE.loadResForDownload(decorItem.fgImgUrl,
                    decorItem.getLocalPropPath(decorItem.fgImgUrl), fgIv, null);
        } else {
            fgIv.setVisibility(GONE);
        }

        int totalSize = decorItem.decorItemSvgaList.size();
        if (totalSize == 1) {
            DiscoverDecorItemSvga svga = decorItem.decorItemSvgaList.get(0);
            if (!TextUtils.isEmpty(svga.url) && (svga.url.equals(decorItem.fgImgUrl) || svga.url.equals(decorItem.bgImgUrl))) {
                totalSize = 0;
            }
        }
        ensureAnimViewNum(totalSize);
        boolean needReport = false;
        for (int i = 0; i < totalSize; i++) {
            DiscoverDecorItemSvga itemSvga = decorItem.decorItemSvgaList.get(i);
            showSvgaItem(itemSvga, animViews.get(i));
            if (TextUtils.isEmpty(itemSvga.url)) {
                needReport = true;
            }
        }
        if (needReport) {
            HLog.aliLog(AliNetLogUtil.PORT.HttpConfig, AliNetLogUtil.TYPE.err, "PropItem: " + animId);
        }
        for (int i = totalSize; i < animViews.size(); i++) {
            animViews.get(i).setVisibility(GONE);
        }
    }

    private void showSvgaItem(DiscoverDecorItemSvga svgaItem, final ImageView imageView) {
        LayoutParams lp = (LayoutParams) imageView.getLayoutParams();
        if (svgaItem.width < 1) {
            lp.width = ViewGroup.LayoutParams.MATCH_PARENT;
            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        } else {
            lp.width = ScreenUtil.dip2px(svgaItem.width);
            lp.height = ScreenUtil.dip2px(svgaItem.height);
        }
        if (svgaItem.alignStart()) {
            lp.removeRule(RelativeLayout.ALIGN_PARENT_END);
            lp.addRule(RelativeLayout.ALIGN_PARENT_START);
            lp.setMarginStart(svgaItem.x);
        } else {
            lp.removeRule(RelativeLayout.ALIGN_PARENT_START);
            lp.addRule(RelativeLayout.ALIGN_PARENT_END);
            lp.setMarginEnd(svgaItem.x);
        }
        lp.topMargin = svgaItem.y;
        imageView.setScaleX(svgaItem.autoMirror ? -1 : 1);
        svgaItem.checkMoveOldRes(new LifeDataCallback(imageView) {
            @Override
            public void onSuccess(Result result) {
                PropResLoaderUtil.INSTANCE.loadResForDownload(svgaItem.url, svgaItem.getLocalPath(), imageView, null);
            }

            @Override
            public void onFail(int code, String msg) {
                PropResLoaderUtil.INSTANCE.loadResForDownload(svgaItem.url, svgaItem.getLocalPath(), imageView, null);
            }
        });
        imageView.setVisibility(VISIBLE);
        imageView.requestLayout();
    }

    private void ensureAnimViewNum(int num) {
        for (int i = animViews.size(); i < num; i++) {
            ImageView iv = new ImageView(getContext());
            if (ScreenUtil.isRtl()) {
                iv.setScaleX(-1L);
            }
            animViews.add(iv);
            iv.setScaleType(ImageView.ScaleType.FIT_XY);
            addView(iv, getChildCount() - 1);
        }
    }

    private void initIv() {
        bgIv = new ImageView(getContext());
        bgIv.setScaleType(ImageView.ScaleType.FIT_XY);
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ScreenUtil.dip2px(100));
        addView(bgIv, lp);

        fgIv = new ImageView(getContext());
        fgIv.setScaleType(ImageView.ScaleType.FIT_XY);
        lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ScreenUtil.dip2px(100));
        addView(fgIv, lp);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        for (ImageView imageView : animViews) {
            if (imageView.isShown()) {
                WpImageLoader.startAnimatedDrawable(imageView.getDrawable(), ValueAnimator.INFINITE);
            }
        }
    }
}
