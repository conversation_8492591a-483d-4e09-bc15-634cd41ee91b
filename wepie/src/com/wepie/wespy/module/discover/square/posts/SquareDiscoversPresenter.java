package com.wepie.wespy.module.discover.square.posts;

import androidx.annotation.Nullable;

import com.huiwan.base.util.ToastUtil;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.model.entity.Discover;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.discover.DiscoverPointerHelper;
import com.wepie.wespy.module.discover.DiscoverPostContainerType;
import com.wepie.wespy.module.discover.square.SquarePostsManager;
import com.wepie.wespy.module.fdiscover.entity.CCommentInfo;
import com.wepie.wespy.module.fdiscover.event.DiscoverCommentEvent;
import com.wepie.wespy.module.fdiscover.event.DiscoverLikeEvent;
import com.wepie.wespy.module.fdiscover.event.DiscoverPostEvent;
import com.wepie.wespy.module.report.ReportBuilder;
import com.wepie.wespy.module.report.ReportConst;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2018/4/23
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class SquareDiscoversPresenter {
    private SquareDiscoversView postsView;

    SquareDiscoversPresenter(SquareDiscoversView postsView){
        this.postsView = postsView;
    }

    void init() {
        showLocal();
        postsView.refresh();
    }

    void refreshList(){
        EventDispatcher.postUpdateSquareBannerEvent();
        SquarePostsManager.getInstance().refreshPosts(new SquarePostsManager.LifePostsCallback(postsView) {
            @Override
            public void onSuccess() {
                postsView.refreshPostsFinished(SquarePostsManager.getInstance().getAllPost());
            }

            @Override
            public void onFail(String msg) {
                ToastUtil.show(msg);
                postsView.refreshPostsFinished(SquarePostsManager.getInstance().getAllPost());
            }
        });
    }

    void loadMoreList(){
        SquarePostsManager.getInstance().loadMore(new SquarePostsManager.MorePostsCallback() {
            @Nullable
            @Override
            public ILife getLife() {
                return ILifeUtil.toLife(postsView);
            }

            @Override
            public void onSuccess(List<Discover> posts) {
                postsView.addMorePostsFinished(posts);
            }

            @Override
            public void onFail(String msg) {
                ToastUtil.show(msg);
                postsView.addMorePostsFinished(new ArrayList<Discover>());

            }
        });
    }

    void delPost(final Discover post){
        postsView.showLoading();
        SquarePostsManager.getInstance().delPost(post, new SquarePostsManager.LifePostsCallback(postsView) {
            @Override
            public void onSuccess() {
                postsView.hideLoading();
            }

            @Override
            public void onFail(String msg) {
                postsView.hideLoading();
                ToastUtil.show(msg);
            }
        });
    }

    @SuppressWarnings("unused")
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRefreshCommentEvent(final DiscoverCommentEvent event) {
        Discover post = SquarePostsManager.getInstance().getDiscoverById(event.discoverId);
        if (post != null){
            if (event.controlType == DiscoverPostEvent.CONTROL_TYPE_DEL){
                postsView.delComment(event.discoverId, event.commentId);
            } else if (event.controlType == DiscoverPostEvent.CONTROL_TYPE_ADD){
                CCommentInfo comment = post.getCommentById(event.commentId);
                if (comment != null){
                    postsView.addComment(post, comment);
                }
            }
        }
    }

    @SuppressWarnings("unused")
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRefreshVoteEvent(final DiscoverLikeEvent event) {
        Discover post = SquarePostsManager.getInstance().getDiscoverById(event.discover_id);
        if (post != null){
            postsView.changeLike(post);
        }
    }

    @SuppressWarnings("unused")
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRefreshDiscoverEvent(final DiscoverPostEvent event) {
        if (event.controlType == DiscoverPostEvent.CONTROL_TYPE_DEL){
            postsView.removePostItem(event.discoverId);
        } else if (event.controlType == DiscoverPostEvent.CONTROL_TYPE_ADD){
            Discover post = SquarePostsManager.getInstance().getDiscoverById(event.discoverId);
            if (post != null){
                postsView.addItem(post);
            }
        }
    }

    public void onClickToComment(final Discover discover, CCommentInfo commentInfo, String content){
        postsView.showLoading();
        final int toUid = commentInfo == null ? 0 : commentInfo.uid;
        String toNickname = commentInfo == null ? "" : commentInfo.nickname;
        SquarePostsManager.getInstance().doComment(discover.getId(), discover.getUid(), toUid,
                toNickname, content, new SquarePostsManager.LifeCommentCallback(postsView) {
                    @Override
                    public void onSuccess(int commentId) {
                        postsView.hideLoading();
                        int pointUid = toUid == 0 ? discover.getUid() : toUid;
                        DiscoverPointerHelper.pointComment(discover, pointUid, false,
                                DiscoverPostContainerType.TypeSquareMain);
                    }

                    @Override
                    public void onFail(String msg) {
                        postsView.hideLoading();
                        ToastUtil.show(msg);
                    }
                });
    }

    public void onClickVote(final Discover discover, boolean like){
        if (like){
            SquarePostsManager.getInstance().like(discover, new SquarePostsManager.LifePostsCallback(postsView) {
                @Override
                public void onSuccess() {
                    DiscoverPointerHelper.pointLike(discover, DiscoverPostContainerType.TypeSquareMain);
                }

                @Override
                public void onFail(String msg) {
                    postsView.changeLike(discover);
                    ToastUtil.show(msg);
                }
            });
        } else {
            SquarePostsManager.getInstance().dislike(discover, new SquarePostsManager.LifePostsCallback(postsView) {
                @Override
                public void onSuccess() {
                    DiscoverPointerHelper.pointLike(discover, DiscoverPostContainerType.TypeSquareMain);
                }

                @Override
                public void onFail(String msg) {
                    postsView.changeLike(discover);
                    ToastUtil.show(msg);
                }
            });
        }
    }

    public void onClickDeleteCommentEvent(final Discover discover, final CCommentInfo commentInfo) {
        SquarePostsManager.getInstance().delComment(discover.getId(), discover.getUid(),
                commentInfo.getToUid(), commentInfo.getId(), new SquarePostsManager.LifeCommentCallback(postsView) {
                    @Override
                    public void onSuccess(int commentId) {
                        DiscoverPointerHelper.pointComment(discover, commentInfo.to_uid, true,
                                DiscoverPostContainerType.TypeSquareMain);
                    }

                    @Override
                    public void onFail(String s) {
                        ToastUtil.show(s);
                    }
                });
    }

    void onAttach(){
        registerEventBus();
    }

    void onDetach(){
        unregisterEventBus();
    }

    private void showLocal(){
        postsView.showLocal(SquarePostsManager.getInstance().getAllPost());
    }

    private void registerEventBus(){
        if (!EventBus.getDefault().isRegistered(this)){
            EventBus.getDefault().register(this);
        }
    }

    private void unregisterEventBus(){
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    public void onClickReportCommentEvent(final Discover discover, final CCommentInfo comment) {
        JumpUtil.gotoReportMainActivity(postsView.getContext(), ReportBuilder.newBuilder().setComment(comment).setSource(TrackSource.SQUARE).setReportType(ReportConst.ReportTypeDiscoverComment));
    }
}
