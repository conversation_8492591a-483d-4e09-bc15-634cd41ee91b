package com.wepie.wespy.module.discover;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;

// Created by <PERSON>wen on 2018/6/12.

public class CircleTitleIndicator extends FrameLayout {

    private String TAG = "CircleTitleIndicator";
    private static final int SELECT_COLOR = Color.parseColor("#27C33B");
    private static final int UNSELECT_COLOR = Color.parseColor("#4A4A4A");

    private Context mContext;
    private TextView circleTv, squareTv;
    private ImageView circleLine, squareLine, scrollLine;
    private OnTabSelectListener listener;
    private long lastClickTime0 = 0, lastClickTime1 = 0;

    public CircleTitleIndicator(@NonNull Context context) {
        super(context);
        mContext = context;
        init();
    }

    public CircleTitleIndicator(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.circle_title_indicator, this);
        circleTv = findViewById(R.id.circle_tv);
        squareTv = findViewById(R.id.square_tv);
        circleLine = findViewById(R.id.circle_line);
        squareLine = findViewById(R.id.square_line);
        scrollLine = findViewById(R.id.scroll_line);
        circleTv.setOnClickListener(clickListener);
        squareTv.setOnClickListener(clickListener);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        //init ui
        refreshUi(true);
        refreshLine(getCircleXy()[0], getCircleXy()[1]);
        scrollLine.setVisibility(VISIBLE);
    }

    public void setOnTabSelectListener(OnTabSelectListener listener) {
        this.listener = listener;
    }

    private void refreshUi(boolean selectCircle) {
        if (selectCircle) {
            circleTv.setTextColor(SELECT_COLOR);
            circleTv.setTypeface(Typeface.DEFAULT_BOLD);
            squareTv.setTextColor(UNSELECT_COLOR);
            squareTv.setTypeface(Typeface.DEFAULT);
        } else {
            circleTv.setTextColor(UNSELECT_COLOR);
            circleTv.setTypeface(Typeface.DEFAULT);
            squareTv.setTextColor(SELECT_COLOR);
            squareTv.setTypeface(Typeface.DEFAULT_BOLD);
        }
    }

    public void dispatchTabClickEvent(int position) {
        if (position == 0) {
            refreshUi(true);
        } else {
            refreshUi(false);
        }
    }

    private void refreshLine(float x, float y) {
        scrollLine.setX(x);
        scrollLine.setY(y);
    }

    private View.OnClickListener clickListener = new OnClickListener() {
        @Override
        public void onClick(View v) {
            if (circleTv == v) {
                refreshUi(true);
                if (listener != null) listener.onSelectTab(0);

                if (listener != null && (System.currentTimeMillis() - lastClickTime0) < 1000) {
                    listener.onDoubleClick(0);
                }
                lastClickTime0 = System.currentTimeMillis();
            } else if (squareTv == v) {
                refreshUi(false);
                if (listener != null) listener.onSelectTab(1);

                if (listener != null && (System.currentTimeMillis() - lastClickTime1) < 1000) {
                    listener.onDoubleClick(0);
                }
                lastClickTime1 = System.currentTimeMillis();
            }
        }
    };

    private float[] getCircleXy() {
        return new float[] {circleLine.getX(), circleLine.getY()};
    }

    private float[] getSquareXy() {
        return new float[] {squareLine.getX(), squareLine.getY()};
    }

    public void scroll(int position, float positionOffset) {
        HLog.i(TAG, "scroll: " + position + " " + positionOffset);
        float rate = position + positionOffset;
        refreshLine(getCircleXy()[0] + (getSquareXy()[0] - getCircleXy()[0]) * rate, getCircleXy()[1]);
    }

    public void onPageSelected(int position) {
        refreshUi(position == 0);
    }
}