package com.wepie.wespy.module.chat.gamemodel;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.store.database.WPModel;
import com.huiwan.store.database.WPStore;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.game.game.activity.AdapterHolderUtil;
import com.wepie.wespy.module.game.game.activity.GameConstant;
import com.wepie.wespy.module.game.util.GameDialogUtil;
import com.wepie.wespy.module.game.util.GameDialogUtil.DialogClickCallback;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

/**
 * 发送图片的view
 *
 * <AUTHOR>
 */
public class MsgSendImageModel extends LinearLayout {

    private String TAG = "MsgSendImageModel";
    private Context mContext;
    private ImageView failImage;
    private ImageView progressImage;
    private TextView statusTx;
    private TextView statusTitleTx;
    private ImageView statusImage;
    private View sendImageLay;

    private AnimationDrawable animation;

    public static final int MODEL_TYPE_GAMEMSG = 1;

    public MsgSendImageModel(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    public MsgSendImageModel(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.msg_send_image_view, this);
        failImage = (ImageView) findViewById(R.id.msg_send_image_fail);
        progressImage = (ImageView) findViewById(R.id.msg_send_image_progress);
        statusTx = (TextView) findViewById(R.id.msg_send_image_status_tx);
        statusTitleTx = findViewById(R.id.msg_send_image_title_tx);
        statusImage = (ImageView) findViewById(R.id.msg_send_iamge_icon);
        sendImageLay = findViewById(R.id.msg_send_image_lay);
        sendImageLay.setLayoutDirection(ScreenUtil.isRtl() ? LAYOUT_DIRECTION_RTL : LAYOUT_DIRECTION_LTR);
    }

    public void updateView(WPModel model, int modelType) {
        failImage.setVisibility(View.GONE);
        progressImage.setVisibility(View.GONE);
        if (modelType == AdapterHolderUtil.TYPE_CHAT_MSG) {
            WPMessage chatMsg = (WPMessage) model;
            updateChatView(chatMsg);
        }
    }

    private void updateChatView(final WPMessage msg) {
        setOnClickListener(null);
        int status = msg.getStatus();
        switch (status) {
            case WPMessage.STATUS_SENDING:
                long curTime = TimeUtil.getServerTime();
                long sendTime = msg.getTime();
                if (curTime - sendTime > GameConstant.SEND_TIME_OUT) {
                    msg.setStatus(WPMessage.STATUS_FAIL);
                    setChatFailStatus(msg);
                    WPStore.saveAsync(msg);
                    break;
                }

                progressImage.setVisibility(View.VISIBLE);
                if (animation == null) {
                    progressImage.setImageResource(R.drawable.anim_sending);
                    animation = (AnimationDrawable) progressImage.getDrawable();
                    animation.start();
                }
                if (!animation.isRunning()) {
                    animation.start();
                }

                statusTx.setText(R.string.msg_sending);
                statusImage.setBackgroundResource(R.drawable.secret_photo_img);
                break;
            case WPMessage.STATUS_OK:
                statusTx.setText(R.string.msg_private_pic_unview);
                statusImage.setBackgroundResource(R.drawable.secret_photo_img);
                break;
            case WPMessage.STATUS_VIEWED:
                statusTx.setText(R.string.msg_private_pic_viewed);
                statusImage.setBackgroundResource(R.drawable.secret_photo_disable_img);
                break;
            case WPMessage.STATUS_SCREENSHOT:
                statusTx.setVisibility(View.GONE);
                statusTitleTx.setText(R.string.msg_private_pic_screen_shot);
                statusImage.setBackgroundResource(R.drawable.secret_photo_disable_img);
                break;
            case WPMessage.STATUS_FAIL:
                setChatFailStatus(msg);
                break;
            default:
                break;
        }
    }

    private void setChatFailStatus(final WPMessage msg) {
        failImage.setVisibility(View.VISIBLE);
        statusTx.setText(R.string.send_failed);
        statusImage.setBackgroundResource(R.drawable.secret_photo_img);
        this.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                GameDialogUtil.showResendEnsureDialog(mContext, new DialogClickCallback() {

                    @Override
                    public void onClickSure(String content) {
                        EventDispatcher.postSingleChatResendMsg(msg);
                    }

                    @Override
                    public void onClickCancel() {
                    }
                });

            }
        });
    }
}
