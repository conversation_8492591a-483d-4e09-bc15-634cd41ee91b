package com.wepie.wespy.module.chat.invitegame;

import static com.wepie.wespy.cocosnew.match.main.ar285.LittleGameTrackUtilsKt.createRoomClickTrack;

import android.app.Dialog;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.user.UserService;
import com.wejoy.littlegame.LittleGame;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.module.common.jump.JumpUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class GroupChooseGameModeView extends FrameLayout {

    private RecyclerView listView;
    private ChooseModeAdapter modeAdapter;
    private GroupChooseGamePresenter chooseGamePresenter;
    private final ProgressDialogUtil progressDialogUtil = new ProgressDialogUtil();
    private Dialog dialog;
    private int gameType;
    private int gid;

    public GroupChooseGameModeView(@NonNull Context context) {
        this(context, null);
    }

    public GroupChooseGameModeView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.choose_game_mode_view, this);
        initViews(context);
    }

    private void initViews(Context context) {
        listView = findViewById(R.id.mode_rv);
        modeAdapter = new ChooseModeAdapter(context);
        listView.setLayoutManager(new LinearLayoutManager(context));
        listView.setAdapter(modeAdapter);
        modeAdapter.setOnItemClickListener((data, index) -> {
            if (data.isVipGameMode() && !UserService.get().getLoginUser().isVip()) {
                JumpUtil.gotoVipMainActivity(context, ResUtil.getStr(R.string.jackaroo_track_room_create));
                return;
            }
            GameConfig.MatchInfo matchInfo = modeAdapter.getItemData(index);
            progressDialogUtil.showLoading(context, null, true);
            chooseGamePresenter.createRoom(matchInfo, gid, gameType);
            createRoomClickTrack(gameType, data, TrackString.SCENE_PRIVATE_GAME);
        });
    }

    public List<GameConfig.MatchInfo> getMatchList() {
        List<GameConfig.MatchGroupInfo> groupInfoList = ConfigHelper.getInstance().getGameConfig().getMatchGroup(gameType);
        if (!groupInfoList.isEmpty()) {
            List<GameConfig.MatchInfo> matchInfoList = new ArrayList<>();
            for (GameConfig.MatchGroupInfo matchGroupInfo : groupInfoList) {
                matchInfoList.addAll(matchGroupInfo.getMatchInfoListForCreate());
            }
            Collections.sort(matchInfoList, new Comparator<GameConfig.MatchInfo>() {
                @Override
                public int compare(GameConfig.MatchInfo o1, GameConfig.MatchInfo o2) {
                    return o1.getCoin() - o2.getCoin();
                }
            });
            return matchInfoList;
        } else {
            return ConfigHelper.getInstance().getGameConfig(gameType).getMatchInfoListForCreate();
        }
    }

    public void jumpToCreateRoom(GameConfig.MatchInfo matchInfo) {
        chooseGamePresenter.inviteFriendGroup(matchInfo, gid);
        dismissDialog();
        LittleGame.touchCreateSuspend();
    }

    public void setDialog(Dialog dialog) {
        this.dialog = dialog;
    }

    public void hideLoading() {
        progressDialogUtil.hideLoading();
    }

    void dismissDialog() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    public static void showChooseModeDialog(Context context, int gameType, int gid, GroupChooseGamePresenter chooseGamePresenter) {
        BottomSheetDialog dialog = new BottomSheetDialog(context, R.style.TransparentBottomSheetStyle);
        dialog.setCanceledOnTouchOutside(true);

        final GroupChooseGameModeView chooseGameView = new GroupChooseGameModeView(context);
        dialog.setContentView(chooseGameView);
        chooseGameView.dialog = dialog;
        chooseGameView.gameType = gameType;
        chooseGameView.gid = gid;
        chooseGameView.chooseGamePresenter = chooseGamePresenter;
        chooseGameView.chooseGamePresenter.setChooseGameModeView(chooseGameView);
        dialog.show();
        chooseGameView.modeAdapter.refresh(chooseGameView.getMatchList());
    }

}
