package com.wepie.wespy.module.chat.send.face;

import android.content.Context;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.widget.OnItemClickListener;
import com.huiwan.widget.rv.BaseRvAdapter;
import com.huiwan.widget.rv.RVHolder;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoadBaseListener;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.RecyclerViewItemClickListener;
import com.wepie.wespy.model.entity.emoticon.EmoticonInfo;
import com.wepie.wespy.model.entity.emoticon.EmoticonItem;
import com.wepie.wespy.model.entity.emoticon.EmoticonItemPageRsp;
import com.wepie.wespy.model.event.EmoticonFavoriteChangeEvent;
import com.wepie.wespy.module.chat.send.ImageSizeCache;
import com.wepie.wespy.module.fdiscover.main.OffsetInfo;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * date 2020/9/7
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class EmoticonPageView extends FrameLayout {
    private final Handler handler = new Handler();
    private final Adapter adapter = new Adapter();
    private GridLayoutManager glm;
    private RecyclerView rv;
    private SmartRefreshLayout srl;
    private OnItemClickListener<EmoticonItem> itemClickListener;
    private EmoticonInfo info;

    public EmoticonPageView(@NonNull Context context) {
        this(context, null);
    }

    public EmoticonPageView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initViews(context);
    }

    public void setOnItemClickListener(OnItemClickListener<EmoticonItem> itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    void init(EmoticonInfo info) {
        this.info = info;
        refreshList(info);
    }

    void initScroll(OffsetInfo offset) {
        if (offset != null) {
            glm.scrollToPositionWithOffset(offset.getPosition(), offset.getOffset());
        }
    }

    public RecyclerView getRv() {
        return rv;
    }

    private void refreshList(EmoticonInfo info) {
        List<EmoticonItem> iconList = info.getItemList();
        adapter.type = info.getType();
        if (info.isFavorite()) {
            List<EmoticonItem> itemList = new ArrayList<>(iconList.size() + 1);
            itemList.add(EmoticonItem.makeAdd());
            itemList.addAll(iconList);
            adapter.refresh(itemList);
        } else {
            adapter.refresh(iconList);
        }
    }

    private void loadMore() {
        if (info == null) {
            return;
        }
        EmoticonHelper.getHelper().loadMore(info, new LifeDataCallback<EmoticonItemPageRsp>(this) {

            @Override
            public void onSuccess(Result<EmoticonItemPageRsp> result) {
                finishLoadMore();
                List<EmoticonItem> data = result.data.itemList;
                if (data.isEmpty()) {
                    ToastUtil.show(ResUtil.getString(R.string.no_more_la));
                } else {
                    adapter.add(data);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                finishLoadMore();
                ToastUtil.show(msg);
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onFavoriteEmoticonChangeListener(EmoticonFavoriteChangeEvent event) {
        if (this.info != null && info.isFavorite()) {
            refreshList(info);
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventDispatcher.registerEventObserver(this);
    }

    @Override
    protected void onDetachedFromWindow() {
        EventDispatcher.unregisterEventObserver(this);
        super.onDetachedFromWindow();
    }

    private void finishLoadMore() {
        srl.finishLoadMore(0);
    }

    private void initViews(Context context) {
        srl = new SmartRefreshLayout(context);
        addView(srl, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        rv = new RecyclerView(context);
        srl.addView(rv, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        rv.setHasFixedSize(true);
        glm = new GridLayoutManager(context, 4);
        rv.setLayoutManager(glm);
        rv.setAdapter(adapter);
        srl.setEnableRefresh(false);
        srl.setEnableLoadMore(true);
        srl.setEnableOverScrollBounce(false);
        srl.setEnableAutoLoadMore(false);
        srl.setOnLoadMoreListener((srl)->loadMore());
        rv.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                adapter.updateState(newState);
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    handler.post(notifyAdapter);
                } else if (newState == RecyclerView.SCROLL_STATE_IDLE){
                    handler.removeCallbacks(notifyAdapter);
                    handler.postDelayed(notifyAdapter, 56);
                }
            }
        });

        RecyclerViewItemClickListener listener = new RecyclerViewItemClickListener(context);
        listener.bindRecyclerView(rv);
        listener.setListener(new RecyclerViewItemClickListener.OnItemClickListener() {
            @Override
            public void onItemClick(View view, int adapterPosition) {
                EmoticonItem item = adapter.getItem(adapterPosition);
                if (itemClickListener != null) {
                    itemClickListener.onClickItem(item, adapterPosition);
                }
            }

            @Override
            public void onItemLongClick(View view, int adapterPosition) { }
        });
    }

    private final Runnable notifyAdapter = adapter::notifyLoadStateChange;

    @NonNull
    @Override
    public String toString() {
        return "EmoticonPageView{" +
                "info=" + info +
                '}';
    }

    static class Adapter extends BaseRvAdapter<EmoticonItem, RVHolder> {
        static final int LOAD_STATE_NORMAL = 0;
        static final int LOAD_STATE_STATIC = 1;
        static final int LOAD_STATE_NONE = 2;
        private final Object stateChangePayload = 123456;
        private int loadState = LOAD_STATE_NORMAL;
        int type;

        private void updateState(int state) {
            if (state == RecyclerView.SCROLL_STATE_SETTLING) {
                loadState = LOAD_STATE_NONE;
            } else if (state == RecyclerView.SCROLL_STATE_DRAGGING) {
                loadState = LOAD_STATE_STATIC;
            } else {
                loadState = LOAD_STATE_NORMAL;
            }
        }

        private void notifyLoadStateChange() {
            notifyItemRangeChanged(0, dataList.size(), stateChangePayload);
        }

        @NonNull
        @Override
        public RVHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
            return new RVHolder(new ItemView(viewGroup.getContext()));
        }

        @Override
        public void onBindViewHolder(@NonNull RVHolder rvHolder, int i) {
            ((ItemView)rvHolder.itemView).bind(dataList.get(i), loadState);
        }

        @Override
        public void onBindViewHolder(@NonNull RVHolder holder, int position, @NonNull List<Object> payloads) {
            if (payloads.isEmpty()) {
                super.onBindViewHolder(holder, position, payloads);
            } else {
                ((ItemView)holder.itemView).bindPayloadChange(dataList.get(position), loadState);
            }
        }
    }

    private static class ItemView extends FrameLayout {
        private EmoticonItem item;
        private EmoticonItem loadedItem;
        private final ImageView iconIv;
        private final TextView nameTv;
        private int loadState;

        public ItemView(@NonNull Context context) {
            super(context);
            LayoutInflater.from(context).inflate(R.layout.send_emoticon_item_view, this);
            iconIv = findViewById(R.id.image_iv);
            nameTv = findViewById(R.id.name_tv);
        }

        void bind(EmoticonItem item, int loadState) {
            this.item = item;
            this.loadState = loadState;
            if (item.isEmoticon()) {
                loadIcon(item);
            } else {
                if (item.type == EmoticonItem.TYPE_ITEM_ADD) {
                    iconIv.setImageResource(R.drawable.emoticon_add);
                    nameTv.setVisibility(GONE);
                }
            }
            checkUpdateName();
        }

        void bindPayloadChange(EmoticonItem item, int loadState) {
            if (item == this.loadedItem) {
                this.loadState = loadState;
                checkAnim();
            } else {
                bind(item, loadState);
            }
            checkUpdateName();
        }

        private void loadIcon(EmoticonItem item) {
            if (loadState == Adapter.LOAD_STATE_NONE) {
                if (loadedItem != item) {
                    iconIv.setImageResource(R.drawable.stub_image_f4);
                }
                return;
            }
            iconIv.setImageResource(R.drawable.stub_image_f4);
            WpImageLoader.downloadOnly(item.url, this, new WpImageLoadBaseListener<String, File>() {
                @Override
                public boolean onComplete(String model, File data) {
                    if (item == ItemView.this.item) {
                        cacheImageSize(model, data);
                        WpImageLoader.load(data, null, ImageLoadInfo.newInfo().owner(iconIv), new WpImageLoadListener<File>() {
                            @Override
                            public boolean onComplete(File model, Drawable data) {
                                if (item == ItemView.this.item) {
                                    iconIv.setImageDrawable(data);
                                    loadedItem = item;
                                    checkAnim();
                                    checkUpdateName();
                                }
                                return true;
                            }

                            @Override
                            public boolean onFailed(File model, Exception e) {
                                return false;
                            }
                        });
                    }
                    return true;
                }

                @Override
                public boolean onFailed(String model, Exception e) {
                    iconIv.setImageResource(R.drawable.stub_image_f4);
                    return true;
                }
            });
        }

        private void cacheImageSize(String url, File file) {
            if (!ImageSizeCache.get().hasImage(url)) {
                ImageSize size = ImageSize.from(file);
                ImageSizeCache.get().cache(url, size.width, size.height, file.length());
            }
        }

        private void checkUpdateName() {
            if (TextUtils.isEmpty(item.name)) {
                nameTv.setVisibility(GONE);
            } else {
                nameTv.setVisibility(VISIBLE);
                nameTv.setText(item.name);
            }
        }

        private void checkAnim() {
            Drawable drawable = iconIv.getDrawable();
            if (drawable instanceof Animatable) {
                if (loadState == Adapter.LOAD_STATE_STATIC) {
                    ((Animatable)drawable).stop();
                } else {
                    ((Animatable)drawable).start();
                }
            }
        }
    }
}
