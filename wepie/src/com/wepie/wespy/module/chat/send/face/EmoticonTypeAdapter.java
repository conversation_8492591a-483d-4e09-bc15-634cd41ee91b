package com.wepie.wespy.module.chat.send.face;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.huiwan.widget.rv.RVHolder;
import com.wepie.wespy.model.entity.emoticon.EmoticonInfo;
import com.huiwan.base.util.ScreenUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2020/9/4
 * email <EMAIL>
 *
 * <AUTHOR>
 */
class EmoticonTypeAdapter extends RecyclerView.Adapter<RVHolder> {

    private List<EmoticonInfo> itemList = new ArrayList<>();
    private int selectedPosition = -1;

    void refresh(List<EmoticonInfo> list) {
        this.itemList.clear();
        this.itemList.addAll(list);
        notifyDataSetChanged();
    }

    void setSelectedItem(int position) {
        selectedPosition = position;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RVHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new RVHolder(new ItemView(viewGroup.getContext()));
    }

    @Override
    public void onBindViewHolder(@NonNull RVHolder rvHolder, int i) {
        ((ItemView)rvHolder.itemView).bind(itemList.get(i), selectedPosition == i);
    }

    @Override
    public int getItemCount() {
        return itemList.size();
    }

    static class ItemView extends RelativeLayout {
        ImageView imageView;
        public ItemView(@NonNull Context context) {
            super(context);
            setLayoutParams(new ViewGroup.LayoutParams(ScreenUtil.dip2px(44), ScreenUtil.dip2px(44)));
            imageView = new AppCompatImageView(context);
            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ScreenUtil.dip2px(30), ScreenUtil.dip2px(30));
            lp.addRule(RelativeLayout.CENTER_IN_PARENT);
            addView(imageView, lp);
            setBackgroundColor(0x90ffff00);
        }

        void bind(EmoticonInfo info, boolean curSelected) {
            if (curSelected) {
                setBackgroundResource(R.drawable.shape_f5f5f5_corner8);
            } else {
                setBackground(null);
            }
            int placeholder = 0;
            int error = 0;
            if (info.isFavorite()) {
                placeholder = R.drawable.emoticon_type_favorite;
                error = R.drawable.emoticon_type_favorite;
            } else if (info.isEmoji()) {
                placeholder = R.drawable.emoticon_type_emoji;
                error = R.drawable.emoticon_type_emoji;
            } else {
                placeholder = R.drawable.stub_image_f4;
                error = R.drawable.emoticon_type_err;
            }
            ImageLoadInfo loadInfo = ImageLoadInfo.newInfo().placeholder(placeholder).error(error);
            WpImageLoader.load(info.getEmoticonType().icon, imageView, loadInfo);
        }
    }
}
