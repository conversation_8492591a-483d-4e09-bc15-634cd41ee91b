package com.wepie.wespy.module.chat.send;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON>wen on 2019-11-14.
 */
public class SendViewConst {

    public static final int ITEM_PHOTO = 1;
    public static final int ITEM_CAMERA = 2;
    public static final int ITEM_SECRET_PHOTO = 3;
    public static final int ITEM_RED_POCKET = 4;
    public static final int ITEM_ID_CARD = 5;
    public static final int ITEM_SEND_GIFT = 6;
    public static final int ITEM_FAMILY_BOX = 7;
    public static final int ITEM_ALL_GIFT = 8;
    public static final int ITEM_VOICE_LINK = 9;
    public static final int ITEM_INVITE_GAME = 10;

    public static int getImage(int itemId) {
        if (itemId == ITEM_PHOTO) {
            return R.drawable.input_photo;
        } else if (itemId == ITEM_CAMERA) {
            return R.drawable.input_camera;
        } else if (itemId == ITEM_RED_POCKET) {
            return R.drawable.input_redpacket;
        } else if (itemId == ITEM_SECRET_PHOTO) {
            return R.drawable.input_secret_photo;
        } else if (itemId == ITEM_ID_CARD) {
            return R.drawable.input_usercard;
        } else if (itemId == ITEM_SEND_GIFT) {
            return R.drawable.send_gift_ic;
        } else if (itemId == ITEM_FAMILY_BOX) {
            return R.drawable.send_family_box_icon;
        } else if (itemId == ITEM_VOICE_LINK) {
            return R.drawable.voice_link_icon;
        } else if (itemId == ITEM_ALL_GIFT) {
            return R.drawable.send_gift_ic;
        } else if (itemId == ITEM_INVITE_GAME) {
            return R.drawable.invite_game_ic;
        } else {
            return R.drawable.shape_d8d8d8_size;
        }
    }

    public static String getName(int itemId) {
        if (itemId == ITEM_PHOTO) {
            return ResUtil.getStr(R.string.send_view_model_func_album);
        } else if (itemId == ITEM_CAMERA) {
            return ResUtil.getStr(R.string.send_view_model_func_camera);
        } else if (itemId == ITEM_RED_POCKET) {
            return ResUtil.getStr(R.string.send_view_model_func_rp);
        } else if (itemId == ITEM_SECRET_PHOTO) {
            return ResUtil.getStr(R.string.send_view_model_func_s_photo);
        } else if (itemId == ITEM_ID_CARD) {
            return ResUtil.getStr(R.string.send_view_model_func_n_card);
        } else if (itemId == ITEM_SEND_GIFT) {
            return ResUtil.getStr(R.string.send_gift);
        } else if (itemId == ITEM_FAMILY_BOX) {
            return ResUtil.getStr(R.string.send_view_box_name);
        } else if (itemId == ITEM_ALL_GIFT) {
            return ResUtil.getStr(R.string.send_all_gift);
        } else if (itemId == ITEM_VOICE_LINK) {
            return ResUtil.getString(R.string.voice_link_text);
        } else if (itemId == ITEM_INVITE_GAME) {
            return ResUtil.getString(R.string.prop_category_game);
        } else {
            return ResUtil.getStr(R.string.common_stay_tuned);
        }
    }

    public static List<Integer> gameRoomList = new ArrayList<>();
    public static List<Integer> fixRoomList = new ArrayList<>();
    public static List<Integer> groupChatList = new ArrayList<>();
    public static List<Integer> singleChatList = new ArrayList<>();
    public static List<Integer> singleChatMyselfList = new ArrayList<>();
    public static List<Integer> singleChatSystemUserList = new ArrayList<>();
    public static List<Integer> familyGroupChatList = new ArrayList<>();
    public static List<Integer> singleChatChattingHuawei = new ArrayList<>();
    static {
        gameRoomList.add(ITEM_PHOTO);
        gameRoomList.add(ITEM_CAMERA);
        gameRoomList.add(ITEM_SECRET_PHOTO);
        gameRoomList.add(ITEM_RED_POCKET);

        fixRoomList.add(ITEM_PHOTO);
        fixRoomList.add(ITEM_CAMERA);
        fixRoomList.add(ITEM_RED_POCKET);

        groupChatList.add(ITEM_PHOTO);
        groupChatList.add(ITEM_CAMERA);
        groupChatList.add(ITEM_RED_POCKET);
        groupChatList.add(ITEM_ALL_GIFT);
        groupChatList.add(ITEM_ID_CARD);

        singleChatList.add(ITEM_PHOTO);
        singleChatList.add(ITEM_CAMERA);
        singleChatList.add(ITEM_SECRET_PHOTO);
        singleChatList.add(ITEM_INVITE_GAME);
        singleChatList.add(ITEM_RED_POCKET);
        singleChatList.add(ITEM_ID_CARD);
        singleChatList.add(ITEM_VOICE_LINK);

        singleChatMyselfList.add(ITEM_PHOTO);
        singleChatMyselfList.add(ITEM_CAMERA);
        singleChatMyselfList.add(ITEM_SECRET_PHOTO);
        singleChatMyselfList.add(ITEM_INVITE_GAME);
        singleChatMyselfList.add(ITEM_RED_POCKET);
        singleChatMyselfList.add(ITEM_ID_CARD);

        singleChatSystemUserList.add(ITEM_PHOTO);
        singleChatSystemUserList.add(ITEM_CAMERA);
        singleChatSystemUserList.add(ITEM_SECRET_PHOTO);
        singleChatSystemUserList.add(ITEM_RED_POCKET);
        singleChatSystemUserList.add(ITEM_ID_CARD);

        familyGroupChatList.add(ITEM_PHOTO);
        familyGroupChatList.add(ITEM_CAMERA);
        familyGroupChatList.add(ITEM_RED_POCKET);
        familyGroupChatList.add(ITEM_FAMILY_BOX);
        familyGroupChatList.add(ITEM_ALL_GIFT);
        familyGroupChatList.add(ITEM_ID_CARD);

        singleChatChattingHuawei.add(ITEM_PHOTO);
        singleChatChattingHuawei.add(ITEM_CAMERA);
        singleChatChattingHuawei.add(ITEM_SECRET_PHOTO);
    }
}
