package com.wepie.wespy.module.chat.ui.group.interest

import android.os.Bundle
import android.widget.GridView
import android.widget.TextView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.configservice.ConfigHelper
import com.huiwan.constants.IntentConfig
import com.huiwan.user.UserListSimpleInfoCallback
import com.huiwan.user.UserService
import com.huiwan.user.entity.User
import com.huiwan.user.entity.UserSimpleInfo
import com.huiwan.widget.actionbar.BaseWpActionBar
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.group.GroupInfo
import com.wepie.wespy.module.chat.dataservice.group.GroupService
import com.wepie.wespy.module.chat.ui.adapter.GroupAdminGridAdapter

class GroupAdminManagerActivityNew : BaseActivity() {

    companion object {
        const val ADD_ADMIN = 1
        const val DELETE_ADMIN = 2
        const val GID = "gid"
        const val GROUP_ADMIN_ICON_WIDTH_UNIT_DP = 62
        const val numColumns = 5
    }
    private var gid = 0
    private lateinit var groupInfo: GroupInfo
    private val maxAdmin by lazy {
        ConfigHelper.getInstance().constConfig.getGroupLevelConfig(
            groupInfo.level
        )?.adminLimit ?: 0
    }
    private val mAdminManagerGrid: GridView by lazy { findViewById(R.id.group_admin_grid) }
    private val mAdminManagerBar: BaseWpActionBar by lazy { findViewById(R.id.admin_manager_bar) }
    private val mTvAdminI: TextView by lazy { findViewById(R.id.tv_admin_i) }
    private lateinit var adapter: GroupAdminGridAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        gid = intent.getIntExtra(GroupAdminManagerActivity.GID, 0)
        groupInfo = GroupService.getInstance().getGroupInfo(gid)
        setContentView(R.layout.activity_group_admin_manager_new)
        mTvAdminI.text = ResUtil.getStr(
            R.string.interest_group_setting_admin_i,
            groupInfo.adminList.size,
            maxAdmin
        )
        mAdminManagerBar.addTitleAndBack("")

        adapter = GroupAdminGridAdapter(this, gid,maxAdmin)
        mAdminManagerGrid.isFocusable = false
        mAdminManagerGrid.numColumns = numColumns
        mAdminManagerGrid.adapter = adapter
        refresh()
    }


    fun refresh() {
        mTvAdminI.text = ResUtil.getStr(
            R.string.interest_group_setting_admin_i,
            groupInfo.adminList.size,
            maxAdmin
        )
        GroupService.getInstance().updateGroupWithConversation(groupInfo)
        UserService.get()
            .getCacheSimpleUserList(groupInfo.adminList, object : UserListSimpleInfoCallback {
                override fun onUserInfoSuccess(userSimpleInfos: MutableList<UserSimpleInfo>?) {

                    val userList = UserService.simpleToUser(userSimpleInfos)
                    val adminInfo: MutableList<User> = ArrayList()
                    adminInfo.addAll(userList)

                    if (groupInfo.adminList.size < maxAdmin) {
                        val addUserInfo = User()
                        addUserInfo.uid = ADD_ADMIN
                        adminInfo.add(addUserInfo)
                    }
                    if (groupInfo.adminList.size > 0) {
                        val deleteAdminInfo = User()
                        deleteAdminInfo.uid = DELETE_ADMIN
                        adminInfo.add(deleteAdminInfo)
                    }

                    adapter.updateAdmin(adminInfo)
                }

                override fun onUserInfoFailed(description: String?) {}
            })
    }


    fun updateResult(list: List<Int>, type: Int) {
        if (type == IntentConfig.TYPE_GROUP_ADMIN) {
            groupInfo.addAllAdmin(list.toList())
        } else if (type == IntentConfig.TYPE_GROUP_ADMIN_DELETE) {
            for (integer in list.toList()) {
                groupInfo.removeAdmin(integer)
            }
        }
        refresh()
    }

    private fun getGridViewNumColumns(): Int {
        val width = ScreenUtil.getScreenWidthDp()
        return width / GROUP_ADMIN_ICON_WIDTH_UNIT_DP
    }
}