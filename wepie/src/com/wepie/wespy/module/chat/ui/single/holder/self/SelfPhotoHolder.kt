package com.wepie.wespy.module.chat.ui.single.holder.self

import android.view.View
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.WPMessage
import com.wepie.wespy.module.chat.gamemodel.MsgSendImageModel
import com.wepie.wespy.module.chat.ui.single.ChatAdapterNew
import com.wepie.wespy.module.chat.ui.single.holder.SelfCommonChatHolder
import com.wepie.wespy.module.game.game.activity.AdapterHolderUtil

class SelfPhotoHolder(view: View, val adapter: ChatAdapterNew) : SelfCommonChatHolder(view) {
    private val sendImgModel: MsgSendImageModel

    init {
        sendImgModel = view.findViewById(R.id.game_item_self_image_lay)
    }

    override fun bind(msg: WPMessage?, position: Int) {
        adapter.showTime(timeLay, time, iconPadding, msg, position)
        updateCommonView(position == adapter.itemCount - 1, adapter.mUser, adapter.chatMsgSource)
        sendImgModel.updateView(msg, AdapterHolderUtil.TYPE_CHAT_MSG)
        sendImgModel.setOnLongClickListener(adapter.ChatLongClickListener(position))
    }
}