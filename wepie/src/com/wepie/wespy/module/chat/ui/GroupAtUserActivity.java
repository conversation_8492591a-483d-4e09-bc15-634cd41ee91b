package com.wepie.wespy.module.chat.ui;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.user.entity.FriendInfo;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.module.chat.presenter.group.GroupAtPresenter;
import com.wepie.wespy.module.chat.ui.adapter.GroupAtAdapter;
import com.wepie.wespy.module.game.util.RecyclerViewSideBar;

import java.util.ArrayList;
import java.util.List;

public class GroupAtUserActivity extends BaseActivity implements GroupAtPresenter.IGroupAt {

    private GroupAtPresenter presenter;
    private RecyclerView mListView;
    private RecyclerViewSideBar mSideBar;
    private GroupAtAdapter mAdapter;
    private EditText searchEditText;
    private ProgressDialogUtil dialogUtil;
    private BaseWpActionBar actionBar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_group_at_user);

        dialogUtil = new ProgressDialogUtil();
        actionBar = findViewById(R.id.action_bar);
        mListView = findViewById(R.id.group_at_list_view);
        mSideBar = findViewById(R.id.friend_choose_list_bar);
        searchEditText = findViewById(R.id.group_at_search_bar);
        presenter = new GroupAtPresenter(this);
        mAdapter = new GroupAtAdapter(this, presenter);
        mListView.setLayoutManager(new LinearLayoutManager(this));
        mListView.setAdapter(mAdapter);

        ArrayList<Integer> uids = getIntent().getIntegerArrayListExtra(IntentConfig.UID_LIST);
        boolean canAtAll = getIntent().getBooleanExtra(IntentConfig.CAN_AT_ALL, false);
        presenter.setUidList(uids, canAtAll);

        actionBar.addTitleAndBack(ResUtil.getStr(R.string.group_at_user_title));

        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                presenter.addFilter(editable.toString());
            }
        });
    }

    @Override
    public void refreshFriendList(List<FriendInfo> friendInfoList, char[] chars) {
        if (friendInfoList.size() > 0) {
            mSideBar.setVisibility(View.VISIBLE);
            mSideBar.setRecycleView(mListView, chars);
        } else {
            mSideBar.setVisibility(View.GONE);
        }
        mAdapter.refreshList(friendInfoList);
    }

    @Override
    public void finish(int uid, String nickName) {
        if (!TextUtils.isEmpty(nickName)) {
            Intent intent = new Intent();
            intent.putExtra(IntentConfig.NICK_NAME, nickName);
            intent.putExtra(IntentConfig.UID, uid);
            setResult(Activity.RESULT_OK, intent);
        }
        finish();
    }

    @Override
    public void showLoading() {
        dialogUtil.showLoading(this, null, false);
    }

    @Override
    public void hideLoading() {
        dialogUtil.hideLoading();
    }
}
