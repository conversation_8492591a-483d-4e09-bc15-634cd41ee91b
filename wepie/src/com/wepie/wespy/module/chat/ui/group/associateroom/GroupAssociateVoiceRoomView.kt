package com.wepie.wespy.module.chat.ui.group.associateroom

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.TextUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.constants.GameType
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wejoy.weplay.ex.ILife
import com.wejoy.weplay.ex.cancellable.postAutoCancel
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.helper.imageLoader.HeadImageLoader
import com.wepie.wespy.model.entity.EnterRoomInfo
import com.wepie.wespy.model.entity.group.GroupInfo
import com.wepie.wespy.module.chat.dataservice.group.GroupService
import com.wepie.wespy.module.chat.ui.group.ParentVisibleByChildView
import com.wepie.wespy.module.common.jump.JumpRoomUtil
import com.wepie.wespy.module.makefriend.viewholder.VoiceRoomVideoViewHolder
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.GetRoomInfoBatchRsp
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender
import java.util.concurrent.TimeUnit

class GroupAssociateVoiceRoomView(
    context: Context,
    attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs) {
    private val associateRoomOnline: TextView
    private val associateRoomHead: ImageView
    private val associateRoomName: TextView
    private val associateRoomTag: TextView
    private val associateRoomLevel: ImageView
    private var isInit = false
    private var hasTrack = false

    init {
        View.inflate(context, R.layout.group_associate_voice_room_view, this)
        associateRoomOnline = findViewById(R.id.associate_room_online)
        associateRoomHead = findViewById(R.id.associate_room_head)
        associateRoomName = findViewById(R.id.associate_room_name)
        associateRoomTag = findViewById(R.id.associate_room_tag)
        associateRoomLevel = findViewById(R.id.associate_room_level)
    }

    fun init(life: ILife, gid: Int) {
        if (isInit) {
            return
        }
        isInit = true
        val syncInterval = ConfigHelper.getInstance().constConfig.syncInterval.toLong()
        waitRefresh(life, TimeUnit.SECONDS.toMillis(syncInterval), gid)
    }

    private fun waitRefresh(life: ILife, syncInterval: Long, gid: Int) {
        val groupInfo = GroupService.getInstance().getGroupInfo(gid)
        val rids = groupInfo.bindAdvRids
        if (rids.isNullOrEmpty()) {
            isVisible = false
            life.postAutoCancel(syncInterval) {
                waitRefresh(life, syncInterval, gid)
            }
        } else {
            refresh(life, groupInfo, rids) {
                life.postAutoCancel(syncInterval) {
                    waitRefresh(life, syncInterval, gid)
                }
            }
        }
    }

    private fun refresh(life: ILife, groupInfo: GroupInfo, rids: List<Int>, onFinish: () -> Unit) {
        VoiceRoomPacketSender.getRoomInfoByRids(rids, object : LifeSeqCallback(life) {
            override fun onSuccess(head: RspHeadInfo) {
                val voiceRoomList = (head.message as GetRoomInfoBatchRsp).voiceRoomListList
                var voiceRoom: TmpRoomPackets.VoiceRoom? = null
                val voiceRooms = voiceRoomList.filter {
                    !it.offline && !it.needPasswd && (it.roomType == 2 || it.roomType == 3)
                }
                if (voiceRooms.isNotEmpty()) {
                    val filter = voiceRooms.filter {
                        it.roomType == 3 // 主播房
                    }
                    voiceRoom = if (filter.isNotEmpty()) {
                        filter[0]
                    } else {
                        voiceRooms[0]
                    }
                }
                isVisible = voiceRoom != null
                if (voiceRoom != null) {
                    update(voiceRoom, groupInfo)
                }
                onFinish.invoke()
            }

            override fun onFail(head: RspHeadInfo?) {
                onFinish.invoke()
            }
        })
    }

    private fun update(voiceRoom: TmpRoomPackets.VoiceRoom, groupInfo: GroupInfo) {
        val trackMap = trackMap(voiceRoom, groupInfo)
        if (!hasTrack) {
            TrackUtil.appViewScreen(TrackScreenName.GROUP_VOICE_ROOM_IN_CHAT, trackMap)
            hasTrack = true
        }
        associateRoomName.text = voiceRoom.name
        WpImageLoader.load(
            voiceRoom.headImage,
            associateRoomHead,
            HeadImageLoader.genHeadLoadInfo()
        )
        VoiceRoomVideoViewHolder.updateLabelInfo(
            voiceRoom.labelType,
            voiceRoom.gameType,
            associateRoomTag
        )
        val config =
            ConfigHelper.getInstance().voiceRoomConfig.advancedRoomConfig.getConfigByLevel(voiceRoom.level)
        if (config != null && !TextUtil.isEmpty(config.iconUrl)) {
            WpImageLoader.load(config.iconUrl, associateRoomLevel)
        }
        associateRoomOnline.text = ResUtil.getStr(R.string.person_d_online, voiceRoom.userNum)
        isVisible = true
        setOnClickListener {
            val enterRoomInfo = EnterRoomInfo.buildEnterRoom(
                context, voiceRoom.rid, GameType.GAME_TYPE_VOICE_ROOM
            ).setSource(TrackSource.INTEREST_GROUP)
            JumpRoomUtil.getInstance().enterRoom(enterRoomInfo)
            TrackUtil.appClick(
                TrackScreenName.GROUP_VOICE_ROOM_IN_CHAT,
                TrackButtonName.GROUP_ENTER_VOICE_ROOM,
                trackMap
            )
        }
    }

    override fun setVisibility(visibility: Int) {
        super.setVisibility(visibility)
        (parent as? ParentVisibleByChildView)?.updateVisibility()
    }

    private fun trackMap(
        voiceRoom: TmpRoomPackets.VoiceRoom,
        groupInfo: GroupInfo
    ): MutableMap<String, Any> {
        val map = mutableMapOf<String, Any>()
        map["rid"] = voiceRoom.rid
        map["group_id"] = groupInfo.gid
        map["group_level"] = groupInfo.level
        map["group_type"] = groupInfo.category_id
        map["group_sub_type"] = groupInfo.sub_cate_id
        map["user_num"] = groupInfo.memberNum
        map["group_admin_cnt"] = groupInfo.adminList.size
        return map
    }
}