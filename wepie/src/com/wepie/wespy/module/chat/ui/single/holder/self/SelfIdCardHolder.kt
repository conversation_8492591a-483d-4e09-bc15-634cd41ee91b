package com.wepie.wespy.module.chat.ui.single.holder.self

import android.view.View
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.WPMessage
import com.wepie.wespy.module.chat.gamemodel.MsgSendIdCardModel
import com.wepie.wespy.module.chat.ui.single.ChatAdapterNew
import com.wepie.wespy.module.chat.ui.single.holder.SelfCommonChatHolder

class SelfIdCardHolder(view: View, val adapter: ChatAdapterNew) : SelfCommonChatHolder(view) {
    private val idCardModel: MsgSendIdCardModel

    init {
        idCardModel = view.findViewById(R.id.game_item_room_id_card_lay)
    }

    override fun bind(msg: WPMessage?, position: Int) {
        adapter.showTime(timeLay, time, iconPadding, msg, position)
        updateCommonView(position == adapter.itemCount - 1, adapter.mUser, adapter.chatMsgSource)
        idCardModel.updateView(msg, false)
        idCardModel.setOnLongClickListener(adapter.ChatLongClickListener(position))
    }
}