package com.wepie.wespy.module.chat.ui.item;

import android.content.Context;
import androidx.annotation.NonNull;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.wepie.wespy.R;
import com.huiwan.base.util.TouchEffectUtil;

/**
 * Created by geeksammao on 03/02/2018.
 */

public class UnrecognizedMsgItem extends FrameLayout {
    private FrameLayout contentLay;
    private TextView chatContentTv;

    public UnrecognizedMsgItem(@NonNull Context context) {
        super(context);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.unrecognized_item, this);
        TouchEffectUtil.addTouchEffect(this);
        initView();
    }

    private void initView() {
        chatContentTv = findViewById(R.id.content_tv);
    }
}
