package com.wepie.wespy.module.chat.ui.group.interest

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.ConstConfig.GroupCategory
import com.huiwan.libtcp.callback.SeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wepie.liblog.main.HLog
import com.wepie.wespy.net.tcp.packet.GroupPackets.RecommendGroupRsp
import com.wepie.wespy.net.tcp.packet.GroupPackets.SimpleGroupInfo
import com.wepie.wespy.net.tcp.sender.ChatPacketSenderNew

/**
 * 兴趣群聊创建 与Fragment共用数据源
 * create by HebeHan
 * 2023/5/4
 */
class InterestGroupMainViewModel : ViewModel() {

    private val TAG = "InterestGroupCreateViewModel"

    //当前选中的tab 默认第一个
    val selectIndex = MutableLiveData(-1)

    //子选项
    val selectSub = MutableLiveData(-1)

    var screenName = ""

    /**
     * 获取群聊创建配置数据 因为是从config拉取的,所以这里不用livedata做了
     *
     * @param index
     * @param type
     */
    fun getCreateConfig(): List<GroupCategory> {
        return ConfigHelper.getInstance().constConfig.groupCategoryList
    }

    /**
     * 更新Title的选中状态
     * @param index
     */
    fun changeSelectTitle(index: Int) {
        ConfigHelper.getInstance().constConfig.groupCategoryList.forEachIndexed { i, groupCategory ->
            groupCategory.isSelect = index == i
        }
        selectIndex.value = index
    }

    /**
     * 根据categoryID获取所在的index
     * @param category
     * @return
     */
    fun getCategoryIndex(category: Int): Int {
        val list = ConfigHelper.getInstance().constConfig.groupCategoryList
        for ((index, groupCategory) in list.withIndex()) {
            if (category == groupCategory.categoryId) {
                return index
            }
        }
        return 0
    }

    /**
     * 获取subtype的index
     *
     * @param category
     * @param subType
     * @return
     */
    fun getSubTypeIndex(category: Int, subType: Int): Int {
        val c = ConfigHelper.getInstance().constConfig.getGroupCateGory(category)
        c?.let {
            for ((index, s) in it.subList.withIndex()) {
                if (s.subCateId == subType) {
                    return index
                }
            }
        }
        return 0
    }

    /**
     * 改变子tab位置
     * @param subIndex
     */
    fun changeSub(subIndex: Int) {
        selectSub.value = subIndex
    }

    /**
     * 下拉刷新
     * @param category 大类型
     * @param type 小类型
     * @param callBack 回调
     * @receiver
     */
    fun refresh(
        category: Int, type: Int,
        callBack: (offset: Int, list: List<SimpleGroupInfo>) -> Unit
    ) {
        HLog.d(TAG, "refresh category = $category, type = $type offset=0")
        getRecommendGroup(category, type, 0, callBack)
    }

    /**
     * 加载更多
     * @param category 大类型
     * @param type 小类型
     * @param offset 服务器数据量累加值
     */
    fun loadMore(
        category: Int,
        type: Int,
        offset: Int,
        callBack: (offset: Int, list: List<SimpleGroupInfo>) -> Unit
    ) {
        HLog.d(TAG, "loadMore category = $category, type = $type offset=$offset")
        getRecommendGroup(category, type, offset, callBack)
    }

    /**
     * 获取兴趣群聊推荐列表
     *
     * @param category
     * @param type
     * @param offset
     * @param callBack
     * @receiver
     */
    private fun getRecommendGroup(
        category: Int,
        type: Int,
        offset: Int,
        callBack: (offset: Int, list: List<SimpleGroupInfo>) -> Unit
    ) {
        ChatPacketSenderNew.getInstance()
            .getRecommendGroupReq(category, type, offset, object : SeqCallback {
                override fun onSuccess(head: RspHeadInfo?) {
                    (head?.message as RecommendGroupRsp).let {
                        callBack(it.offset, it.listList)
                    }
                }

                override fun onFail(head: RspHeadInfo?) {
                    callBack(0, listOf())
                    ToastUtil.show(head?.desc)
                }
            })
    }
}