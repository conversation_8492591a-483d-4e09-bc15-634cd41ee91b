package com.wepie.wespy.module.chat.ui.item;

import android.content.Context;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.component.gift.send.ComboInfo;
import com.huiwan.component.gift.send.MulGiftInfo;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.component.prop.ChatMsgBubbleItem;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.FLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.view.CenterAlignImageSpan;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.GroupChatMsg;
import com.wepie.wespy.model.event.GiftAnimPlayedEvent;
import com.wepie.wespy.model.event.GiftComboEvent;
import com.wepie.wespy.module.chat.gamemodel.MsgTextHelper;
import com.wepie.wespy.module.gift.GiftInfoTransformHelper;
import com.wepie.wespy.module.voiceroom.util.view.gifts.AllGiftDialog;

import org.greenrobot.eventbus.EventBus;

public class MultiGiftItem extends ConstraintLayout {
    private TextView giftDescTv;
    private TextView giftTitleTv;
    private TextView giftCoinTv;
    private View giftDivider;
    private ChatMsgBubbleItem bubbleItem;
    private ImageView giftIcon;

    private View giftExtraDivider;
    private TextView giftExtraReturnTv;
    private View giftLay;
    private boolean isSelf;

    public MultiGiftItem(@NonNull Context context) {
        super(context);
        initView();
    }

    private void initView() {
        if (isSelf) {
            inflate(getContext(), R.layout.chat_mul_gift_item, this);
        } else {
            inflate(getContext(), R.layout.chat_friend_mul_gift_item, this);
        }
        giftLay = findViewById(R.id.gift_lay);
        giftDescTv = (TextView) findViewById(R.id.msg_content_tv);
        giftTitleTv = findViewById(R.id.gift_title_tv);
        giftCoinTv = (TextView) findViewById(R.id.gift_coin_tv);
        giftDivider = findViewById(R.id.gift_div_view);
        bubbleItem = findViewById(R.id.bubble_item);
        giftIcon = findViewById(R.id.gift_icon);
        giftExtraDivider = findViewById(R.id.gift_extra_div_view);
        giftExtraReturnTv = findViewById(R.id.gift_extra_return_coin_tv);
    }

    public MultiGiftItem(Context context, boolean isSelf) {
        this(context, null, isSelf);
    }

    public MultiGiftItem(Context context, @Nullable AttributeSet attrs, boolean isSelf) {
        super(context, attrs);
        this.isSelf = isSelf;
        initView();
    }

    public void bind(final GroupChatMsg msg, OnLongClickListener listener) {
        final MulGiftInfo giftInfo = GroupChatMsg.getMulGiftInfoFromExt(msg);
        refreshContent(msg, giftInfo, listener);
    }

    private void refreshContent(ChatMsg chatMsg, final MulGiftInfo giftInfo, OnLongClickListener listener) {
        setGiftLayoutDirection();
        giftLay.setOnLongClickListener(listener);
        giftCoinTv.setOnClickListener(v -> AllGiftDialog.show(v.getContext(), giftInfo));
        bubbleItem.showBubble(chatMsg.getBubbleId(), isSelf);
        showGiftIcon(chatMsg.getGiftId());

        giftDivider.setVisibility(VISIBLE);
        giftDivider.setBackgroundColor(GiftCardUtil.getGiftDividerColor(bubbleItem, chatMsg.getBubbleId(), isSelf));
        giftDescTv.setTextColor(bubbleItem.getGiftTipColor());
        giftTitleTv.setTextColor(bubbleItem.getTextColor());

        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftInfo.giftId);
        String giftName = gift != null ? gift.getName() : ResUtil.getStr(R.string.unidentified_gift);
        String giftUnit = gift != null ? gift.getUnit() : " ";
        String receiver = giftInfo.isGroup() ? ResUtil.getResource().getString(R.string.group_mul_gift_mic_1) : (giftInfo.isFamily() ? ResUtil.getResource().getString(R.string.family_mul_gift_mic) : ResUtil.getStr(R.string.unknown_person));
        int totalNum = giftInfo.giftNum * giftInfo.list.size();
        try {
            String sendTip = ResUtil.getQuantityStr(R.plurals.gift_common_send_tip_G_new, 2, receiver, totalNum, giftUnit, giftName);
            giftTitleTv.setText(sendTip);
            giftDescTv.setText(chatMsg.getContent());
            giftCoinTv.setTextColor(bubbleItem.getGiftDescColor());
            giftExtraReturnTv.setTextColor(bubbleItem.getGiftTipColor());
            SpannableStringBuilder ssb = new SpannableStringBuilder("");
            if (!TextUtils.isEmpty(giftInfo.totalDesc)) {
                // () 占位
                String recTip = ResUtil.getResource().getString(R.string.gift_total_receive_tip_new, giftInfo.totalDesc);
                int index = recTip.indexOf(giftInfo.totalDesc);
                ssb.append(recTip);
                try {
                    if (index >= 0) {
                        ssb.setSpan(new ForegroundColorSpan(bubbleItem.getGiftTipColor()), 0, index, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                        String detail = ResUtil.getResource().getString(R.string.detail);
                        ssb.append(" ");
                        ssb.append(detail);
                        ssb.setSpan(new ForegroundColorSpan(bubbleItem.getGiftDescColor()), index, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                    } else {
                        ssb.append(recTip);
                    }

                    Drawable drawable = ContextCompat.getDrawable(getContext(), R.drawable.forward_icon_new);
                    if (drawable != null) {
                        drawable.mutate();
                        drawable.setAutoMirrored(true);
                        drawable.setColorFilter(bubbleItem.getGiftDescColor(), PorterDuff.Mode.SRC_ATOP);
                        drawable.setBounds(0, 0, ScreenUtil.dip2px(12), ScreenUtil.dip2px(12));
                    }
                    ssb.append("  ");
                    ImageSpan imageSpan = new CenterAlignImageSpan(drawable);
                    ssb.setSpan(imageSpan, ssb.length() - 2, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                } catch (Exception e) {
                    FLog.e(e);
                }
            }
            giftCoinTv.setText(ssb);
            int extraReturnCoin = chatMsg.getGiftTotalExtraReturnCoinFromExtension();
            if (extraReturnCoin > 0) {
                giftExtraDivider.setVisibility(VISIBLE);
                giftExtraReturnTv.setVisibility(VISIBLE);
                MsgTextHelper.updateGiftExtraReturnCoin(giftExtraReturnTv, extraReturnCoin, bubbleItem.getGiftDescColor());
            } else {
                giftExtraDivider.setVisibility(GONE);
                giftExtraReturnTv.setVisibility(GONE);
            }

            if (chatMsg.getStatus() == ChatMsg.STATUS_OK) {
                chatMsg.setStatus(ChatMsg.STATUS_VIEWED);
                GiftShowInfo showInfo = GiftInfoTransformHelper.fromMulGiftInfo(giftInfo);

                EventBus.getDefault().post(new GiftAnimPlayedEvent(showInfo, chatMsg));
                ComboInfo comboInfo = new ComboInfo();
                comboInfo.setType(ComboInfo.TYPE_ALL_GIFT);
                comboInfo.setMulGiftInfo(giftInfo);
                MulGiftInfo.updateMultiCombo(giftInfo, comboInfo);
                EventBus.getDefault().post(new GiftComboEvent(comboInfo));
            }
        } catch (Exception e) {
            FLog.e(e);
        }
    }

    private void showGiftIcon(int giftId) {
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftId);
        if (gift == null) return;
        String url = gift.getMedia_url();
        if (TextUtils.isEmpty(url)) return;
        giftIcon.setVisibility(VISIBLE);
        WpImageLoader.load(url, giftIcon, ImageLoadInfo.newGiftInfo());
    }

    private void setGiftLayoutDirection() {
        if (ScreenUtil.isRtl()) {
            giftLay.setLayoutDirection(LAYOUT_DIRECTION_RTL);
        } else {
            giftLay.setLayoutDirection(LAYOUT_DIRECTION_LTR);
        }
    }
}
