package com.wepie.wespy.module.lookme.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.StyleSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.ColorInt;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.widget.CustomCircleImageView;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.VisitorPageInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.lookme.RoundedBackgroundSpan;
import com.wepie.wespy.module.lookme.activity.VisitorManager;

import java.util.List;

/**
 * Created by dsc on 2020/3/5.
 */
public class VisitorSimpleHeadView extends FrameLayout {

    private final Context mContext;
    private LinearLayout noGuestLayout;
    private LinearLayout guestLayout;
    private TextView noGuestNameTv;
    private TextView guestNameTv;
    private CustomCircleImageView headImageOne;
    private CustomCircleImageView headImageTwo;
    private CustomCircleImageView headImageThree;
    private TextView allGuestTv;
    private TextView newGuestTv;
    private TextView otherSexTv;
    private TextView noGuestTv;
    private BlurImageManager blurImageManager;


    public VisitorSimpleHeadView(Context context) {
        super(context);
        mContext = context;
        init();
    }


    public VisitorSimpleHeadView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.visitor_simple_head_view, this);
        noGuestLayout = findViewById(R.id.visitor_simple_head_no_lay);
        guestLayout = findViewById(R.id.visitor_simple_head_lay);
        TextView buyNoGuestTv = findViewById(R.id.visitor_simple_head_view_tv);
        TextView buyGuestTv = findViewById(R.id.visitor_simple_head_no_view_tv);
        guestNameTv = findViewById(R.id.visitor_simple_head_name);
        noGuestNameTv = findViewById(R.id.visitor_simple_head_no_name);
        headImageOne = findViewById(R.id.visitor_simple_head_guest_one_image);
        headImageTwo = findViewById(R.id.visitor_simple_head_guest_two_image);
        headImageThree = findViewById(R.id.visitor_simple_head_guest_three_image);
        allGuestTv = findViewById(R.id.total_visitor_desc_tv);
        newGuestTv = findViewById(R.id.new_visitor_desc_tv);
        otherSexTv = findViewById(R.id.other_sex_visitor_desc_tv);
        noGuestTv = findViewById(R.id.visitor_simple_head_no_tv);

        buyNoGuestTv.setOnClickListener(v -> JumpUtil.gotoShopActivityWithIndex(getContext(), PropItem.CATEGORY_ITEM));
        buyGuestTv.setOnClickListener(v -> JumpUtil.gotoShopActivityWithIndex(getContext(), PropItem.CATEGORY_ITEM));
        headImageOne.setWhiteHeadBorder(4);
        headImageTwo.setWhiteHeadBorder(4);
        headImageThree.setWhiteHeadBorder(4);

        blurImageManager = new BlurImageManager();
        updateServerData();
    }


    private void updateServerData() {
        VisitorManager.getInstance().getServerVisitorList(0, new LifeDataCallback<VisitorPageInfo>(this) {
            @Override
            public void onSuccess(Result<VisitorPageInfo> result) {
                if (result.data != null) {
                    update(result.data.latestVisitorPics, result.data.totalVisitorNum, result.data.newVisitorNum, result.data.oppositeSexNum);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private void update(List<String> images, int allGuest, int newGuest, int otherSex) {
        if (images == null || images.size() <= 0 || allGuest == 0) {
            guestLayout.setVisibility(GONE);
            noGuestLayout.setVisibility(VISIBLE);
            handleNoGuest(allGuest);
        } else {
            noGuestLayout.setVisibility(GONE);
            guestLayout.setVisibility(VISIBLE);
            handleGuest(images, allGuest, newGuest, otherSex);
        }
    }

    private void handleGuest(List<String> images, int allGuest, int newGuest, int otherSex) {
        guestNameTv.setText(ResUtil.getStr(R.string.hello_nick, UserService.get().getLoginUser().getNickname()));
        addHeadImage(images);
        addText(allGuest, newGuest, otherSex);
    }

    private void setGuestText(TextView textView, String text, String count, @ColorInt int color) {
        setGuestText(textView, text, count, color, null);
    }

    private void setGuestText(TextView textView, String text, String count, @ColorInt int color, RoundedBackgroundSpan.Background background) {
        SpannableStringBuilder ssb = new SpannableStringBuilder(text);
        int start = text.indexOf(count);

        if (start >= 0) {
            int textSize = ScreenUtil.dip2px(16F);
            if (background != null) {
                background.setPosition(start, start + count.length());
            }
            RoundedBackgroundSpan.Builder builder = new RoundedBackgroundSpan.Builder();
            builder.setTextColor(color)
                    .setTextSize(textSize)
                    .setBackground(background);
            ssb.setSpan(builder.build(), start, start + count.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            ssb.setSpan(new StyleSpan(Typeface.BOLD), start, start + count.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        textView.setText(ssb);
    }

    private void addText(int allGuest, int newGuest, int otherSex) {
        setGuestText(allGuestTv, ResUtil.getQuantityStr(R.plurals.total_visitor_desc, allGuest, allGuest),
                String.valueOf(allGuest), ResUtil.getColor(R.color.color_accent));
        int bgColor = Color.parseColor("#FFF1D3");
        int dp4 = ScreenUtil.dip2px(4F);
        int color = Color.parseColor("#FE7500");
        if (newGuest == 0) {
            newGuestTv.setVisibility(INVISIBLE);
        } else {
            newGuestTv.setVisibility(VISIBLE);
            String s = " " + newGuest + " ";
            setGuestText(newGuestTv, ResUtil.getStr(R.string.recently_visitor_desc, s),
                    s, color, new RoundedBackgroundSpan.ColorBackground(bgColor, dp4));

        }
        if (otherSex == 0) {
            otherSexTv.setVisibility(INVISIBLE);
        } else {
            otherSexTv.setVisibility(VISIBLE);
            String s = " " + otherSex + " ";
            int pluralsId = LoginHelper.getGender() == 2 ? R.plurals.boy_visitor_desc : R.plurals.girl_visitor_desc;
            setGuestText(otherSexTv, ResUtil.getQuantityStr(pluralsId, otherSex, s), s, color,
                    new RoundedBackgroundSpan.ColorBackground(bgColor, dp4));
        }
    }

    private void addHeadImage(List<String> images) {
        int size = images.size();
        if (size == 1) {
            loadImage(images.get(0), headImageOne);
            headImageTwo.setVisibility(GONE);
            headImageThree.setVisibility(GONE);
        } else if (size == 2) {
            loadImage(images.get(0), headImageOne);
            headImageTwo.setVisibility(VISIBLE);
            loadImage(images.get(1), headImageTwo);
            headImageThree.setVisibility(GONE);
        } else {
            loadImage(images.get(0), headImageOne);
            headImageTwo.setVisibility(VISIBLE);
            loadImage(images.get(1), headImageTwo);
            headImageThree.setVisibility(VISIBLE);
            loadImage(images.get(2), headImageThree);
        }
    }

    private void handleNoGuest(int allGuest) {
        noGuestNameTv.setText(ResUtil.getStr(R.string.hello_nick, LoginHelper.getNickname()));
        if (allGuest > 0) {
            noGuestTv.setText(ResUtil.getQuantityStr(R.plurals.visitors_total_tip, allGuest, allGuest));
        } else {
            noGuestTv.setText(R.string.visitors_non);
        }
    }

    private void loadImage(String url, final CustomCircleImageView imgView) {
        ImageLoadInfo loadInfo = ImageLoadInfo.newInfo().owner(imgView).disableAnim();
        WpImageLoader.load(url, null, loadInfo, new WpImageLoadListener<String>() {
            @Override
            public boolean onComplete(String model, Drawable data) {
                if (data instanceof BitmapDrawable) {
                    Bitmap bitmap = ((BitmapDrawable) data).getBitmap();
                    blurImageManager.blurImages(bitmap, mContext, imgView::setImageDrawable);
                    return true;
                }
                return false;
            }

            @Override
            public boolean onFailed(String model, Exception e) {
                return false;
            }
        });
    }

}