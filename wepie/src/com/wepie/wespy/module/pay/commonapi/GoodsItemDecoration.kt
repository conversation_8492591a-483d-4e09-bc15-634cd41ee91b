package com.wepie.wespy.module.pay.commonapi

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ViewUtil
import com.wepie.wespy.R

class GoodsItemDecoration : RecyclerView.ItemDecoration() {
    private val isRtl = ScreenUtil.isRtl()

    override fun getItemOffsets(
        outRect: Rect, view: View,
        parent: RecyclerView, state: RecyclerView.State
    ) {
        if (parent.layoutManager is GridLayoutManager) {
            val manager = parent.layoutManager as GridLayoutManager
            val spanCount = manager.spanCount
            val itemTargetWidth = ScreenUtil.getScreenWidthWithPortrait() / spanCount

            val itemPosition = parent.getChildAdapterPosition(view)
            val column = itemPosition % spanCount
            val itemWidth = ViewUtil.getDimen(R.dimen.coin_item_width)

            val space: Int
            when (column) {
                0 -> {
                    space = itemTargetWidth - itemWidth
                    outRect.left = 0
                    outRect.right = space
                }

                spanCount - 1 -> {
                    space = itemTargetWidth - itemWidth
                    outRect.left = space
                    outRect.right = 0
                }

                else -> {
                    space = (itemTargetWidth - itemWidth) / 2
                    outRect.left = space
                    outRect.right = space
                }
            }
            if (isRtl) {
                val temp = outRect.left
                outRect.left = outRect.right
                outRect.right = temp
            }
        } else {
            super.getItemOffsets(outRect, view, parent, state)
        }
    }
}