package com.wepie.wespy.module.advertisement;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.lifecycle.FullLifecycleObserverAdapter;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.widget.SimpleVideoView;
import com.wepie.lib.api.plugins.track.config.cn.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.liblog.main.HLog;
import com.wepie.skynet.apm.Apm;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.common.jump.JumpUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AdvertisementActivity extends BaseActivity {
    private SimpleVideoView simpleVideoView;
    private ImageView advertIv, advertIcon;
    private ConstraintLayout rootView;
    private ConstraintLayout skipBtn;
    private TextView timeTv;
    private int position = 0;
    private int playtime = 10000;
    private boolean isExit = false;//标记当前activity是否在前台
    private CountDownTimer countDownTimer;
    private List<AdvertisementEntity> advertisementEntityList = new ArrayList<>();
    AdvertisementEntity advertisementEntity;
    private TextView nameTitleTv, decTv, adTv;
    private ConstraintLayout decLay;
    private static final String TAG = "AdvertisementActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Apm.recordPeriod("AdvertisementActivity#onCreate", true);
        super.onCreate(savedInstanceState);
        StatusBarUtil.initStatusBar(this, false);
        setContentView(R.layout.activity_advertisement);
        advertisementEntityList = AdvertiseManager.instance.getAdListNeedShow();
        simpleVideoView = findViewById(R.id.advert_avv);
        advertIv = findViewById(R.id.advert_iv);
        skipBtn = findViewById(R.id.advert_close_cl);
        rootView = findViewById(R.id.root_view);
        timeTv = findViewById(R.id.advert_time_tv);
        advertIcon = findViewById(R.id.advert_icon_iv);
        nameTitleTv = findViewById(R.id.advert_appname_tv);
        decTv = findViewById(R.id.advert_dec_tv);
        adTv = findViewById(R.id.advert_ad_tv);
        decLay = findViewById(R.id.advert_dec_lay);

        initView();
        Apm.recordPeriod("AdvertisementActivity#onCreate", false);
    }

    private void initView() {
        if (advertisementEntityList.isEmpty()) {
            HLog.d(TAG, HLog.USR, "initView! gotoMainActivity");
            gotoMainActivity();
            finish();
            return;
        }
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) skipBtn.getLayoutParams();
        layoutParams.topMargin = ScreenUtil.getStatusBarHeight();
        skipBtn.setLayoutParams(layoutParams);

        Collections.sort(advertisementEntityList);
        advertisementEntity = advertisementEntityList.get(0);
        initBar(advertisementEntity);
        AdvertiseManager.instance.saveViewedAdvert(advertisementEntity);
        Map<String, Object> map = new HashMap<>();
        map.put("advertId", advertisementEntity.getAdId());
        ShenceEvent.appViewScreen(TrackScreenName.ADVERT_SHOW, map);
        File file = new File(advertisementEntity.getAdFileName());
        playtime = advertisementEntity.getShowTime() * 1000;
        if (advertisementEntity.isImage()) {
            advertIv.setVisibility(View.VISIBLE);
            advertIv.setImageURI(Uri.fromFile(file));
            setTimer();
        } else if (advertisementEntity.isVideo()) {
            initVideo(file);
            simpleVideoView.setVisibility(View.VISIBLE);
        }
        rootView.setOnClickListener(v -> {
            if (!TextUtil.isEmpty(advertisementEntity.getWespydeeplink())) {
                ShenceEvent.appClick(TrackScreenName.ADVERT_CLICK, map);
                gotoMainActivity();
                JumpCommon.gotoOtherPager(getContext(), advertisementEntity.getWespydeeplink(), TrackSource.ADVERTISEMENT_PAGE, null);
            }
        });
        skipBtn.setOnClickListener(v -> gotoMainActivity());
    }

    private void initBar(AdvertisementEntity advertisementEntity) {
        if (!advertisementEntity.isBarIsShow()) {
            decLay.setVisibility(View.GONE);
            return;
        }
        if (TextUtil.isEmpty(advertisementEntity.getBarIconImg())) {
            advertIcon.setVisibility(View.GONE);
        } else {
            ImageLoaderUtil.loadNormalImage(advertisementEntity.getBarIconImg(), advertIcon);
        }
        if (TextUtil.isEmpty(advertisementEntity.getBarIconName())) {
            nameTitleTv.setVisibility(View.GONE);
        } else {
            nameTitleTv.setText(advertisementEntity.getBarIconName());
        }
        if (TextUtil.isEmpty(advertisementEntity.getBarSlogan())) {
            decTv.setVisibility(View.GONE);
        } else {
            decTv.setText(advertisementEntity.getBarSlogan());
        }
        if (TextUtil.isEmpty(advertisementEntity.getBarAdName())) {
            adTv.setVisibility(View.GONE);
        } else {
            adTv.setText(advertisementEntity.getBarAdName());
        }
        decLay.setBackgroundColor(advertisementEntity.getBarColor());
    }

    @SuppressLint("SetTextI18n")
    private void initVideo(File file) {
        timeTv.setText(String.valueOf(playtime / 1000));
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) simpleVideoView.getLayoutParams();
        int sWidth = ScreenUtil.getScreenWidth();
        int sHeight = ScreenUtil.getRealHeight(this);
        int vHeigh = advertisementEntity.getHeight();
        int vWidth = advertisementEntity.getWidth();
        if (sWidth / sHeight > vWidth / vHeigh) {
            sHeight = vHeigh * sWidth / vWidth;
        } else {
            sWidth = vWidth * sHeight / vHeigh;
        }
        layoutParams.width = sWidth;
        layoutParams.height = sHeight;
        simpleVideoView.setLayoutParams(layoutParams);
        simpleVideoView.setLooping(false);
        simpleVideoView.setSource(file.getAbsolutePath());
        simpleVideoView.setNeedReplay(true);
        simpleVideoView.startPlay();
        simpleVideoView.setCallback(new SimpleVideoView.Callback() {
            @Override
            public void onStart() {
                if (position < playtime && position != 0) {
                    simpleVideoView.setPlayingTime(position);
                }
                setTimer();
            }
        });
        getLifecycle().addObserver(new FullLifecycleObserverAdapter(simpleVideoView));
    }

    /**
     * 设置跳转的计时器
     */
    private void setTimer() {
        int interval = playtime - position;
        HLog.d(TAG, HLog.USR, "setTimer interval=" + interval);
        if (countDownTimer == null) {
            countDownTimer = new CountDownTimer(interval, 1000) {
                @SuppressLint("SetTextI18n")
                @Override
                public void onTick(long millisUntilFinished) {
                    timeTv.setText(String.valueOf(millisUntilFinished / 1000));
                    position += 1000;
                }

                @Override
                public void onFinish() {
                    if (!isExit) {
                        gotoMainActivity();
                    }
                }
            };
            countDownTimer.start();
        }
    }

    public void gotoMainActivity() {
        Apm.recordTimePoint("AdvertisementActivity#gotoMainActivity");
        JumpUtil.gotoMainActivity(getContext());//push后在MainActivity分发
        finish();
    }

    private Context getContext() {
        return this;
    }


    @Override
    protected void onResume() {
        if (position != 0) {
            if (position >= playtime) {
                gotoMainActivity();
            }
        }
        isExit = false;
        super.onResume();
        Apm.recordTimePoint("AdvertisementActivity#onResume");
    }

    @Override
    protected void onPause() {
        super.onPause();
        isExit = true;
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }

    /**
     * 屏蔽返回按键
     */
    @Override
    public void onBackPressed() {

    }

    /**
     * 屏蔽语音房的悬浮组件
     *
     * @return 0
     */
    @Override
    public int supportFloatView() {
        return 0;
    }

}