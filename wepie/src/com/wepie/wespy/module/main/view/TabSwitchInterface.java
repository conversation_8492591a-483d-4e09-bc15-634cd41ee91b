package com.wepie.wespy.module.main.view;

import android.view.View;

import androidx.annotation.IdRes;

import com.wepie.wespy.module.main.tab.OnTabSelectListener;

/**
 * Created by <PERSON><PERSON> on 12/1/20.
 */
public interface TabSwitchInterface {
    void initColor(int parseColor, int parseColor1);

    void initRes(int[] normalRes, int[] selectedRes, String[] assertAnimArray, String[] trackSouceName);

    void initView(String[] tabTitle, int visibleCount);

    void registerOnTabSelectListener(OnTabSelectListener onTabSelectListener);

    void setSelectView(int realNum);

    void setHomeTab(boolean showReturnTop);

    void setTabTitle(int i, String s);

    void setCellVisible(int i, int gone);

    void showSmallDot(int adjustTabIndex, boolean b);

    void showBigDot(int adjustTabIndex, int num);

    View getView();

    void setId(@IdRes int id);

    int getId();

    void showTabTips(int index);

    void hideTabTips(int index);

    void setCallback(Callback callback);

    interface Callback {
        void onShow(int x);

        void onHide();
    }
}
