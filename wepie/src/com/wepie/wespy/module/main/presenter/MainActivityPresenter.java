package com.wepie.wespy.module.main.presenter;

import static com.wepie.wespy.model.entity.GroupChatMsg.AT_NO;

import android.app.Activity;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ApplicationUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.WejoyUserConfigHelp;
import com.huiwan.constants.GameType;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.IapApi;
import com.huiwan.libtcp.callback.DefaultSeqCallback;
import com.huiwan.libtcp.config.ChatPacketConstants;
import com.huiwan.module.webview.SimpleWebViewInterface;
import com.huiwan.module.webview.WespyWebView;
import com.huiwan.store.database.WPStore;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.voiceservice.AgoraJoinChannelSuccessEvent;
import com.huiwan.voiceservice.VoiceManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.helper.DeepLinkRedDotCheckUtilsKt;
import com.wepie.lib.api.plugins.voice.BaseStreamFilter;
import com.wepie.lib.api.plugins.voice.BaseVoiceManager;
import com.wepie.lib.api.plugins.voice.ChannelStream;
import com.wepie.lib.api.plugins.voice.VoiceConfig;
import com.wepie.liblog.main.HLog;
import com.wepie.webview.WPWebView;
import com.wepie.wespy.R;
import com.wepie.wespy.base.VoiceCheckerKt;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.consts.DownloadSourceUtil;
import com.wepie.wespy.consts.roomutil.RoomUtilManager;
import com.wepie.wespy.helper.bgmusic.BgMusicManager;
import com.wepie.wespy.helper.bgmusic.MusicSource;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.notice.NoticeHelper;
import com.wepie.wespy.helper.redDotHelper.RedDotNode;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.helper.update.WPUpdateUtil;
import com.wepie.wespy.model.chat.event.NewFriendEvent;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.ConversationInfo;
import com.wepie.wespy.model.entity.CouponInfo;
import com.wepie.wespy.model.entity.GameStateInfo;
import com.wepie.wespy.model.entity.GroupChatMsg;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.entity.group.GroupInfo;
import com.wepie.wespy.model.entity.marry.WeddingInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.BgMusicEvent;
import com.wepie.wespy.model.event.ChangeMicStatus;
import com.wepie.wespy.model.event.ExitRoomEvent;
import com.wepie.wespy.model.event.FirstChargeBuyEvent;
import com.wepie.wespy.model.event.JoinChannelEvent;
import com.wepie.wespy.model.event.LeaveChannelEvent;
import com.wepie.wespy.model.event.NodeJsGameMsgChangeEvent;
import com.wepie.wespy.model.event.ProtoMessageEvent;
import com.wepie.wespy.model.event.PushChatMsgEvent;
import com.wepie.wespy.model.event.RedDotUpdateEvent;
import com.wepie.wespy.model.event.ShowTabReddotEvent;
import com.wepie.wespy.model.event.chat.GroupVideoBlockMsgEvent;
import com.wepie.wespy.model.event.chat.PushNewFriend;
import com.wepie.wespy.model.event.chat.RecallGroupMsgEvent;
import com.wepie.wespy.model.event.other.ChangeTabEvent;
import com.wepie.wespy.model.event.other.CircleRemindNumChangeEvent;
import com.wepie.wespy.model.event.other.DeepLinkEvent;
import com.wepie.wespy.model.event.other.MarryRemainEvent;
import com.wepie.wespy.model.event.other.SidErrorEvent;
import com.wepie.wespy.module.chat.conversation.ConversationManager;
import com.wepie.wespy.module.chat.dataservice.group.GroupService;
import com.wepie.wespy.module.chat.presenter.CurrentChatHelper;
import com.wepie.wespy.module.chat.send.face.EmoticonHelper;
import com.wepie.wespy.module.contact.manager.AddFriendManager;
import com.wepie.wespy.module.family.FamilyManager;
import com.wepie.wespy.module.login.helper.LogoutHelper;
import com.wepie.wespy.module.main.MainActivity;
import com.wepie.wespy.module.main.manage.MainActivityDeepLinkManager;
import com.wepie.wespy.module.main.manage.MainDialogManage;
import com.wepie.wespy.module.main.util.MarketRewardUtil;
import com.wepie.wespy.module.marry.MarryRedDot;
import com.wepie.wespy.module.marry.wedding.WeddingRoomSyncUtil;
import com.wepie.wespy.module.marry.wedding.WeddingSoundUtil;
import com.wepie.wespy.module.media.VoiceConfigHelper;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.ChangeHomeTabEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.dataservice.event.SyncWeddingInfoEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.UserEndWeddingEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.VoiceRoomInviteDrawEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.VoiceRoomInviteSpeakEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.WeddingDialogEvent;
import com.wepie.wespy.module.voiceroom.invite.sit.VoiceRoomInviteActivityTask;
import com.wepie.wespy.module.voiceroom.main.BaseVoiceRoomActivity;
import com.wepie.wespy.module.voiceroom.music.VoiceMusicManager;
import com.wepie.wespy.module.voiceroom.util.VoiceMuteManager;
import com.wepie.wespy.module.voiceroom.util.VoiceRoomNotifyService;
import com.wepie.wespy.net.http.api.CouponApi;
import com.wepie.wespy.net.http.api.GameApi;
import com.wepie.wespy.net.tcp.packet.ChatPackets;
import com.wepie.wespy.net.tcp.sender.MarryPacketSender;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

public class MainActivityPresenter {

    private final MainActivity activity;
    private final Handler handler = new Handler(Looper.getMainLooper());
    //    private boolean mHasApkUpdate;
    private final MainActivityDeepLinkManager manager = new MainActivityDeepLinkManager();
    private static final String TAG = "MainActivityPresenter";
    private static final int TOTAL_RECOVER_NUM = 2;
    private int checkedGameRecoverNum = 0;
    private boolean isCheckRecoverGame = false;
    private boolean hasRecoverGame = false;

    private BaseVoiceManager.IStreamFilter filter;

    public MainActivityPresenter(MainActivity activity) {
        this.activity = activity;
        ConversationManager.getInstance(); // 提前初始化消息，消息列表一些异步消息会导致白屏问题
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerDeepLink(DeepLinkEvent event) {
        if (checkedGameRecoverNum >= TOTAL_RECOVER_NUM && !hasRecoverGame) {
            manager.handlerDeepLink(activity, "event");
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void handlerChangeTab(ChangeTabEvent event) {
        activity.changeTab(event.tabNum, event.subIndex, event.roomScene, event.getJumpDetail());
        EventBus.getDefault().removeStickyEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerProtoMessageEvent(ProtoMessageEvent event) {
        if (ChatPacketConstants.CHAT_RS_GAIN_COIN.equals(event.action)) {
            ChatPackets.chat_rs_util_gainCoin msg = (ChatPackets.chat_rs_util_gainCoin) event.message;
            if (msg.getCode() != 200) {
                ToastUtil.show(msg.getDesc());
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerSidError(SidErrorEvent errorEvent) {
        if (LoginHelper.isLogin()) {
            LogoutHelper.onLogout(errorEvent.msg);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerCircleRemindNumChange(CircleRemindNumChangeEvent event) {
        //checkMsgNum
        checkShowMsgNum();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerCircleRemindNumChange(NodeJsGameMsgChangeEvent event) {
        //checkMsgNum
        checkShowMsgNum();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPushNewFriend(PushNewFriend event) {
        //checkMsgNum
        checkShowMsgNum();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerChurchRemind(MarryRemainEvent event) {
        //checkMsgNum
        checkShowMsgNum();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onBgMusicChange(BgMusicEvent event) {
        BgMusicManager.getInstance().checkToPlay(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onJoinChannel(JoinChannelEvent event) {
        if (!event.roomInfo.isOnline) {
            VoiceManager.getInstance().leaveChannel();
            return;
        }
        if (event.source != JoinChannelEvent.SRC_FROM_CLOSE_CHANNEL && event.roomInfo.closeVoiceChannel) {
            HLog.e(TAG, HLog.USR, "leaveChannelOnJoinChannelCloseChannel {}", event.source);
            VoiceManager.getInstance().leaveChannel();
            VoiceCheckerKt.checkVoiceChannelIn();
            return;
        }
        VoiceCheckerKt.leaveCheck();
        boolean local_mic_on = VoiceManager.getInstance().isLocalMicOn();
        VoiceRoomInfo roomInfo = event.roomInfo;
        if (roomInfo.isSupportVoiceRoom()) {
            boolean isBroadcaster = roomInfo.isInSeat(LoginHelper.getLoginUid());
            VoiceConfig config = VoiceConfig.newBuilder()
                    .setVoiceType(roomInfo.voiceType)
                    .setRid(roomInfo.rid)
                    .setMediaType(roomInfo.mediaType)
                    .setChannelName(getChannelName(roomInfo.rid, roomInfo.game_type))
                    .setOpenMic(event.mic_on && local_mic_on)
                    .setBroadcaster(isBroadcaster)
                    .setHighQuality(roomInfo.isHighQuality)
                    .setRejoin(event.isRejoin)
                    .setRoomScene(VoiceConfig.RoomScene.SCENE_VOICE_ROOM)
                    .setVideoRoom(event.roomInfo.isVideoRoom())
                    .setCheckPermission(false)
                    .setConfigSelector(new VoiceConfigHelper.ConfigSelector())
                    .build();
            if (roomInfo.isMsgBarrageRoom()) {
                if (filter == null) {
                    filter = new UnityStreamFilter();
                }
                VoiceManager.getInstance().addStreamFilter(filter);
            }
            VoiceMuteManager.filterVoiceStream();
            VoiceManager.getInstance().joinChannel(config);
            VoiceRoomService.getInstance().setIsInVoiceRoom(true);
            if (!event.mainRestore) {
                VoiceRoomNotifyService.startRoomService(activity, config.getRid());
            }

            if (event.source != JoinChannelEvent.SRC_SYNC_ROOM) {
                VoiceRoomInviteActivityTask.hideVoiceRoomInviteSpeakFloatingView(activity, true);
            }
        }
    }

    private void checkHideVoiceRoomInviteSpeak() {
        VoiceRoomInviteActivityTask.hideVoiceRoomInviteSpeakFloatingView(activity, true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLeaveChannel(LeaveChannelEvent event) {
        VoiceManager.getInstance().leaveChannel();
        VoiceRoomService.getInstance().setIsInVoiceRoom(false);
        VoiceRoomNotifyService.stopRoomService(activity);
        WeddingSoundUtil.getInstance().stopAndReleaseSound();
        EventDispatcher.postAdvanceRoomTips();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onExitRoom(ExitRoomEvent event) {
        if (event.isForceExit) {
            DialogUtil.showRoomKickedDialog(activity, event.reason);
        }
        VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo();
        if (info != null) {
            BgMusicManager.getInstance().stop(MusicSource.SOURCE_VOICE_ROOM);
        }
        VoiceMusicManager.get().clearAndStop();
        VoiceRoomInviteActivityTask.hideVoiceRoomInviteSpeakFloatingView(activity, true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onChangeMicStatus(ChangeMicStatus event) {
        boolean localMicOn = VoiceManager.getInstance().isLocalMicOn();
        boolean result;

        if (event.mic_on) {
            if (localMicOn) {
                result = VoiceManager.getInstance().openMic();
            } else {
                result = VoiceManager.getInstance().setMixingWithoutRecorder();
            }
            if (!VoiceManager.getInstance().isBroadcaster()) {
                VoiceManager.getInstance().setClientRole(true);
            }
        } else {
            VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo();
            boolean isBroadcaster = info.isInSeat(LoginHelper.getLoginUid());
            boolean needMute = !info.isCanSpeak();
            result = VoiceManager.getInstance().closeMic(needMute);
            if (VoiceManager.getInstance().isBroadcaster() ^ isBroadcaster) {
                VoiceManager.getInstance().setClientRole(isBroadcaster);
            }
        }

        //添加日志,记录麦克风每次变化的操作（当前按钮状态，本次操作是否开启麦克风，操作来源）
        HLog.d("Voice Room", HLog.USR, "onChangeMicStatus: btnState={}, Event(MicOn={}, source={}), result={}", localMicOn, event.mic_on, event.source, result);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, priority = 1)
    public void onRecallGroupMsg(RecallGroupMsgEvent event) {
        List<GroupChatMsg> recalledMsgs = event.msgs;

        GroupChatMsg recallMsg = recalledMsgs.get(0);
        final ConversationInfo conversationInfo = ConversationManager.getInstance()
                .getConversationByGid(recallMsg.getGroup_id());

        if (conversationInfo != null && conversationInfo.unread_num > 0) {
            String getOldestUnreadMsgSql = "select * from " + recallMsg.getTableName() + " where group_id = " +
                    recallMsg.getGroup_id() + " and mediaType != " + ChatMsg.MEDIA_TYPE_SYSTEM +
                    " order by time desc limit " + (conversationInfo.unread_num - 1) + ",1";
            WPStore.queryAsync(recallMsg, getOldestUnreadMsgSql, new WPStore.WPModelCallback<GroupChatMsg>() {
                @Override
                public void onResult(GroupChatMsg model, Exception e) {
                    int oldestUnreadSeq = Integer.MAX_VALUE;
                    if (model != null) {
                        oldestUnreadSeq = model.getSequence();
                    }
                    handleRecallGroupMsg(recalledMsgs, oldestUnreadSeq);
                }
            });
        } else {
            handleRecallGroupMsg(recalledMsgs, Integer.MAX_VALUE);
        }
    }

    private void handleRecallGroupMsg(List<GroupChatMsg> recalledMsgs, int oldestUnreadSeq) {
        for (int i = 0; i < recalledMsgs.size(); i++) {
            final GroupChatMsg msg = recalledMsgs.get(i);
            handleRecallGroupMsg(msg, oldestUnreadSeq);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRedDotUtilUpdate(RedDotUpdateEvent event) {
        checkShowMeRedDotByRedDotUtil();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onGroupVideoBlock(GroupVideoBlockMsgEvent event) {
        WPStore.getInstance().transaction(new GroupChatMsg(), transaction -> {
            for (GroupChatMsg msg : event.getMsgs()) {
                String sql = "update " + msg.getTableName() + " set '" + WPMessage.STATUS + "' = " + WPMessage.STATUS_VIDEO_BLOCKED + " where group_id = " + msg.getGroup_id() +
                        " and sequence = " + msg.getVideoBlockSeq();
                transaction.execSql(sql);
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNewFriend(NewFriendEvent event) {
    }

    private void handleRecallGroupMsg(final GroupChatMsg msg, final int oldestUnreadSeq) {
        final int recallMsgSeq = msg.getRecallSeq();
        final int gid = msg.getGroup_id();
        final boolean recallMsgShowState = msg.getRecallMsgShowState();

        String sql = "select * from " + msg.getTableName() + " where group_id = " + gid +
                " and sequence = " + recallMsgSeq;
        WPStore.queryAsync(msg, sql, new WPStore.WPModelCallback<GroupChatMsg>() {
            @Override
            public void onResult(GroupChatMsg model, Exception e) {
                if (model != null) {
                    final GroupChatMsg groupChatMsg = model;
                    final ConversationInfo conversationInfo = ConversationManager.getInstance()
                            .getConversationByGid(groupChatMsg.getGroup_id());
                    if (conversationInfo != null) {
                        // 消去有人艾特我的状态
                        conversationInfo.setAtMe(AT_NO);
                        // 更新对话列表显示的消息内容
                        String getNewestMsgSql = "select * from " + msg.getTableName() + " where group_id = " +
                                gid + " order by time desc limit 2";
                        //GroupChatMsg newestMsg = (GroupChatMsg) WPStore.getInstance().querySync(msg, getNewestMsgSql);
                        List<GroupChatMsg> list = WPStore.getInstance().fetchListSync(msg, getNewestMsgSql);
                        GroupChatMsg newestMsg = null;
                        if (list != null) {
                            newestMsg = list.get(0);
                        }

                        if (newestMsg != null && newestMsg.getSequence() == recallMsgSeq) {
                            conversationInfo.last_msg_type = ChatMsg.MEDIA_TYPE_SYSTEM;
                            int newestMsgSend_uid = newestMsg.getSend_uid();
                            if (newestMsgSend_uid == LoginHelper.getLoginUid()) {
                                updateConversationLastMsgContent(recallMsgShowState, true, "", list, conversationInfo);
                            } else {
                                UserService.get().getCacheSimpleUser(newestMsgSend_uid, new LifeUserSimpleInfoCallback(activity) {
                                    @Override
                                    public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                                        updateConversationLastMsgContent(recallMsgShowState, false, simpleInfo.getRemarkName(), list, conversationInfo);
                                    }

                                    @Override
                                    public void onUserInfoFailed(String description) {
                                        updateConversationLastMsgContent(recallMsgShowState, false, "", list, conversationInfo);
                                    }
                                });
                            }
                        }

                        if (recallMsgShowState) {
                            WPStore.getInstance().removeSync(model);
                        }
                        // 更新红点数
                        if (newestMsg != null && conversationInfo.unread_num > 0) {
                            if (oldestUnreadSeq <= recallMsgSeq) {
                                conversationInfo.unread_num--;
                                updateConversationAndReddot(conversationInfo);
                            }
                        }
                    }
                    if (recallMsgShowState) {
                        return;
                    }

                    //更新消息表
                    groupChatMsg.setMediaType(ChatMsg.MEDIA_TYPE_SYSTEM);
                    groupChatMsg.setSubType(ChatMsg.SYSTEM_SUB_TYPE_RECALL);
                    int sendUid = groupChatMsg.getSend_uid();
                    if (sendUid == LoginHelper.getLoginUid()) {
                        if (!recallMsgShowState) {
                            groupChatMsg.setContent(ResUtil.getStr(R.string.recalled_message));
                        }
                        WPStore.saveAsync(groupChatMsg);
                    } else {
                        UserService.get().getCacheSimpleUser(groupChatMsg.getSend_uid(),
                                new LifeUserSimpleInfoCallback(activity) {

                                    @Override
                                    public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                                        if (!recallMsgShowState) {
                                            groupChatMsg.setContent(ResUtil.getStr(R.string.recalled_message_by_x, simpleInfo.getRemarkName()));
                                        }
                                        WPStore.saveAsync(groupChatMsg);
                                    }

                                    @Override
                                    public void onUserInfoFailed(String description) {
                                        if (!recallMsgShowState) {
                                            groupChatMsg.setContent(ResUtil.getStr(R.string.recalled_message_by_x, ""));
                                        }
                                        WPStore.saveAsync(groupChatMsg);
                                    }
                                });
                    }
                }
            }
        });
    }

    private void updateConversationLastMsgContent(boolean recallMsgShowState, boolean isLoginUid, String remarkName
            , List<GroupChatMsg> groupChatMsg, ConversationInfo conversationInfo) {
        if (!recallMsgShowState) {
            conversationInfo.last_msg_content = isLoginUid ? ResUtil.getStr(R.string.recalled_message) :
                    ResUtil.getStr(R.string.recalled_message_by_x, remarkName);
        } else {
            if (groupChatMsg.size() == 2) {
                GroupChatMsg lastMsg = groupChatMsg.get(1);
                conversationInfo.last_msg_content = lastMsg.getContent();
            } else {
                conversationInfo.last_msg_content = "";
            }
        }
        updateConversationAndReddot(conversationInfo);
    }

    private void updateConversationAndReddot(ConversationInfo conversationInfo) {
        WPStore.saveAsync(conversationInfo);
        EventDispatcher.postConversationUpdateEvent();
        EventDispatcher.postNodeJsMsgChangeEvent();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onShowTabReddot(ShowTabReddotEvent event) {
        activity.showTabReddot(event.isShow, event.tabIndex);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayGroupMsgSound(PushChatMsgEvent event) {
        // 应用不在前台就不播放提示音
        if (!ApplicationUtil.isApplicationAtTop()) return;

        List<ChatMsg> chatMsgList = event.array;
        if (chatMsgList.size() == 0) return;
        // 判断收到的消息是否来自当前正在聊天的对象，根据这个决定播放两种提示音的一种；
        // 群消息需要判断是否打开了群消息提醒 和 是否是被@；
        if (chatMsgList.get(0).isGropChatMsg()) {
            int groupId = ((GroupChatMsg) chatMsgList.get(0)).getGroup_id();

            if (CurrentChatHelper.group_id != 0) {
                boolean shouldPlayInner = false;
                GroupChatMsg groupChatMsg = (GroupChatMsg) chatMsgList.get(0);
                if (groupChatMsg.getGroup_id() == CurrentChatHelper.group_id) {
                    shouldPlayInner = true;
                }

                GroupInfo groupInfo = GroupService.getInstance().getGroupInfo(groupId);
                if (groupInfo.isNotifyOn()) {
                    if (shouldPlayInner) {
                        NoticeHelper.getInstance().playRecvMsgBeep();
                    } else {
                        if (!groupInfo.isTempGroup) {
                            NoticeHelper.getInstance().playBeepAndVibrate();
                        }
                    }
                }
            } else {
                GroupInfo groupInfo = GroupService.getInstance().getGroupInfo(groupId);
                if (groupInfo.isNotifyOn()) {
                    if (!groupInfo.isTempGroup) {
                        NoticeHelper.getInstance().playBeepAndVibrate();
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceiveVoiceRoomInviteSpeak(final VoiceRoomInviteSpeakEvent event) {
        VoiceRoomService.getInstance().setInviteSpeakSeatNum(event.seat_num);
        VoiceRoomService.getInstance().setInviteSpeakInviteUid(event.inviteUid);

        if (!ActivityTaskManager.getInstance().isActivityAtTop(BaseVoiceRoomActivity.class)) {
            VoiceRoomInviteActivityTask.showVoiceRoomInviteSpeakFloatingView(activity);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceiveVoiceRoomInviteDraw(final VoiceRoomInviteDrawEvent event) {
        Activity activity = ActivityTaskManager.getInstance().getTopActivity();
        if (activity instanceof BaseActivity) {
            VoiceRoomInviteActivityTask.showVoiceRoomInviteDraw(activity, event.rid, event.whoInvite);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSyncWedding(SyncWeddingInfoEvent event) {
        WeddingRoomSyncUtil.onSyncWedding(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onShowWeddingDialog(final WeddingDialogEvent event) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        WeddingInfo weddingInfo = null;
        if (roomInfo == null || roomInfo.weddingInfo == null) return;
        weddingInfo = roomInfo.weddingInfo;

        if (event.isEndWedding) {
            VoiceRoomService.getInstance().removeRoom(roomInfo.rid);
            EventDispatcher.postLeaveChannelEvent(roomInfo.rid, LeaveChannelEvent.SRC_SYNC_ROOM);

            final String[] content = new String[1];
            if (weddingInfo.isSelfBride() || weddingInfo.isSelfGroom()) {
                content[0] = ResUtil.getStr(R.string.wedding_end_successful_to_cp);
                showWeddingEndRoomDialog(content[0]);
            } else {
                final WeddingInfo finalWeddingInfo = weddingInfo;
                UserService.get().getCacheSimpleUser(weddingInfo.brideUid, new LifeUserSimpleInfoCallback(activity) {
                    @Override
                    public void onUserInfoSuccess(final UserSimpleInfo bride) {
                        UserService.get().getCacheSimpleUser(finalWeddingInfo.groomUid, new LifeUserSimpleInfoCallback(activity) {
                            @Override
                            public void onUserInfoSuccess(UserSimpleInfo groom) {
                                if (event.isBrideGroomExit) {
                                    content[0] = ResUtil.getStr(R.string.wedding_exit_cause_cp_not_in);
                                } else {
                                    content[0] = ResUtil.getStr(R.string.wedding_end_successful_to_other_s_s, groom.getRemarkName(), bride.getRemarkName());
                                }
                                showWeddingEndRoomDialog(content[0]);
                            }

                            @Override
                            public void onUserInfoFailed(String description) {
                                if (event.isBrideGroomExit) {
                                    content[0] = ResUtil.getStr(R.string.wedding_exit_cause_cp_not_in);
                                } else {
                                    content[0] = ResUtil.getStr(R.string.wedding_end_successful_to_other);
                                }
                                showWeddingEndRoomDialog(content[0]);
                            }
                        });
                    }

                    @Override
                    public void onUserInfoFailed(String description) {
                        if (event.isBrideGroomExit) {
                            content[0] = ResUtil.getStr(R.string.wedding_exit_cause_cp_not_in);
                        } else {
                            content[0] = ResUtil.getStr(R.string.wedding_end_successful_to_other);
                        }
                        showWeddingEndRoomDialog(content[0]);
                    }
                });
            }
        } else {
            DialogUtil.showWeddingRoomSingleBtnDialog(ActivityTaskManager.getInstance().getTopActivity(), event.msg, ResUtil.getStr(R.string.sure), true, null);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUserEndWedding(UserEndWeddingEvent event) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        WeddingInfo weddingInfo = null;
        if (roomInfo == null || roomInfo.weddingInfo == null) return;
        weddingInfo = roomInfo.weddingInfo;

        if (weddingInfo.isSelfGroom() && event.isBride) {
            UserService.get().getCacheSimpleUser(weddingInfo.brideUid, new LifeUserSimpleInfoCallback(activity) {
                @Override
                public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                    showEndWeddingDialog(R.string.bride, simpleInfo.getRemarkName());
                }

                @Override
                public void onUserInfoFailed(String description) {
                    showEndWeddingDialog(R.string.bride, "");
                }
            });
        } else if (weddingInfo.isSelfBride() && !event.isBride) {
            UserService.get().getCacheSimpleUser(weddingInfo.groomUid, new LifeUserSimpleInfoCallback(activity) {
                @Override
                public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                    showEndWeddingDialog(R.string.groom, simpleInfo.getRemarkName());
                }

                @Override
                public void onUserInfoFailed(String description) {
                    showEndWeddingDialog(R.string.groom, "");
                }
            });
        }
    }

    private void showEndWeddingDialog(@StringRes int role, String name) {
        if (TextUtils.isEmpty(name)) name = "";
        showUserEndWeddingDialog(ResUtil.getResource().getString(R.string.wedding_end_req_by_other_tip, ResUtil.getStr(role), name));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onJoinVoiceChannelSuccess(AgoraJoinChannelSuccessEvent event) {
        VoiceMusicManager.get().onJoinChannelSuccess();
        boolean localMicOn = VoiceManager.getInstance().isLocalMicOn();
        VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo();
        boolean needMute = !(info != null && info.isCanSpeak());
        if (!localMicOn && !needMute) {
            VoiceManager.getInstance().setMixingWithoutRecorder();
        }
        if (info != null && VoiceRoomService.getInstance().isMuteRoom(info.rid)) {
            VoiceManager.getInstance().muteAllRemoteAudioStreams(true);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onChangeHomeTabEvent(ChangeHomeTabEvent event) {
        activity.showHomeTabReturnTop(event.showReturnTop);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onFirstChargeBuyEvent(FirstChargeBuyEvent event) {
        activity.onFirstChargeBuy(event.info, event);
    }

    private void showUserEndWeddingDialog(String content) {
        DialogUtil.showWeddingRoomDoubleBtnDialog(ActivityTaskManager.getInstance().getTopActivity(), content,
                ResUtil.getStr(R.string.sure), ResUtil.getStr(R.string.cancel), false, new DialogBuild.DialogCallback() {
                    @Override
                    public void onClickSure() {
                        MarryPacketSender.endWeddingReq(true, new DefaultSeqCallback());
                    }

                    @Override
                    public void onClickCancel() {
                        MarryPacketSender.endWeddingReq(false, new DefaultSeqCallback());
                    }
                });
    }

    private void showWeddingEndRoomDialog(String content) {
        DialogUtil.showWeddingRoomSingleBtnDialog(ActivityTaskManager.getInstance().getTopActivity(), content, ResUtil.getStr(R.string.sure), false,
                () -> EventDispatcher.postExitRoomEvent(false));
    }

    public void checkShowMsgNum() {
        //屏蔽 处理收到game_rs_room_syncRaw时数据库为null的bug, TODO:找到问题的根本原因
//        if (WPStore.getInstance() == null) return;

        //聊天
        int num = ConversationManager.getInstance().getAllUnreadMsgNum() + UserService.get().getNewFriend().getUnreadRecordsNumber();
        if (RedDotUtil.get().hasRedDot(RedDotNode.NODE_GROUP_APPLY)) {
            RedDotInfo redDotInfo = RedDotUtil.get().getRedDotInfo(RedDotInfo.RED_DOT_GROUP_APPLY);
            if (redDotInfo != null) {
                num += redDotInfo.updateData.size();
            }
        }
        num += MarryRedDot.getInstance().getTotalRemind();
        activity.setMsgTabReddot(num);
//        setMsgNum(, num);

        EventDispatcher.postUnreadMsgNumChange();
    }

    public static void setMsgNum(TextView text, int msgNum) {
        if (msgNum == 0) {
            text.setVisibility(View.GONE);
        } else {
            String strNum = msgNum > 99 ? "..." : String.valueOf(msgNum);
            text.setText(strNum);
            text.setVisibility(View.VISIBLE);
        }
    }

    public void clear() {
        //注销
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        EventBus.getDefault().removeAllStickyEvents();
        handler.removeCallbacksAndMessages(null);
        if (filter != null) {
            VoiceManager.getInstance().removeSteamFilter(filter);
        }
    }

    //app初始化数据
    public void initAppData() {
        EventBus.getDefault().register(this);
        handler.postDelayed(() -> {
            EmoticonHelper.getHelper().init();
//            SquareTopicManager.getInstance().checkHasNew();
//            MarryRedDot.getInstance().getChurchDot();
            FamilyManager.getInstance().updateFamilyInfo();
            RoomUtilManager.get().update();
//            RocketConfig.getInstance().init();
            DownloadSourceUtil.downloadConfigData();
            VoiceMusicManager.get().refreshFavoriteIds();
            DeepLinkRedDotCheckUtilsKt.refreshDeeplinkEntranceRedDot();
        }, 2000);
        DownloadSourceUtil.startWorkRequest();
    }

    public void recoverGame() {
        if (isCheckRecoverGame) {
            return;
        }
        isCheckRecoverGame = true;
        handler.postDelayed(() -> {
            //检测恢复XRoom
//            XRoomUtil.checkNeedRecover(activity, this::onCheckedGameRecover);
            //检测恢复其他游戏
            GameApi.getGameState(new LifeDataCallback<GameStateInfo>(activity) {
                @Override
                public void onSuccess(Result<GameStateInfo> result) {
                    GameStateInfo stateInfo = result.data;
                    HLog.d(TAG, HLog.USR, " GameApi.getGameState! stateInfo=" + stateInfo);
                    isCheckRecoverGame = false;
                    if (stateInfo.inGaming) {
                        stateInfo.applyTransform();
                        MainDialogManage.getInstance().addDialogTag(new MainDialogManage.ShowDialogInfo(MainDialogManage.GAME_TYPE) {
                            @Override
                            public void show() {
                                MainDialogManage.getInstance().getCallback().showNext();
                                DialogUtil.showGameRecoverActivity(stateInfo);
                            }
                        });
                        onCheckedGameRecover(true);
                    } else {
                        onCheckedGameRecover(false);
                    }
                }

                @Override
                public void onFail(int code, String msg) {
                    HLog.d(TAG, HLog.USR, " GameApi.getGameState! onFail, code=" + code + ", msg=" + msg);
                    isCheckRecoverGame = false;
                    onCheckedGameRecover(false);
                }
            });
        }, 300);
    }

    /**
     * 记录当前检测过都 recover 项，都 recover 后检测 linked me 外链跳转
     */
    private void onCheckedGameRecover(boolean inGame) {
        HLog.d(TAG, HLog.USR, "onCheckedGameRecover {}", inGame);
        if (inGame) {
            hasRecoverGame = true;
        } else {
            manager.handlerDeepLink(activity, " game recover");
        }
    }

    public void checkShowMeTabReddot() {
//        // 设置红点
//        activity.checkShowMeTabReddot(mHasApkUpdate);
//
//        SettingRedDot.getInstance().checkUpdateDot(new SettingRedDot.Callback() {
//            @Override
//            public void onFinish() {
//                mHasApkUpdate = SettingRedDot.getInstance().hasUpdate;
//                activity.checkShowMeTabReddot(mHasApkUpdate);
//            }
//        });

        checkShowMeRedDotByRedDotUtil();
    }

    public void checkShowMeRedDotByRedDotUtil() {
        activity.checkShowMeTabReddot(RedDotUtil.get().hasRedDot(RedDotNode.NODE_ME));
    }

    public void checkHasMarketReward() {
        if (MarketRewardUtil.isNeedCheckStatus() && MarketRewardUtil.isHasReward()) {
            MarketRewardUtil.sendMarketReward();

            MarketRewardUtil.clearMarketReward();
            MarketRewardUtil.clearCheckStatus();
        }
    }

    public void resumeRefresh(boolean createResume) {
        if (createResume) {
            handler.postDelayed(resumeRefresh, 2000);
        } else {
            resumeRefresh.run();
            handler.removeCallbacks(checkUnHandlerPurchaseRunner);
            handler.postDelayed(checkUnHandlerPurchaseRunner, 5000);
        }
    }

    private final Runnable resumeRefresh = new Runnable() {
        @Override
        public void run() {
//            DiscoverManager.getInstance().checkResumeRefresh();
//            CircleMsgManager.getInstance().checkResumeRefresh();
            checkShowMsgNum(); // 25ms
            checkShowMeTabReddot(); // 21ms

            WPUpdateUtil.getInstance().checkHasUpdate(activity, true);
            WejoyUserConfigHelp.getInstance().updateUserConfig(LoginHelper.getLoginUid());
            if (AddFriendManager.getInstance().shouldShowNotificationDialog) {
                DialogUtil.showFriendOpenNotificationDialog(activity);
            }
        }
    };

    private final Runnable checkUnHandlerPurchaseRunner = () -> ApiService.of(IapApi.class).checkUnHandledPurchase(true);


    public void preloadActivityWebView() {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
            return;
        }
        String defaultUrl = "http://weplayapp.com/";
        CouponApi.getCouponActivity(new LifeDataCallback<List<CouponInfo>>(activity) {

            @Override
            public void onSuccess(Result<List<CouponInfo>> result) {
                List<CouponInfo> couponInfoList = result.data;
                if (couponInfoList == null || couponInfoList.isEmpty()) {
                    preloadWebView(defaultUrl);
                    return;
                }

                String url = couponInfoList.get(0).jump_url;
                if (TextUtils.isEmpty(url)) {
                    preloadWebView(defaultUrl);
                    return;
                }

                preloadWebView(url);
            }

            @Override
            public void onFail(int code, String msg) {
                preloadWebView(defaultUrl);
            }
        });
    }

    private void preloadWebView(String url) {
        try {
            WespyWebView wespyWebView = new WespyWebView(activity);
            wespyWebView.initCore(false);
            wespyWebView.initWebView(url);
            wespyWebView.setWebViewInterface(new SimpleWebViewInterface() {
                boolean isOnPageFinishedCalled = false;

                @Override
                public void onPageFinished(WPWebView view, String url) {
                    if (!isOnPageFinishedCalled) {
                        isOnPageFinishedCalled = true;
                        wespyWebView.releaseRes();
                        wespyWebView.destroy();
                    }
                }
            });
            HLog.d(TAG, "preload webview url = {}", url);
            TimeLogger.msgNoStack("init_web_core finished");
        } catch (Exception e) {
            HLog.e(TAG, HLog.CLR, "error init web view in idle handler");
        }
    }

    private String getChannelName(int rid, int gameType) {
        String channelName = "";
        if (UrlConfig.isDebug()) {
            channelName += VoiceManager.VOICE_DEV;
        }
        channelName += rid;
        //直播弹幕游戏，主播和unity在一个房间，观众在另一个房间
        if (gameType == GameType.GAME_TYPE_MSG_BATTLE) {
            channelName += VoiceConfig.UNITY;
        }
        return channelName;
    }

    private static class UnityStreamFilter extends BaseStreamFilter {
        @Override
        public boolean onStreamUpdate(@NonNull BaseVoiceManager manager, @NonNull ChannelStream stream) {
            String streamId = stream.streamID;
            return !TextUtils.isEmpty(streamId) && streamId.contains(VoiceConfig.UNITY) && stream.isVideo();
        }
    }
}
