package com.wepie.wespy.module.main

import androidx.collection.ArrayMap
import com.google.gson.annotations.SerializedName
import com.huiwan.base.LibBaseUtil
import com.huiwan.user.LoginHelper
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.liblog.main.HLog
import dalvik.system.DexFile
import java.lang.reflect.Modifier
import java.util.Locale
import kotlin.random.Random


object MainPageTrack {
    var checkInSessionId: String = ""

    const val SCENE_CHECK_IN_1 = "开始签到"
    const val SCENE_CHECK_IN_2 = "检查签到"


    @JvmStatic
    fun checkIn1() {
        val random = Random(10000L)
        checkInSessionId = "${System.currentTimeMillis()}_${random.nextInt()}"
        val map: MutableMap<String, Any> = ArrayMap()
        map["scene"] = SCENE_CHECK_IN_1
        map["isFirstTime"] = LibBaseUtil.isFirstIn()
        map["isNewUser"] = LoginHelper.isNewUser()
        map["session_id"] = checkInSessionId
        TrackUtil.appViewScreen(TrackScreenName.HOME_PAGE, map)
    }

    @JvmStatic
    fun checkIn2(lastCheckInDay: Int, currentDayOfYear: Int) {
        val map: MutableMap<String, Any> = ArrayMap()
        map["scene"] = SCENE_CHECK_IN_2
        map["isFirstTime"] = LibBaseUtil.isFirstIn()
        map["currentCheckIn"] = lastCheckInDay == currentDayOfYear
        map["session_id"] = checkInSessionId
        TrackUtil.appViewScreen(TrackScreenName.HOME_PAGE, map)
    }

    @JvmStatic
    fun checkInRequest(url: String, isFirstIn: Boolean) {
        val map: MutableMap<String, Any> = ArrayMap()
        map["session_id"] = checkInSessionId
        TrackUtil.request(url, isFirstIn, map)
    }

    @JvmStatic
    fun checkInResponse(url: String, isSuccess: Boolean, code: Int, isFirst: Boolean) {
        val map: MutableMap<String, Any> = ArrayMap()
        map["session_id"] = checkInSessionId
        TrackUtil.response(url, isSuccess, code, isFirst, map)
    }


    private fun checkFiled(c: Class<*>) {
        val fields = c.declaredFields
        if (fields.isEmpty()) {
            return
        }
        for (f in fields) {
            f.isAccessible = true
            if (Modifier.isTransient(f.modifiers)) {
                continue
            }
            if (Modifier.isStatic(f.modifiers)) {
                continue
            }
            val s = f.getAnnotation(SerializedName::class.java)
            if (s == null) {
//                HLog.d("ClassNames", "null {}#{} ", c.name, f.name)
            } else {
                val fName = f.name
                if (s.value != fName) {
                    if (toCamelCase(s.value) != fName) {
                        HLog.d("ClassNames", "ne {}.  {} {}", c.name, s.value, fName)
                    }
                }
            }
        }
    }

    private fun toCamelCase(underscoreName: String): String {
        val words = underscoreName.split("_")
        val sb = StringBuilder(words[0])
        for (i in 1 until words.size) {
            sb.append(words[i].replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.ENGLISH) else it.toString() })
        }
        return sb.toString()
    }

    @JvmStatic
    fun checkClasses() {
        val apkPath: String = LibBaseUtil.getApplication().applicationInfo.sourceDir

        // 使用 DexFile 加载 APK 文件

        // 使用 DexFile 加载 APK 文件
        val dexFile = DexFile(apkPath)

        // 获取 DexFile 中的所有类名

        // 获取 DexFile 中的所有类名
        val entries = dexFile.entries()
        while (entries.hasMoreElements()) {
            val className = entries.nextElement()
            if (className.startsWith("com.huiwan.configservice")) {
                if (className.contains("functionShield")) {
                    continue
                }
                if (className.contains("ce.R")) {
                    continue
                }
                if (className.contains("ConfigHelper")) {
                    continue
                }
                if (className.contains("ConfigInfo")) {
                    continue
                }
                if (className.contains("ConfigUpdater")) {
                    continue
                }
                if (className.contains("MedalLevelDetailInfo")) {
                    continue
                }
                if (className.contains("GlobalConfigManager")) {
                    continue
                }
                checkFiled(Class.forName(className))
            }
        }

        dexFile.close()
    }
}