package com.wepie.wespy.module.settings.main;

import static com.huiwan.store.file.FileConfig.DATABASE_BACKUP_DIR_NAME;

import android.content.Context;
import android.text.TextUtils;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.littlegame.cocos.CocosAssetBundleResLoader;
import com.huiwan.store.PrefUtil;
import com.huiwan.store.database.WPStore;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.store.file.FileConfig;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.update.CocosVersionManager;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.module.chat.send.face.EmoticonHelper;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.home.checkin.CheckInUtil;

import java.io.File;
import java.io.IOException;
import java.util.LinkedList;
import java.util.List;

/**
 * date 2019-06-28
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class FixUtil {
    private static ProgressDialogUtil dialogUtil = new ProgressDialogUtil();
    private static final String TAG = "FixUtil";
    public static void showFixTipDialog(final Context context) {
        DialogBuild.newBuilder(context).setSingleBtn(false).setContent(R.string.setting_quick_fix_tip).setDialogCallback(new DialogBuild.DialogCallback() {
            @Override
            public void onClickSure() {
                doFix(context);
            }

            @Override
            public void onClickCancel() {

            }
        }).show();
    }

    private static void doFix(final Context context) {
        dialogUtil.showLoading(context, ResUtil.getStr(R.string.setting_quick_fix_running_tip), false);
        try {
            //清理签到缓存
            CheckInUtil.clearLocalCache();
            CocosVersionManager.getInstance().deleteAllRes();
            clearCache(context);
            CocosAssetBundleResLoader.INSTANCE.reset();
            WPStore.fix();
            forceUpdate(new ConfigHelper.UpdateCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show(R.string.setting_quick_fix_finished);
                    ActivityTaskManager.getInstance().finishAllActivities();
                    dialogUtil.hideLoading();
                    JumpUtil.gotoMainActivity(context);
                }

                @Override
                public void onFailed(String msg) {
                    dialogUtil.hideLoading();
                    ToastUtil.show(msg);
                }
            });
            clearBpConfig();
            PrefUtil.getInstance().setBoolean(PrefUtil.CLEAR_WEB_CACHE, true);
            EmoticonHelper.getHelper().forceUpdateFavorite();
        } catch (Exception e) {
            TimeLogger.err("error happen when fix: " + e.getLocalizedMessage());
        }
    }


    private static void forceUpdate(ConfigHelper.UpdateCallback callback) {
        ConfigHelper.getInstance().forceUpdateAllEditions(callback);
    }

    private static void clearCache(final Context context) {
//        File root = context.getFilesDir().getParentFile();
        try {
            List<String> cacheList = new LinkedList<>();
            cacheList.add(context.getDir("webview", Context.MODE_PRIVATE).getPath());
            cacheList.add(context.getDir("hws_webview", Context.MODE_PRIVATE).getPath());
            File cacheDir = new File(FileConfig.getSysCachePath(""));
            File[] subFiles = cacheDir.listFiles();
            if (subFiles != null) {
                for (File file : subFiles) {
                    if (!TextUtils.equals(file.getName(), DATABASE_BACKUP_DIR_NAME)) {
                        cacheList.add(file.getPath());
                    }
                }
            }

            for (String path:cacheList) {
                File file = new File(path);
                if (file.exists()) {
                    if (file.isDirectory()) {
                        try {
                            FileUtil.deleteDirectory(file);
                            TimeLogger.msg("clear file " + file.getName());
                        } catch (IOException e) {
                            TimeLogger.msg("cache file delete error " + file.getAbsolutePath() + ", " + e.getLocalizedMessage());
                        }
                    } else {
                        boolean res = file.delete();
                        TimeLogger.msg("delete file success: " + res + ", file: "+ file.getName());
                    }
                }
            }
        } catch (Exception e) {
            TimeLogger.err("error clear cache: " + e.getLocalizedMessage());
        }
    }

    private static void clearBpConfig() {
        File bpConfig = new File(FileUtil.getFullPath(FileCacheName.BP_CONFIG));
        if (bpConfig.exists() && bpConfig.length() > 0) {
            FileUtil.safeDeleteFile(bpConfig);
        }
        File bpPeriodConfig = new File(FileUtil.getFullPath(FileCacheName.BP_PERIOD_CONFIG));
        if (bpPeriodConfig.exists() && bpPeriodConfig.length() > 0) {
            FileUtil.safeDeleteFile(bpPeriodConfig);
        }
    }

}
