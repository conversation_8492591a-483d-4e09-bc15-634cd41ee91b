package com.wepie.wespy.module.settings.edituser.phone;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.widget.actionbar.BaseWpActionBar;

/**
 * Created by bigwen on 2019-12-11.
 */
public class ReplacePhoneSuccessActivity extends BaseActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_replace_phone_success);
        BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        actionBar.addTitleAndBack("");
        TextView returnTv = findViewById(R.id.return_edit_tv);
        returnTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }
}
