package com.wepie.wespy.module.activity.tab;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;

import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.file.FileCacheName;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.cancellable.EventBusExKt;
import com.wepie.wespy.model.entity.NavigationInfo;
import com.wepie.wespy.model.event.RedDotUpdateEvent;
import com.wepie.wespy.net.http.api.ActivityTabApi;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.concurrent.Future;

public class ActivityTabPresent {
    private final ActivityTabActivity activity;
    private NavigationInfo info = new NavigationInfo();

    public ActivityTabPresent(ActivityTabActivity activity) {
        this.activity = activity;
        EventBusExKt.registerAutoCancel(EventBus.getDefault(), activity, this);
    }

    void getNavigationList(ArrayList<Integer> redDotList, int actId, String url) {
        Future<Object> future = ThreadUtil.runInOtherThread(() -> {
            NavigationInfo navigationInfo = FileUtil.loadEntityFromFile(FileCacheName.ACTIVITY_NAVIGATION_CONFIG, NavigationInfo.class);
            if (navigationInfo != null) {
                info = navigationInfo;
            }
            ActivityTabApi.getNavigation(info.version, new LifeDataCallback<>(activity) {
                @Override
                public void onSuccess(Result<NavigationInfo> result) {
                    if (result.data != null && !info.version.equals(result.data.version)) {
                        FileUtil.writeFileAsync(FileCacheName.ACTIVITY_NAVIGATION_CONFIG, JsonUtil.getGson().toJson(result.data));
                        info = result.data;
                    }
                    activity.runOnUiThread(() -> activity.getNavigationList(info.data, actId, redDotList, url));
                }

                @Override
                public void onFail(int code, String msg) {
                    ToastUtil.show(msg);
                }
            });
            return null;
        });
        activity.getLifecycle().addObserver((LifecycleEventObserver) (lifecycleOwner, event) -> {
            if (event == Lifecycle.Event.ON_DESTROY) {
                future.cancel(true);
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRedDotUtilUpdate(RedDotUpdateEvent event) {
        activity.updateRedDot();
    }
}
