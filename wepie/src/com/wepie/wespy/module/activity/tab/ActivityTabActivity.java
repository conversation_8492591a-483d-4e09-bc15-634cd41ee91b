package com.wepie.wespy.module.activity.tab;

import static com.wepie.wespy.helper.redDotHelper.RedDotNode.NODE_G_ACTIVITY;
import static com.wepie.wespy.model.entity.RedDotInfo.RED_DOT_ACTIVITY_SELF;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.core.widget.ContentLoadingProgressBar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.module.webview.SimpleWebViewInterface;
import com.huiwan.module.webview.WespyWebView;
import com.huiwan.module.webview.util.WebUtil;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.huiwan.widget.decoration.SpaceItemDecoration;
import com.wepie.webview.WPWebView;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.model.entity.NavigationActivityInfo;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.module.activity.ActivityUtil;

import java.util.ArrayList;
import java.util.HashMap;

public class ActivityTabActivity extends BaseActivity {

    private BaseWpActionBar actionBar;
    private WespyWebView webView;
    private RecyclerView tabRv;
    private LinearLayout content;
    private ImageView line1, line2;
    private ActivityTabRecycleAdapter adapter;
    private LinearLayoutManager manager;
    private final HashMap<String, String> titleMap = new HashMap<>();
    private int position = 0;
    private ActivityTabPresent present;
    private ContentLoadingProgressBar loadingView;

    View.OnClickListener backClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (webView.canGoBack()) {
                webView.goBack();
            } else {
                finish();
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_tab_view);
        actionBar = findViewById(R.id.action_bar);
        loadingView = findViewById(R.id.loading_view);
        webView = findViewById(R.id.activity_web_view);
        content = findViewById(R.id.content_layout);
        line1 = findViewById(R.id.tab_line1);
        line2 = findViewById(R.id.tab_line2);
        webView.setWebViewInterface(new SimpleWebViewInterface() {
            @Override
            public void onReceivedTitle(WPWebView view, String title) {
                refreshActionBar(title);
                titleMap.put(webView.getUrl(), title);
            }

            @Override
            public void onPageStarted(WPWebView view, String url, Bitmap favicon) {
                String title = titleMap.get(url);
                refreshActionBar(title != null ? title : ResUtil.getStr(R.string.common_loading));
            }

            @Override
            public void onPageFinished(WPWebView view, String url) {
                super.onPageFinished(view, url);
                loadingView.hide();
                updateTitleBar(url);
            }

            @Override
            public void hideShareBtn() {

            }
        });
        tabRv = findViewById(R.id.tab_bottom_rv);

        present = new ActivityTabPresent(this);
        adapter = new ActivityTabRecycleAdapter(ActivityTabActivity.this);
        adapter.setListener(new OnItemClickListener() {
            @Override
            public void onClick(View view, NavigationActivityInfo info) {
                moveToMiddle(view);
                actionBar.refreshTitleText(R.string.common_loading);
                loadingView.show();
                webView.visitUrl(info.actUrl, true);
            }
        });
        manager = new LinearLayoutManager(ActivityTabActivity.this);
        manager.setOrientation(LinearLayoutManager.HORIZONTAL);
        tabRv.setLayoutManager(manager);
        tabRv.setAdapter(adapter);
        int dp6 = ScreenUtil.dip2px(6F);
        int dp10 = ScreenUtil.dip2px(10F);
        tabRv.addItemDecoration(new SpaceItemDecoration(new Rect(dp6, dp6, dp6, dp6), new Rect(dp10, 0, dp10, 0)));
        actionBar.addTitleAndBack(ResUtil.getStr(R.string.common_loading), backClickListener);
        loadingView.show();
        init();
    }

    private void refreshActionBar(String title) {
        actionBar.refreshTitleText(title);
        actionBar.setCloseVisible(webView.canGoBack());
    }

    private void init() {
        String url = getIntent().getStringExtra(IntentConfig.COUPON_URL);
        if (TextUtils.isEmpty(url)) {
            ToastUtil.show(R.string.link_exception_msg);
            return;
        }
        int actId = ActivityUtil.getActId(url);
        ArrayList<Integer> redDotList = getRedDotList();
        present.getNavigationList(redDotList, actId, url);
    }

    private void updateTitleBar(String url) {
        boolean showTitle = WebUtil.showTitleBar(url);
        actionBar.setVisibility(showTitle ? View.VISIBLE : View.GONE);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) content.getLayoutParams();
        if (null != layoutParams) {
            layoutParams.topMargin = showTitle ? ScreenUtil.getStatusBarHeight() : 0;
        }
        StatusBarUtil.initStatusBar(this);
    }

    void getNavigationList(ArrayList<NavigationActivityInfo> list, int actId, ArrayList<Integer> redDotList, String url) {
        adapter.setList(list, actId, redDotList);
        position = list.size();
        for (int i = 0; i < list.size(); i++) {
            if (actId == list.get(i).actId) {
                position = i;
            }
        }
        webView.initCore(false);
        webView.initWebView(url);
        if (list.size() == 1 || position == list.size()) {
            updateRecycleView(View.GONE);
        } else {
            updateRecycleView(View.VISIBLE);
            tabRv.scrollToPosition(position);
            tabRv.post(new Runnable() {
                @Override
                public void run() {
                    View view = manager.findViewByPosition(position);
                    if (view != null) {
                        moveToMiddle(view);
                    }
                }
            });
        }
    }

    private void updateRecycleView(int vis) {
        line1.setVisibility(vis);
        line2.setVisibility(vis);
        tabRv.setVisibility(vis);
    }


    /**
     * 滚动到中间位置
     *
     * @param clkView 被点击的View
     */
    public void moveToMiddle(View clkView) {
        int itemWidth = clkView.getWidth();
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        int scrollWidth = clkView.getLeft() - (screenWidth / 2 - itemWidth / 2);
        tabRv.smoothScrollBy(scrollWidth, 0);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (webView != null) {
            webView.stopLoading();
            webView.removeAllViews();
            webView.releaseRes();
            webView.destroy();
        }
    }

    @Override
    public void onBackPressed() {
        if (webView != null && webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (webView != null) webView.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
    }

    ArrayList<Integer> getRedDotList() {
        ArrayList<Integer> redDotList = new ArrayList<>();
        if (RedDotUtil.get().hasRedDot(NODE_G_ACTIVITY)) {
            RedDotInfo info = RedDotUtil.get().getRedDotInfo(RED_DOT_ACTIVITY_SELF);
            if (info != null && info.updateData != null && info.updateData.size() > 0) {
                redDotList.clear();
                redDotList.addAll(info.updateData);
            }
        }
        return redDotList;
    }

    public void updateRedDot() {
        adapter.setRedDotList(getRedDotList());
    }
}
