package com.wepie.wespy.module.voiceroom.msg.ar;

import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.MEDIA_TYPE_EMOJI;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.MEDIA_TYPE_PHOTO;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.MEDIA_TYPE_SYSTEM;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.MEDIA_TYPE_TEXT;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_CALL_UP_FANS;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_CLOSE_PCHAT;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_FOLLOW_ROOM;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_JOIN_MEMBER;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_MEMBER_FREE_SIT;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_MOD_BG;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_MOD_LABEL;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_MOD_PROMISE;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_MOD_RNAME;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_MOD_RNOTE;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_MUL_GIFT;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_OPEN_PCHAT;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_PK_END;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_PK_START;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_PK_VOTE;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_RP_CLOSE;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_RP_OPEN;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_RP_RAIN_RESULT;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_SEND_GIFT;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_SEND_RP;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_SET_ADMIN;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_SET_PASSWD;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_SHOW_RNOTE;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_SLOTS;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_SLOT_RESULT_MSG;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_SYSTEM;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_UNSET_PASSWD;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUBTYPE_WEDDING;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_EMOTICON;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_INTERSTELLAR_GIFT_RESULT;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_INTIMACY_CLOSE_NORMAL;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_INTIMACY_CLOSE_OWNER;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_INTIMACY_OPEN_NORMAL;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_INTIMACY_OPEN_OWNER;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_LUCKY_NUM;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_MOD_GAME_TYPE;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_MOD_ONLY_FAMILY_JOIN;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_PHOTO;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_PUB_IMG_CLOSE;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_PUB_IMG_OPEN;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_VOICE_MATCH_REQUEST_SHOW_IDENTITY;
import static com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg.SUB_VOICE_MATCH_SHOW_IDENTITY;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.configservice.model.voiceroom.VoiceMemberMsgExtInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceGiftInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.module.voiceroom.msg.BaseRoomMsgAdapter;
import com.wepie.wespy.module.voiceroom.msg.item.RoomMsgItem;
import com.wepie.wespy.module.voiceroom.msg.item.VoiceMsgUtil;

import java.util.Arrays;
import java.util.List;

public class RoomMsgAdapterAr extends BaseRoomMsgAdapter<BaseHolder> {
    private static final int ITEM_VIEW_TYPE_SYS = 1;
    private static final int ITEM_VIEW_TYPE_CHAT_TEXT = 2;
    private static final int ITEM_VIEW_TYPE_CHAT_IMG = 3;
    private static final int ITEM_VIEW_TYPE_CHAT_GIFT = 4;
    private static final int ITEM_VIEW_TYPE_CHAT_MUL_GIFT = 5;
    private static final int ITEM_VIEW_TYPE_CHAT_TRICK_FACE = 6;
    private static final int ITEM_VIEW_TYPE_SHOW_NOTE = 7;
    private static final int ITEM_VIEW_TYPE_RP_RESULT = 8;
    private static final int ITEM_VIEW_TYPE_WEDDING = 9;
    private static final int ITEM_VIEW_TYPE_PK_VOTE = 10;
    private static final int ITEM_VIEW_TYPE_FOLLOW = 11;
    private static final int ITEM_VIEW_TYPE_SLOT_RESULT = 12;
    private static final int ITEM_VIEW_TYPE_LUCKY_NUM = 13;

    private static final int ITEM_VIEW_TYPE_DRAGON = 14;
    private static final int ITEM_VIEW_TYPE_MEMBER_LEVEL_UP = 15;
    private static final int ITEM_VIEW_TYPE_MEMBER_ENTER_ROOM = 16;
    private static final int ITEM_VIEW_TYPE_MEMBER_FROZEN_REMIND = 17;
    private static final int ITEM_VIEW_TYPE_MEMBER_FOLLOW_ROOM = 18;
    private static final int ITEM_VIEW_TYPE_MEMBER_JOIN = 19;
    private static final int ITEM_VIEW_TYPE_TEXT_ADD_MEMBER = 20;
    private static final int ITEM_VIEW_TYPE_MEMBER_FREE_SIT = 21;
    private static final int ITEM_VIEW_TYPE_ADVANCE_PK_TITLE = 22;

    /**
     * 默认语音房消息宽度 竖屏宽度
     */
    private int contentMaxWidth = ScreenUtil.getScreenWidth() - ScreenUtil.dip2px(12);

    public void setContentMaxWidth(int contentMaxWidth) {
        this.contentMaxWidth = contentMaxWidth;
    }

    private static final List<Integer> systemSubTypes = Arrays.asList(
            SUBTYPE_SET_PASSWD, SUBTYPE_UNSET_PASSWD, SUBTYPE_OPEN_PCHAT, SUBTYPE_CLOSE_PCHAT,
            SUBTYPE_MOD_RNAME, SUBTYPE_MOD_RNOTE, SUBTYPE_MOD_PROMISE, SUBTYPE_MOD_BG,
            SUBTYPE_MOD_LABEL, SUBTYPE_CALL_UP_FANS, SUBTYPE_PK_START, SUBTYPE_PK_END,
            SUBTYPE_SET_ADMIN, SUBTYPE_SLOTS, SUBTYPE_SEND_RP, SUBTYPE_SYSTEM,
            SUBTYPE_RP_OPEN, SUBTYPE_RP_CLOSE, SUB_MOD_GAME_TYPE, SUB_MOD_ONLY_FAMILY_JOIN,
            SUB_VOICE_MATCH_SHOW_IDENTITY, SUB_VOICE_MATCH_REQUEST_SHOW_IDENTITY, SUB_PUB_IMG_OPEN, SUB_PUB_IMG_CLOSE,
            SUB_INTIMACY_CLOSE_NORMAL, SUB_INTIMACY_OPEN_NORMAL, SUB_INTIMACY_CLOSE_OWNER, SUB_INTIMACY_OPEN_OWNER,
            SUB_INTERSTELLAR_GIFT_RESULT, VoiceRoomMsg.SUB_MSG_WITH_POP_UP);

    @Override
    public int getItemViewType(int position) {
        VoiceRoomMsg msg = dataList.get(position);
        int subType = msg.getSubType();
        int mediaType = msg.getMediaType();
        if (systemSubTypes.contains(subType) || mediaType == MEDIA_TYPE_SYSTEM) {
            return ITEM_VIEW_TYPE_SYS;
        }
        if (subType == SUBTYPE_SHOW_RNOTE) {
            return ITEM_VIEW_TYPE_SHOW_NOTE;
        }
        if (subType == SUBTYPE_SEND_GIFT) {
            VoiceGiftInfo giftInfo = msg.getGiftInfo();
            if (giftInfo != null) {
                if (giftInfo.isSupportGiftType()) {
                    if (giftInfo.isTrickFace()) {
                        return ITEM_VIEW_TYPE_CHAT_TRICK_FACE;
                    } else {
                        return ITEM_VIEW_TYPE_CHAT_GIFT;
                    }
                }
            }
        }
        if (subType == SUBTYPE_RP_RAIN_RESULT) {
            return ITEM_VIEW_TYPE_RP_RESULT;
        }
        if (subType == SUBTYPE_PK_VOTE) {
            return ITEM_VIEW_TYPE_PK_VOTE;
        }
        if (subType == SUBTYPE_MUL_GIFT) {
            return ITEM_VIEW_TYPE_CHAT_MUL_GIFT;
        }
        if (subType == SUBTYPE_WEDDING) {
            if (msg.isPastorMsg()) {
                return ITEM_VIEW_TYPE_WEDDING;
            }
            return ITEM_VIEW_TYPE_CHAT_TEXT;
        }
        if (subType == SUBTYPE_FOLLOW_ROOM) {
            return ITEM_VIEW_TYPE_FOLLOW;
        }
        if (subType == SUBTYPE_JOIN_MEMBER) {
            return ITEM_VIEW_TYPE_TEXT_ADD_MEMBER;
        }
        if (subType == SUBTYPE_MEMBER_FREE_SIT) {
            return ITEM_VIEW_TYPE_MEMBER_FREE_SIT;
        }
        if (subType == VoiceRoomMsg.SUB_MSG_ADVANCE_PK_TITLE) {
            return ITEM_VIEW_TYPE_ADVANCE_PK_TITLE;
        }
        if (subType == VoiceRoomMsg.SUB_DRAGON_CALL_WARRIOR_MSG_RESULT || subType == VoiceRoomMsg.SUB_DRAGON_UNITY_KILL_INFO_MSG) {
            return ITEM_VIEW_TYPE_DRAGON;
        }
        if (subType == VoiceRoomMsg.SUB_MEMBER_MSG_CARD) {
            VoiceMemberMsgExtInfo info = VoiceMsgUtil.getMemberMsgInfo(msg.getExt());
            return getTypeByScene(info.getScene());
        }
        if (mediaType == MEDIA_TYPE_TEXT) {
            if (subType == SUB_LUCKY_NUM) {
                return ITEM_VIEW_TYPE_LUCKY_NUM;
            }
            return ITEM_VIEW_TYPE_CHAT_TEXT;
        }
        if (mediaType == MEDIA_TYPE_PHOTO || mediaType == MEDIA_TYPE_EMOJI
                || subType == SUB_EMOTICON || subType == SUB_PHOTO) {
            return ITEM_VIEW_TYPE_CHAT_IMG;
        }
        if (subType == SUBTYPE_SLOT_RESULT_MSG) {
            return ITEM_VIEW_TYPE_SLOT_RESULT;
        }
        return -1;
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case ITEM_VIEW_TYPE_SYS:
                return new SystemMsgSysHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_CHAT_TRICK_FACE:
                return new SystemMsgTrickFaceHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_SHOW_NOTE:
                return new SystemMsgNoteHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_RP_RESULT:
                return new SystemMsgRpResultHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_WEDDING:
                return new SystemMsgWeddingHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_PK_VOTE:
                return new SystemMsgPkVoteHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_FOLLOW:
                return new SystemMsgFollowHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_TEXT_ADD_MEMBER:
                return new SystemMsgAddMemberHolder(getSystemMsgLayout(parent), contentMaxWidth, 1);
            case ITEM_VIEW_TYPE_MEMBER_FREE_SIT:
                return new SystemMsgAddMemberHolder(getSystemMsgLayout(parent), contentMaxWidth, 2);
            case ITEM_VIEW_TYPE_SLOT_RESULT:
                return new SystemMsgSlotResultHolder(getSystemMsgLayout(parent), contentMaxWidth);
//            case ITEM_VIEW_TYPE_DRAGON:
//                return new ChatMsgDragonHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_MEMBER_LEVEL_UP:
                return new SystemMsgMemberLevelUpHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_MEMBER_ENTER_ROOM:
                return new SystemMsgMemberEnterHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_MEMBER_FROZEN_REMIND:
            case ITEM_VIEW_TYPE_MEMBER_JOIN:
                return new SystemMsgMemberJoinHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_MEMBER_FOLLOW_ROOM:
                return new SystemMsgMemberFollowHolder(getSystemMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_CHAT_TEXT:
                return new ChatMsgTextHolder(getChatMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_CHAT_IMG:
                return new ChatMsgImgHolder(getChatMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_CHAT_GIFT:
                return new ChatMsgGiftHolder(getChatMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_CHAT_MUL_GIFT:
                return new ChatMsgMulGiftHolder(getChatMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_LUCKY_NUM:
                return new ChatMsgLuckyNumHolder(getChatMsgLayout(parent), contentMaxWidth);
            case ITEM_VIEW_TYPE_ADVANCE_PK_TITLE:
                return new SystemMsgAdvancePKTitleHolder(getSystemMsgNoBgLayout(parent), contentMaxWidth);
            default:
                return new DefaultHolder(new RoomMsgItem(parent.getContext()), contentMaxWidth);
        }
    }

    private View getChatMsgLayout(ViewGroup parent) {
        return LayoutInflater.from(parent.getContext()).inflate(R.layout.room_msg_chat_item_view, parent, false);
    }

    private View getSystemMsgLayout(ViewGroup parent) {
        return LayoutInflater.from(parent.getContext()).inflate(R.layout.room_msg_system_item_view, parent, false);
    }

    private View getSystemMsgNoBgLayout(ViewGroup parent) {
        View view = getSystemMsgLayout(parent);
        view.findViewById(R.id.room_msg_item_content_lay).setBackground(null);
        return view;
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        holder.bind(dataList.get(position));
    }

    private int getTypeByScene(int scene) {
        int type;
        switch (scene) {
            case VoiceRoomMsg.SCENE_MEMBER_LEVEL_UP:
                type = ITEM_VIEW_TYPE_MEMBER_LEVEL_UP;
                break;
            case VoiceRoomMsg.SCENE_MEMBER_ENTER_ROOM:
                type = ITEM_VIEW_TYPE_MEMBER_ENTER_ROOM;
                break;
            case VoiceRoomMsg.SCENE_MEMBER_FROZEN_REMIND:
                type = ITEM_VIEW_TYPE_MEMBER_FROZEN_REMIND;
                break;
            case VoiceRoomMsg.SCENE_MEMBER_FOLLOW_ROOM:
                type = ITEM_VIEW_TYPE_MEMBER_FOLLOW_ROOM;
                break;
            case VoiceRoomMsg.SCENE_MEMBER_JOIN:
                type = ITEM_VIEW_TYPE_MEMBER_JOIN;
                break;
            default:
                type = -1;
        }
        return type;
    }
}
