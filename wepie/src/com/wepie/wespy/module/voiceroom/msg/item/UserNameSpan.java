package com.wepie.wespy.module.voiceroom.msg.item;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ComposeShader;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.Shader;
import android.os.Build;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.anim.schedule.Animate;
import com.huiwan.anim.schedule.Scheduler;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.decorate.NameTextView;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;

import java.lang.ref.WeakReference;
import java.util.Map;
import java.util.WeakHashMap;

public class UserNameSpan extends ClickableSpan implements Animate {
    private static final int[] G_COLORS_RED_FLASH = new int[]{Color.WHITE, 0xFFFF9D9D, 0xFFFF9D9D, 0xFFFF9D9D, Color.WHITE};
    private static final Map<Integer, Bitmap> bmpRefMap = new WeakHashMap<>();
    private final int uid;
    private int level = 0;
    private final String name;
    private Shader shader = null;
    private final Matrix matrix = new Matrix();
    private float translateX = 0;
    private float textMeasureWidth = 0;
    private boolean needDoAnim = false;
    private final WeakReference<TextView> viewRef;
    private float textSize = 30;
    private float startPos = -1;
    private final float pos;
    private Shader blendedShader = null;
    private boolean blending = false;

    private static final int TYPE_RED = 0;
    private static final int TYPE_GOLD = 1;
    private static final int TYPE_COLOR = 2;

    private float speed = 0;
    private final boolean isRtl;
    private long currentTime = -1;

    public UserNameSpan(TextView view, int uid, String name, float startPos) {
        this.uid = uid;
        this.name = name;
        this.viewRef = new WeakReference<>(view);
        UserSimpleInfo simpleInfo = UserService.get().getCacheSimpleLocalUser(uid);
        if (simpleInfo != null) {
            level = simpleInfo.vip;
        }
        this.pos = startPos;
        isRtl = ScreenUtil.isRtl();
    }

    @Override
    public void updateDrawState(@NonNull TextPaint ds) {
        TextView view = viewRef.get();
        if (view == null || view.getMeasuredWidth() <= 0) {
            return;
        }
        ds.setUnderlineText(false);
        textSize = ds.getTextSize();
        if (startPos < 0) {
            if (isRtl) {
                float viewWidth = view.getMeasuredWidth();
                startPos = viewWidth - view.getPaddingStart() - pos;
            } else {
                startPos = pos;
            }
            this.translateX = startPos;
        }
        if (level >= ConfigHelper.getInstance().getVipConfig().getColorNameMinLevel()) {
            needDoAnim = true;
            ds.setColor(Color.BLACK);
            ds.setShadowLayer(0, 0, 0, Color.TRANSPARENT);
            initShaderIfNeed(ds, TYPE_COLOR);
            updateFlashPos(ds);
        } else if (level >= ConfigHelper.getInstance().getVipConfig().getGoldNameMinLevel()) {
            needDoAnim = true;
            ds.setColor(Color.GREEN);
            initShaderIfNeed(ds, TYPE_GOLD);
            updateFlashPos(ds);
        } else if (level >= ConfigHelper.getInstance().getVipConfig().getRedNameMinLevel()) {
            ds.setColor(Color.RED);
            ds.setShader(null);
            initShaderIfNeed(ds, TYPE_RED);
            needDoAnim = getRedShader() != null;
            updateFlashPos(ds);
        } else {
            needDoAnim = false;
            ds.setShader(null);
            ds.setColor(getNormalColor());
        }
    }

    private void initShaderIfNeed(TextPaint paint, int type) {
        if (textMeasureWidth == 0 && shader == null) {
            textMeasureWidth = paint.measureText(name, 0, name.length());
            if (type == TYPE_RED) {
                shader = getRedShader();
            } else if (type == TYPE_GOLD) {
                shader = getGoldShader();
            } else if (type == TYPE_COLOR) {
                shader = getColorShader();
            }
            paint.setShader(shader);
        }
    }

    private void updateFlashPos(TextPaint paint) {
        if (needDoAnim && shader != null) {
            if (speed <= 0) {
                speed = NameTextView.measureSpeed(name, paint);
            }
            translateX = getTranslateX();
            matrix.reset();
            matrix.postTranslate(translateX, 0);
            shader.setLocalMatrix(matrix);
            Shader finalShader = (blending && blendedShader != null) ? blendedShader : shader;
            paint.setShader(finalShader);
            if (!inScheduler) {
                inScheduler = true;
                Scheduler.register(this);
            }
        }
    }

    private float getTranslateX() {
        long time = System.currentTimeMillis();
        if (time - currentTime < 10) {
            return translateX;
        }
        currentTime = time;
        float localX = translateX;
        if (isRtl) {
            localX -= speed;
            if (localX < startPos - textMeasureWidth) {
                localX = startPos;
            }
        } else {
            localX += speed;
            if (localX > textMeasureWidth + startPos + textSize) {
                localX = startPos - textSize;
            }
        }
        return localX;
    }

    protected int getNormalColor() {
//        return VoiceRoomMsgConst.NAME_COLOR;
        return Color.WHITE;
    }

    private Shader goldShader = null;
    private Shader redShader = null;
    private Shader colorGoldShader = null;

    protected Shader getGoldShader() {
        if (goldShader == null) {
            goldShader = new LinearGradient(0, 0, textSize, textSize * 0.2f, NameTextView.G_COLORS_GOLD_DARK, null, Shader.TileMode.CLAMP);
        }
        blendedShader = null;
        blending = false;
        return goldShader;
    }

    protected Shader getRedShader() {
        if (redShader == null) {
            redShader = new LinearGradient(0, 0, textSize, textSize * 0.2f, G_COLORS_RED_FLASH, null, Shader.TileMode.CLAMP);
        }
        blendedShader = null;
        blending = false;
        return redShader;
    }

    private Bitmap getShaderBmp(int flashWidth) {
        Bitmap bitmap = bmpRefMap.get(flashWidth);
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap((int) (flashWidth * 1.4), (int) flashWidth, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
            float x1 = flashWidth * 0.2f;
            float x2 = x1 + flashWidth;
            paint.setShader(new LinearGradient(x1, 0, x2, flashWidth * 0.2f, NameTextView.G_COLORS_GOLD_TRANS, null, Shader.TileMode.CLAMP));
            canvas.drawRect(0, 0, bitmap.getWidth(), bitmap.getHeight(), paint);
            bmpRefMap.put(flashWidth, bitmap);
        }
        return bitmap;
    }

    protected Shader getColorShader() {
        if (colorGoldShader == null) {
            Bitmap bitmap = getShaderBmp((int) textSize);
            colorGoldShader = new BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP);
        }
        if (blendedShader == null) {
            float x0, x1;
            if (isRtl) {
                x0 = startPos - textMeasureWidth + 0.5F * textSize;
                x1 = startPos;
            } else {
                x0 = startPos + 0.5f * textSize;
                x1 = startPos + textMeasureWidth;
            }
            blendedShader = getBlendedShader(x0, x1);
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N_MR1)
                blendedShader = new ComposeShader(blendedShader, colorGoldShader, PorterDuff.Mode.SRC_OVER);
        }
        blending = true;
        return colorGoldShader;
    }

    protected Shader getBlendedShader(float x0, float x1) {
        return new LinearGradient(x0, 0, x1, 0, NameTextView.G_COLORS_COLORFUL_DARK, null, Shader.TileMode.CLAMP);
    }

    private boolean inScheduler = false;

    @Override
    public boolean isAnimRunning() {
        if (!needDoAnim) {
            return false;
        }
        if (shader == null) {
            return false;
        }
        View view = viewRef.get();
        if (view == null) {
            return false;
        }
        if (ContextUtil.fragmentActivityNotVisible(view.getContext())) {
            return false;
        }
        return true;
    }

    @Override
    public void onAnimSchedule() {
        invalidateView();
    }

    @Override
    public boolean schedulerMayRemove() {
        TextView view = viewRef.get();
        if (view != null) {
            Context context = view.getContext();
            Activity activity = ContextUtil.getActivityFromContext(context);
            if (activity == null) {
                return true;
            }
            if (activity.isFinishing() || activity.isDestroyed()) {
                return true;
            }
            CharSequence s = view.getText();
            if (s instanceof Spanned) {
                int index = ((Spanned) s).getSpanStart(this);
                return index < 0;
            }
        }
        return true;
    }

    @Override
    public void onSchedulerRemove() {
        inScheduler = false;
    }

    private void invalidateView() {
        View view = viewRef.get();
        if (view != null) {
            view.invalidate();
        }
    }

    @Override
    public void onClick(@NonNull View widget) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (roomInfo != null) {
            JumpUtil.enterUserInfoDetailFromVoiceRoom(widget.getContext(), this.uid, roomInfo.rid);
        } else {
            JumpUtil.enterUserInfoDetailActivity(widget.getContext(), this.uid, "");
        }
    }

    @Override
    public int getFps() {
        return 30;
    }
}