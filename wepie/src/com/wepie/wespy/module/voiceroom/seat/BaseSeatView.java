package com.wepie.wespy.module.voiceroom.seat;

import android.app.Dialog;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.WTrace;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.FaceInfo;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.decorate.NameTextView;
import com.huiwan.lib.api.plugins.friend.AddFriendCallback;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.BlackWhiteFrameLayout;
import com.huiwan.widget.SpeakerAnimView;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.component.faceanim.userface.UserFaceView;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.model.entity.voiceroom.VoiceEmotionInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.module.voiceroom.cp.anim.SeatPosition;
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginFrameLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISeatView;
import com.wepie.wespy.module.voiceroom.pk445.ISeatIntercept;

import java.util.List;
import java.util.Objects;

/**
 * Created by geeksammao on 24/10/2017.
 */

public abstract class BaseSeatView extends PluginFrameLayout implements ISeatView {
    protected ViewGroup userInfoLay;
    public DecorHeadImgView headImg;
    protected NameTextView nameTv;
    @Nullable
    protected BlackWhiteFrameLayout memberLay;
    @Nullable
    protected ImageView memberIv;
    protected ImageView emptyIv;
    protected UserFaceView faceView;
    protected TextView scoreTv;
    protected VoiceRoomInfo.SeatInfo seatInfo;

    protected SpeakerAnimView speakerAnimView;
    protected int seatNum;

    public boolean forceUpdateSeatUserInfo = true;
    private BaseSeatViewHelper seatViewHelper = new BaseSeatViewHelper(this);

    public BaseSeatView(Context context) {
        super(context);
    }

    public BaseSeatView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BaseSeatView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SeatView, defStyle, 0);
        seatNum = typedArray.getInt(R.styleable.SeatView_seat_num, 0);
        typedArray.recycle();
        nameTv.setScene(NameTextView.SCENE_DARK);
    }

    @Override
    protected void initData() {
        setClipChildren(false);
        initAnim();
        updateMicView(getMainPlugin().getRoomInfo(), RoomInfoUpdateEvent.ALL);
    }

    @Override
    public void updateMicView(VoiceRoomInfo roomInfo, RoomInfoUpdateEvent event) {
        WTrace.beginSection("BaseSeatView#updateMicView");
        VoiceRoomInfo.SeatInfo seatInfo = roomInfo.getSeatInfoByNum(seatNum);
        if (seatInfo != null) {
            if (event.features == RoomInfoUpdateEvent.F_ALL || event.seatNum == seatNum || event.uid == getCurSeatUid() ||
                    RoomInfoUpdateEvent.hasScene(event.features, RoomInfoUpdateEvent.F_WEDDING_INFO)) {
                update(roomInfo, seatInfo);
                forceUpdateSeatUserInfo = false;
            } else if (RoomInfoUpdateEvent.hasScene(event.features, RoomInfoUpdateEvent.F_WIDGET_SCOREBOARD)) {
                seatViewHelper.updateScoreInfo(seatInfo);
            }
        }
        WTrace.endSection();
    }

    public void initAnim() {
        if (speakerAnimView == null) return;
        if (headImg == null) return;

        speakerAnimView.initFromView(headImg);
        speakerAnimView.registerStopCallback(selfView -> ViewExKt.updateVisibility(speakerAnimView, false));
    }

    public int getEmptySeatRes(boolean isGaming) {
        return R.drawable.room_seat_empty_ic;
    }

    public int getSealedSeatRes() {
        return R.drawable.room_seat_sealed_ic;
    }

    public void update(final VoiceRoomInfo roomInfo, final VoiceRoomInfo.SeatInfo seatInfo) {
        WTrace.beginSection("BaseSeatView#update");
        VoiceRoomInfo.SeatInfo currentSeatInfo = new VoiceRoomInfo.SeatInfo(seatInfo);
        if (roomInfo.isAdvancedOrAnchorRoom() && seatInfo.seat_num == VoiceRoomInfo.SeatInfo.OWNER_SEAT_NUM && seatInfo.uid <= 0) {
            currentSeatInfo.uid = roomInfo.owner;
            currentSeatInfo.seat_status = VoiceRoomInfo.SeatInfo.SEAT_STATUS_EMPTY;
        }
        this.seatInfo = currentSeatInfo;
        seatViewHelper.update(roomInfo, currentSeatInfo);
        WTrace.endSection();
    }

    protected boolean isWeddingOwnerSeat() {
        return false;
    }

    protected boolean forbidClickEmptySeat() {
        return false;
    }

    protected void refreshUserInfo(@Nullable UserSimpleInfo old, UserSimpleInfo simpleInfo) {
        if (old == null || old.uid != simpleInfo.uid ||
                !Objects.equals(old.getRemarkName(), simpleInfo.getRemarkName()) ||
                old.vip != simpleInfo.vip) {
            nameTv.setUserName(simpleInfo);
            if (nameTv.getVisibility() != VISIBLE) {
                nameTv.setVisibility(VISIBLE);
            }
        }
        headImg.showUserHeadWithDecorationCache(simpleInfo.uid, simpleInfo.headimgurl);
    }

    protected void showEmpty() {

    }

    public void standUp(final VoiceRoomInfo roomInfo, final VoiceRoomInfo.SeatInfo seatInfo, final Dialog dialog) {
        for (ISeatIntercept intercept : seatViewHelper.getSeatInterceptList()) {
            boolean interceptFlag = intercept.standUpIntercept(seatInfo.seat_num, mContext, () -> {
                dialog.dismiss();
                return null;
            });
            if (interceptFlag) {
                return;
            }
        }
        if (roomInfo.pkInfo.isInPk(seatInfo.uid)) {
            DialogBuild.newBuilder(mContext).setSingleBtn(false).setTitle("").setContent(R.string.voice_room_stand_c_pk_tip).setCanCancel(true).setDialogCallback(new DialogBuild.DialogCallback() {
                @Override
                public void onClickSure() {
                    RoomViewLogic.unseat(mContext, roomInfo.rid, seatInfo.seat_num);
                    dialog.dismiss();
                }

                @Override
                public void onClickCancel() {

                }
            }).show();
        } else {
            RoomViewLogic.unseat(mContext, roomInfo.rid, seatInfo.seat_num);
            dialog.dismiss();
        }
    }

    public void forceUp(final VoiceRoomInfo roomInfo, final VoiceRoomInfo.SeatInfo seatInfo, final Dialog dialog) {
        for (ISeatIntercept intercept : seatViewHelper.getSeatInterceptList()) {
            boolean interceptFlag = intercept.liftUpUserIntercept(seatInfo.seat_num, seatInfo.uid, mContext, () -> {
                dialog.dismiss();
                return null;
            });
            if (interceptFlag) {
                return;
            }
        }
        if (roomInfo.pkInfo.isInPk(seatInfo.uid)) {
            DialogBuild.newBuilder(mContext).setSingleBtn(false).setContent(R.string.voice_room_force_stand_c_pk_tip)
                    .setCanCancel(true).setDialogCallback(new DialogBuild.DialogCallback() {
                        @Override
                        public void onClickSure() {
                            RoomSenderPresenter.forceUp(roomInfo.rid, seatInfo.seat_num, seatInfo.uid);
                            dialog.dismiss();
                        }

                        @Override
                        public void onClickCancel() {

                        }
                    }).show();
        } else {
            RoomSenderPresenter.forceUp(roomInfo.rid, seatInfo.seat_num, seatInfo.uid);
            dialog.dismiss();
        }
    }

    public int getCurSeatUid() {
        if (seatInfo != null) return seatInfo.uid;
        return 0;
    }

    public void updateFaceInfo(FaceInfo faceInfo, int faceResult) {
        faceView.updateRoomFaceInfo(faceInfo, faceResult);
    }

    public void startSpeakAnim() {
        if (speakerAnimView == null) return;
        if (headImg == null) return;
        speakerAnimView.setVisibility(VISIBLE);
        speakerAnimView.initFromView(headImg);
        speakerAnimView.startAnim();
    }

    public void stopSpeakAnim() {
        speakerAnimView.stopAnim();
    }

    @Override
    public SeatPosition getSeatPosition() {
        int heightDiv = 0;
        if (!StatusBarUtil.isSupportFullScreen()) {
            heightDiv = ScreenUtil.getStatusBarHeight();
        }
        int[] loc = new int[2];
        headImg.getLocationOnScreen(loc);
        int w = headImg.getWidth();
        int h = headImg.getHeight();
        return new SeatPosition(seatNum, loc[0], loc[1] - heightDiv, w, h);
    }

    @Override
    public void onUserSpeak(List<SpeakerInfo> speakerInfos) {
        for (SpeakerInfo speakerInfo : speakerInfos) {
            if (speakerInfo.volume <= 0) continue;//声音太小不显示动画

            if (speakerInfo.uid == getCurSeatUid()) {
                startSpeakAnim();
            }
        }
    }


    @Override
    public void updateSeatUserInfo(User user) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(0);
        VoiceRoomInfo.SeatInfo seatInfo = roomInfo.getSeatInfoByNum(seatNum);
        if (seatInfo != null && user.uid == seatInfo.uid) {
            update(roomInfo, seatInfo);
        }
    }

    @Override
    public void showSeatUserEmotion(VoiceEmotionInfo emotionInfo) {
        int uid = emotionInfo.getEmotionInfo().send_uid;
        int faceId = emotionInfo.getEmotionInfo().face_id;
        int res = emotionInfo.getRandResult();
        FaceInfo faceInfo = ConfigHelper.getInstance().getFaceById(faceId);
        if (faceInfo == null) return;

        if (uid == getCurSeatUid()) {
            updateFaceInfo(faceInfo, res);
        }
    }

    @Override
    public @Nullable FrameLayout getViewPluginLay() {
        return null;
    }

    @Override
    public ViewGroup getUserInfoLay() {
        return userInfoLay;
    }

    @Override
    public void setSeatBorder(String startColor, String endColor, int emptyResId, boolean isOpen) {

    }

    protected View getClickView() {
        return this;
    }

    protected AddFriendCallback getSeatAddFriendCallback() {
        return null;
    }

    //座位号
    public void setSeatNum(int seatNum) {
        this.seatNum = seatNum;
    }

    public int getSeatNum() {
        return this.seatNum;
    }

    @Override
    public void addIntercept(@NonNull ISeatIntercept intercept) {
        seatViewHelper.addSeatIntercept(intercept);
    }

    @Override
    public void removeIntercept(@NonNull ISeatIntercept intercept) {
        seatViewHelper.removeSeatIntercept(intercept);
    }

    @Override
    public void showNameLay() {

    }

    @Override
    public void hideNameLay() {

    }
}