package com.wepie.wespy.module.voiceroom.seat;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.marry.WeddingInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;

public class WeddingGuestSeatView extends BaseSeatView {
    private TextView identitiyIconTv;
    private ImageView forbidMicIv;

    public WeddingGuestSeatView(Context context) {
        super(context);
    }

    public WeddingGuestSeatView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.wedding_guest_seat_view, this);
        userInfoLay = findViewById(R.id.room_guest_info_lay);
        headImg = findViewById(R.id.room_guest_head_img);
        nameTv = findViewById(R.id.room_couple_user_name_tv);
        emptyIv = findViewById(R.id.room_user_empty_iv);
        identitiyIconTv = findViewById(R.id.identitiy_icon_tv);
        forbidMicIv = findViewById(R.id.room_user_forbid_mic_iv);
        faceView = findViewById(R.id.room_user_face_view);
        speakerAnimView = findViewById(R.id.speaker_anim_view);

        headImg.setBorderColor(Color.WHITE);
        headImg.setBorderWidth(ScreenUtil.dip2px(1));
    }

    @Override
    public int getEmptySeatRes(boolean isGaming) {
        return R.drawable.wedding_empty_seat;
    }

    @Override
    public int getSealedSeatRes() {
        return R.drawable.room_seat_sealed_ic;
    }

    @Override
    public void update(final VoiceRoomInfo roomInfo, final VoiceRoomInfo.SeatInfo seatInfo) {
        super.update(roomInfo, seatInfo);
        if (seatInfo.isEmpty()) {
            forbidMicIv.setVisibility(seatInfo.can_speak ? View.GONE : View.VISIBLE);
            identitiyIconTv.setVisibility(GONE);
        } else if (seatInfo.isSealed()) {
            forbidMicIv.setVisibility(View.GONE);
            identitiyIconTv.setVisibility(GONE);
        } else {
            forbidMicIv.setVisibility(seatInfo.can_speak ? View.GONE : View.VISIBLE);
            setIdentity(seatInfo.uid, roomInfo.weddingInfo);
        }
    }

    private void setIdentity(int seatUid, WeddingInfo weddingInfo) {
        if (weddingInfo.isGroomsMan(seatUid)) {
            identitiyIconTv.setVisibility(VISIBLE);
            identitiyIconTv.setBackgroundResource(R.drawable.wedding_groomsmaid_round_tag_icon);
            identitiyIconTv.setText(R.string.best_man);
            identitiyIconTv.setTextColor(ResUtil.getColor(R.color.white));
        } else if (weddingInfo.isBridesMaid(seatUid)) {
            identitiyIconTv.setVisibility(VISIBLE);
            identitiyIconTv.setBackgroundResource(R.drawable.wedding_bridesmaid_round_tag_icon);
            identitiyIconTv.setText(R.string.maid_of_honor);
            identitiyIconTv.setTextColor(ResUtil.getColor(R.color.white));
        } else if (weddingInfo.isHost(seatUid)) {
            identitiyIconTv.setVisibility(VISIBLE);
            identitiyIconTv.setBackgroundResource(R.drawable.wedding_host_round_tag_icon);
            identitiyIconTv.setText(R.string.mc_people);
            identitiyIconTv.setTextColor(ResUtil.getColor(R.color.wedding_host_text_color));
        } else {
            identitiyIconTv.setVisibility(GONE);
        }
    }
}
