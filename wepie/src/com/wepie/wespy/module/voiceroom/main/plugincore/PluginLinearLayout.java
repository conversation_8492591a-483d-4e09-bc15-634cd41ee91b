package com.wepie.wespy.module.voiceroom.main.plugincore;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;

import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IMainPlugin;

/**
 * Created by bigwen on 2019/4/22.
 */
public abstract class PluginLinearLayout extends LinearLayout {

    protected Context mContext;

    public PluginLinearLayout(Context context) {
        super(context);
        mContext = context;
        initView();
        initRegister();
    }

    public PluginLinearLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
        initRegister();
    }

    /**
     * ⚠ 注意：️
     * 此方法在子类成员变量初始化之前
     * 不要这里使用子类中已经实例化的成员变量
     * 或者在此类中实例化子类成员变量
     *
     * 不要在这里调用getMainPlugin()
     */
    protected abstract void initView();

    /**
     * ⚠ 注意：️
     * 此方法在在onAttachedToWindow中调用
     * 这里可以调用getMainPlugin()，获取数据初始化
     */
    protected abstract void initData();

    private void initRegister() {
        VoicePluginService.registerPlugin(this);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        PluginUtil.log("onAttachedToWindow " + this.getClass().getSimpleName());
        VoicePluginService.registerPlugin(this);
        initData();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        PluginUtil.log("onDetachedFromWindow " + this.getClass().getSimpleName());
        VoicePluginService.unregisterPlugin(this);
    }

    /**
     * 不要在View的构造方法和PluginView的initView方法中调用
     */
    public IMainPlugin getMainPlugin() {
        return PluginUtil.getMainPlugin(mContext);
    }
}
