package com.wepie.wespy.module.voiceroom.main.plugincore;

import android.text.TextUtils;

import com.wepie.liblog.main.HLog;
import com.wepie.wespy.module.voiceroom.main.plugincore.global.GlobalDefaultPlugin;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019/4/20.
 * 语音房模块间调用
 */
public class GlobalPluginService {

    private static final String TAG = GlobalPluginService.class.getSimpleName();
    private static List<Object> mainPlugin = new ArrayList<>();

    /**
     * 注意注册时机，尽量早注册，建议在initView后，和onAttach方法中同时调用（兼容了同一个对象重复注册），防止removeView后再addView没有注册
     * <p>
     * 注册时要保证实现的接口可以调用，建议在ui初始化之后调用
     */
    public static void registerPlugin(Object object) {
        if (!hasObject(object)) {
            HLog.i(TAG, "registerPlugin: " + object.getClass().getSimpleName() + " size = " + mainPlugin.size());
            mainPlugin.add(object);
        }
    }

    private static boolean hasObject(Object object) {
        for (Object plugin : mainPlugin) {
            if (plugin == object) return true;
        }
        return false;
    }

    public static <T> boolean hasPlugin(Class<T> interfaceClazz) {
        for (Object plugin : mainPlugin) {
            if (interfaceClazz.isAssignableFrom(plugin.getClass())) return true;
        }
        return false;
    }

    public static void unregisterPlugin(Object clazz) {
        for (Object object : mainPlugin) {
            if (object == clazz) {
                HLog.i(TAG, "unregisterPlugin: " + clazz.getClass().getSimpleName() + " size = " + mainPlugin.size());
                mainPlugin.remove(object);
                break;
            }
        }
    }

    private static GlobalDefaultPlugin defaultPlugin = new GlobalDefaultPlugin();

    public static <T> T getPlugin(Class<T> interfaceClazz) {
        if (!interfaceClazz.isAssignableFrom(defaultPlugin.getClass())) {
            throw new IllegalArgumentException("This interface is not implemented in the GlobalDefaultPlugin class:" + interfaceClazz.getSimpleName());
        }
        for (Object object : mainPlugin) {
            if (interfaceClazz.isAssignableFrom(object.getClass())) {
                return (T) object;
            }
        }
        return (T) defaultPlugin;
    }


    public static void clearAllPlugin() {
        HLog.i(TAG, "clearAllPlugin: ");
        mainPlugin.clear();
    }
}
