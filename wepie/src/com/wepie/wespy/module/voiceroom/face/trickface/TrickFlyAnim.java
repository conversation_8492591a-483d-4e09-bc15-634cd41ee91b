package com.wepie.wespy.module.voiceroom.face.trickface;

import static android.view.View.INVISIBLE;
import static android.view.View.VISIBLE;

import android.animation.ValueAnimator;
import android.view.View;

import androidx.annotation.NonNull;

public class TrickFlyAnim implements ValueAnimator.AnimatorUpdateListener {
    private View animView;
    private int rotateDegree;
    private float toScale;
    private float fromX;
    private float fromY;
    private float toX;
    private float toY;

    private float lpWidth;
    private float lpHeight;

    public TrickFlyAnim(View animView, float toScale, int rotateNum, float fromX, float fromY, float toX, float toY) {
        this.animView = animView;
        this.rotateDegree = -rotateNum * 360;
        this.toScale = toScale;
        this.fromX = fromX;
        this.fromY = fromY;
        this.toX = toX;
        this.toY = toY;
        this.lpWidth = animView.getLayoutParams().width;
        this.lpHeight = animView.getLayoutParams().height;
    }

    private void update(float fraction) {
        if (animView.getMeasuredWidth() == 0) {
            animView.setVisibility(INVISIBLE);
        } else if (animView.getVisibility() == INVISIBLE && animView.getMeasuredWidth() != 0) {
            animView.setVisibility(VISIBLE);
        }
        float scale = (toScale - 1) * fraction + 1;
        animView.setRotation(rotateDegree * fraction);
        if (!Float.isNaN(scale)) {
            animView.setScaleX(scale);
            animView.setScaleY(scale);
        }
        animView.setTranslationX((toX - fromX) * fraction + fromX - lpWidth / 2);
        animView.setTranslationY((toY - fromY) * fraction + fromY - lpHeight / 2);
    }

    @Override
    public void onAnimationUpdate(ValueAnimator animation) {
        Object value = animation.getAnimatedValue();
        if (value == null || Float.isNaN((Float) value)) {
            return;
        }
        update((Float) value);
    }

    @NonNull
    @Override
    public String toString() {
        return "FlyAnim{" +
                "rotateDegree=" + rotateDegree +
                ", toScale=" + toScale +
                ", fromX=" + fromX +
                ", fromY=" + fromY +
                ", toX=" + toX +
                ", toY=" + toY +
                '}';
    }
}
