package com.wepie.wespy.module.voiceroom.rain

import android.content.Context
import android.graphics.Bitmap
import android.util.ArrayMap
import android.util.AttributeSet
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.propextra.RedPacketSkinExtra
import com.wejoy.weplay.ex.lifecycle.observe
import com.wejoy.weplay.helper.life.base.LifeRegister
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.wespy.module.shop.dialogs.PreviewRainHelper
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginFrameLayout

class DelayRpFullView : PluginFrameLayout {

    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    override fun initView() {}

    val life = LifeRegister()
    private val preDownHelper by lazy {
        PreviewRainHelper(context, life, ImageLoadInfo.newInfo())
    }

    private val holder by lazy {
        DelayRpFullViewHolder(context, this)
    }

    override fun initData() {
        val viewModel = VoiceRoomService.getInstance().rpDelayViewModel
        viewModel.showBig.observe(mainPlugin.life) {
            if (it != null) {
                //触发红包时候 DelayRpSmallView 先收到 liveData 执行 visibility 发送同步屏障
                //post 通过同步屏障 在 DelayRpSmallView onLayout后执行
                post {
                    holder.update(it)
                }
            }
        }
        viewModel.liveData.observe(mainPlugin.life) {
            holder.update(it)
            it?.list?.run {
                for (item in this) {
                    val redPacketSkinExtra: RedPacketSkinExtra? =
                        ConfigHelper.getInstance().propConfig.getPropItem(item.packetSkinId)?.redPacketSkinExtra
                    if (redPacketSkinExtra != null) {
                        preDownHelper.getCoreChain(redPacketSkinExtra)
                            .load(ArrayMap<String, Bitmap>()) {}
                    }

                }
            }
        }
        viewModel.countDownLiveData.observe(mainPlugin.life) {
            holder.refreshTime()
        }
        viewModel.initSmallRect.observe(mainPlugin.life) {
            holder.initSmallRect(it)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        holder.destory()
        life.destroy()
    }
}