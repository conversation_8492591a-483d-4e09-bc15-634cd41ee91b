package com.wepie.wespy.module.voiceroom.util;

import android.util.SparseArray;
import android.util.SparseIntArray;

import com.huiwan.voiceservice.VoiceManager;
import com.wepie.lib.api.plugins.voice.VoiceLogUtil;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.main.presenter.VoiceStream;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.http.api.UtilApi;

import java.util.ArrayList;

public class VoiceMuteManager {
    private static final String TAG = "VoiceMuteManager";
    private static final long INTERVAL = 3000;
    private static final SparseArray<Long> timeArray = new SparseArray<>();
    private static final SparseIntArray muteArray = new SparseIntArray();

    public static void dealSpiteSpeaker(int uid, int rid) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
        if (roomInfo.isUserCanSpeak(uid)) {
            checkUnMute(uid);
            return;
        }

        long time = System.currentTimeMillis();

        Long last_deal_time = timeArray.get(uid);
        if (last_deal_time == null) {
            last_deal_time = time;
            timeArray.put(uid, last_deal_time);
        }

        if (time - last_deal_time >= INTERVAL) {
            checkMute(uid, rid);
        }
    }

    public static void resetLastDealTime(int uid) {
        timeArray.remove(uid);
    }

    public static void handleSyncRoom(VoiceRoomInfo roomInfo) {
        ArrayList<VoiceRoomInfo.SeatInfo> seatInfos = roomInfo.seatInfos;
        int size = seatInfos.size();
        for (int i = 0; i < size; i++) {
            VoiceRoomInfo.SeatInfo seatInfo = seatInfos.get(i);
            if (seatInfo.can_speak) checkUnMute(seatInfo.uid);
        }
    }

    public static void filterVoiceStream() {
        if (VoiceStream.hasFiltered(TAG)) {
            return;
        }
        VoiceStream.filter(TAG, uid -> {
            VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo();
            if (info.rid <= 0) {
                return false;
            }
            boolean muteRoom = VoiceRoomService.getInstance().isMuteRoom(info.rid);
            boolean filter = muteRoom || !info.isUserCanSpeak(uid);
            if (filter) {
                VoiceManager.getInstance().muteRemoteAudioStream(uid, true);
            }
            return filter;
        });
    }

    private static void checkMute(int uid, int rid) {
        VoiceManager.getInstance().muteRemoteAudioStream(uid, true);
        muteArray.put(uid, 1);
        UtilApi.kickAgoraUser(rid, uid, null);
        VoiceLogUtil.logMute(TAG, "mute : " + uid + " in " + rid);
    }

    private static void checkUnMute(int uid) {
        if (isUserMuted(uid)) {
            VoiceManager.getInstance().muteRemoteAudioStream(uid, false);
            muteArray.delete(uid);
            VoiceLogUtil.logMute(TAG, "un mute : " + uid);
        }
        timeArray.remove(uid);//是可以说话状态重置上一次检测到异常的时间
    }

    private static boolean isUserMuted(int uid) {
        return muteArray.indexOfKey(uid) >= 0;
    }

    //语音房退房把这里清掉
    public static void clear() {
        muteArray.clear();
        timeArray.clear();
        VoiceStream.clear(TAG);
    }
}
