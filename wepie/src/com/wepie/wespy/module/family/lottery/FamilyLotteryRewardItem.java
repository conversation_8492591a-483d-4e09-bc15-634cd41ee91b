package com.wepie.wespy.module.family.lottery;

import androidx.annotation.Nullable;

import com.huiwan.configservice.constentity.FamilyConfigDetail;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.ConfigHelper;
import com.wepie.wespy.module.family.common.FamilyPropItemInterface;

/**
 * date 2019-12-25
 * email <EMAIL>
 *
 * <AUTHOR>
 */
class FamilyLotteryRewardItem {
    static final int TYPE_PROP_ITEM = 1;
    static final int TYPE_PROP_LEVEL_TITLE = 2;
    int type;

    FamilyPropItemInterface propItem;

    int lotteryLevel;

    private FamilyLotteryRewardItem(){}

    static FamilyLotteryRewardItem forLevel(int lotteryLevel) {
        FamilyLotteryRewardItem item = new FamilyLotteryRewardItem();
        item.type = TYPE_PROP_LEVEL_TITLE;
        item.lotteryLevel = lotteryLevel;
        return item;
    }

    static FamilyLotteryRewardItem forPropItem(FamilyConfigDetail.LotteryReward reward) {
        FamilyLotteryRewardItem item = new FamilyLotteryRewardItem();
        item.type = TYPE_PROP_ITEM;
        item.propItem = new LotteryRewardFamilyPropItem(reward);
        return item;
    }

    private static class LotteryRewardFamilyPropItem implements FamilyPropItemInterface {
        private FamilyConfigDetail.LotteryReward lotteryReward;
        private PropItem item;


        public LotteryRewardFamilyPropItem(FamilyConfigDetail.LotteryReward lotteryReward) {
            this.lotteryReward = lotteryReward;
            this.item = ConfigHelper.getInstance().getPropConfig().getPropItem(lotteryReward.getPropId());
        }

        @Nullable
        @Override
        public PropItem getPropItem() {
            return item;
        }

        @Override
        public String getName() {
            return lotteryReward.getName();
        }

        @Override
        public int getCount() {
            return 1;
        }
    }
}
