package com.wepie.wespy.module.family.box.grab;

import android.content.Context;
import android.content.DialogInterface;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ToastUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.ILife;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.model.entity.family.FamilyAvailableBox;
import com.wepie.wespy.model.entity.family.FamilyGainBoxResult;
import com.wepie.wespy.module.common.SingleClickListener;
import com.wepie.wespy.module.family.FamilyUiConst;
import com.wepie.wespy.module.family.box.BoxAnimUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.FamilyApi;

import java.util.List;

/**
 * Created by bigwen on 2019-11-15.
 */
public class FamilyGrabBoxDialog extends FrameLayout {

    private Context mContext;
    private ImageView boxBgIv, grabIv;
    private View openLay;
    private FamilyGrabBoxDetailView detailView;
    private ImageView coinAnimIv;

    public FamilyGrabBoxDialog(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public FamilyGrabBoxDialog(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.family_grab_box_dialog, this);
        boxBgIv = findViewById(R.id.box_bg_iv);
        grabIv = findViewById(R.id.button_iv);
        detailView = findViewById(R.id.grab_box_detail_view);
        openLay = findViewById(R.id.grab_content_lay);
        coinAnimIv = findViewById(R.id.coin_anim_iv);

        detailView.setVisibility(INVISIBLE);
        coinAnimIv.setVisibility(INVISIBLE);

        playGrabBtnScaleAnim();
    }


    /**
     * @param skipOpen 是否跳过open界面
     * @param canGrab  这个宝箱能否抢
     */
    private void setData(final int boxId, int boxLevel, final boolean skipOpen, final boolean canGrab, final ProgressDialogUtil progressDialogUtil) {
        //设置背景，图片
        detailView.setBoxLevel(boxLevel);
        WpImageLoader.load(FamilyUiConst.getGrabBoxBg(boxLevel), boxBgIv);
        WpImageLoader.load(FamilyUiConst.getGrabBoxBtn(boxLevel), grabIv);

        if (canGrab) {
            if (skipOpen) {
                detailView.setVisibility(VISIBLE);
                openLay.setVisibility(GONE);
                FamilyApi.grabBox(boxId, new LifeDataCallback<FamilyGainBoxResult>(this) {
                    @Override
                    public void onSuccess(Result<FamilyGainBoxResult> result) {
                        progressDialogUtil.hideLoading();
                        detailView.updateData(boxId, 300);//直接请求，服务器反应慢，导致抢宝箱的人还没有自己，

                    }

                    @Override
                    public void onFail(int i, String s) {
                        progressDialogUtil.hideLoading();
                        ToastUtil.show(s);
                    }
                });
            } else {
                progressDialogUtil.hideLoading();
                detailView.setVisibility(GONE);
                openLay.setVisibility(VISIBLE);

                grabIv.setOnClickListener(new SingleClickListener() {
                    @Override
                    public void onSingleClick(View v) {
                        grabBox(boxId, skipOpen);
                    }
                });
            }
        } else {
            progressDialogUtil.hideLoading();
            detailView.setVisibility(VISIBLE);
            openLay.setVisibility(GONE);
            detailView.updateData(boxId, 0);
        }
    }

    private void grabBox(final int boxId, boolean skipOpen) {
        playCoinRotation();
        FamilyApi.grabBox(boxId, new LifeDataCallback<FamilyGainBoxResult>(this) {
            @Override
            public void onSuccess(Result<FamilyGainBoxResult> result) {
                playShowGrabDetail(boxId);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
                playGrabBtnScaleAnim();
            }
        });
    }

    private void playGrabBtnScaleAnim() {
        grabIv.clearAnimation();
        coinAnimIv.clearAnimation();
        coinAnimIv.setVisibility(INVISIBLE);
        grabIv.setVisibility(VISIBLE);
        BoxAnimUtil.grabBtnAnim(grabIv);
    }

    private void playCoinRotation() {
        grabIv.clearAnimation();
        coinAnimIv.clearAnimation();
        coinAnimIv.setVisibility(VISIBLE);
        grabIv.setVisibility(INVISIBLE);
        BoxAnimUtil.coinRotationAnim(coinAnimIv);
    }

    private void playShowGrabDetail(int boxId) {
        grabIv.clearAnimation();
        coinAnimIv.clearAnimation();
        coinAnimIv.setVisibility(INVISIBLE);
        grabIv.setVisibility(VISIBLE);
        BoxAnimUtil.openBoxAnim(openLay, detailView);
        detailView.updateData(boxId, 300);
    }

    private void clearAllAnim() {
        grabIv.clearAnimation();
        coinAnimIv.clearAnimation();
        openLay.clearAnimation();
        detailView.clearAnimation();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        clearAllAnim();
    }

    public void setCallback(DialogCallback callback) {
        detailView.setCallback(callback);
    }

    private static void showDialog(final Context context, int boxId, int boxLevel, boolean skipOpen, final boolean canGrab, ProgressDialogUtil progressDialogUtil, final DismissCallback dismissCallback) {
        final FamilyGrabBoxDialog familyGrabBoxDialog = new FamilyGrabBoxDialog(context);
        familyGrabBoxDialog.setData(boxId, boxLevel, skipOpen, canGrab, progressDialogUtil);
        int resStyle = StatusBarUtil.isSupportFullScreen() ? R.style.dialog_background_80 : R.style.dialog_style_custom;
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, resStyle);
        dialog.setContentView(familyGrabBoxDialog);
        dialog.setCanceledOnTouchOutside(false);
        dialog.initFullWidth();
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (canGrab) {
                    EventDispatcher.postFamilyGroupBoxRefresh(null);
                }
                StatusBarUtil.setStatusFontDarkColor(context);
                if (dismissCallback != null) dialog.dismiss();
            }
        });
        familyGrabBoxDialog.setCallback(new DialogCallback() {
            @Override
            public void onFinish() {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    public static void showWithRequest(final Context context, final int boxId, final int boxLevel, final boolean skipOpen, final DismissCallback dismissCallback) {
        final ProgressDialogUtil progressDialogUtil = new ProgressDialogUtil();
        progressDialogUtil.showLoadingDelay(context);
        ILife life = ContextUtil.getLife(context);
        FamilyApi.getAvailableBox(new LifeDataCallback<List<FamilyAvailableBox>>(life) {
            @Override
            public void onSuccess(Result<List<FamilyAvailableBox>> result) {
                EventDispatcher.postFamilyGroupBoxRefresh(result.data);
                boolean canGrab = false;
                for (FamilyAvailableBox familyAvailableBox : result.data) {
                    if (familyAvailableBox.getBoxId() == boxId) {
                        canGrab = true;
                        break;
                    }
                }
                showDialog(context, boxId, boxLevel, skipOpen, canGrab, progressDialogUtil, dismissCallback);
            }

            @Override
            public void onFail(int i, String s) {
                progressDialogUtil.hideLoading();
                ToastUtil.show(s);
            }
        });
    }

    public interface DialogCallback {
        void onFinish();
    }

    public interface DismissCallback {
        void onDismiss();
    }

}