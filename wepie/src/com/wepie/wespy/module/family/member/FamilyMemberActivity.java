package com.wepie.wespy.module.family.member;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.widget.SimpleOutlineProvider;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.family.detail.FamilyMemberInterface;
import com.wepie.wespy.module.family.main.mine.member.FamilyMemberConst;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-10-14.
 */
public class FamilyMemberActivity extends BaseActivity implements View.OnClickListener {

    public static final int TYPE_DEFAULT = 0;
    public static final int TYPE_CHOOSE = 1;
    private EditText editText;
    private FamilyMemberItemAdapter adapter;
    private FamilyMemberPresenter presenter;
    private BaseWpActionBar actionBar;
    private FamilyMemberBottomPreviewView previewView;
    private View placeHolderView;
    private View selectAllLay;
    private ImageView selectAllIv;
    private boolean isSelectAll = false;
    private TextView coinTv;
    private ImageView coinSortIv;
    private TextView activeTv;
    private ImageView activeSortIv;
    private ImageView activityBannerIv;
    private TextView selectAllTv;
    private int sortMode = FamilyMemberConst.SORT_MODE_WEEK_DOWN;
    private int sortType = FamilyMemberConst.SORT_TYPE_DEFAULT;
    private int familyId = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_family_member);
        editText = findViewById(R.id.edit_text);
        actionBar = findViewById(R.id.action_bar);
        activityBannerIv = findViewById(R.id.activity_banner_iv);
        activityBannerIv.setClipToOutline(true);
        activityBannerIv.setOutlineProvider(new SimpleOutlineProvider(ScreenUtil.dip2px(8F)));
        WpImageLoader.load(
                ResUtil.getStr(R.string.family_title_banner_icon),
                activityBannerIv,
                ImageLoadInfo.newInfo().error(R.drawable.family_title_banner_icon).placeholder(R.drawable.family_title_banner_icon)
        );
        RecyclerView listView = findViewById(R.id.list_view);
        previewView = findViewById(R.id.preview_view);
        placeHolderView = findViewById(R.id.place_holder_view);
        selectAllLay = findViewById(R.id.select_lay);
        selectAllIv = findViewById(R.id.select_all_iv);
        coinTv = findViewById(R.id.coin_sort_tv);
        coinSortIv = findViewById(R.id.coin_sort_iv);
        activeTv = findViewById(R.id.active_sort_tv);
        activeSortIv = findViewById(R.id.active_sort_iv);
        selectAllTv = findViewById(R.id.select_all_tv);
        actionBar.addTitleAndBack(ResUtil.getStr(R.string.family_select_object_title));
        if (getIntent().getIntExtra(IntentConfig.FAMILY_MEMBER_TYPE, TYPE_DEFAULT) == TYPE_CHOOSE) {
            selectAllLay.setVisibility(View.VISIBLE);
            adapter = new FamilyMemberItemAdapter(this, FamilyMemberItemAdapter.TYPE_CHOOSE);
            placeHolderView.setVisibility(View.VISIBLE);
            previewView.setVisibility(View.VISIBLE);
            String uidStr = getIntent().getStringExtra(IntentConfig.UID_LIST);
            List<Integer> uidList = StringUtil.uidString2Array(uidStr);
            adapter.setChooseData(uidList);
        } else {
            String source = getIntent().getStringExtra("source");
            selectAllLay.setVisibility(View.GONE);
            adapter = new FamilyMemberItemAdapter(this);
            adapter.setSource(source == null ? "" : source);
            placeHolderView.setVisibility(View.GONE);
            previewView.setVisibility(View.GONE);
            sortType = FamilyMemberConst.SORT_TYPE_ACTIVE;
            sortMode = FamilyMemberConst.SORT_MODE_DEFAULT;
        }
        listView.setLayoutManager(new LinearLayoutManager(this));
        listView.setAdapter(adapter);
        familyId = getIntent().getIntExtra(IntentConfig.FAMILY_ID, 0);
        presenter = new FamilyMemberPresenter(this);
        presenter.init(familyId);

        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                presenter.search(editText.getText().toString().trim(), false);
            }
        });

        adapter.setCallback(new FamilyMemberAdapterCallback() {
            @Override
            public void onSelect() {
                previewView.refreshData(adapter.getChooseItemList());
                checkSelectAll();
            }

            @Override
            public void onDelete() {
//                previewView.refreshData(adapter.getChooseItemList());
            }
        });

        previewView.setAdapterCallback(new FamilyMemberAdapterCallback() {
            @Override
            public void onSelect() {
            }

            @Override
            public void onDelete() {
                List<Integer> uidList = new ArrayList<>();
                for (FamilyMemberInterface familyMemberInterface : previewView.getDataList()) {
                    uidList.add(familyMemberInterface.getUid());
                }
                adapter.setChooseData(uidList);
                checkSelectAll();
            }
        });

        previewView.setEnterCallback(new FamilyMemberEnterCallback() {
            @Override
            public void onEnter() {
                List<Integer> chooseUid = adapter.getChooseUid();
                Intent intent = new Intent();
                intent.putExtra(IntentConfig.UID_LIST, StringUtil.uidArray2String(chooseUid));
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });
        checkSelectAll();
        coinSortIv.setOnClickListener(this);
        coinTv.setOnClickListener(this);
        activeSortIv.setOnClickListener(this);
        activeTv.setOnClickListener(this);
        selectAllTv.setOnClickListener(this);
        activityBannerIv.setOnClickListener(this);
        selectAllIv.setOnClickListener(this);
    }

    private void checkSelectAll() {
        if (adapter.getChooseUid().size() == adapter.getDataList().size() && adapter.getDataList().size() > 0) {
            isSelectAll = true;
        } else {
            isSelectAll = false;
        }
        refreshSelectAll();
    }

    private void refreshSelectAll() {
        selectAllIv.setImageResource(isSelectAll ? R.drawable.ic_multi_select : R.drawable.ic_un_select);
    }

    public void refreshList(List<FamilyMemberInterface> familyMemberInterfaceList, String searchStr, boolean isInit) {
        adapter.refreshDataList(familyMemberInterfaceList, searchStr);
        onSortModeChange(sortMode, sortType);
        checkSelectAll();
        if (isInit) {
            adapter.handleSelect();
        }
    }

    public void refreshTitle(int memberCount, int totalCount) {
        actionBar.refreshTitleText(ResUtil.getStr(R.string.family_select_object_title_1, memberCount, totalCount));
    }

    @Override
    public void onClick(View v) {
        if (v == coinSortIv || v == coinTv) {
            setNextMode();
            sortType = FamilyMemberConst.SORT_TYPE_COIN;
            onSortModeChange(sortMode, sortType);
        } else if (v == activeTv || v == activeSortIv) {
            setNextMode();
            sortType = FamilyMemberConst.SORT_TYPE_ACTIVE;
            onSortModeChange(sortMode, sortType);
        } else if (v == selectAllTv || v == selectAllIv) {
            if (isSelectAll) {
                adapter.setChooseData(new ArrayList<Integer>());
            } else {
                adapter.setChooseData(adapter.getTotalUidList());
            }
            previewView.refreshData(adapter.getChooseItemList());
            checkSelectAll();
        } else if (v == activityBannerIv) {
            JumpUtil.gotoFamilyTitleDetailActivity(activityBannerIv.getContext(), familyId, TrackSource.OTHER_FAMILY_MEMBER_PAGE);
        }
    }

    private void setNextMode() {
        if (sortMode == FamilyMemberConst.SORT_MODE_WEEK_DOWN) {
            sortMode = FamilyMemberConst.SORT_MODE_WEEK_UP;
        } else {
            sortMode = FamilyMemberConst.SORT_MODE_WEEK_DOWN;
        }
    }

    void onSortModeChange(int sortMode, int sortType) {
        if (sortMode == FamilyMemberConst.SORT_MODE_WEEK_UP) {
            if (sortType == FamilyMemberConst.SORT_TYPE_COIN) {
                coinSortIv.setImageResource(R.drawable.ic_sort_up);
                activeSortIv.setImageResource(R.drawable.ic_sort_none);
            } else if (sortType == FamilyMemberConst.SORT_TYPE_ACTIVE) {
                activeSortIv.setImageResource(R.drawable.ic_sort_up);
                coinSortIv.setImageResource(R.drawable.ic_sort_none);
            }
        } else if (sortMode == FamilyMemberConst.SORT_MODE_WEEK_DOWN) {
            if (sortType == FamilyMemberConst.SORT_TYPE_COIN) {
                coinSortIv.setImageResource(R.drawable.ic_sort_down);
                activeSortIv.setImageResource(R.drawable.ic_sort_none);
            } else if (sortType == FamilyMemberConst.SORT_TYPE_ACTIVE) {
                activeSortIv.setImageResource(R.drawable.ic_sort_down);
                coinSortIv.setImageResource(R.drawable.ic_sort_none);
            }
        } else {
            activeSortIv.setImageResource(R.drawable.ic_sort_none);
            coinSortIv.setImageResource(R.drawable.ic_sort_none);
        }
        adapter.sort(sortMode, sortType);
        setSortTextColor(sortType);
    }

    private void setSortTextColor(int sortType) {
        if (sortType == FamilyMemberConst.SORT_TYPE_COIN) {
            coinTv.setTextColor(0xffff8900);
            activeTv.setTextColor(0xff999999);
        } else if (sortType == FamilyMemberConst.SORT_TYPE_ACTIVE) {
            coinTv.setTextColor(0xff999999);
            activeTv.setTextColor(0xffff8900);
        }
    }

}
