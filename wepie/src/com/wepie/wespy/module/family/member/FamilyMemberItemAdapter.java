package com.wepie.wespy.module.family.member;

import android.content.Context;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.FamilyConfigDetail;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.decorate.NameTextView;
import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.family.FamilyUiConst;
import com.wepie.wespy.module.family.detail.FamilyMemberInterface;
import com.wepie.wespy.module.family.main.mine.member.MemberComparator;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * Created by bigwen on 2019-10-15.
 */
public class FamilyMemberItemAdapter extends RecyclerView.Adapter<FamilyMemberItemAdapter.ViewHolder> {

    public static final int TYPE_DEFAULT = 1;
    public static final int TYPE_CHOOSE = 2;
    public static final int TYPE_DELETE = 3;

    private List<FamilyMemberInterface> dataList = new ArrayList<>();

    private Context context;
    private LayoutInflater layoutInflater;
    private String searchStr = "";
    private int adapterType = TYPE_DEFAULT;
    private HashSet<Integer> chooseSet = new HashSet<>();
    private FamilyMemberAdapterCallback callback;
    private String source = "";
    private int sortMode;
    private int sortType;

    public FamilyMemberItemAdapter(Context context) {
        this.context = context;
        this.layoutInflater = LayoutInflater.from(context);
    }

    public FamilyMemberItemAdapter(Context context, int type) {
        this.context = context;
        this.layoutInflater = LayoutInflater.from(context);
        this.adapterType = type;
    }

    public void refreshDataList(List<FamilyMemberInterface> dataList, String searchStr) {
        this.searchStr = searchStr;
        this.dataList.clear();
        this.dataList.addAll(dataList);
        notifyDataSetChanged();
    }

    public List<Integer> getChooseUid() {
        return new ArrayList<>(chooseSet);
    }

    public List<FamilyMemberInterface> getDataList() {
        return dataList;
    }

    public List<Integer> getTotalUidList() {
        List<Integer> integerList = new ArrayList<>();
        for (FamilyMemberInterface familyMemberInterface: dataList) {
            integerList.add(familyMemberInterface.getUid());
        }
        return integerList;
    }

    public List<FamilyMemberInterface> getChooseItemList() {
        List<FamilyMemberInterface> list = new ArrayList<>();
        for (FamilyMemberInterface mainInterface: dataList) {
            if (chooseSet.contains(mainInterface.getUid())) {
                list.add(mainInterface);
            }
        }
        return list;
    }

    public void setChooseData(List<Integer> chooseList) {
        this.chooseSet.clear();
        this.chooseSet.addAll(chooseList);
        notifyDataSetChanged();
    }

    public void setCallback(FamilyMemberAdapterCallback callback) {
        this.callback = callback;
    }

    @NonNull
    @Override
    public FamilyMemberItemAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ViewHolder(layoutInflater.inflate(R.layout.family_member_item, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        initializeViews(dataList.get(i), viewHolder, i);
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    private void initializeViews(final FamilyMemberInterface familyMemberInterface, final ViewHolder holder, int position) {
        int iconRes = FamilyUiConst.getRoleBg(familyMemberInterface.getRole());
        String roleStr = FamilyUiConst.getRoleStr(familyMemberInterface.getRole());
        if (iconRes > 0) {
            holder.roleIconTv.setVisibility(View.VISIBLE);
            holder.roleIconTv.setBackgroundResource(iconRes);
            holder.roleIconTv.setText(roleStr);
        } else {
            holder.roleIconTv.setVisibility(View.GONE);
        }
        String showName = familyMemberInterface.getNickname();
        if (!TextUtils.isEmpty(familyMemberInterface.getRemarkName())) {
            showName = familyMemberInterface.getRemarkName();
        }

        holder.nameTv.setText(showName);
        if (showName.contains(searchStr)) {
            setNameText(holder.nameTv, showName);
        }
        //搜索不变色
        if (TextUtils.isEmpty(searchStr)) {
            holder.nameTv.setVipLevel(familyMemberInterface.getVipLevel());
        } else {
            holder.nameTv.setVipLevel(0);
        }

        holder.userAvatarIv.showUserHeadWithDecoration(familyMemberInterface.getUid());
        holder.view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JumpUtil.enterUserInfoDetailActivity(v.getContext(), familyMemberInterface.getUid(), source);
            }
        });


        if (adapterType == TYPE_CHOOSE) {
            holder.chooseTv.setVisibility(View.VISIBLE);
            holder.deleteIv.setVisibility(View.GONE);
            holder.activeLl.setVisibility(View.GONE);
            holder.coinValueTv.setVisibility(View.VISIBLE);
            holder.activeValueTv.setText(getFormatString(familyMemberInterface.getWeekActiveValue()));
            holder.coinValueTv.setText(getFormatString(familyMemberInterface.getWeekCoinValue()));
            holder.view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (chooseSet.contains(familyMemberInterface.getUid())) {
                        chooseSet.remove(familyMemberInterface.getUid());
                    } else {
                        chooseSet.add(familyMemberInterface.getUid());
                    }
                    notifyItemChanged(position);
                    if (callback != null) callback.onSelect();
                }
            });
            if (chooseSet.contains(familyMemberInterface.getUid())) {
                holder.chooseTv.setImageResource(R.drawable.ic_multi_select);
            } else {
                holder.chooseTv.setImageResource(R.drawable.ic_un_select);
            }
        } else if (adapterType == TYPE_DELETE){
            holder.chooseTv.setVisibility(View.GONE);
            holder.deleteIv.setVisibility(View.VISIBLE);
            holder.activeLl.setVisibility(View.GONE);
            holder.coinValueTv.setVisibility(View.VISIBLE);
            holder.activeValueTv.setText(getFormatString(familyMemberInterface.getWeekActiveValue()));
            holder.coinValueTv.setText(getFormatString(familyMemberInterface.getWeekCoinValue()));
            holder.deleteIv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dataList.remove(familyMemberInterface);
                    notifyDataSetChanged();
                    if (callback != null) callback.onDelete();
                }
            });
            holder.view.setOnClickListener(null);
        } else {
            holder.chooseTv.setVisibility(View.GONE);
            holder.deleteIv.setVisibility(View.GONE);
            holder.activeLl.setVisibility(View.VISIBLE);
            holder.coinValueTv.setVisibility(View.GONE);
            holder.activeValueTv.getLayoutParams().width = LinearLayout.LayoutParams.WRAP_CONTENT;
            holder.activeValueTv.setText(getFormatString(familyMemberInterface.getActiveValue()));
        }

        // 头衔标签
        if (familyMemberInterface.getHid() > 0) {
            FamilyConfigDetail.TitleConfigInfo titleConfigInfo = ConfigHelper.getInstance().getFamilyConfig().
                    getTitleConfigInfo(familyMemberInterface.getHid());
            if (titleConfigInfo != null) {
                holder.titleIconTv.setVisibility(View.VISIBLE);
                holder.titleIconTv.setText(titleConfigInfo.name);
            } else {
                holder.titleIconTv.setVisibility(View.GONE);
            }
        } else {
            holder.titleIconTv.setVisibility(View.GONE);
        }
    }

    private void setNameText(TextView textView, String name) {
        SpannableStringBuilder builder = new SpannableStringBuilder(name);
        int start = name.indexOf(searchStr);
        int end = start + searchStr.length();
        builder.setSpan(new ForegroundColorSpan(Color.parseColor("#33cc64")), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        textView.setText(builder);
    }

    public void handleSelect() {
        if (callback != null) callback.onSelect();
    }

    public void setSource(String s) {
        source = s;
    }

    public void sort(int sortMode, int sortType) {
        this.sortMode = sortMode;
        this.sortType = sortType;
        Collections.sort(dataList,  MemberComparator.getComparator(this.sortMode, this.sortType));
        notifyDataSetChanged();
    }

    private String getFormatString(long active) {
        if (active > 1000) {
            float af = ((float)active) / 1000;
            return String.format(LibBaseUtil.getTimeFormatLocal(), "%.1fk", af);
        } else {
            return String.format(LibBaseUtil.getTimeFormatLocal(), "%d", active);
        }
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        public View view;
        public DecorHeadImgView userAvatarIv;
        public TextView activeValueTv;
        public TextView coinValueTv;
        public NameTextView nameTv;
        public TextView roleIconTv;
        public TextView titleIconTv;
        public ImageView chooseTv, deleteIv;
        public LinearLayout activeLl;

        public ViewHolder(@NonNull View view) {
            super(view);
            this.view = view;
            userAvatarIv = view.findViewById(R.id.family_avatar_iv);
            activeValueTv = view.findViewById(R.id.week_active_tv);
            coinValueTv = view.findViewById(R.id.week_coin_tv);
            nameTv = view.findViewById(R.id.name_tv);
            roleIconTv = view.findViewById(R.id.family_role_tv);
            titleIconTv = view.findViewById(R.id.tv_family_title);
            chooseTv = view.findViewById(R.id.choose_iv);
            deleteIv = view.findViewById(R.id.delete_tv);
            activeLl = view.findViewById(R.id.active_ll);
        }
    }

}
