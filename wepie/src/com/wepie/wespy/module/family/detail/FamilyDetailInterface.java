package com.wepie.wespy.module.family.detail;

import com.wepie.wespy.model.entity.family.FamilyLightInfo;
import com.wepie.wespy.model.entity.family.FamilyMemberInfo;
import com.wepie.wespy.model.entity.family.FamilyWeekGameInfo;

import java.util.List;

/**
 * Created by bigwen on 2019-10-15.
 */
public interface FamilyDetailInterface {
    String getFamilyName();
    String getFamilyAvatarUrl();
    int getFamilyId();
    int getFamilyFrameId();
    long getTotalActive();
    List<FamilyMemberInfo> getMemberList();
    int getMemberCount();
    int getMaxMemberSize();
    String getIntroduction();
    int getJoinLimitType();
    int getJoinLimitValue();
    int getFamilyLevel();
    int getWeekRank();
    boolean isMyFamily();
    boolean hasFamily();
    FamilyLightInfo getFamilyLightInfo();
    boolean isJoinWeekGame();
    FamilyWeekGameInfo getWeekGameInfo();
}
