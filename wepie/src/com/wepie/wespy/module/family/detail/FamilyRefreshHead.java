package com.wepie.wespy.module.family.detail;

import android.content.Context;
import android.util.AttributeSet;

import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.simple.SimpleComponent;

/**
 * Created by <PERSON><PERSON> on 2019-10-15.
 */
public class FamilyRefreshHead extends SimpleComponent implements RefreshHeader {
    public FamilyRefreshHead(Context context) {
        this(context, null, 0);
    }

    public FamilyRefreshHead(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
}