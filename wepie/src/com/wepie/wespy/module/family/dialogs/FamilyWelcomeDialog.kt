package com.wepie.wespy.module.family.dialogs

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.ViewGroup.LayoutParams
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.PressUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.store.PrefUserUtil
import com.huiwan.user.LoginHelper
import com.huiwan.widget.image.DrawableUtil
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.wespy.R
import com.wepie.wespy.databinding.FamilyWelcomeFresherDialogBinding
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.model.entity.family.FamilyInfo

class FamilyWelcomeDialog(context: Context) {
    private val binding: FamilyWelcomeFresherDialogBinding by lazy {
        FamilyWelcomeFresherDialogBinding.inflate(
            LayoutInflater.from(context)
        ).apply {
            simpleBg.background = DrawableUtil.genGradientDrawable(
                GradientDrawable.Orientation.TOP_BOTTOM,
                intArrayOf(Color.parseColor("#FF9716"), Color.parseColor("#FFD979")), 12
            )
            gotBtn.background = DrawableUtil.genGradientDrawable(
                GradientDrawable.Orientation.BL_TR,
                intArrayOf(Color.parseColor("#FF6263"), Color.parseColor("#FF38D3")), 20
            )
        }
    }
    val view = binding.root

    fun bind(familyInfo: FamilyInfo) {
        binding.leaderTv.text = ResUtil.getStr(R.string.family_owner_name, familyInfo.ownerName)
        binding.levelIv.refresh(familyInfo.level)
        binding.familyNameTv.text = familyInfo.familyName
        familyInfo.familyLightInfo.initPropInfo()
        binding.familyLight.updateFamilyLightView(familyInfo.familyLightInfo)
        binding.titleTv.text = ResUtil.getString(R.string.family_welcome_join)
        val welcomeDesc = if (LoginHelper.getLoginUid() == familyInfo.owner) {
            ResUtil.getStr(R.string.family_leader_welcome_desc)
        } else {
            ResUtil.getStr(R.string.family_normal_member_welcome_desc, familyInfo.familyName)
        }
        binding.welcomeDescTv.text = welcomeDesc
        binding.familyHeadIv.updateDecoration(
            familyInfo.familyAvatarUrl,
            familyInfo.familyAvatarDecor
        )
        PressUtil.addPressEffect(binding.gotBtn)
        trackAppVieScreen(familyInfo.familyId)
    }

    fun onDismiss(dismiss: () -> Unit) {
        binding.gotBtn.setOnDoubleClick {
            dismiss()
        }
    }

    private fun trackAppVieScreen(familyId: Int) {
        TrackUtil.appViewScreen(
            TrackScreenName.FAMILY_WELCOME_DIALOG,
            mapOf("family_id" to familyId)
        )
    }

    companion object {
        @JvmStatic
        fun show(context: Context, familyInfo: FamilyInfo) {
            val dialog = BaseFullScreenDialog(context, R.style.dialog_theme_no_bg)
            dialog.setCanceledOnTouchOutside(false)
            val view = FamilyWelcomeDialog(context).also {
                it.onDismiss { dialog.dismiss() }
                it.bind(familyInfo)
            }
            dialog.setContentView(
                view.view,
                LayoutParams(
                    LayoutParams.MATCH_PARENT,
                    LayoutParams.MATCH_PARENT
                )
            )
            dialog.initFullCenterDialog()
            dialog.show()
            PrefUserUtil.getInstance()
                .setBoolean(PrefUserUtil.KYE_HAS_SHOW_FAMILY_WELCOME_DIALOG, true)
        }
    }

}