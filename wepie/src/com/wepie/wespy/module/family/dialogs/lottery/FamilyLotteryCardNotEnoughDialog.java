package com.wepie.wespy.module.family.dialogs.lottery;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseDialogFragment;
import com.wepie.wespy.helper.dialog.BaseFragCb;

/**
 * date 2019-11-04
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class FamilyLotteryCardNotEnoughDialog extends BaseDialogFragment {

    private int coinNeed;
    private int cardNeed;
    private BaseFragCb<Object> cb;


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.family_lottery_card_not_enough, container, false);
        updateViews(view);
        initEvents(view);
        return view;
    }

    private void updateViews(View root) {
        TextView tipTv = root.findViewById(R.id.tip_tv);
        tipTv.setText(tipTv.getContext().getString(R.string.family_gacha_activity_card_not_enough, coinNeed, cardNeed));
    }

    private void initEvents(View root) {
        root.findViewById(R.id.close_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissAllowingStateLoss();
            }
        });
        root.findViewById(R.id.ok_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (cb != null) cb.onClickSure(v);
                dismissAllowingStateLoss();
            }
        });
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        if (cb != null) {
            cb.onDismiss();
        }
    }

    public static void showDialog(Context context, int cardNeed, int coinNeed, BaseFragCb<Object> callback) {
        FamilyLotteryCardNotEnoughDialog dialog = new FamilyLotteryCardNotEnoughDialog();
        dialog.coinNeed = coinNeed;
        dialog.cardNeed = cardNeed;
        dialog.cb = callback;
        dialog.show(context);
    }
}
