package com.wepie.wespy.module.family.dialogs

import android.content.DialogInterface
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import com.huiwan.base.ktx.dp
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.wheel.contrarywind.adapter.ListWheelAdapter
import com.huiwan.base.ui.wheel.contrarywind.interfaces.IPickerViewData
import com.huiwan.base.ui.wheel.contrarywind.view.WheelView
import com.huiwan.base.util.FontUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.constentity.ConstV3Info
import com.huiwan.configservice.editionentity.liveData
import com.huiwan.decorate.CharmManager
import com.huiwan.decorate.JackarooLevelDrawable
import com.wepie.wespy.R
import com.wepie.wespy.databinding.FamilyManagerJoinRequireDialogBinding
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import com.wepie.wespy.helper.dialog.BaseFragCb
import com.wepie.wespy.helper.view.CenterAlignImageSpan
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface

class FamilyJoinRequireDialog : BaseDialogFragment() {
    private lateinit var binding: FamilyManagerJoinRequireDialogBinding

    private lateinit var typeWv: WheelView
    private lateinit var typeAdapter: ListWheelAdapter<IFamilyJoinRequireData>
    private lateinit var valueWv: WheelView
    private lateinit var valueAdapter: ListWheelAdapter<IFamilyJoinRequireData>

    private var familyInfo: FamilyMainInterface? = null

    private var cb: BaseFragCb<Pair<Int, Int>>? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        binding = FamilyManagerJoinRequireDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
    }

    fun setFamilyInfo(familyInfo: FamilyMainInterface) {
        this.familyInfo = familyInfo
    }

    fun setCallback(cb: BaseFragCb<Pair<Int, Int>>) {
        this.cb = cb
    }

    private fun initView() {
        val isRtl = ScreenUtil.isRtl()
        if (isRtl) {
            typeWv = binding.familyJoinRequireValueWv
            valueWv = binding.familyJoinRequireTypeWv
            typeWv.layoutDirection = View.LAYOUT_DIRECTION_RTL
            valueWv.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            typeWv = binding.familyJoinRequireTypeWv
            valueWv = binding.familyJoinRequireValueWv
            typeWv.layoutDirection = View.LAYOUT_DIRECTION_LTR
            valueWv.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }

        typeWv.apply {
            setItemsVisibleCount(5)
            setCyclic(false)
            setLineSpacingMultiplier(4f)
            setTextSize(16f)
            setTypeface(FontUtil.getTypeface(android.graphics.Typeface.NORMAL))
            setTextColorCenter(ResUtil.getColor(R.color.color_text_primary))
            setTextColorOut(ResUtil.getColor(R.color.color_text_secondary))
            setDividerType(if (isRtl) WheelView.DividerType.RIGHT else WheelView.DividerType.LEFT)
            setAlphaGradient(true)
            setScale(false)
            setOnItemSelectedListener { i ->
                val data = map[i] ?: emptyList()
                valueAdapter = ListWheelAdapter<IFamilyJoinRequireData>(data)
                valueWv.adapter = valueAdapter
                valueWv.currentItem = 0
            }
        }

        valueWv.apply {
            setItemsVisibleCount(5)
            setCyclic(false)
            setLineSpacingMultiplier(4f)
            setTextSize(16f)
            setTypeface(FontUtil.getTypeface(android.graphics.Typeface.NORMAL))
            setTextColorCenter(ResUtil.getColor(R.color.color_text_primary))
            setTextColorOut(ResUtil.getColor(R.color.color_text_secondary))
            setDividerType(if (isRtl) WheelView.DividerType.LEFT else WheelView.DividerType.RIGHT)
            setAlphaGradient(true)
            setScale(false)
        }

        binding.cancelTv.setOnClickListener {
            dismissAllowingStateLoss()
            cb?.onClickCancel()
        }
        binding.enterTv.setOnClickListener {
            val typeIndex = typeWv.currentItem
            val valueIndex = valueWv.currentItem
            cb?.onClickSure(
                Pair(
                    typeList[typeIndex].value,
                    map[typeIndex]?.get(valueIndex)?.value ?: 0
                )
            )
            dismissAllowingStateLoss()
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        cb?.onDismiss()
    }

    private fun initData() {
        typeAdapter = ListWheelAdapter<IFamilyJoinRequireData>(typeList)
        typeWv.adapter = typeAdapter

        val type = familyInfo?.joinLimitType ?: 0
        val value = familyInfo?.getJoinLimitValue() ?: 0
        var typeIndex = if (type == 0) {
            if (value == 0) {
                0
            } else {
                2
            }
        } else {
            1
        }
        val list = map[typeIndex] ?: map[0]!!.also { typeIndex = 0 }
        typeWv.currentItem = typeIndex
        val valueAdapter = ListWheelAdapter(list)
        valueWv.adapter = valueAdapter
        var valueIndex = 0
        if (typeIndex > 0) {
            for (i in 0 until list.size) {
                if (value == list[i].value) {
                    valueIndex = i
                    break
                }
            }
        }
        valueWv.currentItem = valueIndex
    }

    companion object {

        private val typeList = mutableListOf<IFamilyJoinRequireData>()

        private val map = mutableMapOf<Int, List<IFamilyJoinRequireData>>()

        init {
            typeList.add(FamilyJoinRequireText(R.string.family_join_require_none, 0))
            map.put(0, listOf(FamilyJoinRequireText(R.string.family_join_require_any_allow, 0)))
            ConstV3Info::class.liveData().observeForever(object : Observer<ConstV3Info> {
                override fun onChanged(value: ConstV3Info) {
                    typeList.add(FamilyJoinRequireText(R.string.family_join_require_game_level, 1))
                    typeList.add(FamilyJoinRequireText(R.string.family_join_require_charm_level, 0))
                    map.put(1, value.familyJoinGameLevel.map {
                        FamilyJoinRequireGameLevel(it)
                    })
                    val charmLevelList = mutableListOf<Int>()
                    var i = 0
                    while (i < value.charmLevel.size) {
                        charmLevelList.add(value.charmLevel[i])
                        i += 3
                    }
                    map.put(2, charmLevelList.map {
                        FamilyJoinRequireCharmLevel(it)
                    })
                    ConstV3Info::class.liveData().removeObserver(this)
                }
            })
        }

        @JvmStatic
        fun preload() {

        }
    }

}

internal val measurePaint = TextPaint().apply {
    isAntiAlias = true
    textSize = 16.dp.toFloat()
    color = Color.WHITE
    typeface = FontUtil.getTypeface(android.graphics.Typeface.BOLD)
}

internal val fontHeight: Int by lazy {
    (measurePaint.fontMetrics.descent - measurePaint.fontMetrics.ascent).toInt()
}
internal val charWidth: Float by lazy {
    measurePaint.measureText("-")
}

private interface IFamilyJoinRequireData : IPickerViewData {
    val value: Int
}

private class FamilyJoinRequireText :
    IFamilyJoinRequireData {
    private val title: String
    override val value: Int

    constructor(title: String, value: Int) {
        this.title = title
        this.value = value
    }

    constructor(stringRes: Int, value: Int) {
        this.title = ResUtil.getString(stringRes)
        this.value = value
    }

    override fun getPickerViewText(): CharSequence? {
        return title
    }
}

private class FamilyJoinRequireCharmLevel(val level: Int = 0, override val value: Int = level) :
    IFamilyJoinRequireData {

    private val ssb = SpannableStringBuilder()

    init {
        var drawable = ResUtil.getDrawable(CharmManager.getInstance().getCharmShortRes(level))
        drawable = drawable.mutate()
        drawable.setBounds(0, 0, fontHeight, fontHeight)
        val len: Int = ((fontHeight - 1) / charWidth).toInt() + 1
        for (i in 0 until len) {
            ssb.append("-")
        }
        ssb.setSpan(object : CenterAlignImageSpan(drawable) {
            override fun getSize(
                paint: Paint, text: CharSequence?, start: Int, end: Int, fm: Paint.FontMetricsInt?
            ): Int {
                return drawable.bounds.width()
            }

            override fun draw(
                canvas: Canvas, text: CharSequence?, start: Int, end: Int,
                x: Float, top: Int, y: Int, bottom: Int, paint: Paint
            ) {
                drawable.alpha = paint.alpha
                super.draw(canvas, text, start, end, x, top, y, bottom, paint)
            }
        }, 0, ssb.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        ssb.append(ResUtil.getString(R.string.family_manage_activity_base_info_apply_tips_3_common))
    }

    override fun getPickerViewText(): CharSequence? = ssb
}

private class FamilyJoinRequireGameLevel(val level: Int = 0, override val value: Int = level) :
    IFamilyJoinRequireData {

    private lateinit var ssb: SpannableStringBuilder
    private val drawable = JackarooLevelDrawable(level)

    override fun getPickerViewText(): CharSequence? {
        if (::ssb.isInitialized) {
            return ssb
        }
        drawable.updateScale(fontHeight * 1.0F / 20.dp)
        val len: Int = ((drawable.bounds.width() - 1) / charWidth).toInt() + 1
        ssb = SpannableStringBuilder("")
        for (i in 0 until len) {
            ssb.append("-")
        }
        ssb.setSpan(object : CenterAlignImageSpan(drawable) {
            override fun getSize(
                paint: Paint, text: CharSequence?, start: Int, end: Int,
                fm: Paint.FontMetricsInt?
            ): Int {
                return drawable.bounds.width()
            }

            override fun draw(
                canvas: Canvas, text: CharSequence?, start: Int, end: Int,
                x: Float, top: Int, y: Int, bottom: Int, paint: Paint
            ) {
                drawable.alpha = paint.alpha
                super.draw(canvas, text, start, end, x, top, y, bottom, paint)
            }

        }, 0, ssb.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        ssb.append(" ")
        ssb.append(ResUtil.getString(R.string.family_manage_activity_base_info_apply_tips_3_common))
        return ssb
    }
}
