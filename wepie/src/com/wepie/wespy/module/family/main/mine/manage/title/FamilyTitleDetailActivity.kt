package com.wepie.wespy.module.family.main.mine.manage.title

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.integerResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.huiwan.base.util.ResUtilKt
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.configservice.ConfigHelper
import com.huiwan.constants.IntentConfig
import com.huiwan.decorate.compose.DecorHeadImage
import com.huiwan.decorate.compose.HeadDecorInfo
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.widget.actionbar.BaseWpActionBar
import com.wejoy.weplay.composewidget.BasicText
import com.wejoy.weplay.composewidget.TextColorDefault
import com.wejoy.weplay.composewidget.TitleLabelEndColor
import com.wejoy.weplay.composewidget.TitleLabelStartColor
import com.wejoy.weplay.composewidget.UserNameColorsConfig
import com.wejoy.weplay.composewidget.UserNameText
import com.wejoy.weplay.composewidget.WpImage
import com.wejoy.weplay.composewidget.WpPullToRefreshColumn
import com.wejoy.weplay.composewidget.noRippleClickable
import com.wejoy.weplay.composewidget.rtlReverse
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.wespy.R
import com.wepie.wespy.base.composeui.WpTheme
import com.wepie.wespy.module.family.FamilyManager
import com.wepie.wespy.module.family.main.mine.manage.title.FamilyTitleDetailViewModel.FamilyTitleInfo
import com.wepie.wespy.module.family.main.mine.manage.title.FamilyTitleSetUpActivity.Companion.KEY_HID

class FamilyTitleDetailActivity : BaseActivity() {

    private val vm: FamilyTitleDetailViewModel by viewModels<FamilyTitleDetailViewModel>()

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_family_title_detail)
        initActionBar()
        findViewById<ComposeView>(R.id.content_view).setContent {
            val state by vm.state.collectAsState()
            WpTheme {
                WpPullToRefreshColumn(
                    state.isRefreshing,
                    onRefresh = {
                        vm.refresh(true)
                    }
                ) {
                    BodyContent(state)
                }
            }
        }
        trackViewScreen()
    }

    private fun initActionBar() {
        findViewById<BaseWpActionBar>(R.id.action_bar).addTitleRightIconWithBackComponentStyle(
            ResUtilKt.getStr(R.string.family_title_details, this),
            R.drawable.action_bar_icon_help,
            true,
            {
                finish()
            },
            {
                ApiService.of(WebApi::class.java).gotoWebActivity(
                    this,
                    ConfigHelper.getInstance().familyConfig.honorTitleHelpUrl
                )
            }
        )
    }

    private fun trackViewScreen() {
        val source = intent.getStringExtra("source")
        TrackUtil.appViewScreen(
            TrackSource.FAMILY_TITLE_DETAIL,
            mapOf(
                "source" to source,
                "family_id" to FamilyManager.getInstance().selfFamilyInfo.family.familyId
            )
        )
    }

    override fun onStart() {
        super.onStart()
        val targetFid = intent.getIntExtra(IntentConfig.FAMILY_ID, -1)
        vm.init(targetFid)
    }

    companion object {
        fun goSetUpPage(context: Context, hid: Int) {
            val i = Intent(context, FamilyTitleSetUpActivity::class.java)
            i.putExtra(KEY_HID, hid)
            context.startActivity(i)
        }
    }
}


@Composable
private fun BodyContent(state: FamilyTitleDetailViewModel.FamilyTitleInfoList) {
    LazyColumn {
        itemsIndexed(state.titleInfoList) { _, item ->
            val isFamilyAdmin by remember { mutableStateOf(item.isFamilyAdmin()) }
            val isFamilyMember by remember { mutableStateOf(!item.noFamilyRole()) }
            val isLock by remember { mutableStateOf(item.totalNum == 0) }
            if (isFamilyAdmin && isLock || !isLock) {
                showItem(item, isFamilyAdmin, isFamilyMember, isLock)
            }
        }
    }
}

@Composable
private fun showItem(
    item: FamilyTitleInfo,
    isFamilyAdmin: Boolean,
    isFamilyMember: Boolean,
    isLock: Boolean
) {
    val context = LocalContext.current
    Column(
        modifier = Modifier
            .fillMaxSize()
            .noRippleClickable {
                if (item.isFamilyAdmin()) {
                    if (item.totalNum != 0) {
                        FamilyTitleDetailActivity.goSetUpPage(context, item.hid)
                    } else {
                        ToastUtil.show(R.string.family_level_too_low)
                    }
                }
            }
            .padding(start = 20.dp, top = 16.dp, bottom = 16.dp)
    ) {

        FamilyTitleBasic(item, isFamilyAdmin, isFamilyMember, isLock)
        if (item.users.isNotEmpty()) {
            UserItem(users = item.users)
        }
    }
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(8.dp)
            .background(
                color = Color(0xFFFAFAFA)
            )
    )
}

@Preview
@Composable
fun show() {
    Box(
        modifier = Modifier.size(64.dp, 64.dp)
    ) {
        WpImage(
            model = (R.drawable.about_icon),
            contentDescription = "",
            modifier = Modifier
                .width(40.dp)
                .height(40.dp)
                .clip(RoundedCornerShape(40.dp))
        )
    }
}

@Composable
private fun UserItem(users: List<FamilyUserInfo>) {
    val context = LocalContext.current
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(top = 12.dp)
    ) {
        itemsIndexed(users) { _, item ->
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.noRippleClickable {
                    goToUserProfile(context, item.uid)
                }
            ) {
                DecorHeadImage(HeadDecorInfo.Builder {
                    size(48.dp)
                    headCornerSize = 24.dp
                    uid = item.uid
                    mustShowDecor = true
                }.build())
                Spacer(
                    modifier = Modifier.height(4.dp)
                )
                UserNameText(
                    item.userSimpleInfo?.nickname ?: "",
                    colors = item.userSimpleInfo?.getVipColor(false)
                        ?: UserNameColorsConfig(),
                    fontSize = 12.sp,
                    modifier = Modifier.widthIn(max = 64.dp)
                )
            }

        }
    }
}

@Composable
private fun FamilyTitleBasic(
    item: FamilyTitleInfo,
    isFamilyAdmin: Boolean,
    isFamilyMember: Boolean,
    isLock: Boolean
) {
    Column(
        modifier = Modifier.padding(end = 20.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(26.dp)
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxHeight()
                    .wrapContentWidth()
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(TitleLabelStartColor, TitleLabelEndColor).rtlReverse(),
                        ),
                        shape = RoundedCornerShape(6.dp)
                    )
                    .padding(horizontal = 8.dp)
            ) {
                BasicText(
                    text = item.titleName,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontWeight = FontWeight.Bold,
                )
            }
            Box(modifier = Modifier.align(Alignment.CenterEnd)) {
                TitleRightStatus(item, isFamilyAdmin, isFamilyMember, isLock)
            }
        }
        BasicText(
            text = item.desc,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            fontSize = 12.sp,
            color = TextColorDefault,
            textAlign = TextAlign.Start,
            fontWeight = FontWeight.Normal,
            modifier = Modifier.padding(top = 6.dp)
        )
    }
}


@Composable
private fun TitleRightStatus(
    item: FamilyTitleInfo,
    isFamilyAdmin: Boolean,
    isFamilyMember: Boolean,
    isLock: Boolean
) {
    val context = LocalContext.current
    if (isFamilyAdmin && isLock) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .wrapContentWidth()
                .fillMaxHeight()
        ) {
            WpImage(
                model = R.drawable.family_title_lock, Modifier
                    .width(16.dp)
                    .height(16.dp)
            )
            BasicText(
                text = ResUtilKt.getStr(R.string.family_title_details_locked, context),
                fontSize = 14.sp,
                color = TextColorDefault,
                fontWeight = FontWeight.Normal
            )

        }
    } else {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .wrapContentWidth()
                .fillMaxHeight()
        ) {
            BasicText(
                text = "${item.usedNum}/${item.totalNum}",
                fontSize = 14.sp,
                color = TextColorDefault,
                fontWeight = FontWeight.Normal
            )
            WpImage(
                model = R.drawable.base_icons_arrow_right_table_view, Modifier
                    .width(16.dp)
                    .height(16.dp)
                    .alpha(if (isFamilyAdmin) 1f else 0f)
                    .scale(scaleX = integerResource(R.integer.image_scale_x).toFloat(), scaleY = 1f)
            )
        }
    }
}
