package com.wepie.wespy.module.family.main.mine.manage.title

import androidx.lifecycle.viewModelScope
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.FamilyConfigDetail.TitleConfigInfo
import com.huiwan.configservice.constentity.propextra.HeadDecorExtra
import com.huiwan.decorate.UserDecorManager
import com.huiwan.decorate.UserDecorManager.LifeCallback
import com.huiwan.user.UserServiceKt
import com.huiwan.user.http.rspmodel.UserDecoration
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wejoy.weplay.ex.lifecycle.LifeViewModel
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.family.FamilyTitleWithAssignRsp
import com.wepie.wespy.module.family.FamilyManager
import com.wepie.wespy.net.http.api.FamilyApiKt
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

class FamilyTitleDialogViewModel : LifeViewModel() {
    private val _state = MutableStateFlow(FamilyTitleSetupItemList())
    val state: StateFlow<FamilyTitleSetupItemList> = _state
    private val _event = MutableSharedFlow<UpdateEvent>()

    internal val event: SharedFlow<UpdateEvent> = _event

    private var defaultList: List<FamilyTitleSetupItem> = ArrayList()

    private var hid: Int = 0
    private var titleInfoList: List<TitleConfigInfo> = ArrayList()
    private var totalNum: Int = 0

    fun init(hid: Int, totalNum: Int) {
        this.hid = hid
        this.titleInfoList = ConfigHelper.getInstance().familyConfig.titleList
        this.totalNum = totalNum
        refresh()
    }

    private fun refresh() {
        viewModelScope.launch {
            val fid = FamilyManager.getInstance().selfFamilyInfo.family.familyId
            val rsp = FamilyApiKt.detailTitleMemberInfo(fid, hid)
            if (rsp is KtResultSuccess) {
                handleRsp(rsp.data, totalNum)
            } else if (rsp is KtResultFailed) {
                ToastUtil.show(rsp.failedDesc)
            }
        }
    }


    fun handleIntent(intent: FamilySetupDialogIntent) {
        when (intent) {
            is FamilySetupDialogIntent.UpdateSearchList -> {
                val searchStr = intent.searchStr
                if (intent.searchStr.isEmpty()) {
                    onListChanged(defaultList)
                    return
                }
                val showing: MutableList<FamilyTitleSetupItem> = ArrayList(defaultList.size)
                for (member in defaultList) {
                    if ((member.user.userSimpleInfo?.nickname
                            ?: "").contains(searchStr) || (member.user.userSimpleInfo?.wdId?.toString()
                            ?: "")
                            .contains(searchStr)
                    ) {
                        showing.add(member)
                    }
                }
                onListChanged(showing)
            }

            is FamilySetupDialogIntent.Confirm -> {
                handleConfirm(intent.uidList)
            }

            is FamilySetupDialogIntent.Cancel -> { /* nothing to*/
            }

            is FamilySetupDialogIntent.UpdateSelectStatus -> {
                handleSelectedClick(intent.uid)
            }
        }
    }

    private fun handleConfirm(uidList: List<Int>) {
        val fid = FamilyManager.getInstance().selfFamilyInfo.family.familyId
        viewModelScope.launch {
            val rsp = FamilyApiKt.assignTitles(fid, hid, uidList)
            if (rsp is KtResultSuccess) {
                _event.emit(UpdateEvent(ResUtil.getStr(R.string.family_title_setting_success)))
            } else {
                _event.emit(UpdateEvent(rsp.failedDesc))
            }
        }
    }

    private fun handleSelectedClick(id: Int) {
        val newList = state.value.list.map {
            if (it.user.uid == id) it.copy(selected = !it.selected)
            else it
        }
        _state.value = _state.value.copy(list = newList)
    }

    private suspend fun handleRsp(data: FamilyTitleWithAssignRsp, totalNum: Int) {
        val infoMap = titleInfoList.associateBy { it.hid }
        val memberUidList = data.assignedUsers.map { it.uid }
        val usedNum = data.assignedUsers.count { it.hid == hid }
        val user = UserServiceKt.get().getCacheSimpleUserList(memberUidList)
        val decors = updateUserDecors(memberUidList)
        val list = data.assignedUsers.map { member ->
            return@map FamilyTitleSetupItem(
                user = FamilyUserInfo(
                    uid = member.uid,
                    userSimpleInfo = user[member.uid],
                    decorExtra = decors[member.uid]
                ),
                hid = member.hid,
                role = member.role,
                titleName = infoMap[member.hid]?.name ?: "",
                disabled = false,
                selected = false,
            )
        }
        _state.update {
            FamilyTitleSetupItemList(totalNum - usedNum, list)
        }
        //达到设置上限
        if (totalNum == usedNum) {
            _event.emit(UpdateEvent(ResUtil.getStr(R.string.family_title_setting_confirm_limit)))
        }
        this.defaultList = _state.value.list

    }

    private suspend fun updateUserDecors(users: List<Int>) =
        suspendCancellableCoroutine<Map<Int, HeadDecorExtra?>> { c ->
            UserDecorManager.getInstance()
                .updateDecorations(users, object : LifeCallback(toLife()) {
                    override fun onLoadDecoration(success: Boolean, decoration: UserDecoration?) {
                        val decorMap = users.mapNotNull { uid ->
                            UserDecorManager.getInstance().getUserDecorationById(uid)
                        }.associate { decor ->
                            Pair(
                                decor.uid,
                                ConfigHelper.getInstance().getHeadDecorationById(decor.decorId)
                            )
                        }
                        c.resumeWith(Result.success(decorMap))
                    }
                })
        }

    fun onListChanged(list: List<FamilyTitleSetupItem>) {
        _state.value = _state.value.copy(list = list)
    }

    data class FamilyTitleSetupItemList(
        val usedTotalNum: Int = 0,
        val list: List<FamilyTitleSetupItem> = ArrayList()
    )

    class UpdateEvent(val desc: String? = null)
}

sealed class FamilySetupDialogIntent {
    data object Cancel : FamilySetupDialogIntent()
    class Confirm(val uidList: List<Int>) : FamilySetupDialogIntent()
    class UpdateSearchList(val searchStr: String) : FamilySetupDialogIntent()
    class UpdateSelectStatus(val uid: Int) : FamilySetupDialogIntent()
}