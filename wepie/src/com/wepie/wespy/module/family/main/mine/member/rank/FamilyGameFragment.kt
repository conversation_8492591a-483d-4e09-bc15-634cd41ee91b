package com.wepie.wespy.module.family.main.mine.member.rank

import android.os.Bundle
import android.view.View
import com.huiwan.base.str.ResUtil
import com.wepie.wespy.R
import com.wepie.wespy.module.family.main.mine.member.TabType

/**
 * 游戏标签页Fragment
 * 显示成员游戏数据
 */
class FamilyGameFragment : FamilyBaseMemberListFragment() {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 切换当前标签页类型
        viewModel.switchTab(TabType.GAME)
    }

    override fun onRefreshData() {
        viewModel.refreshData(TabType.GAME)
    }

    override fun observeData() {
        viewModel.gameMembers.observe(viewLifecycleOwner) { members ->
            updateDataDisplay(members)
        }
    }

    override fun getRankTips(): String {
        return ResUtil.getStr(R.string.family_member_list_type_rank_game_tips)
    }

    override fun tabType() = TabType.GAME

    companion object {
        fun newInstance(): FamilyGameFragment {
            return FamilyGameFragment()
        }
    }
}