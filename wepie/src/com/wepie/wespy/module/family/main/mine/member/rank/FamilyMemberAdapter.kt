package com.wepie.wespy.module.family.main.mine.member.rank

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import com.huiwan.base.ktx.gone
import com.huiwan.base.ktx.visible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.NameTextView
import com.huiwan.widget.rv.BaseRvAdapter
import com.huiwan.widget.rv.RVHolder
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.family.FamilyMemberInfo
import com.wepie.wespy.model.entity.family.FamilyMemberLimit
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.family.FamilyManager
import com.wepie.wespy.module.family.FamilyUiConst
import com.wepie.wespy.module.family.dialogs.FamilyMemberManageDialog
import com.wepie.wespy.module.family.main.mine.member.FamilyMemberConst
import com.wepie.wespy.module.family.main.mine.member.FamilyMemberData
import com.wepie.wespy.module.family.main.mine.member.TabType
import com.wepie.wespy.module.family.main.mine.member.rank.FamilyMemberAdapter.Companion.PAYLOAD_RANK
import com.wepie.wespy.module.family.main.mine.member.rank.FamilyMemberAdapter.Companion.PAYLOAD_RANK_SCORE
import com.wepie.wespy.module.family.main.mine.member.rank.FamilyMemberAdapter.Companion.PAYLOAD_ROLE
import com.wepie.wespy.module.family.main.mine.member.rank.FamilyMemberAdapter.Companion.PAYLOAD_SORT_MODE
import com.wepie.wespy.module.family.main.mine.member.rank.FamilyMemberAdapter.Companion.PAYLOAD_SORT_TYPE
import com.wepie.wespy.module.family.main.mine.member.rank.FamilyMemberAdapter.Companion.PAYLOAD_TITLE

class FamilyMemberAdapter(
    private val familyMemberData: FamilyMemberData = FamilyMemberData()
) :
    BaseRvAdapter<FamilyMemberInfo, FamilyMemberAdapter.ViewHolder>() {

    companion object {
        const val PAYLOAD_SORT_MODE = 1
        const val PAYLOAD_RANK = 2
        const val PAYLOAD_SORT_TYPE = 3
        const val PAYLOAD_ROLE = 4
        const val PAYLOAD_TITLE = 5
        const val PAYLOAD_RANK_SCORE = 6
    }

    class ViewHolder(view: View) : RVHolder(view) {
        val rankLay: View = view.findViewById(R.id.rank_lay)
        val rank: TextView = view.findViewById(R.id.tv_rank)
        val rankTrophy: ImageView = view.findViewById(R.id.iv_rank_trophy)
        val avatar: DecorHeadImgView = view.findViewById(R.id.iv_head)
        val name: NameTextView = view.findViewById(R.id.tv_name)
        val role: TextView = view.findViewById(R.id.tv_family_role)
        val title: TextView = view.findViewById(R.id.tv_family_title)
        val weekScore: TextView = view.findViewById(R.id.tv_week_score)
        val rankScore: TextView = view.findViewById(R.id.tv_rank_score)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.family_member_item_view, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        onBindViewHolder(holder, position, mutableListOf())
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int, payloads: MutableList<Any>) {
        val member = getItem(position)
        if (payloads.isEmpty()) {
            // 设置排名
            updateRank(holder, member.rank)

            // 设置用户基本信息
            updateUserBaseInfo(holder, member)

            // 设置排行榜对应的分数
            updateScoreByTabType(holder, member)

            setItemClickEvent(holder, member)
        } else {
            for (payload in payloads) {
                if (payload is List<*>) {
                    if (payload.contains(PAYLOAD_SORT_MODE) || payload.contains(PAYLOAD_RANK)) {
                        updateContributeScoreTextColor(holder, member.sortMode)
                        updateRank(holder, member.rank)
                    }

                    if (payload.contains(PAYLOAD_SORT_TYPE)) {
                        updateContributionScore(holder, member)
                    }

                    if (payload.contains(PAYLOAD_ROLE)) {
                        updateRole(holder, member.role)
                    }

                    if (payload.contains(PAYLOAD_TITLE)) {
                        updateTitle(holder, member.hid)
                    }

                    if (payload.contains(PAYLOAD_RANK_SCORE)) {
                        updateScoreByTabType(holder, member)
                    }
                }
                // 可能是直接返回Int值
                else if (payload is Int) {
                    if (payload == PAYLOAD_SORT_MODE || payload == PAYLOAD_RANK) {
                        updateContributeScoreTextColor(holder, member.sortMode)
                        updateRank(holder, member.rank)
                    }

                    if (payload == PAYLOAD_SORT_TYPE) {
                        updateContributionScore(holder, member)
                    }

                    if (payload == PAYLOAD_ROLE) {
                        updateRole(holder, member.role)
                    }

                    if (payload == PAYLOAD_TITLE) {
                        updateTitle(holder, member.hid)
                    }

                    if (payload == PAYLOAD_RANK_SCORE) {
                        updateScoreByTabType(holder, member)
                    }
                }
            }
        }
    }

    private fun updateRank(holder: ViewHolder, rankInt: Int) {
        if (familyMemberData.showRankNum && familyMemberData.mode != FamilyMemberConst.MANAGE_MODE) {
            holder.rankLay.visible()
            if (rankInt <= 3) {
                holder.rankTrophy.visibility = View.VISIBLE
                holder.rank.visibility = View.GONE
                when (rankInt) {
                    1 -> holder.rankTrophy.setImageResource(R.drawable.ic_guard_rank_1)
                    2 -> holder.rankTrophy.setImageResource(R.drawable.ic_guard_rank_2)
                    else -> holder.rankTrophy.setImageResource(R.drawable.ic_guard_rank_3)
                }
            } else {
                holder.rankTrophy.visibility = View.GONE
                holder.rank.visibility = View.VISIBLE
                holder.rank.text = rankInt.toString()
            }
        } else {
            holder.rankLay.gone()
        }
    }

    private fun updateUserBaseInfo(holder: ViewHolder, member: FamilyMemberInfo) {
        // 加载头像
        holder.avatar.showUserHeadWithDecoration(member.uid)

        // 名字
        holder.name.text = member.nickname
        holder.name.setVipLevel(member.vipLevel)

        // 族长标记
        updateRole(holder, member.role)

        // 头衔标签
        updateTitle(holder, member.hid)
    }

    private fun updateRole(holder: ViewHolder, role: Int) {
        val iconRes = FamilyUiConst.getRoleBg(role)
        if (iconRes > 0) {
            holder.role.visibility = View.VISIBLE
            holder.role.setBackgroundResource(iconRes)
            val roleStr = FamilyUiConst.getRoleStr(role)
            holder.role.text = roleStr
        } else {
            holder.role.visibility = View.GONE
        }
    }

    private fun updateTitle(holder: ViewHolder, hid: Int) {
        if (hid > 0) {
            val titleConfigInfo = ConfigHelper.getInstance().familyConfig.getTitleConfigInfo(hid)
            if (titleConfigInfo != null) {
                holder.title.visibility = View.VISIBLE
                holder.title.text = titleConfigInfo.name
            } else {
                holder.title.visibility = View.GONE
            }
        } else {
            holder.title.visibility = View.GONE
        }
    }

    private fun updateScoreByTabType(holder: ViewHolder, member: FamilyMemberInfo) {
        when (familyMemberData.tabType) {
            TabType.CONTRIBUTION -> {
                updateContributionScore(holder, member)
                updateContributeScoreTextColor(holder, member.sortMode)
            }

            TabType.GIFT -> updateScore(holder, member.rankScore)
            TabType.RECEIVING -> updateScore(holder, member.rankScore)
            TabType.GAME -> updateScore(holder, member.rankScore)
        }
    }

    private fun updateContributionScore(holder: ViewHolder, member: FamilyMemberInfo) {
        when (member.sortType) {
            FamilyMemberConst.SORT_TYPE_ACTIVE -> {
                holder.weekScore.text = StringUtil.formatGuardNumberForEN(member.weekActiveValue)
                holder.rankScore.text = StringUtil.formatGuardNumberForEN(member.activeValue)
            }

            else -> {
                holder.weekScore.text = StringUtil.formatGuardNumberForEN(member.weekCoinValue)
                holder.rankScore.text = StringUtil.formatGuardNumberForEN(member.coinValue)
            }
        }
    }

    private fun updateContributeScoreTextColor(holder: ViewHolder, sortMode: Int) {
        when (sortMode) {
            FamilyMemberConst.SORT_MODE_WEEK_DOWN,
            FamilyMemberConst.SORT_MODE_WEEK_UP -> {
                holder.weekScore.setTextColor(ResUtil.getColor(R.color.color_accent_ex))
                holder.rankScore.setTextColor(ResUtil.getColor(R.color.color_text_primary_ex))

            }

            FamilyMemberConst.SORT_MODE_TOTAL_DOWN,
            FamilyMemberConst.SORT_MODE_TOTAL_UP -> {
                holder.weekScore.setTextColor(ResUtil.getColor(R.color.color_text_primary_ex))
                holder.rankScore.setTextColor(ResUtil.getColor(R.color.color_accent_ex))
            }

            else -> {
                holder.weekScore.setTextColor(ResUtil.getColor(R.color.color_text_primary_ex))
                holder.rankScore.setTextColor(ResUtil.getColor(R.color.color_text_primary_ex))
            }
        }
    }

    private fun updateScore(holder: ViewHolder, rankScore: Int) {
        val score = StringUtil.formatGuardNumberForEN(rankScore.toLong())
        holder.rankScore.text = score
    }

    private fun setItemClickEvent(holder: ViewHolder, member: FamilyMemberInfo) {
        holder.itemView.setOnDoubleClick {
            if (familyMemberData.mode == FamilyMemberConst.MANAGE_MODE) {
                FamilyMemberManageDialog.showDialog(
                    holder.itemView.context,
                    familyMemberData.selfRole,
                    member.uid,
                    member.role,
                    familyMemberData.limit
                )
            } else {
                val info = FamilyManager.getInstance().selfFamilyInfo
                if (info != null && info.family.groupChatId > 0) {
                    JumpUtil.enterUserInfoDetailActivityFromGroup(
                        holder.itemView.context,
                        member.uid,
                        info.family.groupChatId
                    )
                } else {
                    JumpUtil.enterUserInfoDetailActivity(
                        holder.itemView.context,
                        member.uid,
                        TrackSource.FAMILY
                    )
                }
            }
        }
    }

    fun updateLimit(limit: FamilyMemberLimit) {
        familyMemberData.limit = limit
    }

    fun updateSelfRole(role: Int) {
        familyMemberData.selfRole = role
    }

    fun updateManagerMode(mode: Int) {
        familyMemberData.mode = mode
    }

    fun updateSortType(sortType: Int) {
        familyMemberData.sortType = sortType
    }

    fun updateData(newData: List<FamilyMemberInfo>) {
        if (newData.isNotEmpty() && newData.size == dataList.size) {
            updateDataWithDiff(newData)
        } else {
            updateAllData(newData)
        }
    }

    private fun updateDataWithDiff(newData: List<FamilyMemberInfo>) {
        val diffCallback = FamilyMemberDiffCallback(
            dataList, newData
        )
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        dataList.clear()
        dataList.addAll(newData)
        diffResult.dispatchUpdatesTo(this)
    }

    fun updateAllData(newData: List<FamilyMemberInfo>) {
        dataList.clear()
        dataList.addAll(newData)
        notifyDataSetChanged()
    }
}

class FamilyMemberDiffCallback(
    private val oldList: List<FamilyMemberInfo>,
    private val newList: List<FamilyMemberInfo>
) : DiffUtil.Callback() {

    override fun getOldListSize(): Int = oldList.size

    override fun getNewListSize(): Int = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldList[oldItemPosition]
        val newItem = newList[newItemPosition]
        return oldItem.uid == newItem.uid
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldList[oldItemPosition]
        val newItem = newList[newItemPosition]

        return oldItem.uid == newItem.uid && oldItem.nickname == newItem.nickname && oldItem.hid == newItem.hid
                && oldItem.avatar == newItem.avatar && oldItem.role == newItem.role
                && oldItem.rankScore == newItem.rankScore && oldItem.weekActiveValue == newItem.weekActiveValue
                && oldItem.weekCoinValue == newItem.weekCoinValue && oldItem.activeValue == newItem.activeValue
                && oldItem.coinValue == newItem.coinValue && oldItem.rank == newItem.rank && oldItem.sortMode == newItem.sortMode
                && oldItem.sortType == newItem.sortType
    }

    override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): MutableList<Int>? {

        val oldItem = oldList[oldItemPosition]
        val newItem = newList[newItemPosition]

        val payloads = mutableListOf<Int>()

        // 检查模式是否变化
        if (oldItem.sortMode != newItem.sortMode) {
            payloads.add(PAYLOAD_SORT_MODE)
        }

        //检查等级是否变化
        if (oldItem.rank != newItem.rank) {
            payloads.add(PAYLOAD_RANK)
        }

        // 检查类型是否变化
        if (oldItem.sortType != newItem.sortType) {
            payloads.add(PAYLOAD_SORT_TYPE)
        }

        // 检查职位是否变化
        if (oldItem.role != newItem.role) {
            payloads.add(PAYLOAD_ROLE)
        }

        // 检查头衔是否变化
        if (oldItem.hid != newItem.hid) {
            payloads.add(PAYLOAD_TITLE)
        }

        // 检查分数是否变化
        if (oldItem.rankScore != newItem.rankScore) {
            payloads.add(PAYLOAD_RANK_SCORE)
        }

        return if (payloads.isEmpty()) null else payloads
    }
}