package com.wepie.wespy.module.family.main.mine

import android.view.View
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.wepie.module.rank.viewmodel.ViewPagerLiveData
import com.wepie.wespy.model.entity.family.FamilyMainInfo
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch

class MyFamilyViewModel : ViewModel() {

    private val _familyMainInfoLiveData = ViewPagerLiveData<FamilyMainInfo>()
    val familyMainInfoLiveData: LiveData<FamilyMainInfo> = _familyMainInfoLiveData

    private val eventFlow = MutableSharedFlow<MyFamilyEvent>()
    private val eventObserverList = ArrayList<Observer<MyFamilyEvent>>()

    init {
        viewModelScope.launch {
            eventFlow.collect {
                for (observer in eventObserverList) {
                    observer.onChanged(it)
                }
            }
        }
    }

    fun updateFamilyMainInfo(info: FamilyMainInfo) {
        _familyMainInfoLiveData.value = info
    }

    fun notifyRefreshRedDot() {
        notifyEvent(MyFamilyEvent.RefreshRedDot)
    }

    fun notifyEvent(event: MyFamilyEvent) {
        viewModelScope.launch {
            eventFlow.emit(event)
        }
    }

    fun observeEvent(owner: LifecycleOwner, observer: Observer<MyFamilyEvent>) {
        owner.lifecycleScope.launch {
            eventFlow.collect {
                observer.onChanged(it)
            }
        }
    }

    fun addEventObserver(observer: Observer<MyFamilyEvent>) {
        eventObserverList.add(observer)
    }

    fun removeEventObserver(observer: Observer<MyFamilyEvent>) {
        eventObserverList.remove(observer)
    }
}

sealed class MyFamilyEvent {
    object RefreshRedDot : MyFamilyEvent()
    class ShowDescPopu(val view: View, val id: Int) : MyFamilyEvent()
}