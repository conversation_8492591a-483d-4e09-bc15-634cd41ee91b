package com.wepie.wespy.module.family.main.mine.manage.title

import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.FamilyConfigDetail.TitleConfigInfo
import com.huiwan.configservice.constentity.propextra.HeadDecorExtra
import com.huiwan.decorate.UserDecorManager
import com.huiwan.decorate.UserDecorManager.LifeCallback
import com.huiwan.user.UserServiceKt
import com.huiwan.user.http.rspmodel.UserDecoration
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wejoy.weplay.ex.lifecycle.LifeViewModel
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.wespy.model.entity.family.FamilyMemberInfo
import com.wepie.wespy.model.entity.family.FamilyTitleMemberInfo
import com.wepie.wespy.module.family.FamilyManager
import com.wepie.wespy.net.http.api.FamilyApiKt
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

class FamilyTitleSetUpViewModel : LifeViewModel() {
    private val _state = MutableStateFlow<TitleSetUpState>(TitleSetUpState.Loading)
    val state: StateFlow<TitleSetUpState> = _state
    private var hid: Int = 0
    private var fid: Int = 0
    private var titleInfoList: List<TitleConfigInfo> = ArrayList()
    private var titleName: String = ""
    private var selfRole: Int = 0

    fun init(hid: Int) {
        this.hid = hid
        this.fid = FamilyManager.getInstance().selfFamilyInfo.family.familyId
        this.titleInfoList = ConfigHelper.getInstance().familyConfig.titleList
        val matchedTitle = titleInfoList.singleOrNull { it.hid == hid }
        this.titleName = matchedTitle?.name ?: ""
        this.selfRole = FamilyManager.getInstance().selfFamilyInfo.myRole
        refresh()
    }

    fun refresh() {
        viewModelScope.launch {
            val rsp = FamilyApiKt.titleInfoByHid(fid, hid)
            if (rsp is KtResultSuccess) {
                if (rsp.data.assignedMembers.isEmpty()) {
                    _state.update {
                        TitleSetUpState.Empty(
                            TitleSetUpInfo(
                                totalNum = rsp.data.totalAvailable,
                                selfRole = selfRole,
                                hid = hid,
                                titleName = titleName,
                                user = ArrayList()
                            )
                        )
                    }
                } else {
                    onDataChanged(rsp.data)
                }
            } else if (rsp is KtResultFailed) {
                _state.update { TitleSetUpState.Failed(rsp.failedDesc) }
            }
        }
    }

    fun onRemoveTitleChanged(item: FamilyTitleSetupItem, totalNum: Int) {
        viewModelScope.launch {
            val rsp = FamilyApiKt.removeTitleInfo(fid, item.hid, item.user.uid)
            if (rsp is KtResultSuccess) {
                val user = transformUserInfo(rsp.data.assignedUsers)
                _state.update {
                    TitleSetUpState.Success(
                        TitleSetUpInfo(
                            totalNum = totalNum,
                            selfRole = selfRole,
                            hid = hid,
                            titleName = titleName,
                            user = user
                        )
                    )
                }
            } else if (rsp is KtResultFailed) {
                ToastUtil.show(rsp.failedDesc)
                refresh()
            }
        }
    }


    private suspend fun updateUserDecors(users: List<Int>) =
        suspendCancellableCoroutine<Map<Int, HeadDecorExtra?>> { c ->
            UserDecorManager.getInstance()
                .updateDecorations(users, object : LifeCallback(toLife()) {
                    override fun onLoadDecoration(success: Boolean, decoration: UserDecoration?) {
                        val decorMap = users.mapNotNull { uid ->
                            UserDecorManager.getInstance().getUserDecorationById(uid)
                        }
                            .associate { decor ->
                                Pair(
                                    decor.uid,
                                    ConfigHelper.getInstance().getHeadDecorationById(decor.decorId)
                                )
                            }
                        c.resumeWith(Result.success(decorMap))
                    }
                })
        }

    private suspend fun transformUserInfo(data: List<FamilyMemberInfo>): List<FamilyTitleSetupItem> {
        val memberUidList = data.map { it.uid }
        val user = UserServiceKt.get().getCacheSimpleUserList(memberUidList)
        val decors = updateUserDecors(memberUidList)
        return data.map { item ->
            return@map FamilyTitleSetupItem(
                user = FamilyUserInfo(
                    uid = item.uid,
                    userSimpleInfo = user[item.uid],
                    decorExtra = decors[item.uid]
                ),
                hid = hid,
                role = item.role,
                selected = false,
                titleName = titleName
            )
        }
    }

    private suspend fun onDataChanged(data: FamilyTitleMemberInfo) {
        _state.update {
            val users = transformUserInfo(data.assignedMembers)
            TitleSetUpState.Success(
                TitleSetUpInfo(
                    totalNum = data.totalAvailable,
                    selfRole = selfRole,
                    hid = hid,
                    titleName = titleName,
                    user = users
                )
            )
        }

    }

    sealed class TitleSetUpState {
        data object Loading : TitleSetUpState()
        data class Success(val titleSetUpInfo: TitleSetUpInfo) : TitleSetUpState()
        data class Failed(val msg: String) : TitleSetUpState()
        data class Empty(val titleSetUpInfo: TitleSetUpInfo) : TitleSetUpState()
    }

    data class TitleSetUpInfo(
        val totalNum: Int,
        val selfRole: Int = 0,
        val hid: Int,
        val titleName: String,
        val user: List<FamilyTitleSetupItem>
    )
}