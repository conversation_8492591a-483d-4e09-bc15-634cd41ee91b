package com.wepie.wespy.module.family.main.mine.member

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.ktx.gone
import com.huiwan.base.ktx.visible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.widget.SimpleOutlineProvider
import com.huiwan.widget.actionbar.AppBarLayoutUtil
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.FamilyTabMemberViewNewBinding
import com.wepie.wespy.model.entity.family.FamilyMemberInfo
import com.wepie.wespy.model.entity.family.FamilyMemberLimit
import com.wepie.wespy.model.event.family.FamilyMemberChangeEvent
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.family.FamilyManager
import com.wepie.wespy.module.family.FamilyTrackEvent
import com.wepie.wespy.module.family.detail.FamilyRoleHelper
import com.wepie.wespy.module.family.main.mine.member.search.FamilyContributeMemberSearchView
import com.wepie.wespy.module.family.main.mine.member.search.FamilyGameMemberSearchView
import com.wepie.wespy.module.family.main.mine.member.search.FamilyGiftMemberSearchView
import com.wepie.wespy.module.family.main.mine.member.search.FamilyReceiveMemberSearchView
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 家庭成员Fragment
 * 整合所有功能，包括顶部吸顶、标签页切换等
 */
class FamilyMembersFragment : Fragment() {

    private lateinit var viewModel: FamilyMembersViewModel
    private lateinit var binding: FamilyTabMemberViewNewBinding
    private var onMemberChangeListener: OnMemberChangeListener? = null
    private val secondTitleAdapter by lazy { FamilyMemberSecondTitleAdapter() }
    private var userSort: Boolean = false
    private var defaultSelectTab: Int = FamilyMembersFragmentAdapter.PAGE_CONTRIBUTION
    private var isFirstEnter = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel = ViewModelProvider(requireActivity())[FamilyMembersViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FamilyTabMemberViewNewBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
        setupSecondaryTabs()
        setupViewPager()
        initObserver()
        initEventBus()
    }

    private fun initEventBus() {
        EventDispatcher.registerEventObserver(this)
    }

    private fun initViews() {
        binding.searchLay.setOnDoubleClick {
            when (viewModel.currentTab.value) {
                TabType.CONTRIBUTION -> {
                    FamilyContributeMemberSearchView.showAsDialog(
                        context = context,
                        limit = viewModel.familyMemberLimit.value ?: FamilyMemberLimit(),
                        memberList = viewModel.contributionMembers.value ?: emptyList(),
                        mode = viewModel.getManageMode(),
                        selfRole = viewModel.familySelfInfo.value?.role ?: 0,
                        sortType = viewModel.contributeSortType.value ?: 0
                    )
                }

                TabType.GIFT -> {
                    FamilyGiftMemberSearchView.showAsDialog(
                        context,
                        viewModel.giftMembers.value ?: emptyList(),
                        viewModel.familySelfInfo.value?.role ?: 0,
                        viewModel.getManageMode()
                    )
                }

                TabType.RECEIVING -> {
                    FamilyReceiveMemberSearchView.showAsDialog(
                        context,
                        viewModel.receivingMembers.value ?: emptyList(),
                        viewModel.familySelfInfo.value?.role ?: 0,
                        viewModel.getManageMode()
                    )
                }

                TabType.GAME -> {
                    FamilyGameMemberSearchView.showAsDialog(
                        context,
                        viewModel.gameMembers.value ?: emptyList(),
                        viewModel.familySelfInfo.value?.role ?: 0,
                        viewModel.getManageMode()
                    )
                }

                else -> {

                }
            }
        }

        binding.manageBtn.setOnDoubleClick {
            gotoFamilyMemberManageActivity()
        }
        binding.memberNumTv.setOnDoubleClick {
            gotoFamilyMemberManageActivity()
        }

        binding.activityBannerIv.clipToOutline = true
        binding.activityBannerIv.outlineProvider =
            SimpleOutlineProvider(ScreenUtil.dip2px(8F).toFloat())
        WpImageLoader.load(
            ResUtil.getStr(R.string.family_title_banner_icon),
            binding.activityBannerIv,
            ImageLoadInfo.newInfo().error(R.drawable.family_title_banner_icon)
                .placeholder(R.drawable.family_title_banner_icon)
        )
        binding.activityBannerIv.setOnDoubleClick {
            val fid = FamilyManager.getInstance().selfFamilyInfo.family.familyId
            JumpUtil.gotoFamilyTitleDetailActivity(context, fid, TrackSource.MY_FAMILY_MEMBER_PAGE)
        }

        binding.familyRankListLay.canScrollChildWhenAppBarShow = true

        AppBarLayoutUtil.adjustEvaluation(binding.familyMemberAppbar)
        binding.familyRankListLay.setupAppBar(binding.familyMemberAppbar)
        updateBannerIv(userSort)
    }

    private fun gotoFamilyMemberManageActivity() {
        if (isManager()) {
            JumpUtil.gotoFamilyMemberManageActivity(
                context,
                viewModel.contributeSortMode.value ?: FamilyMemberConst.SORT_MODE_DEFAULT,
                viewModel.contributeSortType.value ?: FamilyMemberConst.SORT_TYPE_DEFAULT,
                viewModel.currentTab.value?.value ?: FamilyMembersFragmentAdapter.PAGE_CONTRIBUTION
            )
        }
    }

    private fun setupSecondaryTabs() {
        val secondTitleLayoutManager = LinearLayoutManager(context)
        secondTitleLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
        binding.secondaryTabRv.layoutManager = secondTitleLayoutManager
        binding.secondaryTabRv.adapter = secondTitleAdapter
        val secondTitleList = mutableListOf(
            ResUtil.getStr(R.string.family_member_list_type_contribution),
            ResUtil.getStr(R.string.family_member_list_type_rank_send_gift),
            ResUtil.getStr(R.string.family_member_list_type_rank_receive_gift),
            ResUtil.getStr(R.string.family_member_list_type_rank_game)
        )
        secondTitleAdapter.refresh(secondTitleList)
        secondTitleAdapter.setClickCallback(object : FamilyRankClickCallback {
            override fun onRankTitleClick(position: Int) {
                binding.familyRankViewPager.setCurrentItem(position,  false)
                smoothScrollToCenter(position)
            }
        })
    }

    private fun setupViewPager() {
        val pagerAdapter = FamilyMembersFragmentAdapter(requireActivity())
        binding.familyRankViewPager.adapter = pagerAdapter

        binding.familyRankViewPager.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                // 更新当前标签页
                when (position) {
                    FamilyMembersFragmentAdapter.PAGE_CONTRIBUTION ->
                        viewModel.switchTab(TabType.CONTRIBUTION)

                    FamilyMembersFragmentAdapter.PAGE_GIFT ->
                        viewModel.switchTab(TabType.GIFT)

                    FamilyMembersFragmentAdapter.PAGE_RECEIVING ->
                        viewModel.switchTab(TabType.RECEIVING)

                    FamilyMembersFragmentAdapter.PAGE_GAME ->
                        viewModel.switchTab(TabType.GAME)
                }
                secondTitleAdapter.performSelect(position)
            }
        })
        secondTitleAdapter.performSelect(defaultSelectTab)
    }

    private fun initObserver() {
        viewModel.familyMembersNum.observe(viewLifecycleOwner) { num ->
            val count = if (viewModel.isManageMode()) {
                num.filterCount
            } else {
                num.reallyCount
            }
            val maxCount = num.maxCount
            binding.memberNumTv.text = String.format(
                LibBaseUtil.getLocale(),
                "%s/%s",
                count,
                maxCount
            )
            onMemberChangeListener?.onMemberSizeChange(num.reallyCount, maxCount)
        }

        viewModel.familySelfInfo.observe(viewLifecycleOwner) { info ->
            updateManagerBtn()
            updateSelfInfo(info)
        }

        viewModel.manageMode.observe(viewLifecycleOwner) { isManageMode ->
            updateBannerIv(isManageMode)
            updateSelfItemLayVisible(isManageMode)
            updateManagerBtn()
        }
    }

    private fun updateSelfInfo(info: FamilyMemberInfo) {
        binding.selfItem.bind(
            member = info,
            mode = viewModel.getManageMode(),
            tabType = viewModel.currentTab.value ?: TabType.CONTRIBUTION,
            limit = viewModel.familyMemberLimit.value ?: FamilyMemberLimit(),
            sortType = viewModel.contributeSortType.value ?: FamilyMemberConst.SORT_TYPE_DEFAULT
        )
    }

    private fun updateBannerIv(isManageMode: Boolean) {
        if (isManageMode) {
            binding.activityBannerIv.gone()
        } else {
            binding.activityBannerIv.visible()
        }
    }

    private fun updateSelfItemLayVisible(isManageMode: Boolean) {
        if (isManageMode) {
            binding.selfItem.gone()
        } else {
            binding.selfItem.visible()
        }
    }

    private fun updateManagerBtn() {
        if (isManager()) {
            binding.manageBtn.visibility = View.VISIBLE
        } else {
            binding.manageBtn.visibility = View.GONE
        }
    }

    private fun isManager(): Boolean {
        val selfInfo = viewModel.familySelfInfo.value ?: FamilyMemberInfo()
        return viewModel.getManageMode() != FamilyMemberConst.MANAGE_MODE
                && FamilyRoleHelper.hasPermission(
            selfInfo.role,
            FamilyRoleHelper.OPERATE_MANAGE_MEMBER
        )
    }

    // 将选中项居中
    private fun smoothScrollToCenter(position: Int) {
        val layoutManager = binding.secondaryTabRv.layoutManager

        if (layoutManager is LinearLayoutManager) {
            val child = layoutManager.findViewByPosition(position) ?: return
            val childCenter =
                (layoutManager.getDecoratedLeft(child) + layoutManager.getDecoratedRight(child)) / 2
            val screenCenter = (binding.secondaryTabRv.left + binding.secondaryTabRv.right) / 2
            val scrollDistance = childCenter - screenCenter
            binding.secondaryTabRv.smoothScrollBy(scrollDistance, 0)
        }
    }

    override fun setArguments(args: Bundle?) {
        super.setArguments(args)
        if (args != null) {
            userSort = args.getBoolean(EXTRA_USE_SORT, false)
            defaultSelectTab =
                args.getInt(EXTRA_SELECT_TAB, FamilyMembersFragmentAdapter.PAGE_CONTRIBUTION)
            viewModel.updateManageMode(userSort)
            val sortType = args.getInt(EXTRA_SELECT_TYPE, FamilyMemberConst.SORT_TYPE_DEFAULT)
            val sortMode = args.getInt(EXTRA_SELECT_MODE, FamilyMemberConst.SORT_MODE_DEFAULT)
            viewModel.updateContributeSortType(sortType, false)
            viewModel.updateContributeSortMode(sortMode)
        }
    }

    companion object {
        const val EXTRA_USE_SORT: String = "use_sort"
        const val EXTRA_SELECT_TAB: String = "select_tab"
        const val EXTRA_SELECT_MODE: String = "select_mode"
        const val EXTRA_SELECT_TYPE: String = "select_type"

        fun newInstance(): FamilyMembersFragment {
            return FamilyMembersFragment()
        }
    }

    fun setOnMemberChangeListener(onMemberChangeListener: OnMemberChangeListener) {
        this.onMemberChangeListener = onMemberChangeListener
    }

    interface OnMemberChangeListener {
        fun onMemberSizeChange(count: Int, maxCount: Int)
    }

    private fun refreshTabList() {
        viewModel.refreshTabList()
    }

    override fun onResume() {
        super.onResume()
        if (isFirstEnter) {
            isFirstEnter = false
        } else {
            FamilyTrackEvent.trackEvent(
                TrackButtonName.FAMILY_MEMBER,
                FamilyTrackEvent.getSubTab(viewModel.currentTab.value)
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun handleMemberRefresh(event: FamilyMemberChangeEvent?) {
        refreshTabList()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventDispatcher.unregisterEventObserver(this)
    }
}