package com.wepie.wespy.module.family.main.mine.task

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.ConfigHelper
import com.huiwan.widget.image.DrawableUtil
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.FamilyNewcomerMeetingRewardItemLayBinding

internal class NewcomerMeetingRewardAdapter : RecyclerView.Adapter<RewardViewHolder>() {
    private val data: MutableList<MeetingReward> = mutableListOf()
    var onClickMeetingGiftReward: ((v: View, rewardId: Int) -> Unit)? = null
    fun refresh(newData: List<MeetingReward>) {
        data.clear()
        data.addAll(newData)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RewardViewHolder {
        return RewardViewHolder(
            FamilyNewcomerMeetingRewardItemLayBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: RewardViewHolder, position: Int) {
        holder.bind(data[position])
    }

    override fun onViewAttachedToWindow(holder: RewardViewHolder) {
        if (holder.rewardId > 0) {
            holder.itemView.setOnDoubleClick {
                onClickMeetingGiftReward?.invoke(holder.itemView, holder.rewardId)
            }
        }
        super.onViewAttachedToWindow(holder)
    }

}

internal data class MeetingReward(
    val rewardId: Int,
    val rewardNum: Int
)

internal class RewardViewHolder(private val binding: FamilyNewcomerMeetingRewardItemLayBinding) :
    RecyclerView.ViewHolder(binding.root) {
    var rewardId: Int = 0
        private set

    fun bind(
        reward: MeetingReward,
    ) {
        rewardId = reward.rewardId
        binding.rewardNumTv.background =
            DrawableUtil.genColorRadius(ResUtil.getColor(R.color.black_alpha30), 24)
        binding.rewardNumTv.text = ResUtil.getStr(R.string.x_number, reward.rewardNum.toString())
        bindRewardInfo(reward.rewardId)
    }

    private fun bindRewardInfo(rewardId: Int) {
        val propItem = ConfigHelper.getInstance().propConfig.getPropItem(rewardId)
        if (propItem != null) {
            WpImageLoader.load(
                propItem.mediaUrl,
                binding.rewardIv,
                ImageLoadInfo.getGiftInfo()
            )
            binding.rewardNameTv.text = propItem.name
        }
    }
}

