package com.wepie.wespy.module.family.main.mine.msg;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.graphics.drawable.Drawable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ImageSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.decorate.CharmManager;
import com.huiwan.decorate.JackarooLevelDrawable;
import com.huiwan.widget.CustomCircleImageView;
import com.huiwan.widget.LinkMovementNoCrashMethod;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.view.CenterAlignImageSpan;
import com.wepie.wespy.model.entity.family.FamilyMessage;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.game.game.activity.TextJumpExtra;
import com.wepie.wespy.module.game.game.activity.TextSpanUtil;

/**
 * Created by bigwen on 2019-10-25.
 */
public class FamilyMsgViewHolder extends RecyclerView.ViewHolder {

    private LinearLayout msgLay;
    private CustomCircleImageView msgIconIv;
    private TextView msgTv;

    private TextView timeTv;
    private TextView unknowTv;

    public FamilyMsgViewHolder(View view) {
        super(view);
        timeTv = view.findViewById(R.id.time_tv);
        msgLay = view.findViewById(R.id.msg_lay);
        msgIconIv = view.findViewById(R.id.msg_icon_iv);
        msgTv = view.findViewById(R.id.msg_tv);
        unknowTv = view.findViewById(R.id.unknown_tv);
        hideAll();
    }

    private void hideAll() {
        timeTv.setVisibility(GONE);
        msgLay.setVisibility(GONE);
        unknowTv.setVisibility(GONE);
    }

    public void refresh(final FamilyMsgInterface familyMsgInterface, boolean first, int viewType) {
        hideAll();
        int type = familyMsgInterface.getMsgType();
        if (type == FamilyMsgInterface.TYPE_APPLY) {
            msgLay.setVisibility(VISIBLE);
            WpImageLoader.load(familyMsgInterface.getMsgIcon(), msgIconIv, HeadImageLoader.genHeadLoadInfo());
            SpannableStringBuilder ssb = TextSpanUtil.updateDeepLinkSpan(familyMsgInterface.getMsgText(), new TextJumpExtra(0xffFF8900, ""));
            ssb.append(" ");
            FamilyMessage.FamilyMessageData extData = familyMsgInterface.getExtData();
            if (extData.getJoinLimitType() <= 0 && extData.getJoinLimitValue() <= 0) {
                ssb.append(ResUtil.getStr(R.string.family_news_tab_any_value));
            } else if (extData.getJoinLimitType() == 0) {
                int flower = extData.getJoinLimitValue();
                int resInt = CharmManager.getInstance().getCharmShortRes(flower);
                int startLength = ssb.length();
                ssb.append(ResUtil.getStr(R.string.take));
                Drawable drawable = ResUtil.getDrawable(resInt);
                int height = ScreenUtil.dip2px(14);
                drawable.setBounds(0, 0, drawable.getIntrinsicWidth() * height / drawable.getIntrinsicHeight(), height);
                ImageSpan imageSpan = new CenterAlignImageSpan(drawable);
                ssb.setSpan(imageSpan, startLength, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else if (extData.getJoinLimitType() == 1) {
                int gameLevel = extData.getJoinLimitValue();
                Object tag = msgTv.getTag();
                JackarooLevelDrawable drawable = null;
                if (tag instanceof JackarooLevelDrawable) {
                    drawable = (JackarooLevelDrawable) tag;
                    if (drawable.getGameLevel() != gameLevel) {
                        drawable.attach(null);
                        drawable = null;
                    }
                }
                if (drawable == null) {
                    drawable = new JackarooLevelDrawable(gameLevel);
                    drawable.attach(msgTv);
                    msgTv.setTag(drawable);
                }
                int startLength = ssb.length();
                ssb.append(ResUtil.getStr(R.string.take));
                ssb.setSpan(new ImageSpan(drawable), startLength, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            msgTv.setText(ssb);
            msgTv.setMovementMethod(new LinkMovementNoCrashMethod());
        } else if (type == FamilyMsgInterface.TYPE_TEXT_MSG) {
            msgLay.setVisibility(VISIBLE);
            msgTv.setText(familyMsgInterface.getMsgText());
            TextSpanUtil.checkTextSpan(msgTv.getContext(), 0xffFF8900, -1, -1, msgLay, msgTv, familyMsgInterface.getMsgText(), TrackSource.FAMILY_MSG, null);
            HeadImageLoader.loadCircleHeadImage(familyMsgInterface.getMsgIcon(), msgIconIv);
        } else if (type == FamilyMsgInterface.TYPE_TIME) {
            timeTv.setVisibility(VISIBLE);
            timeTv.setText(familyMsgInterface.getMsgText());
            if (first) {
                ((ViewGroup.MarginLayoutParams) timeTv.getLayoutParams()).topMargin = ScreenUtil.dip2px(16);
            } else {
                ((ViewGroup.MarginLayoutParams) timeTv.getLayoutParams()).topMargin = 0;
            }
        } else {
            //未知消息类型，用一个高度为0的text view (解决recycleview一个问题，item为空会导致canScrollVertically()判断异常，如果刚好在第一个，导致无法下拉刷新 )
            unknowTv.setVisibility(VISIBLE);
        }

        msgIconIv.setOnClickListener(v -> {
            if (familyMsgInterface.getUid() > 0 && viewType == FamilyTabMsgView.TYPE_FAMILY_SELF_FAMILY) {
                JumpUtil.enterUserInfoDetailActivity(msgIconIv.getContext(), familyMsgInterface.getUid(), TrackSource.FAMILY_MSG);
            }
        });
    }
}