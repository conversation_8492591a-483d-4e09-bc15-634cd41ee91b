package com.wepie.wespy.module.family.main.mine.task;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ConcatAdapter;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.redDotHelper.RedDotNode;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.family.FamilySimpleInfo;
import com.wepie.wespy.model.entity.task.TaskListInfo;
import com.wepie.wespy.module.family.FamilyTrackEvent;
import com.wepie.wespy.module.family.main.mine.FamilyBaseFragment;
import com.wepie.wespy.module.family.main.mine.MyFamilyEvent;
import com.wepie.wespy.module.task.DetailTaskInfo;
import com.wepie.wespy.module.task.FamilyMeetingTaskInfoKt;
import com.wepie.wespy.module.task.FamilyNewcomerTaskAdapter;
import com.wepie.wespy.module.task.TaskListAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-10-24.
 */
public class FamilyTabTaskView extends FamilyBaseFragment {

    private TaskListAdapter adapter;
    private FamilyTaskActivateBoxAdapter familyTaskActivateBoxAdapter;
    private FamilyNewcomerTaskAdapter familyNewcomerTaskAdapter;
    private FamilyTabTaskPresenter presenter;

    private boolean isFirstResume = true;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.family_tab_task_view, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Context context = requireContext();
        RecyclerView recyclerView = view.findViewById(R.id.recycle_view);
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        adapter = new TaskListAdapter(context,
                (taskInfo, rewardList, valueList) -> presenter.onRecvTask(taskInfo, rewardList, valueList),
                (taskRewardItemView, rewardId) -> {
                    viewModel.notifyEvent(new MyFamilyEvent.ShowDescPopu(taskRewardItemView, rewardId));
                });
        familyNewcomerTaskAdapter = new FamilyNewcomerTaskAdapter(new ITaskDetailOperate() {
            @Override
            public void onJumpOtherPage(@NonNull Context context, @NonNull String jumpUrl) {
                presenter.gotoOtherPager(context, jumpUrl);
            }

            @Override
            public void onClaimReward(@NonNull DetailTaskInfo task, boolean isMeetingGiftTask) {
                presenter.onRecvTask(task, isMeetingGiftTask);
            }

            @Override
            public void addOrUpdatePopupRewardDetail(@NonNull View v, int rewardId) {
                viewModel.notifyEvent(new MyFamilyEvent.ShowDescPopu(v, rewardId));
            }

            @Override
            public void onClickMeetingGiftReward(@NonNull View v, int rewardId) {
                presenter.showRewardPreviewDialog(v, rewardId);
            }
        });
        familyTaskActivateBoxAdapter = new FamilyTaskActivateBoxAdapter(activeStageInfo -> presenter.clickActiveItem(getContext(), activeStageInfo));
        recyclerView.setAdapter(new ConcatAdapter(familyTaskActivateBoxAdapter, familyNewcomerTaskAdapter, adapter));
        presenter = new FamilyTabTaskPresenter(this);

        presenter.reqRefresh();

        viewModel.observeEvent(getViewLifecycleOwner(), myFamilyEvent -> {
            if (myFamilyEvent instanceof MyFamilyEvent.RefreshRedDot) {
                if (RedDotUtil.get().hasRedDot(RedDotNode.NODE_D_F_TASK)) {
                    reqRefresh();
                }
            }
        });
    }

    public void updateTaskList(TaskListInfo taskListInfo) {
        List<TaskListInfo.TaskInfo> familyTaskInfo = taskListInfo.getFamilyTaskInfo();
        familyNewcomerTaskAdapter.refresh(FamilyMeetingTaskInfoKt.loadFamilyNewcomerTaskInfoFrom(taskListInfo));
        adapter.updateTask(familyTaskInfo);
        if (!taskListInfo.hasFamilyTaskRedDot() && RedDotUtil.get().hasRedDot(RedDotNode.NODE_D_F_TASK)) {
            RedDotUtil.get().reportRead(RedDotInfo.RED_DOT_FAMILY_TASK);
        }
    }

    public void updateActivateView(FamilyTaskActivateBoxInfo info) {
        List<FamilyTaskActivateBoxInfo> list = new ArrayList<>();
        list.add(info);
        familyTaskActivateBoxAdapter.update(list);
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void refreshFamilyInfo(@NonNull FamilyMainInfo familyInfo) {
        presenter.updateFamilyInfo(familyInfo);
    }

    public void onResume() {
        super.onResume();
        FamilyTrackEvent.trackEvent(TrackSource.TASK, "");
        if (isFirstResume) {
            isFirstResume = false;
        } else {
            ViewExKt.postAutoCancel(this.getView(), 200, this::reqRefresh);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        this.getView().removeCallbacks(this::reqRefresh);
    }

    public void reqRefresh() {
        presenter.reqRefresh();
    }
}