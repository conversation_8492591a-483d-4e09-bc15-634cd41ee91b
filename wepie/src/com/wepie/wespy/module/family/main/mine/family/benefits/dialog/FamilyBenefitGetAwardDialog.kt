import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.wejoy.weplay.composewidget.Body1
import com.wejoy.weplay.composewidget.H4
import com.wejoy.weplay.composewidget.WpImage
import com.wepie.wespy.R
import com.wepie.wespy.base.composeui.WpTheme

@Composable
fun AwardDialog(
    onDismiss: () -> Unit,
    title: AnnotatedString = buildAnnotatedString { append("") },
    titleColor: Color = MaterialTheme.colorScheme.onPrimary,
    message: AnnotatedString = buildAnnotatedString { append("") },
    messageColor: Color = MaterialTheme.colorScheme.onTertiary,
    buttonText: AnnotatedString = buildAnnotatedString { append("") },
    iconResId: Int = R.drawable.family_main_coin_reward_icon
) {
    androidx.compose.ui.window.Dialog(
        onDismissRequest = onDismiss,
        properties = androidx.compose.ui.window.DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        WpTheme {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable(onClick = onDismiss)
            ) {
                // 底部弹窗内容
                Column(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .width(303.dp)
                        .clickable(enabled = false) { }
                        .background(
                            Color.White,
                            RoundedCornerShape(12.dp)
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    // 顶部图标
                    WpImage(
                        model = iconResId,
                        contentDescription = "Award Icon",
                        modifier = Modifier.size(64.dp)
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // 标题
                    H4(
                        text = title,
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 24.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // 消息内容
                    Body1(
                        text = message,
                        color = messageColor,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 24.dp)
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    // 确认按钮
                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF49CCB6)
                        ),
                        shape = RoundedCornerShape(24.dp),
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .padding(horizontal = 16.dp, vertical = 9.dp)
                            .width(183.dp)
                            .height(40.dp)
                    ) {
                        H4(
                            text = buttonText,
                            color = Color.White,
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))
                }
            }
        }
    }
}
