package com.wepie.wespy.module.family.main.mine;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.module.webview.WebActivity;
import com.huiwan.user.LoginHelper;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.family.FamilyMainShenceEvent;
import com.wepie.wespy.module.family.main.mine.family.FamilyTabMainView;
import com.wepie.wespy.module.family.main.mine.manage.FamilyTabManageView;
import com.wepie.wespy.module.family.main.mine.member.FamilyMembersFragment;
import com.wepie.wespy.module.family.main.mine.msg.FamilyTabMsgView;
import com.wepie.wespy.module.family.main.mine.task.FamilyTabTaskView;
import com.wepie.wespy.module.task.view.popup.TaskPopupDescriptionView;
import com.wepie.wespy.utils.PopupUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2019-10-16
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MyFamilyView extends FrameLayout implements View.OnClickListener, Observer<MyFamilyEvent> {

    private MyFamilyPresenter presenter;
    private final List<FamilyTabItem> tabItemList = new ArrayList<>();
    private final String[] tabNameArray = new String[]{ResUtil.getStr(R.string.my_family_tab_main),
            ResUtil.getStr(R.string.my_family_tab_task),
            ResUtil.getStr(R.string.my_family_tab_members),
            ResUtil.getStr(R.string.family_news_tab_content),
            ResUtil.getStr(R.string.my_family_tab_manage)};
    private ImageView settingIv;
    private ViewPager2 viewPager;
    private MyFamilyViewModel viewModel;
    private FragmentStateAdapter adapter;

    private TabLayout familyTabLayout;
    private TaskPopupDescriptionView descView;

    public MyFamilyView(@NonNull Context context) {
        super(context);
        initViews();
    }

    private void initViews() {
        LayoutInflater.from(getContext()).inflate(R.layout.my_family_view, this);
        familyTabLayout = findViewById(R.id.family_tab_layout);
        descView = findViewById(R.id.task_desc_popup);
        initViewPager();
        initTitle();
        presenter = new MyFamilyPresenter(this);
        presenter.init();
        refreshTab(0);
    }

    private void initTitle() {
        ImageView rankIv = findViewById(R.id.rank_iv);
        ImageView backIv = findViewById(R.id.back_iv);
        settingIv = findViewById(R.id.setting_iv);

        backIv.setOnClickListener(v -> {
            Activity activity = ContextUtil.getActivityFromContext(getContext());
            if (activity != null) {
                activity.finish();
            }
        });

        rankIv.setOnClickListener(v -> {
            FamilyMainShenceEvent.appClick(TrackButtonName.RANK);
            JumpUtil.gotoFamilyRankActivity(getContext());
        });
    }

    private void initViewPager() {
        viewPager = findViewById(R.id.view_pager);
        FragmentActivity activity = ContextUtil.getFragmentActivityFromContext(getContext());
        viewModel = new ViewModelProvider(activity).get(MyFamilyViewModel.class);
        adapter = new FragmentStateAdapter(activity) {
            @NonNull
            @Override
            public Fragment createFragment(int position) {
                switch (position) {
                    case 0:
                        return new FamilyTabMainView();
                    case 1:
                        return new FamilyTabTaskView();
                    case 2:
                        return new FamilyMembersFragment();
                    case 3:
                        return new FamilyTabMsgView();
                    case 4:
                        return new FamilyTabManageView();
                    default:
                        return new Fragment();
                }
            }

            @Override
            public int getItemCount() {
                return 5;
            }
        };
        viewPager.setAdapter(adapter);
        viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {

            @Override
            public void onPageSelected(int position) {
                for (int i = 0; i < tabItemList.size(); i++) {
                    FamilyTabItem tabItem = tabItemList.get(i);
                    tabItem.refreshSelect(position);
                    tabItem.updateRedDot();
                }
            }
        });
        new TabLayoutMediator(familyTabLayout, viewPager, (tab, position) -> {
            tab.setCustomView(R.layout.family_main_tab_item);
            tab.view.setClipChildren(false);
            tab.view.setPaddingRelative(0, 0, 0, 0);
            FamilyTabItem tabItem = new FamilyTabItem(tab.getCustomView(), position);
            tabItem.setName(tabNameArray[position]);
            tabItemList.add(tabItem);
        }).attach();
    }

    public void update(final FamilyMainInfo mainInfo) {
        final boolean selfIsOwner = mainInfo.getFamily().getOwner() == LoginHelper.getLoginUid();
        if (viewModel != null) {
            viewModel.updateFamilyMainInfo(mainInfo);
        }
        settingIv.setOnClickListener(v -> {
            FrameLayout frameLayout = new FrameLayout(getContext());
            View view = LayoutInflater.from(getContext()).inflate(R.layout.family_main_setting_view, frameLayout);
            View helpView = view.findViewById(R.id.help_lay);
            View exitView = view.findViewById(R.id.exit_lay);
            TextView exitTextView = view.findViewById(R.id.exit_tv);
            exitTextView.setText(selfIsOwner ? R.string.family_dismiss : R.string.family_quit);
            final PopupWindow popupWindow = PopupUtil.showPopupView(settingIv, frameLayout);
            helpView.setOnClickListener(v1 -> {
                WebActivity.go(getContext(), ConfigHelper.getInstance().getFamilyConfig().getFamilyHelpUrl());
                popupWindow.dismiss();
            });
            exitView.setOnClickListener(v12 -> {
                presenter.exitFamily(selfIsOwner, mainInfo);
                popupWindow.dismiss();
            });
        });
    }

    @Override
    public void onClick(View v) {

    }

    public void refreshTab(int defaultIndex) {
        if (defaultIndex >= 0 && defaultIndex < adapter.getItemCount()) {
            viewPager.setCurrentItem(defaultIndex);
        }
    }

    public void refreshRedDot() {
        for (int i = 0; i < tabItemList.size(); i++) {
            FamilyTabItem tabItem = tabItemList.get(i);
            tabItem.updateRedDot();
        }
        viewModel.notifyRefreshRedDot();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        viewModel.addEventObserver(this);
    }

    @Override
    protected void onDetachedFromWindow() {
        presenter.detach();
        viewModel.removeEventObserver(this);
        super.onDetachedFromWindow();
    }

    @Override
    public void onChanged(MyFamilyEvent event) {
        if (event instanceof MyFamilyEvent.ShowDescPopu) {
            MyFamilyEvent.ShowDescPopu popu = (MyFamilyEvent.ShowDescPopu) event;
            descView.addOrUpdatePopDesc(popu.getView(), popu.getId());
        }
    }
}
