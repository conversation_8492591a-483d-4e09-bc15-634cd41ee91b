package com.wepie.wespy.module.family.main.mine.task;

import android.content.Context;
import android.os.SystemClock;
import android.view.View;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.family.FamilyRewardList;
import com.wepie.wespy.model.entity.family.FamilySimpleInfo;
import com.wepie.wespy.model.entity.task.TaskListInfo;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.family.dialogs.FamilyActiveAwardDialog;
import com.wepie.wespy.module.shop.dialogs.PropInfoConfig;
import com.wepie.wespy.module.shop.dialogs.PropInfoDialog;
import com.wepie.wespy.module.task.DetailTaskInfo;
import com.wepie.wespy.net.http.api.FamilyApi;
import com.wepie.wespy.net.http.api.TaskApi;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-10-25.
 */
public class FamilyTabTaskPresenter {

    private FamilyMainInfo mainInfo;
    private TaskListInfo listInfo;
    private final FamilyTabTaskView tabTaskView;
    private final ILife life;
    private FamilySimpleInfo familySimpleInfo;
    private long lastReqRefreshTime = 0L;
    private long ReqRefreshInterval = 100L;

    public FamilyTabTaskPresenter(FamilyTabTaskView tabTaskView) {
        this.tabTaskView = tabTaskView;
        life = ILifeUtil.toLife(tabTaskView);
    }

    public void reqRefresh() {
        reqRefresh(false);
    }

    public void reqRefresh(boolean forceReq) {
        //这里会由于 MyFamilyEvent.RefreshRedDot 连续发送多次 reqRefresh，这里简单处理,限制一下频率
        if (SystemClock.elapsedRealtime() - lastReqRefreshTime < ReqRefreshInterval && !forceReq) {
            return;
        }
        lastReqRefreshTime = SystemClock.elapsedRealtime();
        refreshList();
        refreshBaseInfo();
    }

    public void refreshBaseInfo() {
        FamilyApi.simpleInfo(new LifeDataCallback<FamilySimpleInfo>(life) {
            @Override
            public void onSuccess(Result<FamilySimpleInfo> result) {
                familySimpleInfo = result.data;
                tabTaskView.updateActivateView(new FamilyTaskActivateBoxInfo(listInfo, familySimpleInfo));
            }

            @Override
            public void onFail(int i, String s) {

            }
        });
    }

    public void refreshList() {
        TaskApi.getTaskListNew(TaskListInfo.FAMILY_TASK, new LifeDataCallback<TaskListInfo>(life) {
            @Override
            public void onSuccess(Result<TaskListInfo> result) {
                listInfo = result.data;
                tabTaskView.updateTaskList(result.data);
                tabTaskView.updateActivateView(new FamilyTaskActivateBoxInfo(listInfo, familySimpleInfo));
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    private void requestActiveReward(final Context context, final TaskListInfo.ActiveStageInfo activeStageInfo) {
        TaskApi.recvStage(activeStageInfo.stageId, TaskListInfo.FAMILY_TASK, new LifeDataCallback<Object>(life) {
            @Override
            public void onSuccess(Result<Object> result) {
                FamilyActiveAwardDialog.showAward(context,
                        FamilyRewardList.formData(activeStageInfo.rewardIds, activeStageInfo.rewardValues).getDataList(), true);
                reqRefresh(true);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    public void clickActiveItem(Context mContext, TaskListInfo.ActiveStageInfo activeStageInfo) {
        if (activeStageInfo.canReceive()) {
            requestActiveReward(mContext, activeStageInfo);
        } else if (activeStageInfo.isOpen()) {
            ToastUtil.show(ResUtil.getStr(R.string.family_task_activity_already_get_tips));
        } else {
            FamilyActiveAwardDialog.showPreview(mContext,
                    FamilyRewardList.formData(activeStageInfo.rewardIds, activeStageInfo.rewardValues).getDataList(),
                    activeStageInfo.needActive, true);
        }
    }

    void updateFamilyInfo(FamilyMainInfo familyMainInfo) {
        mainInfo = familyMainInfo;
        if (mainInfo == null || listInfo == null) {
            return;
        }
        tabTaskView.updateActivateView(new FamilyTaskActivateBoxInfo(listInfo, familySimpleInfo));
    }

    public void gotoOtherPager(Context context, String jumpUrl) {
        JumpCommon.gotoOtherPager(context, jumpUrl, TrackButtonName.TASK, null);
    }

    public void onRecvTask(final TaskListInfo.TaskInfo taskInfo, final List<Integer> rewardList, final List<Integer> valueList) {
        TaskApi.recvTask(taskInfo.taskId, new LifeDataCallback<Object>(life) {
            @Override
            public void onSuccess(Result<Object> result) {
                if (rewardList != null && valueList != null) {
                    List<Integer> totalRewardValues = new ArrayList<>(valueList.size());
                    for (Integer value : valueList) {
                        int totalValue = value * taskInfo.remainRewardNum;
                        totalRewardValues.add(totalValue);
                    }

                    FamilyActiveAwardDialog.showAward(tabTaskView.getContext(),
                            FamilyRewardList.formData(rewardList, totalRewardValues).getDataList(), true);
                }
                reqRefresh(true);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    public void onRecvTask(final DetailTaskInfo taskInfo, boolean isMeetingGiftTask) {
        TaskApi.recvTask(taskInfo.getTaskId(), new LifeDataCallback<Object>(life) {
            @Override
            public void onSuccess(Result<Object> result) {
                List<Integer> valueList = taskInfo.getRewardValues();
                List<Integer> totalRewardValues = new ArrayList<>(valueList.size());
                for (Integer value : valueList) {
                    int totalValue = value * taskInfo.getRemainRewardNum();
                    totalRewardValues.add(totalValue);
                }
                if (isMeetingGiftTask && tabTaskView.getContext() != null) {
                    FamilyNewcomerMeetingGiftDialog.show(tabTaskView.getContext(), FamilyNewcomerMeetingGiftDialog.rewardsFrom(taskInfo, taskInfo.getRewardValues()));
                } else if (null != tabTaskView.getContext()) {
                    FamilyActiveAwardDialog.showAward(tabTaskView.getContext(),
                            FamilyRewardList.formData(taskInfo.getRewardIds(), totalRewardValues).getDataList(), true);
                }

                reqRefresh(true);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    public void showRewardPreviewDialog(View v, int rewardId) {
        PropInfoConfig config = PropInfoConfig.fromOutOfShop(rewardId);
        PropInfoDialog.show(v.getContext(), config, null);
    }
}
