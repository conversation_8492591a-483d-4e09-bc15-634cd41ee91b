package com.wepie.wespy.module.family.main.mine.manage.title

import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.propextra.HeadDecorExtra
import com.huiwan.decorate.UserDecorManager
import com.huiwan.decorate.UserDecorManager.LifeCallback
import com.huiwan.user.UserServiceKt
import com.huiwan.user.http.rspmodel.UserDecoration
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wejoy.weplay.ex.lifecycle.LifeViewModel
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.wespy.model.entity.family.FamilyTitleRep
import com.wepie.wespy.module.family.FamilyManager
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface.ROLE_NONE
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface.ROLE_OWNER
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface.ROLE_SUB_OWNER
import com.wepie.wespy.net.http.api.FamilyApiKt
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

class FamilyTitleDetailViewModel : LifeViewModel() {
    private val _state = MutableStateFlow(FamilyTitleInfoList())
    val state: StateFlow<FamilyTitleInfoList> = _state
    private var targetFid = 0

    private suspend fun updateUserDecors(users: List<Int>) =
        suspendCancellableCoroutine<Map<Int, HeadDecorExtra?>> { c ->
            UserDecorManager.getInstance()
                .updateDecorations(users, object : LifeCallback(toLife()) {
                    override fun onLoadDecoration(success: Boolean, decoration: UserDecoration?) {
                        val decorMap = users.mapNotNull { uid ->
                            UserDecorManager.getInstance().getUserDecorationById(uid)
                        }.associate { decor ->
                            Pair(
                                decor.uid,
                                ConfigHelper.getInstance().getHeadDecorationById(decor.decorId)
                            )
                        }
                        c.resumeWith(Result.success(decorMap))
                    }
                })
        }

    fun init(targetFid: Int) {
        this.targetFid = targetFid
        refresh()
    }

    fun refresh(isRefreshing: Boolean = false) {
        viewModelScope.launch {
            _state.update { it.copy(isRefreshing = isRefreshing) }
            val rsp = FamilyApiKt.titleInfo(<EMAIL>)
            if (rsp is KtResultSuccess) {
                onDataChanged(rsp.data)
            } else if (rsp is KtResultFailed) {
                ToastUtil.show(rsp.failedDesc)
            }
        }
    }


    private suspend fun onItemChanged(data: FamilyTitleRep.FamilyTitleDetailInfo): List<FamilyUserInfo> {
        val user = UserServiceKt.get().getCacheSimpleUserList(data.assignedUids)
        val decors = updateUserDecors(data.assignedUids)
        val displayInfoList = data.assignedUids.map { uid ->
            return@map FamilyUserInfo(
                uid = uid,
                userSimpleInfo = user[uid],
                decorExtra = decors[uid]
            )
        }
        return displayInfoList
    }

    private suspend fun onDataChanged(data: FamilyTitleRep) {
        val titleInfoList = ConfigHelper.getInstance().familyConfig.titleList
        val role = if (targetFid == FamilyManager.getInstance().selfFamilyInfo.family.familyId) {
            FamilyManager.getInstance().selfFamilyInfo.myRole
        } else {
            ROLE_NONE
        }
        val infoMap = titleInfoList.associateBy { it.hid }
        val list = data.titleDetailList.map {
            return@map FamilyTitleInfo(
                it.hid,
                role,
                it.totalAvailable,
                it.used,
                infoMap[it.hid]?.name ?: "",
                infoMap[it.hid]?.desc ?: "",
                onItemChanged(it)
            )
        }
        _state.update { it.copy(isRefreshing = false, titleInfoList = list) }

    }

    data class FamilyTitleInfoList(
        val isRefreshing: Boolean = false,
        val titleInfoList: List<FamilyTitleInfo> = ArrayList()
    )

    data class FamilyTitleInfo(
        val hid: Int = 0,
        val selfRole: Int = 0,
        val totalNum: Int = 0,
        val usedNum: Int = 0,
        val titleName: String = "",
        val desc: String = "",
        val users: List<FamilyUserInfo> = ArrayList()
    ) {
        /**
         * 是家族管理员
         */
        fun isFamilyAdmin(): Boolean {
            return selfRole == ROLE_OWNER || selfRole == ROLE_SUB_OWNER
        }

        /**
         * 非家族成员
         */
        fun noFamilyRole(): Boolean {
            return selfRole == ROLE_NONE
        }
    }
}