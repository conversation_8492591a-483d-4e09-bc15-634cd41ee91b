package com.wepie.wespy.module.family.main.mine.family

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.TextUnitType
import androidx.compose.ui.unit.dp
import com.huiwan.base.str.ResUtil
import com.wejoy.weplay.composewidget.BasicText
import com.wejoy.weplay.composewidget.WpImage
import com.wejoy.weplay.composewidget.autoMirrored
import com.wepie.wespy.R
import com.wepie.wespy.base.composeui.WpTheme
import com.wepie.wespy.model.entity.family.FamilyWeekGameInfo

fun setContent(view: ComposeView, horizontalPadding: Int, vm: FamilyWeekGameViewModel) {
    view.setContent {
        val weekGameInfo by vm.weekGameInfoFlow.collectAsState()
        val gameTime by vm.weekGameTimeFlow.collectAsState()
        WpTheme {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp)
            ) {
                Spacer(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(10.dp)
                        .background(Color(0xFFF4F4F4))
                )
                FamilyWeekGameTitle(
                    weekGameInfo.gameStatus,
                    gameTime,
                    Modifier.padding(horizontal = horizontalPadding.dp)
                )
                FamilyWeekGameDetail(
                    weekGameInfo,
                    Modifier.padding(horizontal = horizontalPadding.dp),
                    onClick = {
                        vm.onClick(weekGameInfo.jumpUrl)
                    }
                )
            }
        }
    }
}

@Composable
fun FamilyWeekGameTitle(
    gameStatus: Int,
    gameTime: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(40.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(
            modifier = Modifier
                .width(4.dp)
                .height(12.dp)
                .background(color = Color(0xFF49CCB6), RoundedCornerShape(2.dp))
        )
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .padding(start = 4.dp)
                .align(Alignment.CenterVertically)
                .weight(1F),
            contentAlignment = Alignment.CenterStart
        ) {
            BasicText(
                text = stringResource(R.string.family_main_activity_family_week_game),
                fontSize = TextUnit(14f, TextUnitType.Sp),
                color = Color(0xFF333333),
                fontWeight = FontWeight.Bold,
            )
        }
        FamilyWeekGameTimer(gameStatus, gameTime)
    }

}

@Composable
fun FamilyWeekGameTimer(gameStatus: Int, gameTime: String) {
    if (gameStatus == 2) {
        Image(
            painter = painterResource(R.drawable.icon_timer),
            contentDescription = "icon_timer",
            modifier = Modifier.size(12.dp)
        )
        BasicText(
            text = gameTime,
            fontSize = TextUnit(12f, TextUnitType.Sp),
            color = MaterialTheme.colorScheme.onTertiary,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(start = 2.dp)
        )
    } else {
        BasicText(
            text = ResUtil.getStr(
                R.string.family_main_activity_family_week_next_round_title,
                gameTime
            ),
            fontSize = TextUnit(12f, TextUnitType.Sp),
            color = MaterialTheme.colorScheme.onTertiary,
            fontWeight = FontWeight.Medium,
        )
    }
}

@Composable
fun FamilyWeekGameDetail(
    info: FamilyWeekGameInfo,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Card(
        shape = RoundedCornerShape(size = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF2F4F9)
        ),
        modifier = modifier
            .fillMaxWidth()
            .height(80.dp)
            .border(
                width = 0.5.dp,
                color = Color(0xFFDBE0ED),
                shape = RoundedCornerShape(size = 8.dp)
            ),
        onClick = onClick,
    ) {
        Row(
            modifier = Modifier
                .fillMaxHeight()
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 8.dp),
        ) {
            WpImage(info.gradeIcon, Modifier.size(64.dp))
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .padding(start = 12.dp)
                    .weight(1f),
                contentAlignment = Alignment.CenterStart
            ) {
                Column {
                    BasicText(
                        text = info.grade,
                        fontSize = TextUnit(16f, TextUnitType.Sp),
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontWeight = FontWeight.Bold,
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        BasicText(
                            text = info.gameInfoDesc,
                            fontSize = TextUnit(10f, TextUnitType.Sp),
                            color = MaterialTheme.colorScheme.onTertiary,
                            fontWeight = FontWeight.Normal,
                            modifier = Modifier.padding(end = 8.dp)
                        )
                        Spacer(
                            modifier = Modifier
                                .width(1.dp)
                                .height(8.dp)
                                .background(Color(0xFF999CB4))
                        )
                        BasicText(
                            text = info.rankInfo,
                            fontSize = TextUnit(10f, TextUnitType.Sp),
                            color = MaterialTheme.colorScheme.onTertiary,
                            fontWeight = FontWeight.Normal,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }
            Image(
                painter = painterResource(R.drawable.forward_icon_new),
                contentDescription = "Forward Icon",
                colorFilter = ColorFilter.tint(
                    color = Color(0xFF999CB4),
                    blendMode = BlendMode.SrcIn
                ),
                modifier = Modifier
                    .size(12.dp)
                    .autoMirrored()
                    .align(Alignment.CenterVertically)
            )
        }
    }
}

@Preview
@Composable
fun FamilyWeekGamePreview() {
    val weekGameInfo = FamilyWeekGameInfo()
    weekGameInfo.gameStatus = 2
    weekGameInfo.grade = "Treasury"
    weekGameInfo.gameInfoDesc = "Family points: 200"
    weekGameInfo.rankInfo = "Current ranking: 2"

    val gameTime = "02:00:00"

    WpTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp)
        ) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(10.dp)
                    .background(Color(0xFFF4F4F4))
            )
            FamilyWeekGameTitle(
                weekGameInfo.gameStatus,
                gameTime,
                Modifier.padding(horizontal = 10.dp)
            )
            FamilyWeekGameDetail(
                weekGameInfo,
                Modifier.padding(horizontal = 10.dp),
                onClick = {
                }
            )
        }
    }

}