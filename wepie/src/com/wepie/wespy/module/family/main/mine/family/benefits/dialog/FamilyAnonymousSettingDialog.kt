package com.wepie.wespy.module.family.main.mine.family.benefits.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.huiwan.base.util.ResUtilKt
import com.wejoy.weplay.composewidget.BasicText
import com.wejoy.weplay.composewidget.H4
import com.wepie.wespy.R
import com.wepie.wespy.base.composeui.WpTheme
import com.wepie.wespy.module.family.main.mine.family.benefits.generateAnnotatedColorString

/**
 * 匿名设置底部弹窗
 * 点击空白区域可关闭弹窗
 * @param show 是否显示弹窗
 * @param onDismiss 关闭弹窗的回调
 */
@Composable
fun AnonymousSettingsBottomSheet(
    show: Boolean,
    onDismiss: () -> Unit,
    viewModel: FamilyAnonymousSettingViewModel = viewModel()
) {
    if (show) {
        val switchStatus = viewModel.anonymousSettingSwitchStatus.collectAsState()
        androidx.compose.ui.window.Dialog(
            onDismissRequest = onDismiss,
            properties = androidx.compose.ui.window.DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            WpTheme {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .clickable(onClick = onDismiss)
                ) {
                    // 底部弹窗内容
                    Column(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                            .clickable(enabled = false) { }
                            .background(
                                Color.White,
                                RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                            )
                    ) {
                        Spacer(modifier = Modifier.height(12.dp))

                        // 标题
                        H4(
                            text = ResUtilKt.getStr(R.string.family_anonymous_setting, LocalContext.current),
                            color = MaterialTheme.colorScheme.onPrimary,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(44.dp)
                                .padding(vertical = 11.dp, horizontal = 24.dp),
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // 充值回馈区域
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 24.dp)
                                .background(Color(0xFFFFF4E8), RoundedCornerShape(6.dp))
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(vertical = 12.dp, horizontal = 10.dp)
                            ) {
                                // 图标
                                Image(
                                    painter = painterResource(id = R.drawable.family_recharge_benefits_tips_icon),
                                    contentDescription = ResUtilKt.getStr(R.string.family_recharge_feedback, LocalContext.current),
                                    modifier = Modifier.size(48.dp)
                                )

                                Spacer(modifier = Modifier.width(12.dp))

                                // 文本内容
                                Column(modifier = Modifier.weight(1f)) {
                                    BasicText(
                                        text = ResUtilKt.getStr(
                                            R.string.family_recharge_feedback,
                                            LocalContext.current
                                        ),
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Bold,
                                        textAlign = TextAlign.Start,
                                        color = MaterialTheme.colorScheme.onPrimary,
                                        lineHeight = 16.sp
                                    )

                                    Spacer(modifier = Modifier.height(4.dp))

                                    BasicText(
                                        ResUtilKt.getStr(
                                            R.string.family_contributed_a_generous_benefit,
                                            LocalContext.current
                                        ).generateAnnotatedColorString(
                                            "%1\$s",
                                            ResUtilKt.getStr(
                                                R.string.family_mysterious_man,
                                                LocalContext.current
                                            ),
                                            Color(0xFF49CCB6)
                                        ),
                                        fontSize = 12.sp,
                                        color = MaterialTheme.colorScheme.onTertiary,
                                        maxLines = 2,
                                        overflow = TextOverflow.Ellipsis,
                                        textAlign = TextAlign.Start
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // 匿名切换选项
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight()
                                .padding(horizontal = 24.dp)
                        ) {
                            H4(
                                text = ResUtilKt.getStr(R.string.family_anonymous_contribution_to_family_welfare, LocalContext.current),
                                color = MaterialTheme.colorScheme.onPrimary,
                                modifier = Modifier.weight(1f),
                                fontWeight = FontWeight.Normal,
                                textAlign = TextAlign.Start
                            )

                            Spacer(modifier = Modifier.width(16.dp))

                            // 开关
                            androidx.compose.material3.Switch(
                                checked = switchStatus.value,
                                onCheckedChange = {
                                    viewModel.updateFamilyAnonymousSetting(it)
                                },
                                colors = androidx.compose.material3.SwitchDefaults.colors(
                                    checkedThumbColor = Color.White,
                                    checkedTrackColor = Color(0xFF49CCB6),
                                    checkedBorderColor = Color.Transparent,
                                    uncheckedThumbColor = Color.White,
                                    uncheckedTrackColor = Color(0xFFCCCCCC),
                                    uncheckedBorderColor = Color.Transparent,
                                ),
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // 底部按钮区域
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 8.dp)
                        ) {
                            // 取消按钮
                            Button(
                                onClick = onDismiss,
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFFF7F8FA)
                                ),
                                shape = RoundedCornerShape(24.dp),
                                modifier = Modifier
                                    .weight(1f)
                                    .height(48.dp)
                            ) {
                                BasicText(
                                    text = ResUtilKt.getStr(R.string.cancel, LocalContext.current),
                                    fontSize = 16.sp,
                                    lineHeight = 22.sp,
                                    color = MaterialTheme.colorScheme.onSecondary,
                                    fontWeight = FontWeight.SemiBold,
                                    textAlign = TextAlign.Center,
                                )
                            }

                            Spacer(modifier = Modifier.width(15.dp))

                            // 保存设置按钮
                            Button(
                                onClick = {
                                    viewModel.saveFamilyAnonymousSetting()
                                    onDismiss()
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF49CCB6)
                                ),
                                shape = RoundedCornerShape(24.dp),
                                modifier = Modifier
                                    .weight(1f)
                                    .height(48.dp)
                            ) {
                                BasicText(
                                    text = ResUtilKt.getStr(R.string.family_save_setting, LocalContext.current),
                                    fontSize = 16.sp,
                                    lineHeight = 22.sp,
                                    fontWeight = FontWeight.SemiBold,
                                    textAlign = TextAlign.Center,
                                    color = Color.White,
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(20.dp))
                    }
                }
            }
        }
    }
}