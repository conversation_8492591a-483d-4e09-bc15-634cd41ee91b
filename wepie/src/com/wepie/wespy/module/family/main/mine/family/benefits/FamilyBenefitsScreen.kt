package com.wepie.wespy.module.family.main.mine.family.benefits

import AwardDialog
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ContextUtilKt
import com.huiwan.base.util.ResUtilKt
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.TimeUtil
import com.huiwan.configservice.ConfigHelper
import com.wejoy.jackaroo.create.wrapForPreView
import com.wejoy.weplay.composewidget.BasicText
import com.wejoy.weplay.composewidget.ColorAccent
import com.wejoy.weplay.composewidget.EmptyView
import com.wejoy.weplay.composewidget.OutlineText
import com.wejoy.weplay.composewidget.WpImage
import com.wejoy.weplay.composewidget.WpPullToRefreshColumn
import com.wejoy.weplay.composewidget.autoMirrored
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.wespy.R
import com.wepie.wespy.helper.BottomTitleWebViewDialog
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.family.main.mine.family.benefits.dialog.AnonymousSettingsBottomSheet
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FamilyBenefitsScreen(
    viewModel: FamilyBenefitsViewModel = viewModel()
) {
    val benefitItems by viewModel.benefitItems.collectAsState()
    val listState = rememberLazyListState()
    val dailyLimit by viewModel.dailyLimit.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    val showAwardDialog by viewModel.showAwardDialog.collectAsState()
    val showAnonymousSettingsDialog by viewModel.showAnonymousSettingsDialog.collectAsState()

    LaunchedEffect(isRefreshing) {
        if (!isRefreshing) {
            listState.scrollToItem(0)
        }
    }

    if (showAwardDialog.showDialog) {
        AwardDialog(
            title = showAwardDialog.title,
            message = showAwardDialog.message,
            buttonText = showAwardDialog.buttonText,
            iconResId = showAwardDialog.iconResId,
            onDismiss = { viewModel.dismissAwardDialog() }
        )
    }

    if (showAnonymousSettingsDialog) {
        AnonymousSettingsBottomSheet(
            show = true,
            onDismiss = { viewModel.dismissAnonymousSettingsDialog() },
        )
    }
    Box(modifier = Modifier.fillMaxSize()) {
        // 顶部背景图
        WpImage(
            model = ResUtilKt.getStr(R.string.family_benefit_top_bg, LocalContext.current),
            modifier = Modifier
                .fillMaxWidth()
                .height(getScaleValue(designValueInDp = 210f).dp)
                .autoMirrored(),
            ImageLoadInfo.newInfo().placeholder(R.drawable.family_benefits_top_bg)
                .error(R.drawable.family_benefits_top_bg),
            alignment = Alignment.TopStart,
            contentDescription = "Top Background",
            contentScale = ContentScale.FillWidth,
        )

        // 顶部区域
        TopHeader(viewModel, onAnonymousSettingsClicked = {
            viewModel.showAnonymousSettingsDialog()
        })

        // 限额和列表卡片
        Card(
            modifier = Modifier.fillMaxWidth().padding(top = getScaleValue(designValueInDp = 190f).dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF5E2D0D)
            ),
            border = BorderStroke(
                1.dp, brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFFE3BA87),
                        Color(0x00E3BA87),
                    )
                )
            ),
            shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp, top = 12.dp, bottom = 12.dp),
            ) {
                // 显示每日限额
                DailyLimitDisplay(dailyLimit.first, dailyLimit.second)

                Spacer(modifier = Modifier.height(8.dp))

                val state = rememberPullToRefreshState()
                WpPullToRefreshColumn(
                    isRefreshing = isRefreshing,
                    onRefresh = {
                        viewModel.refresh()
                    },
                    state = state
                ) {
                    // 福利列表
                    BenefitsList(
                        items = benefitItems,
                        viewModel = viewModel,
                        listState
                    )
                }
            }
        }
    }
}

/**
 * 获取缩放之后的值
 * @param designWidthInDp 设计稿宽度
 * @param designValueInDp 设计稿数值
 * @return 缩放之后的数值
 */
private fun getScaleValue(designWidthInDp: Float = 375f, designValueInDp: Float) : Float {
    return (ScreenUtil.getScreenWidthDp() / designWidthInDp) * designValueInDp
}

/**
 * 顶部标题栏
 */
@Composable
private fun TopHeader(viewModel: FamilyBenefitsViewModel, onAnonymousSettingsClicked: () -> Unit) {
    Column(modifier = Modifier.fillMaxSize()) {
        Spacer(modifier = Modifier.height(getScaleValue(designValueInDp = 44f).dp))

        // 顶部操作view，返回按钮、设置匿名以及查看说明页
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .height(getScaleValue(designValueInDp = 44f).dp)
                .padding(horizontal = 16.dp)
        ) {
            val context = LocalContext.current
            Image(
                painter = painterResource(id = R.drawable.action_bar_back),
                contentDescription = "Back",
                colorFilter = ColorFilter.tint(Color.White),
                modifier = Modifier
                    .size(getScaleValue(designValueInDp = 24f).dp)
                    .clickable {
                        EventDispatcher.postFamilyBenefitRedDotUpdateEvent(
                            viewModel.hasUnClaimedBenefit.value ?: false
                        )
                        ContextUtilKt.getActivityFromContext(context)?.finish()
                    }
            )

            Row {
                Image(
                    painter = painterResource(id = R.drawable.action_bar_anonymous_setting),
                    contentDescription = "Anonymous Setting",
                    modifier = Modifier
                        .size(getScaleValue(designValueInDp = 24f).dp)
                        .clickable {
                            onAnonymousSettingsClicked()
                        }
                )

                Spacer(modifier = Modifier.width(getScaleValue(designValueInDp = 12f).dp))

                Image(
                    painter = painterResource(id = R.drawable.action_bar_icon_help),
                    contentDescription = "Help",
                    colorFilter = ColorFilter.tint(Color.White),
                    modifier = Modifier
                        .size(getScaleValue(designValueInDp = 24f).dp)
                        .clickable {
                            BottomTitleWebViewDialog.show(
                                context,
                                ResUtil.getStr(R.string.help2),
                                ConfigHelper.getInstance().familyConfig.familyWelfareHelpUrl,
                                308,
                                0,
                                true
                            )
                        }
                )
            }
        }

        Spacer(modifier = Modifier.height(getScaleValue(designValueInDp = 5f).dp))

        // 标题和描述
        val colorStops = arrayOf(
            0.0f to Color(0xFFFFD370),
            0.5f to Color(0xFFFFFDCD),
            1f to Color(0xFFFFFDC4)
        )
        val brush = Brush.horizontalGradient(colorStops = colorStops)

        Text(
            text = ResUtilKt.getStr(R.string.family_benefit_title, LocalContext.current),
            style = TextStyle(
                textAlign = TextAlign.Center,
                fontSize = getScaleValue(designValueInDp = 20f).sp,
                fontWeight = FontWeight.ExtraBold,
                brush = brush
            ),
            fontFamily = FontFamily(Font(R.font.tajawal_bold)),
            color = Color(0xFFFFD700),
            modifier = Modifier.padding(start = 20.dp, bottom = getScaleValue(designValueInDp = 8f).dp)
        )

        BasicText(
            text = ResUtilKt.getStr(
                R.string.family_recharge_benefit_illustrate,
                LocalContext.current
            ),
            fontSize = getScaleValue(designValueInDp = 12f).sp,
            lineHeight = getScaleValue(designValueInDp = 18f).sp,
            fontWeight = FontWeight.Normal,
            color = Color.White,
            textAlign = TextAlign.Start,
            modifier = Modifier
                .width(getScaleValue(designValueInDp = 188f).dp)
                .padding(start = 20.dp)
        )

        Spacer(modifier = Modifier.height(getScaleValue(designValueInDp = 11f).dp))
    }
}

/**
 * 每日限额显示
 */
@Composable
fun DailyLimitDisplay(currentLimit: Int = 0, maxLimit: Int) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        BasicText(
            text = ResUtilKt.getStr(R.string.family_daily_benefit_limit, LocalContext.current),
            fontSize = 12.sp,
            fontWeight = FontWeight.Normal,
            textAlign = TextAlign.Center,
            color = Color.White,
        )

        Spacer(modifier = Modifier.width(4.dp))

        BasicText(
            text = "${currentLimit.coerceAtMost(maxLimit)}/${maxLimit}",
            fontSize = 12.sp,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Bold,
            color = Color(0xFFF3D892),
        )
    }
}

/**
 * 福利列表组件
 */
@Composable
fun BenefitsList(
    items: List<FamilyBenefitItem>,
    viewModel: FamilyBenefitsViewModel,
    listState: LazyListState
) {
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .then(if (isRefreshing) Modifier else Modifier),
        state = listState,
        userScrollEnabled = !isRefreshing
    ) {
        if (items.isEmpty()) {
            item {
                EmptyView(
                    icon = R.drawable.base_empty_no_gift,
                    text = ResUtilKt.getStr(
                        R.string.family_no_benefits_to_receive_tip,
                        LocalContext.current
                    ),
                    textColor = Color(0xFFBD987E),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(620.dp)
                )
            }
        } else {
            val unclaimedItems = items.filter { it.status == BenefitStatus.CLAIM.value }
            val claimedItems = items.filter { it.status == BenefitStatus.CLAIMED.value }
            // 未领取的福利项
            items(
                items = unclaimedItems,
                key = { "unclaimed-${it.id}" }
            ) { item ->
                BenefitItemContent(
                    benefitItem = item,
                    viewModel = viewModel,
                    onClaim = { viewModel.claimBenefit(item.id) }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }

            // 已未领取的福利项
            items(
                items = claimedItems,
                key = { "claimed-${it.id}" }
            ) { item ->
                BenefitItemContent(
                    benefitItem = item,
                    viewModel = viewModel,
                    onClaim = { viewModel.claimBenefit(item.id) }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

/**
 * 福利项内容
 */
@Composable
fun BenefitItemContent(
    benefitItem: FamilyBenefitItem,
    viewModel: FamilyBenefitsViewModel,
    onClaim: () -> Unit,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFF4E8)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val benefitsInfo =
                ConfigHelper.getInstance().familyConfig.getWelfareConfigItem(benefitItem.welfareType)
            // 图标
            Box {
                WpImage(
                    model = if (benefitItem.status == BenefitStatus.CLAIM.value) {
                        benefitsInfo.welfare_icon
                    } else {
                        //已领取icon暂时取本地资源
                        R.drawable.family_coin_reward_claimed_icon
                    },
                    contentDescription = null,
                    modifier = Modifier.size(56.dp),
                )

                if (benefitItem.status == BenefitStatus.CLAIMED.value) {
                    BasicText(
                        text = "x${benefitItem.receivedFamilyCoin}",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Normal,
                        lineHeight = 10.sp,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(horizontal = 4.dp, vertical = 2.dp)
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(bottom = 2.dp) // 精确控制距底部2dp
                    ) {
                        BenefitCountdown(
                            benefitItem.id,
                            viewModel, modifier = Modifier
                                .height(14.dp)
                                .align(Alignment.Center)
                                .background(
                                    color = Color(0x33000000),
                                    shape = RoundedCornerShape(3.dp)
                                )
                                .padding(horizontal = 4.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 文本内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                BasicText(
                    text = benefitsInfo.title,
                    color = Color.DarkGray,
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Start,
                )

                Spacer(modifier = Modifier.height(4.dp))

                val context = LocalContext.current
                BasicText(
                    text = benefitsInfo.welfareDesc.generateAnnotatedColorStringWithClick(
                        "{#NickName#}",
                        benefitItem.orderUserName,
                        ColorAccent,
                        onClicked = {
                            if (benefitItem.orderUid > 0) {
                                JumpUtil.enterUserInfoDetailActivity(
                                    context,
                                    benefitItem.orderUid,
                                    TrackSource.FAMILY_BENEFIT_LIST
                                )
                            }
                        }
                    ),
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.Normal,
                    textAlign = TextAlign.Start,
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 按钮
            if (benefitItem.status == BenefitStatus.CLAIMED.value) {
                BasicText(
                    text = ResUtilKt.getStr(R.string.common_clamied, LocalContext.current),
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier.padding(8.dp)
                )
            } else {
                Button(
                    onClick = onClaim,
                    shape = RoundedCornerShape(24.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent
                    ),
                    border = BorderStroke(1.dp, Color(0x80ffffff)),
                    modifier = Modifier
                        .padding(vertical = 9.dp)
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFF108585),
                                    Color(0xFF62EACA),
                                )
                            ), shape = ButtonDefaults.shape
                        )
                        .height(32.dp)
                        .width(80.dp)
                ) {
                    OutlineText(
                        text = ResUtilKt.getStr(R.string.claim, LocalContext.current)
                            .wrapForPreView("Claim"),
                        textStyle = MaterialTheme.typography.bodyLarge.copy(
                            color = Color(0xFFFFFFFF),
                            fontSize = 14.sp,
                            lineHeight = 19.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center,
                        ),
                        strokeWidth = 2.dp,
                        strokeColor = Color(0xFF006F64),
                        fillColor = Color.White,
                    )
                }
            }
        }
    }
}

@Composable
fun BenefitCountdown(
    itemId: Int,
    viewModel: FamilyBenefitsViewModel,
    modifier: Modifier
) {
    val remainingTime by viewModel.getCountdownFor(itemId).collectAsState()
    remainingTime?.let { time ->
        val displayText = remember(time) {
            TimeUtil.formTotalTime((time).toInt())
        }
        BasicText(
            text = displayText,
            color = Color.White,
            fontWeight = FontWeight.Normal,
            fontSize = 10.sp,
            lineHeight = 10.sp,
            modifier = modifier
        )
    }
}

/**
 * Fun：给对应的字符占位符文字添加颜色
 */
fun String.generateAnnotatedColorString(
    placeHolder: String,
    replaceString: String,
    color: Color
): AnnotatedString {
    val placeHolderIndex = this.indexOf(placeHolder)
    if (placeHolderIndex == -1) {
        return buildAnnotatedString { append(this@generateAnnotatedColorString) }
    }
    return buildAnnotatedString {
        append(<EMAIL>(0, placeHolderIndex))
        withStyle(style = SpanStyle(color = color)) {
            append(replaceString)
        }
        append(<EMAIL>(placeHolderIndex + placeHolder.length))
    }
}


/**
 * Fun：给对应的字符占位符文字添加颜色,并且颜色给占位符添加点击事件
 * @param placeHolder 占位符
 * @param replaceString 替换的字符串
 * @param color 占位符的颜色
 * @param onClicked 点击事件回调
 */
fun String.generateAnnotatedColorStringWithClick(
    placeHolder: String,
    replaceString: String,
    color: Color,
    onClicked: () -> Unit
): AnnotatedString {
    val placeHolderIndex = this.indexOf(placeHolder)
    if (placeHolderIndex == -1) {
        return buildAnnotatedString { append(this@generateAnnotatedColorStringWithClick) }
    }
    val styles = TextLinkStyles(style = SpanStyle(color = color))
    return buildAnnotatedString {
        append(<EMAIL>(0, placeHolderIndex))
        withLink(
            link = LinkAnnotation.Clickable(
                tag = "colorTextClicked",
                styles = styles,
                linkInteractionListener = {
                    onClicked.invoke()
                })
        ) {
            append(replaceString)
        }
        append(<EMAIL>(placeHolderIndex + placeHolder.length))
    }
}