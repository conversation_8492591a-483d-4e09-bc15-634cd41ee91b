package com.wepie.wespy.module.family.main.mine.task

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.Utils.dp
import com.huiwan.base.ui.Utils.padding
import com.huiwan.base.util.PressUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.huiwan.widget.image.DrawableUtil
import com.wepie.wespy.R
import com.wepie.wespy.databinding.FamilyNewcomerBenefitTaskViewBinding
import com.wepie.wespy.module.task.AllTaskFinish
import com.wepie.wespy.module.task.DetailTaskInfo
import com.wepie.wespy.module.task.FamilyMeetingTaskInfo
import com.wepie.wespy.module.task.TodayTaskFinish

class FamilyNewcomerBenefitTaskView @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    FrameLayout(context, attr, defStyleAttr) {
    private val binding =
        FamilyNewcomerBenefitTaskViewBinding.inflate(LayoutInflater.from(context), this)
    private val outTopMargin = 12.dp
    private val outBottomMargin = 5.dp

    init {
        layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
        padding(16.dp, outTopMargin, 16.dp, outBottomMargin)
        binding.container.background = DrawableUtil.genGradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(Color.parseColor("#FF9716"), Color.parseColor("#FFD979")), 12
        )
        clipToPadding = false
        binding.newcomerMeetingTaskLay.init()
        binding.taskDetailsContainerRv.let {
            it.layoutManager = LinearLayoutManager(it.context)
            it.addItemDecoration(SpaceItemDecoration(0, 0, 8.dp, 0))
        }
    }


    fun bind(
        taskInfo: FamilyMeetingTaskInfo,
        taskDetailOperate: ITaskDetailOperate,
    ) {
        binding.helpIv.setOnDoubleClick {
            showMeetingBenefitIntroDialog(context, taskInfo.meetingBenefitIntroContent)
        }
        PressUtil.addPressEffect(binding.helpIv)

        binding.newcomerMeetingTaskLay.bind(
            taskInfo.meetingTaskInfo,
            taskDetailOperate::onClaimReward,
            taskDetailOperate::onClickMeetingGiftReward
        )
        binding.dividerTitleTv.text = ResUtil.getStr(
            R.string.family_newcomer_task_summary,
            taskInfo.finishedTaskCount,
            taskInfo.totalTaskCount
        )
        binding.taskDetailsContainerRv.bindAsTaskDetailsContainerRv(
            taskInfo.taskDetails,
            taskDetailOperate
        )
    }
}