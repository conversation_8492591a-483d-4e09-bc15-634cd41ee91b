package com.wepie.wespy.module.family.main.mine.task

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.wepie.wespy.R
import com.wepie.wespy.databinding.FamilyNewcomerBenefitFinishAllViewBinding
import com.wepie.wespy.databinding.FamilyNewcomerTaskDetailItemBinding
import com.wepie.wespy.module.task.AllTaskFinish
import com.wepie.wespy.module.task.DetailTaskInfo
import com.wepie.wespy.module.task.TaskInfo

internal class TaskDetailAdapter(private val itemOperate: ITaskDetailOperate) :
    RecyclerView.Adapter<TaskDetailVh>() {
    private val data: MutableList<TaskInfo> = mutableListOf()
    fun refresh(data: List<TaskInfo>) {
        this.data.clear()
        this.data.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = when (viewType) {
        TaskDetailVh.TaskDetailContentVh.itemViewType -> {
            TaskDetailVh.TaskDetailContentVh(
                FamilyNewcomerTaskDetailItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent, false
                ), itemOperate
            )
        }

        else -> {
            TaskDetailVh.EmptyTaskDetailVh(
                FamilyNewcomerBenefitFinishAllViewBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )
        }
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: TaskDetailVh, position: Int) {
        if (holder is TaskDetailVh.TaskDetailContentVh) {
            holder.bind(data[position])
        } else if (holder is TaskDetailVh.EmptyTaskDetailVh) {
            holder.bind(data[position])
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (data[position] !is DetailTaskInfo) {
            TaskDetailVh.EmptyTaskDetailVh.itemViewType
        } else {
            TaskDetailVh.TaskDetailContentVh.itemViewType
        }
    }

}

internal sealed class TaskDetailVh(itemView: View) : RecyclerView.ViewHolder(itemView) {
    internal class TaskDetailContentVh(
        private val binding: FamilyNewcomerTaskDetailItemBinding,
        private val itemOperate: ITaskDetailOperate
    ) :
        TaskDetailVh(binding.root) {
        fun bind(info: TaskInfo) {
            if (info !is DetailTaskInfo) return
            binding.bind(info, itemOperate)
        }

        companion object {
            const val itemViewType = 0
        }
    }

    internal class EmptyTaskDetailVh(private val binding: FamilyNewcomerBenefitFinishAllViewBinding) :
        TaskDetailVh(binding.root) {
        fun bind(info: TaskInfo) {
            binding.finishAllTv.text = if (info is AllTaskFinish) {
                ResUtil.getStr(R.string.family_task_complete_all)
            } else {
                ResUtil.getStr(R.string.family_newcomer_today_task_all_finish)
            }
        }

        companion object {
            const val itemViewType = 1
        }
    }
}

interface ITaskDetailOperate {
    fun onJumpOtherPage(context: Context, jumpUrl: String)

    /**
     * @param isMeetingGiftTask 是否是见面礼任务的领奖
     */
    fun onClaimReward(task: DetailTaskInfo, isMeetingGiftTask: Boolean)

    /**
     *为 view 添加点击item后展示奖励详情的功能
     */
    fun addOrUpdatePopupRewardDetail(v: View, rewardId: Int)

    /**
     * 点击某个见面礼奖励
     */
    fun onClickMeetingGiftReward(v: View, rewardId: Int)
}