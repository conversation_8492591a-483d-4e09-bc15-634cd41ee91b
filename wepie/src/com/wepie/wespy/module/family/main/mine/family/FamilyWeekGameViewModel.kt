package com.wepie.wespy.module.family.main.mine.family

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.TimeUtil
import com.wejoy.weplay.ex.lifecycle.LifeViewModel
import com.wepie.wespy.model.entity.family.FamilyWeekGameInfo
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class FamilyWeekGameViewModel : LifeViewModel() {
    val weekGameInfoFlow = MutableStateFlow(FamilyWeekGameInfo())
    val weekGameTimeFlow = MutableStateFlow("")
    private var timerJob: Job? = null
    private var isTimerStart = false

    private val eventFlow = MutableSharedFlow<WeekGameEvent>()

    fun updateFamilyWeekGameInfo(info: FamilyWeekGameInfo) {
        viewModelScope.launch {
            weekGameInfoFlow.emit(info)
            isTimerStart = true
            startTimer()
        }
    }

    fun onClick(jumpUrl: String) {
        viewModelScope.launch {
            eventFlow.emit(WeekGameEvent.Click(jumpUrl))
        }
    }

    fun observeEvent(owner: LifecycleOwner, observer: Observer<WeekGameEvent>) {
        owner.lifecycleScope.launch {
            eventFlow.collect {
                observer.onChanged(it)
            }
        }
    }

    private fun formatGameTime(startTime: Long): String {
        val seconds = TimeUtil.getLeftServerSecond(startTime)
        return if (seconds <= 0) {
            ""
        } else {
            TimeUtil.formTotalTime(seconds.toInt())
        }
    }

    fun startTimer() {
        if (!isTimerStart) {
            return
        }
        timerJob?.cancel()
        timerJob = viewModelScope.launch {
            while (true) {
                val info = weekGameInfoFlow.value
                val timeStr = formatGameTime(info.gameTime)
                if (timeStr.isEmpty()) {
                    isTimerStart = false
                    eventFlow.emit(WeekGameEvent.Refresh)
                    break
                }
                weekGameTimeFlow.emit(timeStr)
                delay(1000)
            }
        }
    }

    fun stopTimer() {
        timerJob?.cancel()
        timerJob = null
    }
}

sealed interface WeekGameEvent {
    object Refresh : WeekGameEvent

    class Click(val jumpUrl: String) : WeekGameEvent
}