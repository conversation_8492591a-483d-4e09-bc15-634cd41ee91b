package com.wepie.wespy.module.family.main.mine.family

import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.annotation.ColorInt
import androidx.constraintlayout.widget.ConstraintLayout
import com.huiwan.base.ktx.gone
import com.huiwan.base.ktx.visible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.TimeUtil
import com.huiwan.base.util.setOnDoubleClick
import com.wepie.wespy.R
import com.wepie.wespy.databinding.FamilyDefencePreviewBinding
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog

class FamilyDefencePreviewDialogView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {
    private lateinit var dismissListener: () -> Unit
    private var binding: FamilyDefencePreviewBinding =
        FamilyDefencePreviewBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        initView()
    }

    private fun initView() {
        binding.sureTv.setOnDoubleClick {
            dismissListener.invoke()
        }
    }

    fun setDialogDismissListener(dismissListener: () -> Unit) {
        this.dismissListener = dismissListener
    }

    private fun updateContentView(defenceDisplayInfo: DefenceDisplayInfo) {
        if (defenceDisplayInfo.canShowCurrentDefenceDetailInfo) {
            val currentDefenceNum = defenceDisplayInfo.currentDefenceNum.toString()
            binding.defenceContentTv.text = createColorText(
                currentDefenceNum,
                ResUtil.getStr(R.string.family_defence_leader_tips, currentDefenceNum),
                Color.parseColor("#49CCB6")
            )
            binding.defenceLeftTimeTv.visible()
            binding.defenceLeftTimeTv.text = ResUtil.getStr(
                R.string.expires, TimeUtil.formMouthDayHourMinSecond(
                    (defenceDisplayInfo.leftTime * 1000 - TimeUtil.getServerTime())
                )
            )
        } else {
            binding.defenceContentTv.text = ResUtil.getStr(R.string.family_defence_not_leader_tips)
            binding.defenceLeftTimeTv.gone()
        }
    }

    private fun createColorText(
        desc: String,
        originSource: String,
        @ColorInt color: Int
    ): SpannableString {
        val spannableString = SpannableString(originSource)

        // 找到数量值在字符串中的位置
        val startIndex = originSource.indexOf(desc)
        val endIndex = startIndex + desc.length

        // 设置前景色为绿色
        val colorSpan = ForegroundColorSpan(color)
        spannableString.setSpan(colorSpan, startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        return spannableString
    }

    companion object {
        @JvmStatic
        fun show(
            context: Context,
            canShowCurrentDefenceDetailInfo: Boolean = false,
            currentDefenceNum: Int = 0,
            leftTime: Int = 0
        ) {
            val dialog = BaseFullScreenDialog(context, R.style.dialog_style_custom)
            val familyDefencePreviewDialogView = FamilyDefencePreviewDialogView(
                context = context,
            )
            familyDefencePreviewDialogView.updateContentView(
                DefenceDisplayInfo(
                    canShowCurrentDefenceDetailInfo = canShowCurrentDefenceDetailInfo,
                    currentDefenceNum = currentDefenceNum,
                    leftTime = leftTime
                )
            )
            familyDefencePreviewDialogView.setDialogDismissListener {
                dialog.dismiss()
            }
            dialog.setContentView(familyDefencePreviewDialogView)
            dialog.setCanceledOnTouchOutside(true)
            dialog.initCenterDialog()
            dialog.show()
        }
    }

    data class DefenceDisplayInfo(
        val canShowCurrentDefenceDetailInfo: Boolean = false,
        val currentDefenceNum: Int = 0,
        val leftTime: Int = 0
    )
}