package com.wepie.wespy.module.family.main.mine.task

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.ktx.applyBold
import com.huiwan.base.ktx.dp
import com.huiwan.base.ktx.updateVisible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.PressUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.ConfigHelper
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.huiwan.widget.image.DrawableUtil
import com.wepie.wespy.R
import com.wepie.wespy.databinding.FamilyNewcomerMeetingTaskLayBinding
import com.wepie.wespy.databinding.FamilyNewcomerTaskDetailItemBinding
import com.wepie.wespy.module.task.DetailTaskInfo
import com.wepie.wespy.module.task.TaskInfo
import com.wepie.wespy.module.task.view.TaskRewardItemView

internal fun FamilyNewcomerTaskDetailItemBinding.bind(
    info: DetailTaskInfo,
    itemOperate: ITaskDetailOperate
) {
    val rewardMarginEnd = 8.dp
    info.rewardIds.zip(info.rewardValues).forEachIndexed { i, (rewardId, rewardValue) ->
        genRewardItem(rewardLay.context, i, rewardId, rewardValue)?.let {
            itemOperate.addOrUpdatePopupRewardDetail(it, rewardId)
            rewardLay.addView(it)
            rewardLay.addView(View(rewardLay.context), ViewGroup.LayoutParams(rewardMarginEnd, 1))
        }
    }
    taskTitleTv.text = info.title
    taskDescTv.text = info.desc
    taskDetailStateBtn.bindDetailTaskBtn(
        info,
        itemOperate::onJumpOtherPage,
        itemOperate::onClaimReward
    )
}

private fun genRewardItem(
    context: Context,
    rewardIndex: Int,
    rewardId: Int,
    rewardValue: Int
): View? {
    val propItem = ConfigHelper.getInstance().propConfig.getPropItem(rewardId) ?: return null
    return TaskRewardItemView(context).also {
        it.setReward(propItem.mediaUrl, rewardValue)
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
        )
        layoutParams.marginStart = ScreenUtil.dip2px(55f) * rewardIndex
    }
}

sealed interface TaskRewardStatus {
    object NOT_FINISH : TaskRewardStatus
    object FINISH : TaskRewardStatus
    object RECEIVED_REWARD : TaskRewardStatus

    companion object {
        fun from(rewardStatus: Int): TaskRewardStatus = when (rewardStatus) {
            0 -> NOT_FINISH
            1 -> FINISH
            else -> RECEIVED_REWARD
        }
    }
}

private fun TextView.bindDetailTaskBtn(
    info: DetailTaskInfo,
    onJumpOtherPage: (context: Context, jumpUrl: String) -> Unit,
    onClaimReward: (task: DetailTaskInfo, isMeetingGiftTask: Boolean) -> Unit,
) {
    val (btnBgDrawable, btnText, clickAction, tColor) = when (info.rewardStatus) {
        TaskRewardStatus.NOT_FINISH -> {
            BtnUIState(
                DrawableUtil.genColorRadius(Color.parseColor("#FFA94A"), 16),
                ResUtil.getStr(R.string.go),
                { onJumpOtherPage(context, info.jumpUrl) },
                Color.WHITE
            )
        }

        TaskRewardStatus.FINISH -> {
            BtnUIState(
                DrawableUtil.genColorRadius(Color.parseColor("#FFE821"), 16),
                ResUtil.getStr(R.string.claim),
                { onClaimReward(info, false) },
                Color.parseColor("#8D1D1D")
            )
        }

        TaskRewardStatus.RECEIVED_REWARD -> {
            BtnUIState(
                null,
                ResUtil.getStr(R.string.task_list_item_new_1),
                null,
                ResUtil.getColor(R.color.color_gray_icon_secondary)
            )
        }
    }
    applyBold(info.rewardStatus == TaskRewardStatus.FINISH)
    PressUtil.addPressEffect(this)
    btnBgDrawable?.let {
        setPadding(16.dp, 4.dp, 16.dp, 4.dp)
    } ?: setPadding(0, 0, 0, 0)
    background = btnBgDrawable
    text = btnText
    setTextColor(tColor)
    clickAction?.let {
        isEnabled = true
        setOnDoubleClick {
            it()
        }
    } ?: run {
        isEnabled = false
        setOnClickListener(null)
    }

}

data class BtnUIState(
    val btnBgDrawable: Drawable?,
    val btnText: String,
    val clickAction: (() -> Unit)?,
    val btnColor: Int
)

internal fun FamilyNewcomerMeetingTaskLayBinding.init() {
    newcomerBenefitGiftPreviewRv.let {
        it.layoutManager = LinearLayoutManager(it.context, LinearLayoutManager.HORIZONTAL, false)
        it.addItemDecoration(SpaceItemDecoration(0, 0, 0, 12.dp))
        it.adapter = NewcomerMeetingRewardAdapter()
    }
}

internal fun FamilyNewcomerMeetingTaskLayBinding.bind(
    taskInfo: DetailTaskInfo,
    onClaimReward: (taskId: DetailTaskInfo, isMeetingGiftTask: Boolean) -> Unit,
    onClickMeetingGiftReward: (v: View, rewardId: Int) -> Unit,
) {
    rvFadeBg.post {
        val isLtr = rvFadeBg.layoutDirection == View.LAYOUT_DIRECTION_LTR
        val gradientOrientation = if (isLtr) {
            GradientDrawable.Orientation.LEFT_RIGHT
        } else {
            GradientDrawable.Orientation.RIGHT_LEFT
        }
        rvFadeBg.background = DrawableUtil.genGradientDrawable(
            gradientOrientation,
            intArrayOf(Color.parseColor("#00000000"), Color.parseColor("#FFA830")), 0
        )
        rvFadeBg.updateVisible(
            newcomerBenefitGiftPreviewRv.canScrollHorizontally(if (isLtr) 1 else -1)
        )
    }

    newcomerMeetingRewardStateBtn.bindNewcomerMeetingRewardBtn(taskInfo, onClaimReward)
    (newcomerBenefitGiftPreviewRv.adapter as? NewcomerMeetingRewardAdapter)?.let {
        it.onClickMeetingGiftReward = onClickMeetingGiftReward
        it.refresh(
            taskInfo.rewardIds
                .zip(taskInfo.rewardValues)
                .map { reward -> MeetingReward(reward.first, reward.second) }
        )
    }
    benefitBigTitleTv.text = taskInfo.title
    benefitDescTv.text = taskInfo.desc
}

private fun TextView.bindNewcomerMeetingRewardBtn(
    taskInfo: DetailTaskInfo,
    onClaimReward: (task: DetailTaskInfo, isMeetingGiftTask: Boolean) -> Unit
) {
    val hasFinishAllTask = taskInfo.rewardStatus != TaskRewardStatus.NOT_FINISH
    val (bgDrawable, btnText, clickAction, textColor) = if (hasFinishAllTask) {
        BtnUIState(
            DrawableUtil.genGradientDrawable(
                GradientDrawable.Orientation.BL_TR,
                intArrayOf(Color.parseColor("#FF6263"), Color.parseColor("#FF38D3")), 16
            ),
            ResUtil.getStr(R.string.claim),
            {
                onClaimReward(taskInfo, true)
            },
            Color.WHITE
        )
    } else {
        BtnUIState(
            DrawableUtil.genColorRadius(
                Color.parseColor("#FFF8E8"), 16
            ),
            ResUtil.getStr(R.string.family_task_not_complete),
            null,
            ResUtil.getColor(R.color.color_yellow_default)
        )
    }
    PressUtil.addPressEffect(this)
    background = bgDrawable
    text = btnText
    setTextColor(textColor)
    clickAction?.let {
        isEnabled = true
        setOnDoubleClick {
            it()
        }
    } ?: run {
        isEnabled = false
        setOnClickListener(null)
    }

}

internal fun RecyclerView.bindAsTaskDetailsContainerRv(
    tasks: List<TaskInfo>,
    itemOperate: ITaskDetailOperate
) {
    adapter = TaskDetailAdapter(itemOperate).apply {
        refresh(tasks)
    }
}