package com.wepie.wespy.module.family.main.mine.family;

import static com.huiwan.configservice.international.regoin.IDRegionConfig.ID_REGION_TYPE_FAMILY_ID;
import static com.wepie.lib.api.plugins.track.config.os.TrackButtonName.FAMILY_WEEK_GAME;
import static com.wepie.lib.api.plugins.track.config.os.TrackSource.FAMILY_MAIN;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.lifecycle.ViewModelProvider;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.huiwan.widget.FamilyLightView;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.cancellable.EventBusExKt;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.redDotHelper.RedDotNode;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.model.entity.family.FamilyInfo;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.task.TaskListInfo;
import com.wepie.wespy.module.abtest.AbTestManager;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.family.FamilyBenefitRedDotUpdateEvent;
import com.wepie.wespy.module.family.FamilyLevelIconView;
import com.wepie.wespy.module.family.FamilyLightNameUpdateEvent;
import com.wepie.wespy.module.family.FamilyMainShenceEvent;
import com.wepie.wespy.module.family.FamilyTrackEvent;
import com.wepie.wespy.module.family.detail.FamilyRoleHelper;
import com.wepie.wespy.module.family.dialogs.FamilyCallDialog;
import com.wepie.wespy.module.family.dialogs.FamilyDonateDialog;
import com.wepie.wespy.module.family.main.mine.FamilyBaseFragment;
import com.wepie.wespy.module.family.steal.FamilyStealRedDot;
import com.wepie.wespy.module.game.room.roomlist.InputTextDialog;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.FamilyApi;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Map;

/**
 * Created by bigwen on 2019-10-16.
 */
public class FamilyTabMainView extends FamilyBaseFragment implements View.OnClickListener {

    private SmartRefreshLayout refreshLayout;
    private ImageView chatIv;
    private ImageView voiceRoomIv;
    private ImageView benefitsIv;
    private ImageView lotteryIv;
    private ImageView moreIv;
    private View moreLay;
    private View donateLay;
    private View stealLay;
    private View shopLay;
    private View callLay;
    private TextView stealRedDotTv;
    private DecorHeadImgView avatarIv;
    private TextView nameTv;
    private FamilyLevelIconView levelIv;
    private TextView familyIdTv;
    private TextView weekRankTv;
    private FamilyMainActiveView activeView;
    private FamilyMainBoxView boxView;
    private TextView noteTv;
    private FamilyTabMainPresenter presenter;
    private View editNoteView;
    private View emptySpaceView;
    private FamilyActiveCallback activeCallback;
    private ImageView lotteryRedDotIv;
    private ImageView benefitRedDotIv;
    private ImageView moreRdIv;
    private FamilyLightView familyLightView;

    private FamilyMainInfo familyMainInfo = new FamilyMainInfo();

    private FamilyWeekGameViewModel familyWeekGameViewModel;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.family_tab_main_view, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        refreshLayout = view.findViewById(R.id.refresh_lay);
        chatIv = view.findViewById(R.id.chat_iv);
        voiceRoomIv = view.findViewById(R.id.call_iv);
        benefitsIv = view.findViewById(R.id.benefits_iv);
        lotteryIv = view.findViewById(R.id.lottery_iv);
        moreIv = view.findViewById(R.id.more_iv);
        moreLay = view.findViewById(R.id.more_lay);
        donateLay = view.findViewById(R.id.donate_lay);
        stealLay = view.findViewById(R.id.steal_lay);
        stealRedDotTv = view.findViewById(R.id.steal_red_dot_tv);
        shopLay = view.findViewById(R.id.shop_lay);
        callLay = view.findViewById(R.id.call_lay);
        avatarIv = view.findViewById(R.id.avatar_iv);
        nameTv = view.findViewById(R.id.name_tv);
        levelIv = view.findViewById(R.id.level_iv);
        familyIdTv = view.findViewById(R.id.family_id_tv);
        weekRankTv = view.findViewById(R.id.week_active_rank_tv);
        activeView = view.findViewById(R.id.active_view);
        boxView = view.findViewById(R.id.box_view);
        noteTv = view.findViewById(R.id.note_tv);
        editNoteView = view.findViewById(R.id.edit_note_view);
        emptySpaceView = view.findViewById(R.id.empty_space_view);
        lotteryRedDotIv = view.findViewById(R.id.lottery_red_dot_iv);
        benefitRedDotIv = view.findViewById(R.id.benefit_red_dot_iv);
        moreRdIv = view.findViewById(R.id.more_rd_iv);
        familyLightView = view.findViewById(R.id.family_light_view);
        familyWeekGameViewModel = new ViewModelProvider(requireActivity()).get(FamilyWeekGameViewModel.class);

        presenter = new FamilyTabMainPresenter(this);
        presenter.init();

        moreIv.setOnClickListener(this);
        voiceRoomIv.setOnClickListener(this);
        benefitsIv.setOnClickListener(this);
        lotteryIv.setOnClickListener(this);
        chatIv.setOnClickListener(this);
        donateLay.setOnClickListener(this);
        stealLay.setOnClickListener(this);
        shopLay.setOnClickListener(this);
        callLay.setOnClickListener(this);
        editNoteView.setOnClickListener(this);
        emptySpaceView.setOnClickListener(this);
        weekRankTv.setOnClickListener(this);
        emptySpaceView.setVisibility(View.GONE);
        moreLay.setVisibility(View.GONE);

        refreshLayout.setOnRefreshListener(refreshlayout -> {
            EventDispatcher.postFamilyMainRefreshEvent();
            EventDispatcher.postFamilyMemberRefreshEvent();
        });

        EventBusExKt.registerAutoCancel(EventBus.getDefault(), this, this);

        viewModel.observeEvent(getViewLifecycleOwner(), myFamilyEvent -> updateRedDot());
        if (AbTestManager.getInstance().isOpenFamilyWeekGame()) {
            FamilyWeekGameKt.setContent(view.findViewById(R.id.week_game_compose_view), 12, familyWeekGameViewModel);
            familyWeekGameViewModel.observeEvent(getViewLifecycleOwner(), event -> {
                if (event instanceof WeekGameEvent.Refresh) {
                    EventDispatcher.postFamilyMainRefreshEvent();
                } else if (event instanceof WeekGameEvent.Click) {
                    Map<String, Object> map = new ArrayMap<>();
                    map.put("family_id", familyMainInfo.getFamily().getFamilyId());
                    TrackUtil.appClick(FAMILY_MAIN, FAMILY_WEEK_GAME, map);
                    ApiService.of(WebApi.class).gotoWebActivity(getContext(), ((WeekGameEvent.Click) event).getJumpUrl());
                }
            });
        }
    }

    @Override
    protected void refreshFamilyInfo(@NonNull FamilyMainInfo mainInfo) {
        familyMainInfo = mainInfo;
        familyWeekGameViewModel.updateFamilyWeekGameInfo(mainInfo.getFamily().getWeekGameInfo());
        updateDescPop();

        FamilyInfo familyInfo = mainInfo.getFamily();
        nameTv.setText(familyInfo.getFamilyName());
        levelIv.refresh(mainInfo.getFamily().getLevel());
        avatarIv.updateDecoration(familyInfo.getFamilyAvatarUrl(), familyInfo.getFamilyAvatarDecor());
        familyIdTv.setText("ID：" + IDRegionUtil.INSTANCE.getFinalIDStrByType(familyInfo.getFamilyId(), ID_REGION_TYPE_FAMILY_ID, true));
        if (familyInfo.getWeekRank() <= 0 || familyInfo.getWeekRank() > presenter.getMaxRank()) {
            weekRankTv.setVisibility(View.INVISIBLE);
        } else {
            weekRankTv.setVisibility(View.VISIBLE);
            weekRankTv.setText(ResUtil.getStr(R.string.family_main_activity_family_week_rank_tips, familyInfo.getWeekRank()));
        }
        activeCallback = new FamilyActiveCallback() {
            @Override
            public void upgrade() {
                presenter.upgrade(mainInfo.getFamily());
            }

            @Override
            public void addActive() {
                FamilyMainShenceEvent.appClick(TrackButtonName.FAMILY_ACTIVE);
                FamilyCallDialog.showCallDialog(getContext(), familyMainInfo.getFamily().getTotalActive(), null);
            }

            @Override
            public void clickItem(final TaskListInfo.ActiveStageInfo activeStageInfo) {
                presenter.clickActiveItem(getContext(), mainInfo, activeStageInfo);
            }
        };
        boolean hasUpgradeTips = !TextUtils.isEmpty(presenter.getUpgradeTips(mainInfo.getFamily().getLevel()));
        activeView.refresh(mainInfo, activeCallback, hasUpgradeTips);
        boxView.refresh(mainInfo, new FamilyMainBoxAdaperCallback() {
            @Override
            public void unlock(FamilyBoxInterface familyBoxInterface) {
                presenter.unlock(familyBoxInterface, mainInfo);
            }

            @Override
            public void addCoin(FamilyMainInterface main) {
                FamilyMainShenceEvent.appClick(TrackButtonName.FAMILY_FUND);
                FamilyDonateDialog.showDonateDialog(getContext(), main.getFunds(),
                        main.getTotalActive(), main.getLevelUpActiveScore(), null);
            }

            @Override
            public void reqUpdateBoxes() {
                presenter.reqUpdateFamilyBoxInfo();
            }
        });
        if (TextUtils.isEmpty(familyInfo.getFamilyNote())) {
            noteTv.setText(R.string.family_main_activity_family_notice_none);
        } else {
            noteTv.setText(familyInfo.getFamilyNote());
        }
        if (FamilyRoleHelper.hasPermission(mainInfo.getFamily().getSelfRole(), FamilyRoleHelper.OPERATE_EDIT_NOTE)) {
            editNoteView.setVisibility(View.VISIBLE);
        } else {
            editNoteView.setVisibility(View.INVISIBLE);
        }
        editNoteView.setOnClickListener(v -> InputTextDialog.showEditFamilyNoteDialog(getContext(), mainInfo.getFamily().getFamilyNote(), new InputTextDialog.DialogCallback() {
            @Override
            public void onEnter(String text, final Dialog dialog) {
                FamilyApi.modAnnouncement(text, new LifeDataCallback<Object>(editNoteView) {
                    @Override
                    public void onSuccess(Result<Object> result) {
                        dialog.dismiss();
                        ToastUtil.show(ResUtil.getStr(R.string.common_modify_success));
                        EventDispatcher.postFamilyMainRefreshEvent();
                    }

                    @Override
                    public void onFail(int i, String s) {
                        ToastUtil.show(s);
                    }
                });
            }

            @Override
            public void onCancel(Dialog dialog) {
                dialog.dismiss();
            }
        }));
        refreshStealDot(mainInfo.getMineData().getStealCount());

        chatIv.setOnClickListener(v -> JumpUtil.gotoGroupChatActivity(getContext(), mainInfo.getFamily().getGroupChatId()));
        refreshLayout.finishRefresh(500);
        voiceRoomIv.setVisibility(mainInfo.isHasVoiceRoom() ? View.VISIBLE : View.GONE);
        familyInfo.getFamilyLightInfo().initPropInfo();
        familyLightView.updateFamilyLightView(familyInfo.getFamilyLightInfo(), false);
        refreshBenefitRedDot(familyMainInfo.hasWelfareRedDot(), false);
    }

    public void refreshActive(FamilyMainInfo familyMainInfo) {
        activeView.refreshActive(familyMainInfo, activeCallback);
    }


    public void updateDescPop() {
        viewModel.notifyEvent(boxView.getPopuEvent());
    }

    public void updateRedDot() {
        checkLotteryRedDot();
    }

    @Override
    public void onClick(View v) {
        if (familyMainInfo == null) {
            return;
        }
        if (v == moreIv) {
            if (moreLay.getVisibility() == View.VISIBLE) {
                emptySpaceView.setVisibility(View.GONE);
                moreLay.setVisibility(View.GONE);
            } else {
                emptySpaceView.setVisibility(View.VISIBLE);
                moreLay.setVisibility(View.VISIBLE);
            }
        } else if (v == voiceRoomIv) {
            IDAuthCheckManager.doTaskOrShowNeedCertificate(v.getContext(), AuthApi.SCENE_ENTER_VOICE_ROOM,
                    () -> presenter.reqGotoVoiceRoom(familyMainInfo));
        } else if (v == benefitsIv) {
            JumpUtil.gotoFamilyBenefitsActivity(getContext());
        } else if (v == lotteryIv) {
            JumpUtil.gotoFamilyLotteryActivity(getContext());
        } else if (v == donateLay) {
            FamilyMainShenceEvent.appClick(TrackButtonName.FAMILY_DONATE);
            FamilyInfo info = familyMainInfo.getFamily();
            FamilyDonateDialog.showDonateDialog(getContext(), info.getFunds(), info.getTotalActive(), info.getLevelUpActiveScore(), null);
        } else if (v == shopLay) {
            FamilyMainShenceEvent.appClick(TrackButtonName.FAMILY_SHOP);
            JumpUtil.gotoShopActivityWithIndex(getContext(), PropItem.CATEGORY_FAMILY);
        } else if (v == callLay) {
            FamilyMainShenceEvent.appClick(TrackButtonName.FAMILY_CALL);
            FamilyCallDialog.showCallDialog(v.getContext(), familyMainInfo.getFamily().getTotalActive(), null);
        } else if (v == editNoteView) {
            InputTextDialog.showEditFamilyNoteDialog(getContext(), "", new InputTextDialog.DialogCallback() {
                @Override
                public void onEnter(String text, final Dialog dialog) {
                    FamilyApi.modAnnouncement(text, new LifeDataCallback<Object>(FamilyTabMainView.this) {
                        @Override
                        public void onSuccess(Result<Object> result) {
                            dialog.dismiss();
                            ToastUtil.show(ResUtil.getStr(R.string.common_modify_success));
                            EventDispatcher.postFamilyMainRefreshEvent();
                        }

                        @Override
                        public void onFail(int i, String s) {
                            ToastUtil.show(s);
                        }
                    });
                }

                @Override
                public void onCancel(Dialog dialog) {
                    dialog.dismiss();
                }
            });
        } else if (v == emptySpaceView) {
            moreLay.setVisibility(View.GONE);
            emptySpaceView.setVisibility(View.GONE);
        } else if (v == stealLay) {
//            ToastUtil.debugShow("在refreshStealDot处理");
        } else if (v == weekRankTv) {
            FamilyMainShenceEvent.appClick(TrackButtonName.RANK_WEEK);
            JumpUtil.gotoFamilyRankActivity(getContext());
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        presenter.onTabShow();
        checkLotteryRedDot();
        familyWeekGameViewModel.startTimer();
        FamilyTrackEvent.trackEvent(TrackSource.FAMILY, "");
    }

    @Override
    public void onPause() {
        super.onPause();
        familyWeekGameViewModel.stopTimer();
    }

    public void refreshStealDot(final int num) {
        if (FamilyStealRedDot.hasRedDot(num)) {
            stealRedDotTv.setVisibility(View.VISIBLE);
            if (num > 999) {
                stealRedDotTv.setText("999+");
            } else {
                stealRedDotTv.setText(String.valueOf(num));
            }
            moreRdIv.setVisibility(View.VISIBLE);
        } else {
            stealRedDotTv.setVisibility(View.INVISIBLE);
            moreRdIv.setVisibility(View.GONE);
        }

        stealLay.setOnClickListener(v -> {
            FamilyMainShenceEvent.appClick(TrackButtonName.FAMILY_STEAL);
            FamilyStealRedDot.setNumStealCount(num);
            stealRedDotTv.setVisibility(View.INVISIBLE);//清此时的红点
            moreRdIv.setVisibility(View.GONE);
            JumpUtil.gotoFamilyBoxStealActivity(getContext());
        });
    }

    private void checkLotteryRedDot() {
        if (RedDotUtil.get().hasRedDot(RedDotNode.NODE_D_F_M_WHEEL)) {
            lotteryRedDotIv.setVisibility(View.VISIBLE);
        } else {
            lotteryRedDotIv.setVisibility(View.GONE);
        }
    }

    private void refreshBenefitRedDot(boolean hasWelfare, boolean fromViewModel) {
        if (hasWelfare) {
            benefitRedDotIv.setVisibility(View.VISIBLE);
        } else {
            benefitRedDotIv.setVisibility(View.GONE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateFamilyLightName(FamilyLightNameUpdateEvent event) {
        familyLightView.setFamilyLightName(event.getName());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateFamilyBenefitRedDot(FamilyBenefitRedDotUpdateEvent event) {
        refreshBenefitRedDot(event.getHasBenefitRedDot(), false);
    }
}