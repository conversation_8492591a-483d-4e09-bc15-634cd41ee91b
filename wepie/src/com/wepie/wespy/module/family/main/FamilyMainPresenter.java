package com.wepie.wespy.module.family.main;

import com.huiwan.store.PrefUserUtil;
import com.huiwan.store.PrefUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.event.family.FamilyDissolveEvent;
import com.wepie.wespy.model.event.family.FamilyJoinEvent;
import com.wepie.wespy.model.event.family.FamilyKickedEvent;
import com.wepie.wespy.model.event.family.FamilyMainRefreshEvent;
import com.wepie.wespy.model.event.family.FamilyShieldCountEvent;
import com.wepie.wespy.module.family.FamilyManager;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.module.family.dialogs.FamilyWelcomeDialog;
import com.wepie.wespy.net.http.api.FamilyApi;
import com.huiwan.base.util.ToastUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by bigwen on 2019-10-14.
 */
public class FamilyMainPresenter {

    private FamilyMainActivity activity;
    private boolean hasFamily = false;
    private boolean selfOwner = false;

    public FamilyMainPresenter(FamilyMainActivity activity) {
        this.activity = activity;
    }

    public void init(int defaultIndex) {
        //预加载
        FamilyMainInfo mainInfo = FamilyManager.getInstance().getSelfFamilyInfo();
        handlerFamilyData(mainInfo, defaultIndex);
        refresh(defaultIndex);
    }

    public void refresh(final int defaultIndex) {
        FamilyApi.getInfo(new LifeDataCallback<FamilyMainInfo>(activity) {
            @Override
            public void onSuccess(Result<FamilyMainInfo> result) {
                if (activity.isDestroyed() || activity.isFinishing()) {
                    HLog.d("Family", HLog.USR, "refresh getInfo cancel.");
                    return;
                }
                handlerFamilyData(result.data, defaultIndex);
            }

            @Override
            public void onFail(int i, String s) {
                hasFamily = false;
                ToastUtil.show(s);
            }
        });
    }

    private void handlerFamilyData(FamilyMainInfo familyMainInfo, int defaultIndex) {
        if (familyMainInfo.isHasFamily()) {
            hasFamily = true;
            selfOwner = familyMainInfo.getFamily().getOwner() == LoginHelper.getLoginUid();
            activity.showMyFamily(familyMainInfo, defaultIndex);
            checkShowWelcomeDialog(familyMainInfo);
        } else {
            selfOwner = false;
            hasFamily = false;
            activity.showNotJoin();
        }
    }

    public void register() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    public void unregister() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleRefresh(FamilyMainRefreshEvent event) {
        refresh(-1);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleKicked(FamilyKickedEvent event) {
        if (hasFamily) {
            ToastUtil.show(R.string.group_chat_kick_out_family);
            activity.finishActivity();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleDissolve(FamilyDissolveEvent event) {
        if (hasFamily) {
            if (!selfOwner) {
                ToastUtil.show(R.string.family_disbanded_by_leader);
            }
            activity.finishActivity();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleShieldCountChange(FamilyShieldCountEvent event) {
        FamilyManager.getInstance().updateShieldCount(event.count);
        handlerFamilyData(FamilyManager.getInstance().getSelfFamilyInfo(), -1);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleFamilyJoin(FamilyJoinEvent event) {
        refresh(-1);
    }

    public void checkShowWelcomeDialog(FamilyMainInfo familyMainInfo) {
        boolean hasRecWelcomeGift = familyMainInfo.isReceivedMeetingGift();
        if (hasRecWelcomeGift) {
            return;
        }
        String key = PrefUserUtil.KYE_HAS_SHOW_FAMILY_WELCOME_DIALOG;
        boolean hasShown = PrefUserUtil.getInstance().getBoolean(key, false);
        if (hasShown) {
            return;
        }
        FamilyWelcomeDialog.show(activity, FamilyManager.getInstance().getSelfFamilyInfo().getFamily());
    }
}
