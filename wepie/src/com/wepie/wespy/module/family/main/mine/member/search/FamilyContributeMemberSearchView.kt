package com.wepie.wespy.module.family.main.mine.member.search

import android.content.Context
import android.util.AttributeSet
import com.huiwan.base.ktx.visible
import com.huiwan.base.str.ResUtil
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import com.wepie.wespy.model.entity.family.FamilyMemberInfo
import com.wepie.wespy.model.entity.family.FamilyMemberLimit
import com.wepie.wespy.module.family.main.mine.member.FamilyMemberConst
import com.wepie.wespy.module.family.main.mine.member.TabType

/**
 * 贡献成员搜索视图
 * 继承基础搜索视图，实现贡献排序相关功能
 *
 * date 2023-07-18
 * Author: zcs
 */
class FamilyContributeMemberSearchView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : FamilyBaseMemberSearchView(context, attrs, defStyleAttr, defStyleRes) {

    override fun getTabType(): TabType {
        return TabType.CONTRIBUTION
    }

    override fun getTabTitle(): String {
        return ResUtil.getStr(R.string.family_rank_coin)
    }

    /**
     * 更新搜索视图
     */
    fun update(mode: Int, selfRole: Int, sortType: Int, limit: FamilyMemberLimit, memberList: List<FamilyMemberInfo>) {
        // 清空现有数据
        members.clear()
        updateAdapterInfo(selfRole = selfRole, mode = mode, limit = limit, sortType = sortType)
        // 调用父类方法更新成员列表
        super.update(memberList, selfRole, mode)

        // 根据排序类型设置不同的标题
        updateTitle(sortType)
    }

    private fun updateAdapterInfo(
        selfRole: Int,
        mode: Int,
        limit: FamilyMemberLimit,
        sortType: Int
    ) {
        adapter.updateSelfRole(selfRole)
        adapter.updateManagerMode(mode)
        adapter.updateLimit(limit)
        adapter.updateSortType(sortType)
    }

    private fun updateTitle(sortType: Int) {
        when (sortType) {
            FamilyMemberConst.SORT_TYPE_COIN -> {
                binding.rankTypeTv.text = ResUtil.getStr(R.string.family_rank_coin)
                binding.weekActiveTv.text = ResUtil.getStr(R.string.family_week_donate)
                binding.totalActiveTv.text = ResUtil.getStr(R.string.family_total_donate)
            }

            FamilyMemberConst.SORT_TYPE_ACTIVE -> {
                binding.rankTypeTv.text = ResUtil.getStr(R.string.family_rank_activity)
                binding.weekActiveTv.text = ResUtil.getStr(R.string.family_week_contribute)
                binding.totalActiveTv.text = ResUtil.getStr(R.string.family_total_contribute)
            }
        }
    }

    override fun updateTitleTips() {
        binding.weekActiveTv.visible()
        binding.totalActiveTv.visible()
    }

    companion object {

        @JvmStatic
        fun showAsDialog(
            context: Context?,
            limit: FamilyMemberLimit,
            memberList: List<FamilyMemberInfo>,
            mode: Int,
            selfRole: Int,
            sortType: Int
        ) {
            if (context == null) return

            val searchView = FamilyContributeMemberSearchView(context)
            val dialog = BaseDialogFragment()
            dialog.contentView = searchView
            dialog.initBottom()
            dialog.initMatchParent()
            dialog.setWindowAnim(R.style.search_dialog_anim)
            dialog.show(context, FamilyContributeMemberSearchView::class.java.simpleName)

            searchView.update(mode, selfRole, sortType, limit, memberList)
            searchView.hideListener = object : HideListener {
                override fun onHide() {
                    dialog.dismissAllowingStateLoss()
                }
            }
        }
    }
}