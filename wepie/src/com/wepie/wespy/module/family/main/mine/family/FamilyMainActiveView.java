package com.wepie.wespy.module.family.main.mine.family;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.IFunctionShieldApi;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.task.TaskListInfo;
import com.wepie.wespy.module.family.detail.FamilyRoleHelper;
import com.wepie.wespy.net.http.api.TaskApi;

import java.util.List;

/**
 * Created by bigwen on 2019-10-16.
 */
public class FamilyMainActiveView extends ConstraintLayout {

    private final Context mContext;
    private TextView activeTv;
    private ImageView addActiveIv;
    private TextView weekActiveTv;
    private final FamilyActiveItem[] activeGiftArray = new FamilyActiveItem[5];
    private FamilyActiveValueView activeValueView;
    private TextView upgradeBtn;
    private ImageView activeInfoIv;
    private List<TaskListInfo.ActiveStageInfo> activeDataList;
    private FamilyActiveCallback activeCallback;
    private boolean canOpen = false;

    public FamilyMainActiveView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public FamilyMainActiveView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.family_main_active_view, this);
        activeTv = findViewById(R.id.member_num_tv);
        addActiveIv = findViewById(R.id.add_active_iv);
        weekActiveTv = findViewById(R.id.week_active_tv);
        TextView weekActiveTipsTv = findViewById(R.id.week_active_tips_tv);
        activeValueView = findViewById(R.id.active_value_view);
        upgradeBtn = findViewById(R.id.upgrade_tv);
        activeInfoIv = findViewById(R.id.family_active_info_iv);
        activeGiftArray[0] = new FamilyActiveItem(findViewById(R.id.active_item1));
        activeGiftArray[1] = new FamilyActiveItem(findViewById(R.id.active_item2));
        activeGiftArray[2] = new FamilyActiveItem(findViewById(R.id.active_item3));
        activeGiftArray[3] = new FamilyActiveItem(findViewById(R.id.active_item4));
        activeGiftArray[4] = new FamilyActiveItem(findViewById(R.id.active_item5));

        weekActiveTipsTv.setText(ApiService.of(IFunctionShieldApi.class).getFamilyActiveRefreshTip());
    }

    @SuppressLint("SetTextI18n")
    public void refresh(final FamilyMainInfo data, final FamilyActiveCallback callback, final boolean hasUpgradeTips) {
        activeTv.setText(data.getFamily().getTotalActive() + "/" + data.getFamily().getLevelUpActiveScore());
        weekActiveTv.setText("" + data.getFamily().getWeekActive());
        activeValueView.refresh((data.getFamily().getTotalActive() - data.getPrevActiveLimit()) * 1f / (data.getFamily().getLevelUpActiveScore() - data.getPrevActiveLimit()));
        if (data.getFamily().getTotalActive() >= data.getFamily().getLevelUpActiveScore()) {
            if (hasUpgradeTips) {//服务器有配置升级文案，才显示按钮，否则说明已经到达顶级
                if (FamilyRoleHelper.hasPermission(data.getFamily().getSelfRole(), FamilyRoleHelper.OPERATE_UPGRADE)) {
                    upgradeBtn.setVisibility(VISIBLE);
                    upgradeBtn.setOnClickListener(v -> {
                        if (callback != null) callback.upgrade();
                    });
                } else {
                    upgradeBtn.setVisibility(VISIBLE);
                    upgradeBtn.setOnClickListener(v -> ToastUtil.show(R.string.no_permission_upgrade));
                }
                addActiveIv.setVisibility(GONE);
            } else {
                upgradeBtn.setVisibility(GONE);
                addActiveIv.setVisibility(VISIBLE);
            }
        } else {
            upgradeBtn.setVisibility(GONE);
            addActiveIv.setVisibility(VISIBLE);
        }

        addActiveIv.setOnClickListener(v -> {
            if (callback != null) callback.addActive();
        });
        activeInfoIv.setOnClickListener(v -> {
            View view = LayoutInflater.from(mContext).inflate(R.layout.family_active_info_introduce_view, null);
            TextView tipsTv = view.findViewById(R.id.active_tips_tv);
            SpannableStringBuilder stringBuilder = new SpannableStringBuilder(ResUtil.getStr(R.string.family_active_introduce, ResUtil.getStr(R.string.family_active_introduce_special)));
            int start = stringBuilder.toString().indexOf(ResUtil.getStr(R.string.family_active_introduce_special));
            int end = start + ResUtil.getStr(R.string.family_active_introduce_special).length();
            stringBuilder.setSpan(new ForegroundColorSpan(0xffffb319), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            tipsTv.setText(stringBuilder);
            PopupWindow popupWindow = new PopupWindow(view, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            popupWindow.setOutsideTouchable(true);
            int[] location = new int[2];
            activeInfoIv.getLocationOnScreen(location);
            int x = location[0] - ScreenUtil.dip2px(80);
            if (ScreenUtil.isRtl()) {
                x = ScreenUtil.getScreenWidth(mContext) - x;
            }
            popupWindow.showAtLocation(activeInfoIv, Gravity.NO_GRAVITY, x, location[1] - ScreenUtil.dip2px(66));
        });
        //刷新活跃值
        refreshActive(data, callback);
    }

    public void refreshActive(final FamilyMainInfo data, final FamilyActiveCallback callback) {
        TaskApi.getTaskListNew(TaskListInfo.FAMILY_ACTIVE_TASK, new LifeDataCallback<TaskListInfo>(ViewExKt.toLife(this)) {
            @Override
            public void onSuccess(Result<TaskListInfo> result) {
                if (!FamilyMainActiveView.this.isAttachedToWindow()) {
                    HLog.d("Family", HLog.USR, "refreshActive getTaskListNew cancel.");
                    return;
                }
                refreshActiveItem(result.data.activeStageInfos, FamilyRoleHelper.hasPermission(data.getMyRole(), FamilyRoleHelper.OPERATE_CLICK_ACTIVE_REWARD), callback);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    private void refreshActiveItem(List<TaskListInfo.ActiveStageInfo> dataList, boolean canOpen, final FamilyActiveCallback callback) {
        activeDataList = dataList;
        activeCallback = callback;
        this.canOpen = canOpen;
        for (int i = 0; i < activeGiftArray.length; i++) {
            FamilyActiveItem activeGift = activeGiftArray[i];
            if (i < dataList.size()) {
                activeGiftArray[i].getRootView().setVisibility(VISIBLE);
                final TaskListInfo.ActiveStageInfo activeStageInfo = dataList.get(i);
                activeGift.refresh(activeStageInfo, i, canOpen, true, v -> {
                    if (callback != null) callback.clickItem(activeStageInfo);
                });
            } else {
                activeGiftArray[i].getRootView().setVisibility(GONE);
            }
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (activeDataList != null) {//pagerview中被移除，恢复动画
            refreshActiveItem(activeDataList, canOpen, activeCallback);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        clearAnim();
    }

    private void clearAnim() {
        for (FamilyActiveItem familyActiveItem : activeGiftArray) {
            familyActiveItem.clearAnim();
        }
    }

}