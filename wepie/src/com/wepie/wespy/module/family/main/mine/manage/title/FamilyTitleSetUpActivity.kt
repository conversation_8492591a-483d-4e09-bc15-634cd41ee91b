package com.wepie.wespy.module.family.main.mine.manage.title

import android.content.Context
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.dialog.HWUIDialogBuilder
import com.huiwan.base.ui.dialog.HWUIDialogBuilder.Companion.show
import com.huiwan.base.util.ResUtilKt
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.decorate.compose.DecorHeadImage
import com.huiwan.decorate.compose.HeadDecorInfo
import com.huiwan.user.entity.User.GENDER_DEFAULT
import com.wejoy.weplay.composewidget.BasicText
import com.wejoy.weplay.composewidget.Body1
import com.wejoy.weplay.composewidget.ColorAccent
import com.wejoy.weplay.composewidget.EmptyView
import com.wejoy.weplay.composewidget.TitleLabelEndColor
import com.wejoy.weplay.composewidget.TitleLabelStartColor
import com.wejoy.weplay.composewidget.UserNameColorsConfig
import com.wejoy.weplay.composewidget.UserNameText
import com.wejoy.weplay.composewidget.WpImage
import com.wejoy.weplay.composewidget.noRippleClickable
import com.wejoy.weplay.composewidget.rtlReverse
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.wespy.R
import com.wepie.wespy.base.composeui.WpTheme
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.family.FamilyManager
import com.wepie.wespy.module.family.FamilyUiConst
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface.ROLE_OWNER
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface.ROLE_SUB_OWNER
import com.wepie.wespy.module.family.main.mine.manage.title.FamilyTitleSetUpViewModel.TitleSetUpInfo

class FamilyTitleSetUpActivity : BaseActivity() {
    val vm: FamilyTitleSetUpViewModel by viewModels<FamilyTitleSetUpViewModel>()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val hid = intent.getIntExtra(KEY_HID, -1)
        if (hid < 0) {
            finish()
            return
        }
        setContent {
            val state: FamilyTitleSetUpViewModel.TitleSetUpState by vm.state.collectAsState()
            WpTheme {
                Column {
                    FamilyTitleSetUp(state, hid)
                    BodyContent(state, onRemoveBack = { item, totalNum ->
                        vm.onRemoveTitleChanged(item, totalNum)
                    })
                }
            }
        }
        vm.init(hid)
    }

    companion object {
        const val KEY_HID = "_key_hid"
    }
}

@Composable
fun ActionBarComponent(
    title: String,
    onBackClick: () -> Unit = {},
    rightText: String? = null,
    onRightTextClick: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .noRippleClickable { onBackClick() }
                .padding(end = 16.dp)
                .width(42.dp)
                .wrapContentHeight()
                .wrapContentHeight()
                .padding(horizontal = 16.dp)
        ) {
            Image(
                painter = painterResource(R.drawable.action_bar_back),
                contentDescription = "",
                modifier = Modifier
                    .width(16.dp)
                    .height(16.dp)
            )
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxHeight()
                .weight(1f)
        ) {
            BasicText(
                text = title,
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 20.sp,
                fontWeight = FontWeight.Normal,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        if (!rightText.isNullOrBlank()) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxHeight()
                    .padding(end = 16.dp)
                    .noRippleClickable { onRightTextClick() }
            ) {
                BasicText(
                    text = rightText,
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onSecondary,
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier
                        .padding(bottom = 1.dp)
                )
            }
        }
    }
}

private fun showDialog(context: Context, hid: Int, totalNum: Int) {
    FamilyTitleDialog.show(context, hid, totalNum)
}


@Composable
private fun FamilyTitleSetUp(state: FamilyTitleSetUpViewModel.TitleSetUpState, hid: Int) {
    val context = LocalContext.current
    val setUpTitle = ResUtilKt.getStr(R.string.family_title_setting, context)
    val title = remember(state) {
        when (state) {
            is FamilyTitleSetUpViewModel.TitleSetUpState.Loading,
            is FamilyTitleSetUpViewModel.TitleSetUpState.Failed -> setUpTitle

            is FamilyTitleSetUpViewModel.TitleSetUpState.Success ->
                "$setUpTitle (${state.titleSetUpInfo.user.size}/${state.titleSetUpInfo.totalNum})"

            is FamilyTitleSetUpViewModel.TitleSetUpState.Empty ->
                "$setUpTitle (${state.titleSetUpInfo.user.size}/${state.titleSetUpInfo.totalNum})"
        }
    }
    ActionBarComponent(
        title = title,
        rightText = ResUtilKt.getStr(R.string.new_friends_add, context),
        onRightTextClick = {
            val setUpState =
                if (state is FamilyTitleSetUpViewModel.TitleSetUpState.Success) {
                    state.titleSetUpInfo
                } else if (state is FamilyTitleSetUpViewModel.TitleSetUpState.Empty) {
                    state.titleSetUpInfo
                } else {
                    return@ActionBarComponent
                }
            if (setUpState.user.size == setUpState.totalNum) {
                ToastUtil.show(
                    ResUtil.getStr(
                        R.string.family_title_setting_limit,
                        setUpState.titleName
                    )
                )
            } else {
                showDialog(
                    context,
                    hid,
                    setUpState.totalNum
                )
            }
        },
        onBackClick = {
            if (context is FamilyTitleSetUpActivity) {
                context.finish()
            }
        }

    )
}

@Composable
private fun BodyContent(
    state: FamilyTitleSetUpViewModel.TitleSetUpState,
    onRemoveBack: (FamilyTitleSetupItem, Int) -> Unit
) {
    when (state) {
        FamilyTitleSetUpViewModel.TitleSetUpState.Loading -> {
            BodyLoading()
        }

        is FamilyTitleSetUpViewModel.TitleSetUpState.Success -> {
            BodySuccess(state.titleSetUpInfo, onRemoveBack)
        }

        is FamilyTitleSetUpViewModel.TitleSetUpState.Failed -> {
            BodyFailed()
        }

        is FamilyTitleSetUpViewModel.TitleSetUpState.Empty -> {
            BodyEmpty(state.titleSetUpInfo)
        }
    }
}

@Composable
private fun BodyLoading() {

}

@Composable
private fun BodyEmpty(data: TitleSetUpInfo) {
    val context = LocalContext.current
    EmptyView(
        icon = R.drawable.base_empty_no_data,
        text = ResUtil.getStr(
            R.string.family_title_member_not_set_tip,
            data.titleName
        ),
        buttonText = ResUtil.getStr(R.string.new_friends_add),
        buttonClick = {
            showDialog(
                context = context,
                data.hid,
                data.totalNum
            )
        },
        modifier = Modifier.fillMaxSize()
    )
}

@Composable
private fun BodyFailed() {

}

@Composable
private fun BodySuccess(data: TitleSetUpInfo, onRemoveBack: (FamilyTitleSetupItem, Int) -> Unit) {
    val context = LocalContext.current
    LazyColumn {
        itemsIndexed(data.user) { _, item ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .height(80.dp)
                    .padding(
                        start = 12.dp,
                        end = 16.dp
                    )
            ) {
                TitleBody(item, true)
                Spacer(
                    modifier = Modifier
                        .fillMaxHeight()
                        .weight(1f)
                )
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .wrapContentWidth()
                        .border(
                            width = 1.dp,
                            shape = RoundedCornerShape(16.dp),
                            color = ColorAccent
                        )
                        .clip(RoundedCornerShape(16.dp))
                        .defaultMinSize(minWidth = 60.dp, minHeight = 32.dp)
                        .noRippleClickable {
                            if (data.selfRole == ROLE_SUB_OWNER && item.role == ROLE_OWNER) {
                                ToastUtil.show(R.string.family_leader_title_remove_not_allowed)
                            } else {
                                showRemoveConfirmDialog(
                                    context,
                                    onRemoveBack,
                                    item,
                                    data.totalNum
                                )
                            }
                        }
                ) {
                    Body1(
                        text = ResUtilKt.getStr(R.string.family_title_setting_remove, context),
                        color = ColorAccent,
                        fontWeight = FontWeight.Normal
                    )
                }
            }
        }
    }
}

@Composable
fun TitleBody(item: FamilyTitleSetupItem, isShowUserProfile: Boolean) {
    val context = LocalContext.current
    Box(
        modifier = Modifier.noRippleClickable {
            if (isShowUserProfile) {
                goToUserProfile(context, item.user.uid)
            }
        }
    ) {
        DecorHeadImage(
            HeadDecorInfo.Builder {
                uid = item.user.uid
                size(48.dp)
                mustShowDecor = true
                headCornerSize = 24.dp
            }.build()
        )
    }
    Spacer(
        modifier = Modifier.width(8.dp)
    )
    Column(
        verticalArrangement = Arrangement.Center,
        modifier = Modifier
            .fillMaxHeight()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            modifier = Modifier
                .height(24.dp)
                .wrapContentWidth()
        ) {
            UserNameText(
                item.user.userSimpleInfo?.nickname ?: "",
                fontSize = if (isShowUserProfile) 16.sp else 14.sp,
                colors = item.user.userSimpleInfo?.getVipColor(false)
                    ?: UserNameColorsConfig()
            )
            if (item.user.userSimpleInfo?.gender != GENDER_DEFAULT) {
                WpImage(
                    model = if (item.user.userSimpleInfo?.isMale == true) {
                        R.drawable.wejoy_home_friend_list_male
                    } else {
                        R.drawable.wejoy_home_friend_list_female
                    },
                    contentDescription = "",
                    modifier = Modifier
                        .width(14.dp)
                        .height(14.dp)
                )

            }
        }
        Spacer(modifier = Modifier.height(4.dp))
        UserTagLabel(item)
    }
}

@Composable
private fun UserTagLabel(item: FamilyTitleSetupItem) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        val iconRes = FamilyUiConst.getRoleBg(item.role)
        if (iconRes > 0) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .wrapContentWidth()
                    .height(14.dp)
                    .clip(RoundedCornerShape(4.dp))
            ) {
                WpImage(
                    model = iconRes, contentScale = ContentScale.FillHeight,
                    modifier = Modifier
                        .matchParentSize()
                        .widthIn(max = 80.dp)
                )
                BasicText(
                    text = FamilyUiConst.getRoleStr(item.role),
                    fontSize = 10.sp,
                    color = Color.White,
                    fontWeight = FontWeight.Normal,
                    lineHeight = 12.sp,
                    modifier = Modifier.padding(horizontal = 3.dp)
                )
            }
        }
        if (item.titleName.isNotEmpty()) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .height(14.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(TitleLabelStartColor, TitleLabelEndColor).rtlReverse()
                        ),
                        shape = RoundedCornerShape(4.dp)
                    )
                    .widthIn(max = 80.dp)
                    .padding(horizontal = 3.dp)

            ) {
                BasicText(
                    text = item.titleName,
                    fontSize = 10.sp,
                    color = Color(0xFF282D45),
                    fontWeight = FontWeight.Normal,
                    maxLines = 1,
                    lineHeight = 10.sp,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
    }
}

fun goToUserProfile(
    context: Context,
    uid: Int
) {
    val info = FamilyManager.getInstance().selfFamilyInfo
    if (info != null && info.family.groupChatId > 0) {
        JumpUtil.enterUserInfoDetailActivityFromGroup(
            context,
            uid,
            info.family.groupChatId
        )
    } else {
        JumpUtil.enterUserInfoDetailActivity(
            context,
            uid,
            TrackSource.FAMILY
        )
    }
}

private fun showRemoveConfirmDialog(
    context: Context,
    onRemoveBack: (FamilyTitleSetupItem, Int) -> Unit,
    item: FamilyTitleSetupItem,
    totalNum: Int
) {
    val name = item.user.userSimpleInfo?.nickname ?: ""
    val content = ResUtil.getStr(
        R.string.family_title_dialog_withdraw, name
    )
    val start: Int = content.indexOf(name)
    val end: Int = start + name.length
    val spannableString = SpannableString(content)
    spannableString.setSpan(
        ForegroundColorSpan(-0xb6334a),
        start,
        end,
        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    val dialog = HWUIDialogBuilder.newBuilder(context).show {
        title(spannableString)
        setCanCancel(false)
        negativeButton(com.weplay.competition.R.string.cancel, onClick = {
            onDismiss()
        })
        positiveButton(com.weplay.competition.R.string.confirm, onClick = {
            onRemoveBack(item, totalNum)
        })
    }
}