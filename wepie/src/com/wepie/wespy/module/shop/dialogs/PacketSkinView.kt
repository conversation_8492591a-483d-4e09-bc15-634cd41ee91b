package com.wepie.wespy.module.shop.dialogs

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.widget.FrameLayout
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.constentity.propextra.RedPacketSkinExtra
import com.huiwan.configservice.model.PropItem
import com.huiwan.user.LoginHelper
import com.wejoy.weplay.ex.ILifeRegistry
import com.wejoy.weplay.helper.life.base.LifeRegister
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.redpacket.RedPacketInfo
import com.wepie.wespy.module.redpacket.OpenRedPacketView


/**
 * Created by lsy on 2022/9/2.
 */
class PacketSkinView : FrameLayout {

    constructor(context: Context) : super(context)

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet)

    constructor(
        context: Context,
        attributeSet: AttributeSet,
        defStyleAttr: Int
    ) : super(context, attributeSet, defStyleAttr)

    private val leftMargin = ScreenUtil.dip2px(10f)
    private val rightMargin = ScreenUtil.dip2px(10f)
    private val centerMargin = ScreenUtil.dip2px(7f)
    private val topMargin = ScreenUtil.dip2px(32f)
    private val bottomMargin = ScreenUtil.dip2px(12f)
    private val scare = 128.0f / 227.0f
    private val life by lazy {
        LifeRegister()
    }
    private val helper by lazy {
        PreviewRainHelper(context, life, ImageLoadInfo().width(100).height(100))
    }

    private val openView by lazy {
        OpenRedPacketView(context).apply {
            layoutParams = LayoutParams(MATCH_PARENT, MATCH_PARENT)
            setBackgroundResource(R.drawable.shape_f4f4f4_corner6)
        }
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        addView(openView)
        addView(helper.previewVoiceBg)
        addView(helper.svgaImage)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val size = MeasureSpec.getSize(widthMeasureSpec)
        val itemWidth = (size - leftMargin - rightMargin - centerMargin) / 2
        val itemHeight = itemWidth / scare
        for (i in 0 until childCount) {
            val childAt = getChildAt(i)
            measureChildWithMargins(
                childAt,
                MeasureSpec.makeMeasureSpec(itemWidth, MeasureSpec.EXACTLY), 0,
                MeasureSpec.makeMeasureSpec(itemHeight.toInt(), MeasureSpec.EXACTLY), 0
            )
        }
        setMeasuredDimension(
            widthMeasureSpec,
            MeasureSpec.makeMeasureSpec(
                (itemHeight + topMargin + bottomMargin).toInt(),
                MeasureSpec.EXACTLY
            )
        )
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        if (ScreenUtil.isRtl()) {
            var r = right - leftMargin
            for (i in 0 until childCount) {
                val child = getChildAt(i)
                child.layout(
                    r - child.measuredWidth, topMargin, r,
                    topMargin + child.measuredHeight
                )
                if (i == 0) {
                    r -= child.measuredWidth + centerMargin;
                }
            }
        } else {
            var l = leftMargin
            for (i in 0 until childCount) {
                val child = getChildAt(i)
                child.layout(
                    l, topMargin, l + child.measuredWidth,
                    topMargin + child.measuredHeight
                )
                if (i == 0) {
                    l += child.measuredWidth + centerMargin;
                }
            }
        }
    }

    fun update(extra: RedPacketSkinExtra, propItem: PropItem) {
        life.destroy()
        life.register(object : ILifeRegistry() {
            override fun onDestroy() {
                helper.svgaImage.stopAnimation()
            }
        })
        openView.update(
            RedPacketInfo().setUid(LoginHelper.getLoginUid()).setPacketSkinId(propItem.itemId),
            0,
            OpenRedPacketView.TYPE_PREVIEW_SMALL
        )
        helper.downloadImg(extra)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        life.destroy()
    }
}