package com.wepie.wespy.module.redpacket.detail;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.ktx.ViewExtKt;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.RedPacketSkinExtra;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.user.LoginHelper;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.redpacket.RPReceiveInfo;
import com.wepie.wespy.model.entity.redpacket.RedPacketInfo;
import com.wepie.wespy.model.entity.redpacket.RpReceiveResult;
import com.wepie.wespy.module.redpacket.detail.view.PacketDetailCoinViewHolder;
import com.wepie.wespy.module.redpacket.detail.view.PacketDetailNameViewHolder;
import com.wepie.wespy.module.redpacket.detail.view.PacketDetailTipViewHolder;
import com.wepie.wespy.module.redpacket.detail.view.PacketRecItemViewHolder;
import com.wepie.wespy.module.redpacket.detail.view.scroll.PacketRecyclerView;
import com.wepie.wespy.module.redpacket.detail.view.scroll.PacketSkinLayout;
import com.wepie.wespy.net.http.api.RedPacketApi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * Created by three on 15/10/14.
 */
public class RedPacketDetailView extends LinearLayout {
    private final Context mContext;
    private PacketRecyclerView recyclerView;
    private PacketSkinLayout skinLayout;
    private PacketDetailAdapter adapter;
    private RedPacketInfo mRedPacketInfo;
    private ArrayList<RPReceiveInfo> mReceiveInfos = new ArrayList<>();
    private int grabCoin = -1, totalGrabCoin = -1;
    private ArrayList<Integer> luckyArray = new ArrayList<>();
    private String tips;

    public RedPacketDetailView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public RedPacketDetailView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.red_packet_detail_view, this);
        recyclerView = findViewById(R.id.packet_detail_list);
        skinLayout = findViewById(R.id.skin_ly);
        View backIv = findViewById(R.id.back_iv);
        backIv.setOnClickListener(v -> eventCallback.onClickClose());
        ViewExtKt.setMarginTopPx(backIv, ScreenUtil.getStatusBarHeight());
        initList();
    }

    private void initList() {
        adapter = new PacketDetailAdapter();
        recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        recyclerView.setAdapter(adapter);
    }

    public void update(String rpId, final boolean isFromChat, int rpSkinId) {
        if (TextUtils.isEmpty(rpId)) {
            return;
        }
        PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(rpSkinId);
        if (propItem != null) {
            RedPacketSkinExtra redPacketSkinExtra = propItem.getRedPacketSkinExtra();
            if (redPacketSkinExtra != null) {
                skinLayout.setSkinUrl(redPacketSkinExtra.detail_head_pic);
            }
        }
        int uid = LoginHelper.getLoginUid();
        RedPacketApi.getRedPacketInfo(rpId, uid, new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<RpReceiveResult> result) {
                mRedPacketInfo = result.data.getRedPacketInfo();
                mReceiveInfos = result.data.getReceiveInfoList();
                Collections.sort(mReceiveInfos, (o1, o2) -> Long.compare(o2.time, o1.time));
                for (RPReceiveInfo receiveInfo : mReceiveInfos) {
                    if (receiveInfo.uid == uid) {
                        grabCoin = receiveInfo.coin;
                        break;
                    }
                }
                initLuckyArray();
                List<PacketDetailItem> list = new ArrayList<>();
                list.add(new PacketDetailItem(PacketDetailAdapter.TYPE_NAME, null));
                initTips(list);
                for (RPReceiveInfo receiveInfo : mReceiveInfos) {
                    list.add(new PacketDetailItem(PacketDetailAdapter.TYPE_ITEM, receiveInfo));
                }
                adapter.setData(list);
            }

            @Override
            public void onFail(int code, String msg) {
//                mProgressDialogUtil.hideLoading();
                ToastUtil.show(msg);
            }
        });
    }

    private void initLuckyArray() {
        int maxCoin = 0;
        totalGrabCoin = 0;
        HashMap<String, ArrayList<Integer>> map = new HashMap<>();
        for (RPReceiveInfo receiveInfo : mReceiveInfos) {
            int uid = receiveInfo.uid;
            int coin = receiveInfo.coin;
            totalGrabCoin += receiveInfo.coin;
            if (coin >= maxCoin) {
                maxCoin = coin;
                if (!map.containsKey(maxCoin + "")) {
                    map.put(maxCoin + "", new ArrayList<>());
                }
                ArrayList<Integer> array = map.get(maxCoin + "");
                array.add(uid);
            }
        }
        if (map.containsKey(maxCoin + "")) {
            luckyArray = map.get(maxCoin + "");
        }
    }

    private void initTips(List<PacketDetailItem> list) {
        int num = mRedPacketInfo.number;
        if (mReceiveInfos.size() == 0 && mRedPacketInfo.isTimeOut()) {
            tips = ResUtil.getResource().getString(R.string.red_packet_overdue_view_1);
            list.add(new PacketDetailItem(PacketDetailAdapter.TYPE_TIP, null));
            return;
        }
        if (mRedPacketInfo.recv_uid != 0) {
            if (totalGrabCoin == mRedPacketInfo.coin) {
                if (grabCoin != -1) {
                    list.add(new PacketDetailItem(PacketDetailAdapter.TYPE_COIN, null));
                } else {
                    tips = ResUtil.getResource().getString(R.string.red_packet_x_gold, mRedPacketInfo.coin);
                    list.add(new PacketDetailItem(PacketDetailAdapter.TYPE_TIP, null));
                }
            } else {
                tips = ResUtil.getResource().getString(R.string.red_packet_x_gold_wait_for_claimed, mRedPacketInfo.coin);
                list.add(new PacketDetailItem(PacketDetailAdapter.TYPE_TIP, null));
            }
        } else {
            if (grabCoin != -1) {
                list.add(new PacketDetailItem(PacketDetailAdapter.TYPE_COIN, null));
            }
            int received = mReceiveInfos.size();
            int totalCoin = mRedPacketInfo.coin;
            tips = ResUtil.getResource().getString(R.string.red_packet_x_of_x_claimed_x_of_x_gold_left, received, num, totalGrabCoin, totalCoin);
            list.add(new PacketDetailItem(PacketDetailAdapter.TYPE_TIP, null));
        }
    }

    static class PacketDetailItem {
        final int type;
        final RPReceiveInfo receiveInfo;

        public PacketDetailItem(int type, RPReceiveInfo receiveInfo) {
            this.type = type;
            this.receiveInfo = receiveInfo;
        }
    }

    class PacketDetailAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
        private static final int TYPE_HEAD = 0;
        private static final int TYPE_ITEM = 1;
        public static final int TYPE_NAME = 2;
        public static final int TYPE_TIP = 3;
        public static final int TYPE_COIN = 4;
        private List<PacketDetailItem> datas = new ArrayList<>();

        public void setData(List<PacketDetailItem> datas) {
            if (datas != null) {
                this.datas.clear();
                this.datas.addAll(datas);
                notifyDataSetChanged();
            }
        }

        @Override
        public int getItemCount() {
            return datas.size();
        }

        @Override
        public int getItemViewType(int position) {
            return datas.get(position).type;
        }

        @NonNull
        @Override
        public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            switch (viewType) {
                case TYPE_ITEM:
                    return new PacketRecItemViewHolder(new PacketRecItemView(mContext));
                case TYPE_NAME:
                    return new PacketDetailNameViewHolder((ViewGroup) LayoutInflater.from(mContext).inflate(R.layout.red_packet_detail_name_view, parent, false));
                case TYPE_TIP:
                    return new PacketDetailTipViewHolder((ViewGroup) LayoutInflater.from(mContext).inflate(R.layout.red_packet_detail_tip_view, parent, false));
                case TYPE_COIN:
                    return new PacketDetailCoinViewHolder((ViewGroup) LayoutInflater.from(mContext).inflate(R.layout.red_packet_detail_coin_view, parent, false));
            }
            return new PacketDetailTipViewHolder((ViewGroup) LayoutInflater.from(mContext).inflate(R.layout.red_packet_detail_tip_view, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
            if (holder instanceof PacketRecItemViewHolder) {
                ((PacketRecItemViewHolder) holder).update(datas.get(position).receiveInfo, luckyArray, mRedPacketInfo.number - mReceiveInfos.size());
            } else if (holder instanceof PacketDetailNameViewHolder) {
                ((PacketDetailNameViewHolder) holder).update(mRedPacketInfo);
            } else if (holder instanceof PacketDetailTipViewHolder) {
                ((PacketDetailTipViewHolder) holder).update(tips);
            } else if (holder instanceof PacketDetailCoinViewHolder) {
                ((PacketDetailCoinViewHolder) holder).update(grabCoin);
            }
        }
    }

    private EventCallback eventCallback;

    public void registerEventCallback(EventCallback callback) {
        this.eventCallback = callback;
    }

    public interface EventCallback {
        void onClickClose();
    }
}
