package com.wepie.wespy.module.contact.searchuser

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.platform.ThreadUtil
import com.huiwan.user.LoginHelper
import com.huiwan.widget.actionbar.BaseWpActionBar
import com.wepie.lib.api.plugins.share.ShareCallback
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.share.SharePlugin
import com.wepie.lib.share.ShareAdapter
import com.wepie.lib.share.ShareItemView.ShareItemViewCallBack
import com.wepie.lib.share.SpaceItemDecoration
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.helper.imageLoader.HeadImageLoader
import com.wepie.wespy.utils.QRCodeEncodingUtils
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by bigwen on 2018/2/8.
 */
@SuppressLint("ViewConstructor")
class AddFriendQRCodeDialog(
    context: Context,
    private val actionBarBackClickListener: OnClickListener? = null
) : FrameLayout(context) {
    private val mContext: Context
    private lateinit var qrcodeIv: ImageView
    private lateinit var userHeadIv: ImageView
    private var userName: TextView
    private var userId: TextView
    private lateinit var actionBar: BaseWpActionBar
    private var shareLay: RecyclerView
    private lateinit var selfInfoLayout: ConstraintLayout

    private var callback: DialogCallback? = null
    private val handler = Handler(Looper.getMainLooper())

    private val shareItemCallBack: ShareItemViewCallBack by lazy {
        ShareItemViewCallBack {
            val bitmap = Bitmap.createBitmap(
                selfInfoLayout.width, selfInfoLayout.height, Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bitmap)
            selfInfoLayout.draw(canvas)
            bitmap
        }
    }

    init {
        mContext = context
        LayoutInflater.from(context).inflate(R.layout.wdid_qrcode_dialog, this)
        initActionBar()
        selfInfoLayout = findViewById(R.id.content_lay)
        userName = findViewById(R.id.user_name)
        userId = findViewById(R.id.user_id)

        userName.text = LoginHelper.getNickname()
        userId.text = ResUtil.getStr(R.string.activity_qrcode_my_id, LoginHelper.getWodi())
        shareLay = findViewById(R.id.share_recycle_view)

        findViewById<TextView>(R.id.invite_content).text = ConfigHelper.getInstance().appSlogan
    }

    private fun initActionBar() {
        actionBar = findViewById(R.id.action_bar)
        actionBar.addTitleAndBack(ResUtil.getStr(R.string.activity_edit_userinfo_5), actionBarBackClickListener)
        actionBar.setActionBarBackgroundColor(resources.getColor(R.color.qrcode_background_color))
        actionBar.refreshBackIcon(R.drawable.action_bar_icon_back_white)
        actionBar.setTitleTextColor(Color.WHITE)
    }

    fun update(plugins: List<SharePlugin?>?, info: ShareInfo, callback: ShareCallback?) {
        refreshQrcode(info.link)
        shareLay.adapter = ShareAdapter(plugins, info, callback, true, shareItemCallBack)
        shareLay.layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        if (info.supportedTypes.size >= SHARE_BUTTON_NUM) {
            val dp12 = ScreenUtil.dip2px(12F)
            shareLay.addItemDecoration(SpaceItemDecoration(0, dp12, 0, dp12))
        } else {
            val dp20 = ScreenUtil.dip2px(20F)
            shareLay.addItemDecoration(SpaceItemDecoration(0, dp20, 0, dp20))
        }
    }

    private fun refreshQrcode(shareUrl: String) {
        qrcodeIv = findViewById(R.id.qrcode_iv)
        userHeadIv = findViewById(R.id.qrcode_user_icon_iv)
        HeadImageLoader.loadHeadImage(LoginHelper.getHeadUrl(), 4, userHeadIv)

        ThreadUtil.runInOtherThread {
            val user = LoginHelper.getLoginUser()
            val bitmap = generateQRCode(if (user == null) "" else user.uid2code())
            handler.post { qrcodeIv.setImageBitmap(bitmap) }
        }
    }

    private fun generateQRCode(uid: String): Bitmap {
        val jsonObject = JSONObject()
        try {
            jsonObject.put("invite_code", uid)
        } catch (e: JSONException) {
            e.printStackTrace()
            HLog.d(TAG, HLog.USR, "generateQRCode, JSONException={}")
        }
        val dp248 = ScreenUtil.dip2px(248F)
        return QRCodeEncodingUtils.createQRCode(jsonObject.toString(), dp248, dp248, null)
    }

    fun setDialogCallback(callback: DialogCallback?) {
        this.callback = callback
    }

    interface DialogCallback {
        fun onDismiss()
    }

    companion object {
        private const val TAG = "AddFriendQRCodeDialog"

        private const val SHARE_BUTTON_NUM = 5
    }
}
