package com.wepie.wespy.module.contact.title

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.ui.empty.HWUIEmptyView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.user.entity.UserTitle
import com.wepie.wespy.R

class UserTitleTypeFragment : Fragment() {

    private lateinit var mTitleAllEmptyView: HWUIEmptyView
    private lateinit var mTitleAllRv: RecyclerView
    private lateinit var allAdapter: UserTitleAdapter

    private lateinit var viewModel: UserTitleViewModel

    private var type: Int = 0

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.user_title_all_item_view, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        mTitleAllRv = view.findViewById(R.id.title_all_rv)
        mTitleAllEmptyView = view.findViewById(R.id.title_all_none_view)
        val manager = GridLayoutManager(context, 2)
        mTitleAllRv.layoutManager = manager
        mTitleAllRv.addItemDecoration(GridSpacingItemDecoration(2, ScreenUtil.dip2px(8f)))
        allAdapter = UserTitleAdapter(-1, -1, UserTitleAdapter.FROM_TITLE_ALL, viewLifecycleOwner)
        mTitleAllRv.adapter = allAdapter

        viewModel = ViewModelProvider(requireActivity())[UserTitleViewModel::class.java]

        var list = ConfigHelper.getInstance().userTagConfig.getShownUserTagList()
        list = list.filter { it.category == type }
        if (list.isEmpty()) {
            mTitleAllRv.visibility = View.GONE
            mTitleAllEmptyView.visibility = View.VISIBLE
        }
        viewModel.acquireMapLiveData.observe(viewLifecycleOwner) { acquireMap ->
            if (list.isNotEmpty()) {
                val userList =
                    list.map { UserTitle(acquireMap["${it.tagID}"] ?: 0, 0, 0, it.tagID, 0) }
                        .sortedByDescending { it.titleId }
                mTitleAllRv.visibility = View.VISIBLE
                mTitleAllEmptyView.visibility = View.GONE
                allAdapter.refresh(userList, acquireMap)
            }
        }
    }

    override fun setArguments(args: Bundle?) {
        super.setArguments(args)
        type = args?.getInt("type") ?: 0
    }
}