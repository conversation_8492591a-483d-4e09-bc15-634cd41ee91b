package com.wepie.wespy.module.contact.title

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.empty.HWUIEmptyView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.activity.BaseFragment
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.NameTextView
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserTagStatusV2
import com.huiwan.user.http.UserApi
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.three.http.callback.LifeDataCallback
import com.three.http.callback.Result
import com.wepie.wespy.R

/**
 * 用户已获得称号页面
 * create by HebeHan
 * 2023/12/3
 */
class UserTitleAcquireFragment : BaseFragment() {

    private lateinit var mTitleUserHistoryRefresh: SmartRefreshLayout
    private lateinit var mTitleUserHistoryRv: RecyclerView
    private lateinit var mTitleUserHistorySuffix: TextView
    private lateinit var mTitleUserHistory: TextView
    private lateinit var mTitleNowUseListLayout: FrameLayout
    private lateinit var mTitleUseEmptyView: HWUIEmptyView
    private lateinit var mTitleNowUseRv: RecyclerView
    private lateinit var mTitleUserName: NameTextView
    private lateinit var mTitleUserHead: DecorHeadImgView
    private lateinit var mTitleAllAcquireCount: TextView
    private lateinit var mTitleNowCount: TextView

    private var userData: UserTagStatusV2 = UserTagStatusV2()

    private var page = 1
    private val pageSize = 20

    //哪个用户的页面
    private var targetUid: Int = 0

    //跳转过来的称号ID,用于闪烁指示
    private var titleId: Int = -1

    //有效列表
    private lateinit var validAdapter: UserTitleAdapter

    //历史列表
    private lateinit var historyAdapter: UserTitleAdapter
    private lateinit var mRootScrollView: NestedScrollView


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.fragment_user_title_acquire, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        targetUid = arguments?.getInt("targetUid") ?: 0
        titleId = arguments?.getInt("titleId") ?: 0
        initViews(view)
    }


    private fun initViews(view: View) {
        mTitleNowCount = view.findViewById(R.id.title_now_count)
        mTitleAllAcquireCount = view.findViewById(R.id.title_all_acquire_count)
        mTitleUserHead = view.findViewById(R.id.title_user_head)
        mTitleUserName = view.findViewById(R.id.title_user_name)
        mTitleNowUseRv = view.findViewById(R.id.title_now_use_rv)
        mTitleUseEmptyView = view.findViewById(R.id.title_use_none_view)
        mTitleNowUseListLayout = view.findViewById(R.id.title_now_use_list_layout)
        mTitleUserHistory = view.findViewById(R.id.title_user_history)
        mTitleUserHistorySuffix = view.findViewById(R.id.title_user_history_suffix)
        mTitleUserHistoryRv = view.findViewById(R.id.title_user_history_rv)
        mTitleUserHistoryRefresh = view.findViewById(R.id.title_user_history_refresh)
        mRootScrollView = view.findViewById(R.id.root_scroll_view)

        //可用
        val manager = GridLayoutManager(context, 2)
        mTitleNowUseRv.layoutManager = manager
        mTitleNowUseRv.addItemDecoration(GridSpacingItemDecoration(2, ScreenUtil.dip2px(8f)))
        validAdapter = UserTitleAdapter(
            titleId, targetUid, UserTitleAdapter.FROM_TITLE_VALID, viewLifecycleOwner
        )
        mTitleNowUseRv.adapter = validAdapter

        //历史
        val managerHistory = GridLayoutManager(context, 2)
        mTitleUserHistoryRv.layoutManager = managerHistory
        mTitleUserHistoryRv.addItemDecoration(GridSpacingItemDecoration(2, ScreenUtil.dip2px(8f)))
        historyAdapter = UserTitleAdapter(
            titleId, targetUid, UserTitleAdapter.FROM_TITLE_HISTORY, viewLifecycleOwner
        )
        mTitleUserHistoryRv.adapter = historyAdapter


        //设置上拉刷新
        mTitleUserHistoryRefresh.setEnableRefresh(false)
        mTitleUserHistoryRefresh.setOnLoadMoreListener { loadMoreHistory() }

        //加载自己的数据
        refreshData()
        showHistoryView(View.GONE)
        //处理一下不同视角
        //自己的视角 加载历史数据
        if (targetUid == LoginHelper.getLoginUid()) {
            loadMoreHistory()
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        //获取用户的数据
        UserApi.getUserTagV2(targetUid, object : LifeDataCallback<UserTagStatusV2>(this) {
            override fun onSuccess(result: Result<UserTagStatusV2>) {
                result.data?.let {
                    userData = it
                }
                refreshUserInfo()
                refreshAcquireListUi()
            }

            override fun onFail(code: Int, msg: String?) {
                ToastUtil.show(msg)
            }
        })

    }

    /**
     * 更新历史数据
     */
    private fun loadMoreHistory() {
        UserApi.getUserTitleHistory(
            targetUid, page, pageSize,
            object : LifeDataCallback<UserTagStatusV2>(this) {
                override fun onSuccess(result: Result<UserTagStatusV2>) {
                    result.data?.apply {
                        if (historyList.isNotEmpty()) {
                            showHistoryView(View.VISIBLE)
                        }
                        historyAdapter.add(historyList)
                        if (historyAdapter.itemCount >= total) {
                            //这里设置false会拉不上去
                            mTitleUserHistoryRefresh.setEnableLoadMore(false)
                            mTitleUserHistoryRv.isNestedScrollingEnabled = false
                            //第一次自动加载如果加载完了,不弹提示,后续上拉弹提示
                            if (page != 1) {
                                ToastUtil.show(ResUtil.getStr(R.string.no_more))
                            }
                        } else {
                            mTitleUserHistoryRefresh.setEnableLoadMore(true)
                        }
                    }
                    page++
                    mTitleUserHistoryRefresh.finishLoadMore()
                }

                override fun onFail(code: Int, msg: String?) {
                    ToastUtil.show(msg)
                    mTitleUserHistoryRefresh.finishLoadMore()
                }
            })
    }

    /**
     * 数据完全空白
     */
    private fun showTotalEmpty() {
        mTitleUseEmptyView.visibility = View.VISIBLE
        mTitleNowUseRv.visibility = View.GONE

        mTitleUserHistory.visibility = View.GONE
        mTitleUserHistorySuffix.visibility = View.GONE
        mTitleUserHistoryRefresh.visibility = View.GONE
    }

    /**
     * 展示他人视角
     */
    fun showHistoryView(visibility: Int) {
        mTitleUserHistory.visibility = visibility
        mTitleUserHistorySuffix.visibility = visibility
        mTitleUserHistoryRefresh.visibility = visibility
    }

    /**
     * 刷新用户信息UI
     */
    private fun refreshUserInfo() {
        mTitleNowCount.text = userData.validTitles.size.toString()
        mTitleAllAcquireCount.text = userData.total.toString()
        mTitleUserHead.showUserHeadWithDecoration(targetUid)
        UserService.get().getCacheSimpleUser(targetUid) {
            mTitleUserName.setUserName(it)
        }
        //如果历史为0,那么直接展示空白就好了
        if (userData.total == 0) {
            showTotalEmpty()
        }
    }

    /**
     * 刷新用户称号列表UI
     */
    private fun refreshAcquireListUi() {
        if (userData.validTitles.isEmpty()) {
            mTitleUseEmptyView.visibility = View.VISIBLE
            mTitleNowUseRv.visibility = View.GONE
        } else {
            mTitleUseEmptyView.visibility = View.GONE
            mTitleNowUseRv.visibility = View.VISIBLE
            val data = if (targetUid == LoginHelper.getLoginUid())
                userData.validTitles
            else
                userData.validTitles.filter { it.status == 1 }
            validAdapter.refresh(data, emptyMap())
            //进入后滚动到指定位置
            data.forEachIndexed { index, userTitle ->
                if (userTitle.titleId == titleId) {
                    mRootScrollView.post {
                        val itemHeight = if (targetUid == LoginHelper.getLoginUid()) 195f else 160f
                        //(index - index % 2)/2 因为每行有2个,每次移动的高度只按第一个item算,因为不管选中第一个还是第二个,都是滚动一样的高度,再次/2是为了将0,2,4,6,8转为0,1,2,3,4
                        mRootScrollView.scrollTo(
                            0, ScreenUtil.dip2px(itemHeight) * (index - index % 2) / 2
                        )
                    }
                }
            }
        }
    }
}