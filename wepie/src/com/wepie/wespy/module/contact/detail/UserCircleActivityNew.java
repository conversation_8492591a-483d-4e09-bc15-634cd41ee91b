package com.wepie.wespy.module.contact.detail;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.android.material.appbar.MaterialToolbar;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.R;
import com.wepie.wespy.module.discover.msg.DiscoverMsgActivity;
import com.wepie.wespy.module.report.module.ReportCircleAlertDialog;
import com.wepie.wespy.module.settings.edituser.UserInfoDialogHelper;

/**
 * Created by bigwen on 16/7/21.
 */
public class UserCircleActivityNew extends BaseActivity {
    private Context mContext;
    private int uid;
    private UserCircleView circleView;
    private ImageView newMsgIv, sendCircleIv;
    private TextView msgDotTv;
    private UserCircleActivityPresenter presenter;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.uid = getIntent().getIntExtra(IntentConfig.UID, -1);
        boolean isReport = getIntent().getBooleanExtra(IntentConfig.IS_REPORT, false);
        mContext = this;
        presenter = new UserCircleActivityPresenter(this);
        setContentView(R.layout.acticity_user_circle_new);
        circleView = findViewById(R.id.user_circle_view);
        newMsgIv = findViewById(R.id.friend_circle_new_alarm);
        msgDotTv = findViewById(R.id.friend_circle_new_remind_number);
        sendCircleIv = findViewById(R.id.friend_circle_new_camera);
        circleView.init(uid);
        initEvents();
        updateAlarmNum(presenter.getRemindNumber());

        MaterialToolbar toolbar = findViewById(R.id.toolbar);
        if (uid == LoginHelper.getLoginUid()) {
            toolbar.setTitle(R.string.user_circle_my_moments);
        } else {
            toolbar.setTitle(R.string.user_circle_moments);
        }
        toolbar.setNavigationOnClickListener(ContextUtil::finishActivity);

        if (isReport) {
            ThreadUtil.runOnUiThreadDelay(500, () -> ReportCircleAlertDialog.show(mContext, null));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        circleView.setCurUid(uid);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        presenter.clear();
        circleView.clear();
    }

    private void initEvents() {
        newMsgIv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(mContext, DiscoverMsgActivity.class);
                mContext.startActivity(intent);
            }
        });

        sendCircleIv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IDAuthCheckManager.doTaskOrShowNeedCertificate(UserCircleActivityNew.this, AuthApi.SCENE_CIRCLE_CAMERA,
                        new IDAuthCheckManager.TaskCallback() {
                            @Override
                            public void onDoTask() {
                                UserInfoDialogHelper.showSendDialogNew((Activity) mContext);
                            }
                        });
            }
        });
    }

    public void updateAlarmNum(int num) {
        if (num > 0) {
            String numStr = num > 99 ? "..." : String.valueOf(num);
            msgDotTv.setText(numStr);
            msgDotTv.setVisibility(VISIBLE);
        } else {
            msgDotTv.setVisibility(GONE);
        }
    }
}
