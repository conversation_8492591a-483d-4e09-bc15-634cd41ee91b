package com.wepie.wespy.module.contact.detail

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.Space
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo
import com.huiwan.user.LoginHelper
import com.huiwan.widget.actionbar.BaseWpActionBar
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.three.http.callback.ContinuationDataCallback
import com.three.http.core.KtResultSuccess
import com.wejoy.weplay.ex.ILife
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wejoy.weplay.module.makefriend.WejoyVoiceRoomTimerManager
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.makefriend.VoiceRoomListInfo
import com.wepie.wespy.model.entity.voiceroom.UserAdvanceVoiceRoomInfo
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.contact.detail.view.UserAdvanceRoomInfoView
import com.wepie.wespy.module.makefriend.dataservice.VoiceListCallback
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomListService
import com.wepie.wespy.net.http.api.RoomApi
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.GetRoomListByRidsRsp
import com.wepie.wespy.net.tcp.sender.HwContinuationSeqCallback
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

class UserVoiceRoomListActivity : BaseActivity() {
    companion object {
        private const val INTENT_UID = "uid"

        @JvmStatic
        fun start(context: Context, uid: Int) {
            val starter = Intent(context, UserVoiceRoomListActivity::class.java)
                .putExtra(INTENT_UID, uid)
            context.startActivity(starter)
        }
    }

    private lateinit var actionBar: BaseWpActionBar
    private lateinit var refreshLayout: SmartRefreshLayout
    private lateinit var roomListView: RecyclerView
    private lateinit var timerManager: WejoyVoiceRoomTimerManager

    private var offset = 0
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_user_voice_room_list)
        actionBar = findViewById(R.id.action_bar)
        refreshLayout = findViewById(R.id.room_list_refresh)
        roomListView = findViewById(R.id.room_list_view)
        actionBar.addTitleAndBack(ResUtil.getStr(R.string.common_advanced_voice_room))
        val uid = intent.getIntExtra("uid", -1)
        if (uid < 0) {
            finish()
            return
        }

        roomListView.layoutManager = LinearLayoutManager(this)
        timerManager = WejoyVoiceRoomTimerManager()
        val advancedAdapter = AdvancedVoiceRoomAdapter(uid, timerManager)
        val joinedAdapter = JoinedVoiceRoomAdapter(timerManager)
        val concatAdapter = ConcatAdapter(joinedAdapter)
        roomListView.adapter = concatAdapter

        lifecycleScope.launch {
            val result = suspendCancellableCoroutine {
                RoomApi.getAdvanceVoiceRoomInfo(
                    uid,
                    object : ContinuationDataCallback<UserAdvanceVoiceRoomInfo>(it) {})
            }
            if (result is KtResultSuccess) {
                val advanceVoiceRoomInfos = result.data.advanceVoiceRoomInfos
                if (advanceVoiceRoomInfos.isNotEmpty()) {
                    val roomList: MutableList<Int> = ArrayList()
                    advanceVoiceRoomInfos.forEach {
                        roomList.add(it.rid)
                    }
                    val head = suspendCancellableCoroutine {
                        VoiceRoomPacketSender.getVoiceRoomByRidList(
                            roomList,
                            object : HwContinuationSeqCallback(it) {}
                        )
                    }
                    if (head.codeOk()) {
                        val voiceRoomListRsp = head.message as GetRoomListByRidsRsp
                        val voiceRoomListInfos: List<VoiceRoomListInfo> =
                            VoiceRoomListInfo.parseList(voiceRoomListRsp.voiceRoomListList)
                        advancedAdapter.update(voiceRoomListInfos)
                    } else {
                        advancedAdapter.update(mutableListOf())
                    }
                } else {
                    advancedAdapter.update(mutableListOf())
                }
                roomListView.scrollToPosition(0)
                concatAdapter.addAdapter(0, advancedAdapter)
            }
        }
        val voiceRoomListService = VoiceRoomListService()
        loadJoinedList(uid, voiceRoomListService, joinedAdapter)
        refreshLayout.setEnableRefresh(false)
        refreshLayout.setOnLoadMoreListener {
            loadJoinedList(uid, voiceRoomListService, joinedAdapter)
        }
    }

    private fun loadJoinedList(
        uid: Int,
        voiceRoomListService: VoiceRoomListService,
        joinedAdapter: JoinedVoiceRoomAdapter
    ) {
        voiceRoomListService.getVoiceRoomList(
            offset,
            VoiceLabelInfo.LABEL_JOINED,0,
            "",
            uid,
            object : VoiceListCallback {
                override fun onSuccess(
                    roomListInfos: MutableList<VoiceRoomListInfo>,
                    label: Int,
                    roomListId: String
                ) {
                    joinedAdapter.add(roomListInfos)
                    if (roomListInfos.isNotEmpty()) {
                        offset++
                    } else {
                        ToastUtil.show(ResUtil.getStr(R.string.no_more))
                    }
                    refreshLayout.finishLoadMore()
                }

                override fun onFail(msg: String?) {
                    ToastUtil.show(msg)
                    refreshLayout.finishLoadMore()
                }

                override val life: ILife?
                    get() = toLife()
            })
    }

    override fun onDestroy() {
        super.onDestroy()
        timerManager.destroy()
    }

}

private class AdvancedVoiceRoomAdapter(private val uid: Int, val timerManager: WejoyVoiceRoomTimerManager) :
    RecyclerView.Adapter<AdvancedVoiceRoomAdapter.AdvancedViewHolder>() {
    private val advanceRoomInfos = mutableListOf<VoiceRoomListInfo>()

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): AdvancedViewHolder {
        return AdvancedViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_advanced_room_info, parent, false
            ),
            timerManager
        )
    }

    override fun getItemCount(): Int {
        return if (uid == LoginHelper.getLoginUid() || advanceRoomInfos.isNotEmpty()) {
            1
        } else {
            0
        }
    }

    override fun onBindViewHolder(
        holder: AdvancedViewHolder,
        position: Int
    ) {
        holder.update(uid, advanceRoomInfos)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun update(advanceRoomInfos: List<VoiceRoomListInfo>) {
        this.advanceRoomInfos.clear()
        this.advanceRoomInfos.addAll(advanceRoomInfos)
        notifyDataSetChanged()
    }

    @Suppress("DEPRECATED_SMARTCAST")
    class AdvancedViewHolder(itemView: View, val timerManager: WejoyVoiceRoomTimerManager) : RecyclerView.ViewHolder(itemView) {
        private val title: TextView
        private val root: LinearLayout

        init {
            root = itemView.findViewById(R.id.item_advanced_root)
            title = itemView.findViewById(R.id.item_advanced_title)
        }

        fun update(
            uid: Int,
            advanceRoomInfos: List<VoiceRoomListInfo>
        ) {
            root.removeAllViews()
            if (uid == LoginHelper.getLoginUid()) {
                title.text = ResUtil.getString(R.string.voice_room_my_sub_title)
                if (advanceRoomInfos.isEmpty()) {
                    val view = LayoutInflater.from(itemView.context).inflate(
                        R.layout.item_advanced_room_info_open, root
                    )
                    view.setOnClickListener {
                        JumpUtil.gotoSuperiorRoomDetailActivity(itemView.context)
                    }
                    root.addView(
                        Space(itemView.context),
                        ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ScreenUtil.dip2px(20f)
                        )
                    )
                } else {
                    advanceRoomInfos.forEach {
                        val userAdvanceRoomInfoView = UserAdvanceRoomInfoView(itemView.context)
                        userAdvanceRoomInfoView.bindData(it, timerManager)
                        root.addView(userAdvanceRoomInfoView)
                        root.addView(
                            Space(itemView.context),
                            ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ScreenUtil.dip2px(20f)
                            )
                        )
                    }
                }
            } else {
                title.text = ResUtil.getString(R.string.voice_room_his_sub_title)
                advanceRoomInfos.forEach {
                    val userAdvanceRoomInfoView = UserAdvanceRoomInfoView(itemView.context)
                    userAdvanceRoomInfoView.bindData(it, timerManager)
                    root.addView(userAdvanceRoomInfoView)
                    root.addView(
                        Space(itemView.context),
                        ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ScreenUtil.dip2px(20f)
                        )
                    )
                }
            }
        }
    }

}

private class JoinedVoiceRoomAdapter(val timerManager: WejoyVoiceRoomTimerManager) :
    RecyclerView.Adapter<JoinedVoiceRoomAdapter.JoinedViewHolder>() {
    private val joinedRoomInfos = mutableListOf<VoiceRoomListInfo>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): JoinedViewHolder {
        return JoinedViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_advanced_room_info, parent, false
            ),
            timerManager
        )
    }

    override fun getItemCount(): Int {
        return joinedRoomInfos.size
    }

    override fun onBindViewHolder(holder: JoinedViewHolder, position: Int) {
        holder.update(position, joinedRoomInfos[position])
    }

    class JoinedViewHolder(itemView: View,var timerManager: WejoyVoiceRoomTimerManager) : RecyclerView.ViewHolder(itemView) {
        private val title: TextView
        private val userAdvanceRoomInfoView: UserAdvanceRoomInfoView

        init {
            title = itemView.findViewById(R.id.item_advanced_title)
            title.isVisible = false
            title.text = ResUtil.getString(R.string.voice_room_joined_sub_title)
            userAdvanceRoomInfoView = UserAdvanceRoomInfoView(itemView.context)
            val root = itemView.findViewById<LinearLayout>(R.id.item_advanced_root)
            root.addView(userAdvanceRoomInfoView)
            root.addView(
                Space(itemView.context),
                ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ScreenUtil.dip2px(20f))
            )
        }

        fun update(
            position: Int,
            joinedRoomInfo: VoiceRoomListInfo
        ) {
            title.isVisible = position == 0
            userAdvanceRoomInfoView.bindData(position, joinedRoomInfo, timerManager)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun add(joinedRoomInfos: List<VoiceRoomListInfo>) {
        this.joinedRoomInfos.addAll(joinedRoomInfos)
        notifyDataSetChanged()
    }

}