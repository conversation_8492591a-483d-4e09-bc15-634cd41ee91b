package com.wepie.wespy.module.contact.detail;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.ConcatAdapter;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.anim.LifecycleAnimView;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.InitializerManagerUtils;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.component.gift.show.GiftContentView;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.HomeAnimExtra;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.constants.ActivityResultCode;
import com.huiwan.constants.HttpCode;
import com.huiwan.constants.IntentConfig;
import com.huiwan.decorate.UserDecorManager;
import com.huiwan.glview.SpriteGLView;
import com.huiwan.glview.VideoSprite;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.IDebugPluginApi;
import com.huiwan.lib.api.plugins.friend.AddFriendCallback;
import com.huiwan.lib.api.plugins.friend.AddFriendInfo;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.config.ChatPacketConstants;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.littlegame.cocos.CocosLaunchInfo;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.LifeUserInfoLoadCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserInfoLoadCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.FriendInfo;
import com.huiwan.user.entity.User;
import com.huiwan.widget.MarqueeTextView;
import com.huiwan.widget.decoration.SpaceItemDecoration;
import com.tencent.qgame.animplayer.util.ScaleType;
import com.wejoy.gamematch.net.GameMatchSender;
import com.wejoy.weplay.ex.cancellable.EventBusExKt;
import com.wejoy.weplay.helper.life.base.LifeRegister;
import com.wepie.download.DownloadUtil;
import com.wepie.download.Downloader;
import com.wepie.download.LifeDownloadCallback;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.liblog.main.HLog;
import com.wepie.startup.InitializerManager;
import com.wepie.wespy.R;
import com.wepie.wespy.base.GameStatusCheckManager;
import com.wepie.wespy.base.call.DownLoadFile;
import com.wepie.wespy.cocosnew.match.main.ar285.LittleGameTrackUtilsKt;
import com.wepie.wespy.cocosnew.util.CommonUtil;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.helper.shence.ShenceUtil;
import com.wepie.wespy.helper.shence.util.ShenceGiftUtil;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.model.entity.JumpCocosTeamInfo;
import com.wepie.wespy.model.entity.MatchState;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.event.ProtoMessageEvent;
import com.wepie.wespy.model.event.ServerSendGiftEvent;
import com.wepie.wespy.model.event.chat.GameStateRefresh;
import com.wepie.wespy.model.event.other.LoverHomeEvent;
import com.wepie.wespy.model.event.other.RefreshSelfTagEvent;
import com.wepie.wespy.module.chat.ChatUtil;
import com.wepie.wespy.module.chat.conversation.GameState;
import com.wepie.wespy.module.chat.conversation.GameStateManager;
import com.wepie.wespy.module.chat.conversation.MatchStateManager;
import com.wepie.wespy.module.chat.ui.single.ChatActivity;
import com.wepie.wespy.module.common.jump.JumpCocosTeamUtil;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.contact.detail.item.IUserDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserAdvanceRoomDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserBaseInfoDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserCircleDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserFamilyInfoDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserGiftWallDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserGuardInfoDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserMedalDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserSignatureDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserStatsDetailItem;
import com.wepie.wespy.module.contact.detail.item.UserTagDetailItem;
import com.wepie.wespy.module.contact.manager.AddFriendManager;
import com.wepie.wespy.module.family.FamilyLightNameUpdateEvent;
import com.wepie.wespy.module.gift.ComboCallback;
import com.wepie.wespy.module.gift.GiftComboUtil;
import com.wepie.wespy.module.gift.GiftComboView;
import com.wepie.wespy.module.gift.GiftInfoTransformHelper;
import com.wepie.wespy.module.gift.GiftShowConfigHelper;
import com.wepie.wespy.module.gift.SendGiftComboCallback;
import com.wepie.wespy.module.marry.MarryUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.dataservice.event.ShowCareGiftAnimEvent;
import com.wepie.wespy.module.voiceroom.main.enter.car.helper.CarAnimHelper;
import com.wepie.wespy.module.voiceroom.main.enter.car.queue.items.CarViewAction;
import com.wepie.wespy.module.voiceroom.main.enter.car.queue.items.DownloadCarAnimFileAction;
import com.wepie.wespy.module.voiceroom.main.enter.car.queue.items.UserHomeAnimAction;
import com.wepie.wespy.module.voiceroom.main.enter.car.queue.items.UserHomeAnimApiAction;
import com.wepie.wespy.net.tcp.packet.ChatPackets;
import com.wepie.wespy.net.tcp.packet.ChatPackets.chat_rs_friend_add;
import com.wepie.wespy.net.tcp.packet.ChatPackets.chat_rs_util_report;
import com.wepie.wespy.net.tcp.packet.GameMatchPackets;
import com.wepie.wespy.net.tcp.sender.ChatPacketSender;
import com.wepie.wespy.utils.HomeLottieUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.Unit;

public class UserInfoDetailActivity extends BaseActivity {

    public static final String KEY_FORBID_ROOM_GIFT = "forbid_room_gift";
    public static final String KEY_IS_NEED_RETURN_VOICE_ROOM = "is_need_return_voice_room";
    public static final String INTENT_KEY_TRACK_EXT_JSON = "intent_track_ext_json";
    public static final String FIRST_SEND_GIFT = "first_send_gift";
    public static final String TAG = "UserInfoDetailActivity";
    public static final int TYPE_DEFAULT = 0;//普通进来
    public static final int TYPE_GAME_ROOM = 1;//桌游房间
    public static final int TYPE_VOICE_ROOM = 2;//语音房
    public static final int TYPE_GROUP = 3;//群聊
    public static final int TYPE_FIX_ROOM = 4;//常驻房
    public static final int TYPE_COCOS_GAME = 5;//小游戏

    public static final int BE_BLOCKED = 0;
    public static final int NOT_BE_BLOCKED = 1;

    private Context mContext;

    private ImageView mNavigationMenuImageView;
    private ImageView mBackImageView;
    private TextView mEditTv;

    private final List<IUserDetailItem> itemList = new ArrayList<>();
    private ConcatAdapter concatAdapter;

    private LifecycleAnimView vapView;

    private View mOperateLay;
    private View gameStateInfoLay;
    private MarqueeTextView gameStateInfoTv;
    private FrameLayout mAddFriendLayout;
    private View mAddFriendBtn;
    private TextView mAddFriendTv;
    private FrameLayout mSendFlowerLayout;
    private View mSendFlowerBtn;
    private TextView mSendFlowerTv;
    private FrameLayout mSendMsgLayout;
    private View mSendMsgBtn;
    private TextView mSendMsgTv;

    private boolean isFirstEnter = true;//第一次进来
    private int roomId;
    private int mUid;
    private int groupId;
    private String jumpSource; // 神策跳转来源统计
    private final Map<String, Object> jsonJumpExt = new HashMap<>(); // 埋点额外信息
    private int extraOpType;
    private int gameType;
    private boolean forbidRoomGift = false;
    private boolean isNeedReturnVoiceRoom = true;
    private int fromType = TYPE_DEFAULT;


    private final CarAnimHelper carAnimHelper = new CarAnimHelper(this);
    private ImageView animView;

    private GiftContentView giftContentView;
    private GiftComboView giftComboView;
    private boolean isVisible = false;

    private SpriteGLView glAnimView;
    private String videoFilePath;

    private boolean isPaused = false;
    private boolean needRefreshRing = false;

    private UserInfoDetailViewModel viewModel;
    private final LifeRegister lifeRegister = new LifeRegister();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarUtil.initStatusBar(this);
        mContext = this;
        setContentView(R.layout.activity_userinfo_detail);

        initViews();
        initData();
        initEvents();
        carAnimHelper.offerQueue(new UserHomeAnimApiAction(mUid));
        carAnimHelper.offerQueue(new DownloadCarAnimFileAction());
        carAnimHelper.offerQueue(new CarViewAction(this));
        carAnimHelper.offerQueue(new UserHomeAnimAction(this::showUserHomeAnim));
        EventBusExKt.registerAutoCancel(EventBus.getDefault(), this, this);

        checkExtraOp();
        HLog.d("", "user detail activity source: {}", jumpSource);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateRingEvent(LoverHomeEvent event) {
        if (isPaused) {
            needRefreshRing = true;
            return;
        }
        viewModel.updateRing();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onGameStateRefresh(GameStateRefresh event) {
        updateGameState();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerProtoMessageEvent(ProtoMessageEvent event) {
        String action = event.action;
        if (ChatPacketConstants.CHAT_RS_FRIEND_ADD.equals(action)) {
            chat_rs_friend_add msg = (chat_rs_friend_add) event.message;
            ToastUtil.show(msg.getCode() == 200 ? ResUtil.getStr(R.string.common_sent) : ResUtil.getStr(R.string.user_detail_apply_friend_fail));

        } else if (ChatPacketConstants.CHAT_RS_UTIL_REPORT.equals(action)) {
            chat_rs_util_report msg = (chat_rs_util_report) event.message;
            ToastUtil.show(msg.getCode() == 200 ? ResUtil.getStr(R.string.common_reported_success) : ResUtil.getStr(R.string.common_reported_fail));
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSendGift(ShowCareGiftAnimEvent event) {
        if (!isVisible) return;//不是当前Activity不处理
        ChatPackets.chat_rs_msg_send msg = event.protoMsg;
        WPMessage message = event.wpMessage;

        if (msg.getCode() == HttpCode.CODE_OK && message != null) {
            showGiftAnim(GiftInfoTransformHelper.fromWpMessage(message));
            String source = jumpSource;
            if (TextUtils.isEmpty(source)) source = TrackSource.DEFAULT;
            viewModel.getCareInfoListV2();
            GiftSendInfo sendInfo = message.getGiftSendInfoFromExtension();
            if (sendInfo != null) {
                sendInfo.gameType = gameType;
                saveLastComboData(sendInfo);
                ShenceGiftUtil.reportSendGift(sendInfo, source, "", jsonJumpExt);
            }
            giftComboView.showGiftAnim(GiftComboUtil.parseComboInfo(message));
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerRefresh(RefreshSelfTagEvent event) {
        if (mUid != LoginHelper.getLoginUid()) return;
        viewModel.notifyEvent(new UserInfoDetailEvent.RefreshSelfTagEvent(event.userTagStatuses));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateFamilyLightName(FamilyLightNameUpdateEvent event) {
        viewModel.notifyEvent(new UserInfoDetailEvent.FamilyLightNameUpdate(event.getName()));
    }

    private void saveLastComboData(GiftSendInfo sendInfo) {
        giftComboView.sendGiftSuccess(sendInfo);
        giftComboView.setComboTimes(sendInfo.comboTimes);
    }

    private void showGiftAnim(GiftShowInfo showInfo) {
        if (giftContentView != null) {
            giftContentView.showGiftAnim(showInfo);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onServerSendGiftEvent(ServerSendGiftEvent event) {
        GiftShowInfo showInfo = GiftInfoTransformHelper.fromWpMessage(event.wpMessage);
        showGiftAnim(showInfo);

        String source = jumpSource;
        if (TextUtils.isEmpty(source)) source = TrackSource.DEFAULT;
        viewModel.getCareInfoListV2();
        GiftSendInfo sendInfo = event.wpMessage.getGiftSendInfoFromExtension();
        if (sendInfo != null) {
            sendInfo.gameType = gameType;
            ShenceGiftUtil.reportSendGift(sendInfo, source);
        }
        giftComboView.showGiftAnim(GiftComboUtil.parseComboInfo(event.wpMessage));
    }

    @Override
    protected void onResume() {
        super.onResume();
        viewModel.getCareInfoListV2();
        if (glAnimView != null && glAnimView.isEmpty() && !TextUtils.isEmpty(videoFilePath)) {
            VideoSprite videoSprite = new VideoSprite(glAnimView, videoFilePath, this);
            videoSprite.setLoop(true);
            videoSprite.setIsRtl(ScreenUtil.isRtl());
            glAnimView.addSprite(videoSprite);
        }
        if (needRefreshRing) {
            viewModel.updateRing();
        }

        if (!isFirstEnter) {//第一次进来不刷新
            UserService.get().getCacheUserLocal(mUid, new LifeUserInfoLoadCallback(this) {
                @Override
                public void onUserInfoSuccess(User userInfo) {
                    refresh(userInfo);
                }
            });
        }
        ApiService.of(IDebugPluginApi.class).showCurPageMsg("current uid", String.valueOf(mUid));
        isFirstEnter = false;
        isVisible = true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        isVisible = false;
        if (glAnimView != null && !glAnimView.isEmpty() && !TextUtils.isEmpty(videoFilePath)) {
            glAnimView.removeAllSprite();
        }
        isPaused = true;
    }

    private void initData() {
        try {
            Intent intent = getIntent();
            fromType = intent.getIntExtra(IntentConfig.USER_VIEW_TYPE, TYPE_DEFAULT);
            groupId = intent.getIntExtra(IntentConfig.GROUP_ID, -1);
            roomId = intent.getIntExtra(IntentConfig.KEY_USER_DETAIL_ROOM_RID, -1);
            mUid = intent.getIntExtra(IntentConfig.KEY_REC_USER_UID, -1);
            jumpSource = intent.getStringExtra("source");
            if (jumpSource == null) {
                jumpSource = "";
            }
            initJumpTrackExt(intent);
            extraOpType = intent.getIntExtra(IntentConfig.OP_TYPE, -1);
            gameType = intent.getIntExtra(IntentConfig.KEY_GAME_TYPE, -1);
            forbidRoomGift = intent.getBooleanExtra(KEY_FORBID_ROOM_GIFT, false);
            isNeedReturnVoiceRoom = intent.getBooleanExtra(KEY_IS_NEED_RETURN_VOICE_ROOM, false);
            if (mUid == -1) throw new Exception("uid invalid");
        } catch (Exception e) {
            HLog.e(TAG, HLog.CLR, "{}", e);
            finish();
            return;
        }
        itemList.add(new UserBaseInfoDetailItem(mUid, this, TrackScreenName.AVATAR_SHOW_RANK.equals(jumpSource)));
        itemList.add(new UserTagDetailItem(mUid, this));
        itemList.add(new UserStatsDetailItem(mUid, this));
        itemList.add(new UserCircleDetailItem(mUid, this));
        itemList.add(new UserGiftWallDetailItem(mUid, this));
        itemList.add(new UserMedalDetailItem(mUid, this));
        itemList.add(new UserSignatureDetailItem(mUid, this));
        itemList.add(new UserGuardInfoDetailItem(mUid, this));
        itemList.add(new UserAdvanceRoomDetailItem(mUid, this));
        itemList.add(new UserFamilyInfoDetailItem(mUid, this));

        viewModel = new ViewModelProvider(this).get(UserInfoDetailViewModel.class);
        viewModel.init(mUid);
        viewModel.observeEvent(this, event -> {
            if (event instanceof UserInfoDetailEvent.AddFriend) {
                UserService.get().getCacheUserLocal(mUid, new LifeUserInfoLoadCallback(UserInfoDetailActivity.this) {
                    @Override
                    public void onUserInfoSuccess(User userInfo) {
                        showAddFriendDialog(userInfo);
                    }
                });
            } else if (event instanceof UserInfoDetailEvent.ShowGiftDialog) {
                sendGiftLogic();
            } else if (event instanceof UserInfoDetailEvent.BlockedState) {
                if (!((UserInfoDetailEvent.BlockedState) event).isBlock()) {
                    showUserPlayingIfNeed();
                }

            }
        });
        viewModel.getAvatarImageInfoLiveData().observe(this, avatarImageInfo -> {
            User userInfo = viewModel.getUserInfo();
            if (userInfo == null) {
                return;
            }
            if (!TextUtils.isEmpty(jumpSource)) {
                ShenceEvent.viewUserDetail(String.valueOf(mUid), userInfo.getLevel(), userInfo.getFlower(), userInfo.getArea(), userInfo.avatarShowInMain(), !TextUtils.isEmpty(avatarImageInfo.avatarImage), jumpSource);
            }
        });
        viewModel.getUserLiveData().observe(this, user -> {
            //更新好友信息
            FriendInfoCacheManager.getInstance().updateFriendInfo(user);
        });
        showUserInfo();
        UserDecorManager.getInstance().forceUpdateDecoration(mUid, null);
    }

    private void initJumpTrackExt(Intent intent) {
        String jsonStr = intent.getStringExtra(INTENT_KEY_TRACK_EXT_JSON);
        HLog.d(TAG, "shushu ext: {}", jsonStr);
        if (!TextUtils.isEmpty(jsonStr) && jsonStr.charAt(0) == '{') {
            try {
                JSONObject jo = new JSONObject(jsonStr);
                for (Iterator<String> it = jo.keys(); it.hasNext(); ) {
                    String key = it.next();
                    jsonJumpExt.put(key, jo.get(key));
                }
            } catch (JSONException ignored) {
            }
        }
    }

    private void initViews() {
        mNavigationMenuImageView = findViewById(R.id.navigation_menu);
        mBackImageView = findViewById(R.id.back_button);
        mEditTv = findViewById(R.id.navigation_menu_edit);

        mOperateLay = findViewById(R.id.friend_operate_lay);
        mSendFlowerLayout = findViewById(R.id.send_flower_layout);
        mSendFlowerBtn = findViewById(R.id.send_flower_btn);
        mSendFlowerTv = findViewById(R.id.send_flower_tv);
        mAddFriendLayout = findViewById(R.id.add_friend_layout);
        mAddFriendBtn = findViewById(R.id.add_friend_btn);
        mAddFriendTv = findViewById(R.id.add_friend_tv);
        mSendMsgLayout = findViewById(R.id.send_msg_layout);
        mSendMsgBtn = findViewById(R.id.send_msg_btn);
        mSendMsgTv = findViewById(R.id.send_msg_tv);

        gameStateInfoLay = findViewById(R.id.user_detail_game_info_lay);
        gameStateInfoTv = findViewById(R.id.user_detail_game_info_tv);

        animView = findViewById(R.id.user_info_anim_view);

        giftContentView = findViewById(R.id.gift_content_view);
        giftComboView = findViewById(R.id.gift_combo_view);
        giftComboView.setLightTheme();
        giftComboView.setComboCallback(new SendGiftComboCallback() {
            @Override
            public void comboGift(GiftSendInfo sendInfo, ComboCallback callback) {
                ChatUtil.sendGiftMsg(sendInfo);
            }

            @Override
            public void hideSolderCombo() {

            }
        });

        RecyclerView rv = findViewById(R.id.user_detail_list);
        rv.setHasFixedSize(true);
        rv.setLayoutManager(new LinearLayoutManager(this));
        rv.addItemDecoration(new SpaceItemDecoration(new Rect(), new Rect(0, 0, 0, (int) ResUtil.getResource().getDimension(R.dimen.wp90))));
        concatAdapter = new ConcatAdapter();
        rv.setAdapter(concatAdapter);
    }

    private void initEvents() {
        mBackImageView.setOnClickListener(v -> finish());
        mNavigationMenuImageView.setOnClickListener(v -> {
//                showUserOperateDialog();
            String source = jumpSource;
            if (TextUtils.isEmpty(source)) {
                source = TrackSource.DEFAULT;
            }
            JumpUtil.gotoUserSettingActivity(UserInfoDetailActivity.this, mUid, viewModel.getHasCared(), source);
        });
        mEditTv.setOnClickListener(view -> JumpUtil.gotoEditUserInfoActivity(UserInfoDetailActivity.this));
    }

    private void showUserInfo() {
        final ProgressDialogUtil dialog = new ProgressDialogUtil();
        dialog.showLoading(mContext, ResUtil.getStr(R.string.common_loading), true);
        viewModel.requestUserInfo(new UserInfoLoadCallback() {
            @Override
            public void onUserInfoSuccess(User userInfo) {
                refresh(userInfo);
                dialog.hideLoading();
            }

            @Override
            public void onUserInfoFailed(String description) {
                ToastUtil.show(description);
                dialog.hideLoading();
            }
        });
    }

    private void refresh(final User user) {
        //操作菜单
        mNavigationMenuImageView.setVisibility(View.VISIBLE);
        mEditTv.setVisibility(View.GONE);
        if (mUid == LoginHelper.getLoginUid()) {
            mEditTv.setVisibility(View.VISIBLE);
            mNavigationMenuImageView.setVisibility(View.GONE);
        }
        if (UserService.get().isSystemUser(mUid)) {
            mNavigationMenuImageView.setVisibility(View.GONE);
        }

        List<IUserDetailItem> list = itemList;
        List<? extends RecyclerView.Adapter<? extends RecyclerView.ViewHolder>> adapters = concatAdapter.getAdapters();
        int i;
        if (list.size() > adapters.size()) {
            for (i = adapters.size(); i < list.size(); i++) {
                concatAdapter.addAdapter(list.get(i).getAdapter());
            }
        } else if (list.size() < adapters.size()) {
            for (i = list.size(); i < adapters.size(); i++) {
                concatAdapter.removeAdapter(adapters.get(i));
            }
        }
        adapters = concatAdapter.getAdapters();
        for (i = 0; i < adapters.size(); i++) {
            RecyclerView.Adapter<? extends RecyclerView.ViewHolder> adapter = adapters.get(i);
            IUserDetailItem item = list.get(i);
            item.update(user);
            RecyclerView.Adapter<? extends RecyclerView.ViewHolder> itemAdapter = item.getAdapter();
            if (adapter == itemAdapter) {
                continue;
            }
            concatAdapter.addAdapter(i, itemAdapter);
            concatAdapter.removeAdapter(adapter);
        }

        //更新底部操作区
        FriendInfo friendInfo = FriendInfoCacheManager.getInstance().getFriendInfoByUid(user.getUid());
        refreshUserBottomOprationLayout(user, friendInfo);

    }

    // 更新底部操作区
    private void refreshUserBottomOprationLayout(User user, FriendInfo friendInfo) {
        mOperateLay.setVisibility(View.VISIBLE);
        if (CommonUtil.isInGame()) {//小游戏中
            if (mUid == LoginHelper.getLoginUid() || friendInfo != null) {//好友或者自己
                mOperateLay.setVisibility(View.GONE);
            } else {
                mOperateLay.setVisibility(View.VISIBLE);
                mAddFriendLayout.setVisibility(View.VISIBLE);
                mSendFlowerLayout.setVisibility(View.GONE);
                mSendMsgLayout.setVisibility(View.GONE);
            }
        } else if (fromType == TYPE_GAME_ROOM || fromType == TYPE_GROUP || fromType == TYPE_FIX_ROOM || fromType == TYPE_VOICE_ROOM) {
            if (mUid == LoginHelper.getLoginUid() || friendInfo != null) {//好友或者自己
                mAddFriendLayout.setVisibility(View.GONE);
                mSendFlowerLayout.setVisibility(View.VISIBLE);
                mSendMsgLayout.setVisibility(View.VISIBLE);
            } else {
                mAddFriendLayout.setVisibility(View.VISIBLE);
                mSendFlowerLayout.setVisibility(forbidRoomGift ? View.GONE : View.VISIBLE);
                mSendMsgLayout.setVisibility(View.GONE);
            }
        } else if (UserService.get().isSystemUser(mUid)) {//法官
            mAddFriendLayout.setVisibility(View.GONE);
            mSendFlowerLayout.setVisibility(View.GONE);
            mSendMsgLayout.setVisibility(View.VISIBLE);
        } else if (mUid == LoginHelper.getLoginUid() || friendInfo != null) {//好友,自己
            mAddFriendLayout.setVisibility(View.GONE);
            mSendFlowerLayout.setVisibility(View.VISIBLE);
            mSendMsgLayout.setVisibility(View.VISIBLE);
        } else {
            mAddFriendLayout.setVisibility(View.VISIBLE);
            mSendFlowerLayout.setVisibility(View.GONE);
            mSendMsgLayout.setVisibility(View.GONE);
        }

        mSendMsgBtn.setOnClickListener(v -> {
            Intent intent = new Intent(mContext, ChatActivity.class);
            intent.putExtra(IntentConfig.KEY_REC_USER_UID, user.getUid());
            startActivity(intent);
        });

        mSendFlowerBtn.setOnClickListener(v -> {
            if (PrefUserUtil.getInstance().getBoolean(FIRST_SEND_GIFT, true)) {
                PrefUserUtil.getInstance().setBoolean(FIRST_SEND_GIFT, false);
                showSendGiftFirstDialog();
            } else {
                sendGiftLogic();
            }
        });

        mAddFriendBtn.setOnClickListener(v -> showAddFriendDialog(user));

        //更新底部送礼物和发消息按钮UI
        refreshBottomBtnBg(View.VISIBLE == gameStateInfoLay.getVisibility());
    }

    /**
     * 更新底部送礼物和发消息按钮UI
     *
     * @param isShowGameState 按钮是否显示文字（当底部游戏中按钮显示且这两个按钮也显示时，需去掉按钮文字使按钮变短）
     */
    private void refreshBottomBtnBg(boolean isShowGameState) {
        if (mAddFriendLayout != null && View.VISIBLE == mAddFriendLayout.getVisibility()) {
            if (mSendFlowerLayout != null && View.VISIBLE == mSendFlowerLayout.getVisibility()) {
                if (mAddFriendBtn != null) {
                    mAddFriendBtn.setBackgroundResource(isShowGameState ? R.drawable.add_friend_btn_bg_short : R.drawable.add_friend_btn_bg_mid);
                    mAddFriendBtn.getLayoutParams().width = isShowGameState ? ScreenUtil.dip2px(75) : ViewGroup.LayoutParams.MATCH_PARENT;
                }

                if (mAddFriendTv != null) {
                    mAddFriendTv.setVisibility(isShowGameState ? View.GONE : View.VISIBLE);
                }

                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mAddFriendLayout.getLayoutParams();
                layoutParams.weight = isShowGameState ? 2 : 3;
            } else {
                if (mAddFriendBtn != null) {
                    mAddFriendBtn.setBackgroundResource(R.drawable.add_friend_btn_bg_mid);
                    mAddFriendBtn.getLayoutParams().width = ViewGroup.LayoutParams.MATCH_PARENT;
                }

                if (mAddFriendTv != null) {
                    mAddFriendTv.setVisibility(View.VISIBLE);
                }

                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mAddFriendLayout.getLayoutParams();
                layoutParams.weight = 3;
            }
        }

        if (mSendFlowerLayout != null && View.VISIBLE == mSendFlowerLayout.getVisibility()) {
            if (mSendFlowerBtn != null) {
                mSendFlowerBtn.setBackgroundResource(isShowGameState ? R.drawable.send_flower_btn_bg_short : R.drawable.send_flower_btn_bg_mid);
                mSendFlowerBtn.getLayoutParams().width = isShowGameState ? ScreenUtil.dip2px(75) : ViewGroup.LayoutParams.MATCH_PARENT;
            }

            if (mSendFlowerTv != null) {
                mSendFlowerTv.setVisibility(isShowGameState ? View.GONE : View.VISIBLE);
            }

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mSendFlowerLayout.getLayoutParams();
            layoutParams.weight = isShowGameState ? 2 : 3;
        }

        if (mSendMsgLayout != null && View.VISIBLE == mSendMsgLayout.getVisibility()) {
            if (mSendMsgBtn != null) {
                mSendMsgBtn.setBackgroundResource(isShowGameState ? R.drawable.send_msg_btn_bg_short : R.drawable.add_friend_btn_bg_mid);
                mSendMsgBtn.getLayoutParams().width = isShowGameState ? ScreenUtil.dip2px(75) : ViewGroup.LayoutParams.MATCH_PARENT;
            }

            if (mSendMsgTv != null) {
                mSendMsgTv.setVisibility(View.VISIBLE);
                mSendMsgTv.setCompoundDrawablePadding(isShowGameState ? 0 : ScreenUtil.dip2px(4F));
                mSendMsgTv.setText(isShowGameState ? "" : ResUtil.getStr(R.string.activity_userinfo_detail_3));
            }

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mSendMsgLayout.getLayoutParams();
            layoutParams.weight = isShowGameState ? 2 : 3;
        }
    }

    private void showSendGiftFirstDialog() {
        DialogBuild.newBuilder(this).setSingleBtn(true).setColorStateList(ResUtil.getColorStateList(R.color.sel_accent_ex_accent_gray)).setContent(getString(R.string.send_gift_long_text_des1)).setSureTx(getString(R.string.i_kown)).setDialogCallback(() -> sendGiftLogic()).show();
    }

    private void sendGiftLogic() {
        if (fromType == TYPE_VOICE_ROOM && VoiceRoomService.getInstance().getRoomInfo(roomId).rid == roomId && isNeedReturnVoiceRoom) {
            JumpUtil.gotoVoiceRoomActivityAndSendGift(mContext, roomId, mUid);
        } else if (fromType == TYPE_GROUP) {
            JumpUtil.gotoGroupChatActivityAndSendGift(mContext, groupId, mUid);
        } else {
            GiftAnimUtil.showGiftView(mContext, GiftShowConfigHelper.userHome(mUid, false), ChatUtil::sendGiftMsg);
        }
    }

    private void checkExtraOp() {
        if (extraOpType == IntentConfig.OP_TYPE_OPEN_GIFT_CARD) {
            GiftAnimUtil.showGiftView(this, GiftShowConfigHelper.userHome(mUid, true), ChatUtil::sendGiftMsg);
        }
    }

    private void showAddFriendDialog(final User user) {
        final String source;
        if (TextUtils.isEmpty(jumpSource)) {
            source = TrackSource.DEFAULT;
        } else {
            source = jumpSource;
        }

        int scene = AddFriendInfo.SCENE_NORMAL;
        if (fromType == TYPE_FIX_ROOM || fromType == TYPE_GAME_ROOM || fromType == TYPE_COCOS_GAME) {
            scene = AddFriendInfo.SCENE_GAME;
        } else if (fromType == TYPE_VOICE_ROOM) {
            scene = AddFriendInfo.SCENE_VOICE_ROOM;
        }
        AddFriendInfo info = AddFriendInfo.newBuilder().setTargetUid(user.uid).setSource(source)
                .setScreenName(TrackScreenName.SELF_HOMEPAGE)
                .setGameType(gameType).setScene(scene).putExtData(jsonJumpExt);
        Object subSource = jsonJumpExt.get("sub_source");
        if (subSource instanceof String) {
            info.setSubSource((String) subSource);
        }
        AddFriendManager.getInstance().addFriend(this, info, new AddFriendCallback() {
            @Override
            public void onSuccess(String msg) {
                refresh(user);
                DialogUtil.showAddFriendSuccessDialog(mContext, null);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private Unit showUserHomeAnim(int homeAnimId) {
        HomeAnimExtra animInfo = getHomeAnimInfo(homeAnimId);
        HLog.d(TAG, HLog.USR, "showUserHomeAnim! animInfo=" + animInfo);
        if (animInfo != null) {
            if (animInfo.isLottie()) {
                HomeLottieUtil.loadUserHomeAnim(animView, animInfo);
            } else if (animInfo.isAlphaVideo()) {
                showAlphaVideoAnim(animInfo);
            } else if (animInfo.isVap()) {
                showVapVideoAnim(animInfo);
            } else {
                HLog.d(TAG, HLog.USR, "showUserHomeAnim error! ,animInfo=" + animInfo);
            }
        }
        return Unit.INSTANCE;
    }

    private void showAlphaVideoAnim(final HomeAnimExtra extra) {
        glAnimView = initGlAnimView(extra);
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) glAnimView.getLayoutParams();
        if (extra.getAdaptType() == HomeAnimExtra.TOP_ALIGN) {
            params.gravity = Gravity.TOP;
        } else if (extra.getAdaptType() == HomeAnimExtra.BOTTOM_ALIGN) {
            params.gravity = Gravity.BOTTOM;
        } else {
            params.gravity = Gravity.CENTER_VERTICAL;
        }
        glAnimView.setLayoutParams(params);

        videoFilePath = extra.getLocalPath();
        File file = new File(videoFilePath);
        if (file.exists() && file.length() > 0) {
            playAlphaVideoAnim(videoFilePath, extra.autoMirror);
        } else {
            DownloadUtil.downloadFileWithRetry(extra.getAnimationUrl(), videoFilePath, 3, true, new LifeDownloadCallback(this) {
                @Override
                public void onSuccess(String url, String path) {
                    playAlphaVideoAnim(videoFilePath, extra.autoMirror);
                }

                @Override
                public void onFail(String msg) {

                }

                @Override
                public void onPercent(int percent) {

                }
            });
        }
    }

    private void playAlphaVideoAnim(String fileName, boolean autoMirror) {
        glAnimView.setVisibility(View.VISIBLE);
        if (isVisible) {
            final VideoSprite videoSprite = new VideoSprite(glAnimView, fileName, UserInfoDetailActivity.this);
            videoSprite.setLoop(true);
            videoSprite.setIsRtl(ScreenUtil.isRtl() & autoMirror);
            glAnimView.addSprite(videoSprite);
        }
    }

    private LifecycleAnimView initVapView(final HomeAnimExtra extra) {
        LifecycleAnimView lifecycleAnimView = new LifecycleAnimView(this);
        int w = ScreenUtil.getScreenWidth();
        int h = (int) (w * extra.getVideoRatio());
        getLifecycle().addObserver(lifecycleAnimView.getAdapter());
        ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(w, h);
        ((ViewGroup) findViewById(R.id.user_info_anim_lay)).addView(lifecycleAnimView, lp);
        return lifecycleAnimView;
    }

    private void showVapVideoAnim(final HomeAnimExtra extra) {
        vapView = initVapView(extra);
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) vapView.getLayoutParams();
        if (extra.getAdaptType() == HomeAnimExtra.TOP_ALIGN) {
            params.gravity = Gravity.TOP;
        } else if (extra.getAdaptType() == HomeAnimExtra.BOTTOM_ALIGN) {
            params.gravity = Gravity.BOTTOM;
        } else {
            params.gravity = Gravity.CENTER_VERTICAL;
        }
        vapView.setLayoutParams(params);

        videoFilePath = extra.getLocalPath();
        File file = new File(videoFilePath);
        if (file.exists() && file.length() > 0) {
            playVapVideoAnim(videoFilePath, extra.autoMirror);
        } else {
            lifeRegister.destroy();
            DownLoadFile downLoadFile = new DownLoadFile(lifeRegister, Downloader.newBuilder().setUrl(extra.getAnimationUrl()).setCacheFilePath(videoFilePath).setRetryTimes(3).setShouldDownloadImmediate(true));
            downLoadFile.getData(bean -> {
                if (bean.isSuccess()) {
                    playVapVideoAnim(downLoadFile.getSavePath(), extra.autoMirror);
                } else {
                    HLog.d(TAG, HLog.USR, "showVapVideoAnim onFail ,msg=" + downLoadFile.getFailMsg());
                }
            });
        }
    }

    private void playVapVideoAnim(String fileName, boolean autoMirror) {
        vapView.setVisibility(View.VISIBLE);
        if (autoMirror) {
            vapView.setScaleX(ResUtil.getInteger(R.integer.image_scale_x));
        }
        vapView.setScaleType(ScaleType.FIT_XY);
        vapView.supportOldVideo(false);
        vapView.setLoop(Integer.MAX_VALUE);
        vapView.startPlay(new File(fileName));
    }

    @Nullable
    private HomeAnimExtra getHomeAnimInfo(int id) {
        PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(id);
        if (item != null) {
            return item.getHomeAnimExtra();
        }
        return null;
    }

    //更新新游戏中的按钮状态
    private void updateGameState() {
        final GameState gameState = GameStateManager.getInstance().getGameState(mUid);
        if (gameState == null) {
            return;
        }
        String gameNameText = ConfigHelper.getInstance().getShortGameName(gameState.game_type);
        if (TextUtils.isEmpty(gameNameText)) {
            gameStateInfoLay.setVisibility(View.GONE);

            //如果送礼和发消息按钮当前在显示则添加这两个按钮的文字
            refreshBottomBtnBg(false);
            return;
        }
        gameStateInfoLay.setVisibility(View.VISIBLE);
        if (GameConfig.isJackarooVip(gameState.game_type, gameState.gameMode)) {
            gameStateInfoTv.setText(R.string.jackaroo_vip_room_playing);
        } else {
            String stateText = ResUtil.getStr(R.string.tab_msg_conversation_item_content_playing, gameNameText);
            gameStateInfoTv.setText(stateText);
        }
        Drawable gameStateIcon = ResUtil.getDrawable(gameState.lock || MarryUtil.showCpRoomLock(mUid, gameState.game_type) ? R.drawable.user_detail_game_lock_icon : R.drawable.user_detail_game_playing_icon);
        gameStateIcon.setBounds(0, 0, gameStateIcon.getIntrinsicWidth(), gameStateIcon.getIntrinsicHeight());
        gameStateInfoTv.setCompoundDrawables(gameStateIcon, null, null, null);
        gameStateInfoLay.setOnClickListener(v -> {
            gotoRoom(gameState.rid, mUid, gameState.game_type);
            ShenceUtil.followClickReport(TrackScreenName.SELF_HOMEPAGE, gameState.game_type, mUid);
        });

        //如果送礼和发消息按钮当前在显示则清掉这两个按钮的文字
        refreshBottomBtnBg(true);
    }

    private void gotoRoom(int rid, int targetUid, int game_type) {
        EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(mContext, rid, game_type).setFollowUid(targetUid).setSource(TrackSource.DEFAULT);
        JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
        EventDispatcher.postChatGameInviteCancelEvent();
    }

    private void updateMatchState() {
        final MatchState state = MatchStateManager.getInstance().getMatchState(mUid);
        if (state != null) {
            String gameNameText = ConfigHelper.getInstance().getShortGameName(state.game_type);
            if (TextUtils.isEmpty(gameNameText)) {
                gameStateInfoLay.setVisibility(View.GONE);
            } else {
                gameStateInfoLay.setVisibility(View.VISIBLE);
                if (GameConfig.isJackarooVip(state.game_type, state.gameMode)) {
                    gameStateInfoTv.setText(R.string.jackaroo_vip_team_waiting);
                } else {
                    String stateText = ResUtil.getStr(R.string.user_info_joining, gameNameText);
                    gameStateInfoTv.setText(stateText);
                }
                Drawable gameStateIcon = ResUtil.getDrawable(R.drawable.user_detail_game_playing_icon);
                gameStateIcon.setBounds(0, 0, gameStateIcon.getIntrinsicWidth(), gameStateIcon.getIntrinsicHeight());
                gameStateInfoTv.setCompoundDrawables(gameStateIcon, null, null, null);
                gameStateInfoLay.setOnClickListener(view -> {
                    boolean isVip = GameConfig.isJackarooVip(state.game_type, state.gameMode);
                    JumpCocosTeamInfo jumpCocosTeamInfo = JumpCocosTeamInfo.build(mContext).setTid(state.tid)
                            .setGameType(state.game_type).setIsVip(isVip)
                            .setTargetUid(mUid).setScene(LittleGameTrackUtilsKt.SCENE_USER_INFO_DETAIL);
                    if (isVip) {
                        jumpCocosTeamInfo.setEnterVipReferScreenName(TrackButtonName.FOLLOW_VIP_GAME_ROOM);
                    }
                    JumpCocosTeamUtil.jumpToCocosGame(jumpCocosTeamInfo, CocosLaunchInfo.ENTER_MODE_ENTER_TO_COCOS);
                });
            }
        }

        //更新底部送礼物和发消息按钮UI
        refreshBottomBtnBg(View.VISIBLE == gameStateInfoLay.getVisibility());
    }

    private void showUserPlayingIfNeed() {
        if (!GameStatusCheckManager.isInGame()) {
            ArrayList<Integer> uidList = new ArrayList<>(1);
            uidList.add(mUid);
            ChatPacketSender.chatRqMsgSyncUserGameState(uidList, null);
            GameMatchSender.getMatchStateReq(uidList, new LifeSeqCallback(this) {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    if (head.message == null) {
                        return;
                    }
                    GameMatchPackets.GetMatchStateRsp getMatchStateRsp = (GameMatchPackets.GetMatchStateRsp) head.message;
                    MatchState.parseList(getMatchStateRsp.getMatchStateListList());
                    updateMatchState();
                }

                @Override
                public void onFail(RspHeadInfo head) {

                }
            });
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == ActivityResultCode.CARE_MAIN_REQUEST_CODE) {//可能在守护界面加了好友，回来刷新界面
            showUserInfo();
        }
    }

    @Override
    public void onBackPressed() {
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        carAnimHelper.destroy();
        lifeRegister.destroy();
        animView.setImageDrawable(null);
        if (glAnimView != null) {
            glAnimView.removeAllSprite();
        }
    }

    private SpriteGLView initGlAnimView(final HomeAnimExtra extra) {
        SpriteGLView glView = new SpriteGLView(this);
        int w = ScreenUtil.getScreenWidth();
        int h = (int) (w * extra.getVideoRatio());
        ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(w, h);
        ((ViewGroup) findViewById(R.id.user_info_anim_lay)).addView(glView, lp);
        return glView;
    }

    @Override
    protected void filterStartup() {
        InitializerManagerUtils.wait(InitializerManager.FILTER_FOUR);
    }
}
