package com.wepie.wespy.module.contact.detail;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.ui.empty.HWUIEmptyView;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.model.entity.Discover;
import com.wepie.wespy.module.fdiscover.main.CircleAdapter;

import java.util.ArrayList;

public class UserCircleView extends RelativeLayout {
    private Context mContext;
    private SmartRefreshLayout smartRefreshLayout;
    private RecyclerView mRecyclerView;
    private CircleAdapter adapter;
    private UserCirclePresenter presenter;
    private ProgressDialogUtil progressDialogUtil;
    private HWUIEmptyView emptyView;

    public UserCircleView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public UserCircleView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.circle_main_view, this);
        presenter = new UserCirclePresenter(this);
        emptyView = findViewById(R.id.circle_empty_view);
        smartRefreshLayout = findViewById(R.id.circle_main_refresher);
        smartRefreshLayout.setEnableAutoLoadMore(false);
        smartRefreshLayout.setEnableLoadMoreWhenContentNotFull(false);
        smartRefreshLayout.setReboundDuration(150);//回弹动画时长

        smartRefreshLayout.setOnRefreshListener(refreshlayout -> presenter.onPullDown());
        smartRefreshLayout.setOnLoadMoreListener(refreshlayout -> presenter.onPullUp());

        mRecyclerView = (RecyclerView) findViewById(R.id.circle_main_recyclerview);
        adapter = new CircleAdapter(mContext, "my_circle");
        mRecyclerView.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false));
        mRecyclerView.setAdapter(adapter);

        progressDialogUtil = new ProgressDialogUtil();
        emptyView.setVisibility(View.GONE);
    }

    public void init(int uid) {
        presenter.initData(uid);
    }

    public void setCurUid(int uid) {
        presenter.setCurUid(uid);
    }

    public void clear() {
        presenter.clear();
    }

    public void finishRefresh() {
        smartRefreshLayout.finishRefresh(0);
    }

    public void finishLoadMore() {
        smartRefreshLayout.finishLoadMore(0);
    }

    public void refreshAdapter(ArrayList<Discover> discovers) {
        adapter.refresh(discovers);
        if (discovers.isEmpty()) {
            emptyView.setType(HWUIEmptyView.base_empty_no_posted);
            emptyView.setVisibility(View.VISIBLE);
        } else {
            emptyView.setVisibility(View.GONE);
        }
    }

    public void refreshMore(ArrayList<Discover> discovers) {
        adapter.refreshMore(discovers);
        finishLoadMore();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        presenter.onAttach();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        presenter.onDetach();
    }

    public void showLoading() {
        progressDialogUtil.showLoadingDelay(mContext);
    }

    public void hideLoading() {
        progressDialogUtil.hideLoading();
    }


}
