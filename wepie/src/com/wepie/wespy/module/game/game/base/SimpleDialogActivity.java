package com.wepie.wespy.module.game.game.base;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseDialogActivity;
import com.huiwan.lib.api.DataCallback;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.libcaptcha.CaptchaCloseType;
import com.wepie.libcaptcha.WpCaptchaListener;
import com.wepie.libcaptcha.YiDunCaptchaManager;
import com.wepie.liblog.LogUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogActivity;

import org.json.JSONObject;

public class SimpleDialogActivity extends BaseDialogActivity implements DialogActivity, WpCaptchaListener {
    private static final String TAG = "SimpleDialogActivity";

    private ConstraintLayout rootLay;
    private TextView sureTv;
    private TextView cancelTv;
    private SimpleDialogBuilder builder;
    private SimpleDialogManager.SimpleDialogCallback callback;

    public final static String DIALOG_ID = "dialog_id";
    public final static String DIALOG_SET = "dialog_set";


    private String dialogId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_simple_dialog);
        rootLay = findViewById(R.id.root_cl);
        dialogId = getIntent().getStringExtra(DIALOG_ID);
        initData();
        initView();
        addListener();
    }

    private void initData() {
        if (TextUtil.isEmpty(dialogId)) {
            ToastUtil.debugShow("dialogId empty");
            finish();
        } else {
            builder = SimpleDialogManager.getInstance().getBuilder(dialogId);
            callback = SimpleDialogManager.getInstance().getCallback(dialogId);
            SimpleDialogManager.getInstance().clearAll();
            if (builder == null) {
                ToastUtil.debugShow("set build construct param first builder");
                finish();
            } else if (builder.isShowCaptchaDialog()) {
                String setting = getIntent().getStringExtra(DIALOG_SET);
                if (setting == null) {
                    setting = "";
                }
                YiDunCaptchaManager.showCaptchaDialog(this, setting);
            }
        }
        HLog.d(TAG, HLog.USR, "initData dialogId={}, builder={}", dialogId, builder);
    }

    private void initView() {
        if (builder == null) {
            return;
        }

        sureTv = findViewById(R.id.id_tip_password_sure_btn);
        cancelTv = findViewById(R.id.id_tip_password_cancel_btn);
        TextView titleTv = findViewById(R.id.dialog_title_tv);
        TextView contentTv = findViewById(R.id.dialog_content_tv);
        sureTv.setText(builder.getSure());
        cancelTv.setText(builder.getCancel());
        titleTv.setText(builder.getTitle());
        contentTv.setText(builder.getContent());
        if (!builder.isHasTitle()) {
            titleTv.setVisibility(View.GONE);
        }
        if (!builder.isDoubleBtn()) {
            cancelTv.setVisibility(View.GONE);
        }
        LinearLayout inputLay = findViewById(R.id.input_ll);
        inputLay.setOnClickListener(v -> {
        });

        if (builder.isShowCaptchaDialog()) {
            inputLay.setVisibility(View.GONE);
        } else {
            inputLay.setVisibility(View.VISIBLE);
        }
    }


    private void addListener() {
        if (builder == null) {
            return;
        }

        sureTv.setOnClickListener(view -> {
            if (callback != null) {
                callback.onSure(getActivity());
            }
            finish();
        });
        cancelTv.setOnClickListener(view -> {
            if (callback != null) {
                callback.onCancel();
            }
            finish();
        });

        rootLay.setOnClickListener(v -> finish());
    }

    private Activity getActivity() {
        return this;
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void onValidate(@NonNull String captchaId, @NonNull String result, @NonNull String validate, @NonNull String msg) {
        HLog.d(TAG + "RiskCaptcha", HLog.USR, "onValidate, Result={}, validate={}, msg={}", result, validate, msg);
        LogUtil.upload(AliNetLogUtil.PORT.base, AliNetLogUtil.TYPE.err, "RiskCaptcha onValidate Result :" + result + " validate = " + validate + " msg = " + msg);
        if (result.equalsIgnoreCase("false")) {
            return;
        }

        DataCallback<String> captchaCb = builder.getCaptchaCallBack();
        if (captchaCb != null) {
            JSONObject riskVerificationObj = new JSONObject();
            try {
                riskVerificationObj.put("captcha_id", captchaId);
                riskVerificationObj.put("validate", validate);
            } catch (Exception e) {
                HLog.d(TAG + "RiskCaptcha", HLog.USR, "dealShowCaptchaDialog error, e={}", e);
            }
            captchaCb.onCall(riskVerificationObj.toString());
        }
    }

    @Override
    public void onError(int code, @NonNull String msg) {
        HLog.d(TAG + "RiskCaptcha", HLog.USR, "onError, code={}, msg={}", code, msg);
        LogUtil.upload(AliNetLogUtil.PORT.base, AliNetLogUtil.TYPE.err, "RiskCaptcha onError Result code :" + code + " msg = " + msg);
        DataCallback<String> captchaCb = builder.getCaptchaCallBack();
        if (captchaCb != null) {
            captchaCb.onCall(null);
        }
    }

    @Override
    public void onClose(@NonNull CaptchaCloseType closeType) {
        HLog.d(TAG + "RiskCaptcha", HLog.USR, "onClose, closeType={}", closeType);
        if (closeType == CaptchaCloseType.USER_CLOSE || closeType == CaptchaCloseType.VERIFY_SUCCESS_CLOSE) {
            YiDunCaptchaManager.INSTANCE.clear();
            DataCallback<String> captchaCb = builder.getCaptchaCallBack();
            if (captchaCb instanceof YiDunDataCallback) {
                ((YiDunDataCallback<String>) captchaCb).onClose(closeType);
            }
            finish();
        }
    }
}
