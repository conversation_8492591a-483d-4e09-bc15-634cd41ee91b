package com.wepie.wespy.module.game.room.roomcreate.typeselect

import android.annotation.SuppressLint
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.ktx.applyBold
import com.huiwan.base.str.ResUtil
import com.huiwan.constants.GameType
import com.huiwan.store.PrefUserUtil
import com.huiwan.widget.inflate
import com.wejoy.weplay.ex.view.updateColor
import com.wepie.wespy.R
import com.wepie.wespy.module.abtest.AbTestManager
import com.wepie.wespy.module.game.room.roomcreate.RoomTypeInfoUtils
import com.wepie.wespy.module.game.room.roomcreate.typeselect.RoomTypeAdapter.RoomTypeListHolder


/**
 * @author: luo<PERSON><PERSON><PERSON>
 * @date: 2024/06/17 12:02
 */
class RoomTypeAdapter(private var selectGameType: Int = GameType.GAME_TYPE_VOICE_ROOM) :
    RecyclerView.Adapter<RoomTypeListHolder>() {
    // 作为刷新时的payloads
    private val SELECT_TYPE_CHANGED = 0X1

    private val roomTypeInfoList = ArrayList<RoomTypeItemData>()

    @SuppressLint("NotifyDataSetChanged")
    fun refreshData(list: ArrayList<RoomTypeItemData>) {
        roomTypeInfoList.clear()
        roomTypeInfoList.addAll(list)
        notifyDataSetChanged()
    }


    var changeCallback: ((newGameType: Int) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RoomTypeListHolder {
        val view: View = parent.inflate(R.layout.create_room_type_item, false)
        return RoomTypeListHolder(view)
    }

    override fun getItemCount(): Int = roomTypeInfoList.size

    override fun onBindViewHolder(holder: RoomTypeListHolder, position: Int) {
        if (position < roomTypeInfoList.size) {
            val curData = roomTypeInfoList[position]
            holder.initView(selectGameType, curData)

            holder.itemView.setOnClickListener {
                selectGameType = curData.gameType
                changeCallback?.invoke(selectGameType)
                notifyItemRangeChanged(0, roomTypeInfoList.size, SELECT_TYPE_CHANGED)
            }
        }
    }

    override fun onBindViewHolder(
        holder: RoomTypeListHolder, position: Int, payloads: List<Any>
    ) {
        var flag = 0
        for (i in payloads.indices) {
            if (payloads[i] is Int) {
                flag = flag or payloads[i] as Int
            }
        }
        if (flag == SELECT_TYPE_CHANGED) {
            if (position < roomTypeInfoList.size) {
                val curData = roomTypeInfoList[position]
                holder.updateSelectView(selectGameType, curData)
            }
        } else {
            onBindViewHolder(holder, position)
        }
    }

    class RoomTypeListHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val typeIcon = itemView.findViewById<ImageView>(R.id.room_type_iv)
        private val typeText = itemView.findViewById<TextView>(R.id.room_type_tv)
        private val typeTips = itemView.findViewById<TextView>(R.id.room_type_tips)

        fun initView(selectGameType: Int, curData: RoomTypeItemData) {
            itemView.setBackgroundResource(curData.itemBg)
            typeText.text = curData.typeName
            typeIcon.setImageResource(curData.itemIcon)
            updateSelectView(selectGameType, curData)
            if (GameType.isAuctionRoom(curData.gameType) || GameType.isGuessSongRoom(curData.gameType)) {
                updateNewTips(
                    curData.updateLocalData, curData.gameType,
                    RoomTypeInfoUtils.getTipsName(curData.gameType)
                )
            } else {
                typeTips.visibility = View.GONE
            }
        }

        fun updateSelectView(selectGameType: Int, curData: RoomTypeItemData) {
            if (selectGameType == curData.gameType) {
                itemView.isSelected = true
                typeText.setTextColor(curData.textColor)
                typeText.applyBold(true)
            } else {
                itemView.isSelected = false
                typeText.updateColor(ResUtil.getColor(R.color.color_text_accent_dark))
                typeText.applyBold(false)
            }
        }

        // 同步华语服拍拍房、猜歌、小游戏自建房NEW标签
        private fun updateNewTips(updateLocalData: Boolean, gameType: Int, tipsName: String) {
            if (GameType.isGuessSongRoom(gameType) && !AbTestManager.getInstance()
                    .canShowGuessSong()
            ) return
            val hasShowNewTips = PrefUserUtil.getInstance().getBoolean(tipsName, false)
            if (!hasShowNewTips) {
                typeTips.visibility = View.VISIBLE
                if (updateLocalData) {
                    PrefUserUtil.getInstance().setBoolean(tipsName, true)
                }
            } else {
                typeTips.visibility = View.GONE
            }
        }
    }

    /**
     * @param textColor 房间类型名称的字体颜色
     * @param itemBg item的背景
     * @param itemIcon 类型的icon
     * @param typeName 类型的名称
     * @param gameType 服务器返回的房间游戏类型
     * @param updateLocalData 是否更新本地new信息（拍拍、猜歌）
     */
    data class RoomTypeItemData(
        val textColor: Int,
        @DrawableRes val itemBg: Int,
        @DrawableRes val itemIcon: Int,
        val typeName: String,
        val gameType: Int,
        val updateLocalData: Boolean = false
    )
}
