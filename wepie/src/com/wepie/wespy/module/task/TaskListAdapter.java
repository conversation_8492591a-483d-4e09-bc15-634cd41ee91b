package com.wepie.wespy.module.task;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TouchEffectUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.widget.image.DrawableUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.task.TaskListInfo;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.task.view.TaskPagerAdapter;
import com.wepie.wespy.module.task.view.TaskRewardItemView;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class TaskListAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final int FOOTER = 1;
    private static final int HEADER = 2;
    private static final String PAYLOAD_TITLE_KEY = "payload_title_key";
    private static final String PAYLOAD_DESC_KEY = "payload_desc_key";
    private static final String PAYLOAD_REWARD_KEY = "payload_reward_key";

    private final Context context;
    private final List<TaskListInfo.TaskInfo> taskInfos = new ArrayList<>();
    private final RefreshCallback callback;
    private final PopuDescCallback descCallback;
    private int taskType = 0;

    private boolean needHeader;

    private String note;

    public TaskListAdapter(Context context, RefreshCallback callback, PopuDescCallback descCallback) {
        this.context = context;
        this.callback = callback;
        this.descCallback = descCallback;
    }

    public void setNeedHeader(boolean needHeader) {
        this.needHeader = needHeader;
    }

    public void updateTask(List<TaskListInfo.TaskInfo> newTaskList) {
        if (!newTaskList.isEmpty() && newTaskList.size() == taskInfos.size()) {
            updateWithDiff(newTaskList);
        } else {
            updateAll(newTaskList);
        }
    }

    public void updateWithDiff(List<TaskListInfo.TaskInfo> newTaskList) {
        DiffUtil.DiffResult result = getDiffResult(taskInfos, newTaskList);
        taskInfos.clear();
        taskInfos.addAll(newTaskList);
        result.dispatchUpdatesTo(this);
    }

    public void updateAll(List<TaskListInfo.TaskInfo> newTaskList) {
        taskInfos.clear();
        taskInfos.addAll(newTaskList);
        notifyDataSetChanged();
    }

    public DiffUtil.DiffResult getDiffResult(List<TaskListInfo.TaskInfo> oldDataList, List<TaskListInfo.TaskInfo> newDataList) {
        return DiffUtil.calculateDiff(new DiffUtil.Callback() {
            @Override
            public int getOldListSize() {
                return oldDataList.size();
            }

            @Override
            public int getNewListSize() {
                return newDataList.size();
            }

            @Override
            public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
                return oldDataList.get(oldItemPosition).taskId == newDataList.get(newItemPosition).taskId;
            }

            @Override
            public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
                return oldDataList.get(oldItemPosition) == newDataList.get(newItemPosition);
            }

            @Override
            public Object getChangePayload(int oldItemPosition, int newItemPosition) {
                TaskListInfo.TaskInfo oldItem = oldDataList.get(oldItemPosition);
                TaskListInfo.TaskInfo newItem = newDataList.get(newItemPosition);
                ArrayList<Object> payloads = new ArrayList<>();

                if (!Objects.equals(oldItem.title, newItem.title) || oldItem.current != newItem.current
                        || oldItem.condition != newItem.condition || oldItem.showProgress != newItem.showProgress) {
                    payloads.add(PAYLOAD_TITLE_KEY);
                }

                if (!Objects.equals(oldItem.desc, newItem.desc)) {
                    payloads.add(PAYLOAD_DESC_KEY);
                }

                if (hasSameReward(oldItem.rewardIds, newItem.rewardIds)
                        || hasSameReward(oldItem.rewardValues, newItem.rewardValues)
                        || !Objects.equals(oldItem.jumpUrl, newItem.jumpUrl)
                        || oldItem.rewardStatus != newItem.rewardStatus || oldItem.remainRewardNum != newItem.remainRewardNum) {
                    payloads.add(PAYLOAD_REWARD_KEY);
                }

                return payloads;
            }
        });
    }

    private boolean hasSameReward(List<Integer> oldRewardList, List<Integer> newRewardList) {
        if (oldRewardList == null || newRewardList == null) return false;
        if (oldRewardList.size() != newRewardList.size()) return false;
        for (int i = 0; i < oldRewardList.size(); i++) {
            if (!oldRewardList.get(i).equals(newRewardList.get(i))) return false;
        }
        return true;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void update(List<TaskListInfo.TaskInfo> taskListItems) {
        this.taskInfos.clear();
        this.taskInfos.addAll(taskListItems);
        notifyDataSetChanged();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void setNote(String note) {
        if (this.note == null && !TextUtils.isEmpty(note)) {
            this.note = note;
            notifyDataSetChanged();
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == HEADER) {
            View view = new View(context);
            view.setBackgroundResource(R.color.white_color);
            view.setLayoutParams(new RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ScreenUtil.dip2px(16)));
            return new Header(view);
        }
        if (viewType == FOOTER) {
            return new Footer(LayoutInflater.from(context).inflate(R.layout.task_list_footer, parent, false));
        }
        return new TaskListItem(LayoutInflater.from(context).inflate(R.layout.task_list_item_new, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull final RecyclerView.ViewHolder viewHolder, int position) {
        if (viewHolder instanceof TaskListItem) {
            bindTaskListHolder((TaskListItem) viewHolder, position);
        } else if (viewHolder instanceof Footer) {
            bindFooterHolder((Footer) viewHolder);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull final RecyclerView.ViewHolder viewHolder, int position, @NonNull List<Object> payloads) {
        if (viewHolder instanceof TaskListItem) {
            if (payloads.isEmpty()) {
                bindTaskListHolder((TaskListItem) viewHolder, position);
            } else {
                int actualPosition = needHeader ? position - 1 : position;
                final TaskListInfo.TaskInfo taskInfo = taskInfos.get(actualPosition);
                for (Object payload : payloads) {
                    if (payload instanceof String) {
                        if (PAYLOAD_TITLE_KEY.equals(payload)) {
                            updateTitle(taskInfo, ((TaskListItem) viewHolder).taskTitle);
                        } else if (PAYLOAD_DESC_KEY.equals(payload)) {
                            updateDesc(taskInfo, ((TaskListItem) viewHolder).taskDesc);
                        } else if (PAYLOAD_REWARD_KEY.equals(payload)) {
                            updateRewards(taskInfo, ((TaskListItem) viewHolder).finishedTv, ((TaskListItem) viewHolder).actionTv,
                                    ((TaskListItem) viewHolder).rewardIconLay);
                        }
                    }
                }
            }
        } else if (viewHolder instanceof Footer) {
            bindFooterHolder((Footer) viewHolder);
        }
    }

    private void updateTitle(TaskListInfo.TaskInfo taskInfo, TextView taskTitle) {
        String title = taskInfo.title;
        if (taskInfo.isShowProgress()) {
            title += "(" + taskInfo.current + "/" + taskInfo.condition + ")";
        }
        taskTitle.setText(title);
    }

    private void updateDesc(TaskListInfo.TaskInfo taskInfo, TextView taskDesc) {
        taskDesc.setText(taskInfo.desc);
    }

    private void updateRewards(TaskListInfo.TaskInfo taskInfo, TextView finishedTv, TextView actionTv, FrameLayout rewardIconLay) {
        rewardIconLay.removeAllViews();
        final List<Integer> rewardList = taskInfo.rewardIds;
        final List<Integer> valueList = taskInfo.rewardValues;

        if (rewardList != null && valueList != null) {
            for (int i = 0; i < rewardList.size(); i++) {
                int rewardId = rewardList.get(i);
                PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(rewardId);

                if (propItem != null && i < valueList.size()) {
                    int value = valueList.get(i);
                    TaskRewardItemView taskRewardItemView = new TaskRewardItemView(context);
                    taskRewardItemView.setReward(propItem.getMediaUrl(), value);

                    FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                    layoutParams.setMarginStart(ScreenUtil.dip2px(55) * i);
                    rewardIconLay.addView(taskRewardItemView, layoutParams);

                    descCallback.addOrUpdatePopDesc(taskRewardItemView, rewardId);
                }
            }
        }

        if (taskInfo.isReceived()) {
            finishedTv.setVisibility(View.VISIBLE);
            actionTv.setVisibility(View.GONE);
        } else if (taskInfo.canReceive()) {
            finishedTv.setVisibility(View.GONE);
            actionTv.setVisibility(View.VISIBLE);
            actionTv.setTextColor(0xff8d1d1d);
            actionTv.setBackground(DrawableUtil.genColorRadius(0xFFFFE821, 100));
            if (taskInfo.remainRewardNum > 1) {
                actionTv.setText(R.string.quick_claim);
            } else {
                actionTv.setText(R.string.claim);
            }
            actionTv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (callback != null) callback.onRecvTask(taskInfo, rewardList, valueList);
                }
            });
        } else {
            finishedTv.setVisibility(View.GONE);
            actionTv.setVisibility(View.VISIBLE);
            if (taskType == TaskPagerAdapter.FAMILY_TASK_PAGE) {
                actionTv.setBackgroundResource(R.drawable.shape_00ccf9_corner16);
            } else {
                actionTv.setBackgroundResource(R.drawable.sel_accent_corner16);
            }
            actionTv.setTextColor(Color.WHITE);
            actionTv.setText(R.string.go);
            actionTv.setOnClickListener(v -> JumpCommon.gotoOtherPager(context, taskInfo.jumpUrl, TrackSource.TASK, null));
        }
    }

    private void bindFooterHolder(@NonNull Footer viewHolder) {
        if (!TextUtils.isEmpty(note)) {
            viewHolder.note.setText(note);
        }
    }

    public void setTaskType(int taskType) {
        this.taskType = taskType;
    }

    private void bindTaskListHolder(@NonNull TaskListItem viewHolder, int position) {
        int actualPosition = needHeader ? position - 1 : position;
        final TaskListInfo.TaskInfo taskInfo = taskInfos.get(actualPosition);
        updateTitle(taskInfo, viewHolder.taskTitle);
        updateDesc(taskInfo, viewHolder.taskDesc);
        updateRewards(taskInfo, viewHolder.finishedTv, viewHolder.actionTv, viewHolder.rewardIconLay);
    }

    @Override
    public int getItemCount() {
        int extraItemCount = 0;
        if (needHeader) extraItemCount++;
        if (note != null) extraItemCount++;

        return taskInfos.size() + extraItemCount;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0 && needHeader) {
            return HEADER;
        }
        if (note != null && position == getItemCount() - 1) {
            return FOOTER;
        }
        return super.getItemViewType(position);
    }

    static class TaskListItem extends RecyclerView.ViewHolder {
        TextView taskTitle;
        TextView taskDesc;
        FrameLayout rewardIconLay;
        TextView actionTv;
        TextView finishedTv;

        public TaskListItem(@NonNull View itemView) {
            super(itemView);
            taskTitle = itemView.findViewById(R.id.task_title);
            taskDesc = itemView.findViewById(R.id.task_desc);
            rewardIconLay = itemView.findViewById(R.id.reward_lay);
            actionTv = itemView.findViewById(R.id.action_tv);
            finishedTv = itemView.findViewById(R.id.task_finished_tv);

            TouchEffectUtil.addTouchEffect(actionTv);
        }
    }

    static class Header extends RecyclerView.ViewHolder {
        public Header(@NonNull View itemView) {
            super(itemView);
        }
    }

    static class Footer extends RecyclerView.ViewHolder {
        TextView note;

        public Footer(@NonNull View itemView) {
            super(itemView);
            note = itemView.findViewById(R.id.footer_note);
        }
    }

    public interface RefreshCallback {
        void onRecvTask(TaskListInfo.TaskInfo taskInfo, List<Integer> rewardList, List<Integer> valueList);
    }

    public interface PopuDescCallback {

        void addOrUpdatePopDesc(TaskRewardItemView taskRewardItemView, int rewardId);
    }
}
