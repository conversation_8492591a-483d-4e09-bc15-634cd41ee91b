package com.wepie.wespy.module.task.view.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.PrivilegeConfig;
import com.huiwan.configservice.model.PropItem;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFragCb;
import com.wepie.wespy.helper.dialog.androidx.BaseDialog;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.media.sound.SoundUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2018/11/29
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class RewardDialog extends BaseDialog {
    private static final String RECEIVE_IDS = "task_receive_ids";
    private static final String RECEIVE_NUM = "task_receive_num";
    private static final String VIP_RECEIVE_IDS = "vip_receive_ids";
    private static final String VIP_RECEIVE_NUM = "vip_receive_num";
    private static final String VIP_WELFARE = "vip_welfare";
    public static final String IS_TREASURE_DIALOG = "is_treasure_dialog";
    public static final String SURE_BTN_COLOR = "sure_btn_color";
    public static final String CONTENT_TEXT_COLOR = "content_text_color";
    private LayoutInflater inflater;
    private LinearLayout itemContainer;
    private BaseFragCb<Object> cb;
    private TextView contentDesc;
    private LinearLayout mainBgLayout;
    private View okBtn;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        this.inflater = inflater;
        View v = inflater.inflate(R.layout.task_receive_dialog_view, container, false);
        itemContainer = v.findViewById(R.id.receive_items_lay);
        contentDesc = v.findViewById(R.id.jump_vip_tv);
        mainBgLayout = v.findViewById(R.id.main_bg_layout);
        okBtn = v.findViewById(R.id.sure_btn);
        okBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (cb != null) {
                    cb.onClickSure(new Object());
                }
                dismissAllowingStateLoss();
            }
        });
        contentDesc.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Activity activity = getActivity();
                if (activity == null) return;
                JumpUtil.gotoVipMainActivity(activity, TrackScreenName.SIGN_IN_SUCCESS_DIALOG);
            }
        });
        checkShowData();
        return v;
    }

    private void checkShowData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            List<Integer> receiveIds = bundle.getIntegerArrayList(RECEIVE_IDS);
            List<Integer> receiveReward = bundle.getIntegerArrayList(RECEIVE_NUM);
            List<Integer> vipRewardIds = bundle.getIntegerArrayList(VIP_RECEIVE_IDS);
            List<Integer> vipRewardNum = bundle.getIntegerArrayList(VIP_RECEIVE_NUM);
            boolean hasVipWelfare = bundle.getBoolean(VIP_WELFARE, false);
            if (receiveIds != null && receiveReward != null && receiveIds.size() == receiveReward.size()) {
                showExtras(receiveIds, receiveReward, false);
            }

            if (vipRewardIds != null && vipRewardNum != null && vipRewardIds.size() == vipRewardNum.size()) {
                showExtras(vipRewardIds, vipRewardNum, true);
            }
            boolean isTreasureDialog = bundle.getBoolean(IS_TREASURE_DIALOG, false);

            PrivilegeConfig privilegeConfig = ConfigHelper.getInstance().getVipConfig().getPrivilegeById(PrivilegeConfig.PRIVILEGE_SIGN_IN);
            if (privilegeConfig != null && hasVipWelfare) {
                contentDesc.setVisibility(View.VISIBLE);
                contentDesc.setText(privilegeConfig.getCheckInRewardDialogDesc());
            } else if (isTreasureDialog) {
                contentDesc.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                contentDesc.setVisibility(View.VISIBLE);
                contentDesc.setText(R.string.gift_get_success_msg);
            } else {
                contentDesc.setVisibility(View.GONE);
            }

            int sureButtonColor = bundle.getInt(SURE_BTN_COLOR, 0);
            if (sureButtonColor != 0) {
                GradientDrawable drawable = (GradientDrawable) okBtn.getBackground();
                drawable.setColor(sureButtonColor);
            }
            String contentTextColor = bundle.getString(CONTENT_TEXT_COLOR, "");
            if (!TextUtils.isEmpty(contentTextColor)) {
                contentDesc.setTextColor(Color.parseColor(contentTextColor));
            }
            SoundUtil.getInstance().playSound(SoundUtil.TYPE_RECEIVE_REWARD);

        }
    }

    private void showExtras(List<Integer> ids, List<Integer> numbers, boolean isVipReward) {
        int size = ids.size();
        for (int i = 0; i < size; i++) {
            PropItem info = ConfigHelper.getInstance().getPropConfig().getPropItem(ids.get(i));
            if (info != null) {
                showExtraItem(info, numbers.get(i), isVipReward);
            }
        }
    }

    public static void showDialog(Context context, List<Integer> rewardIds, List<Integer> rewardValues, BaseFragCb<Object> cb) {
        showDialog(context, rewardIds, rewardValues, new ArrayList<Integer>(), new ArrayList<Integer>(), false, cb);
    }

    private void updateNumTxt(TextView textView, String text) {
        float width = textView.getPaint().measureText(text);
        if (width > ScreenUtil.dip2px(52)) {
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10);
        }
        textView.setText(text);
    }

    private void updateNameTxt(TextView textView, String text) {
        float width = textView.getPaint().measureText(text);
        if (width > ScreenUtil.dip2px(60)) {
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10);
        }
        textView.setText(text);
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (cb != null) {
            cb.onDismiss();
        }
    }

    public static void showDialog(Context context, List<Integer> rewardIds, List<Integer> rewardValues, BaseFragCb<Object> cb, Bundle bundle) {
        showDialog(context, rewardIds, rewardValues, new ArrayList<Integer>(), new ArrayList<Integer>(), false, cb, bundle);
    }

    public static void showDialog(Context context, List<Integer> rewardIds, List<Integer> rewardValues, List<Integer> vipRewardIds, List<Integer> vipRewardValues, boolean hasVipWelfare, BaseFragCb<Object> cb) {
        showDialog(context, rewardIds, rewardValues, vipRewardIds, vipRewardValues, hasVipWelfare, cb, null);
    }

    private static void showDialog(Context context, List<Integer> rewardIds, List<Integer> rewardValues, List<Integer> vipRewardIds, List<Integer> vipRewardValues, boolean hasVipWelfare, BaseFragCb<Object> cb, Bundle bundle) {
        RewardDialog dialog = new RewardDialog();
        if (null == bundle) {
            bundle = new Bundle();
        }
        bundle.putIntegerArrayList(RECEIVE_IDS, new ArrayList<>(rewardIds));
        bundle.putIntegerArrayList(RECEIVE_NUM, new ArrayList<>(rewardValues));
        bundle.putIntegerArrayList(VIP_RECEIVE_IDS, new ArrayList<>(vipRewardIds));
        bundle.putIntegerArrayList(VIP_RECEIVE_NUM, new ArrayList<>(vipRewardValues));
        bundle.putBoolean(VIP_WELFARE, hasVipWelfare);
        dialog.cb = cb;
        dialog.setArguments(bundle);
        dialog.setStyle(STYLE_NO_FRAME, R.style.dialog_style_custom);
        dialog.show(context, RewardDialog.class.getSimpleName());
    }

    private void showExtraItem(PropItem info, int num, boolean isVipReward) {
        View v = inflater.inflate(R.layout.task_receive_item_view, itemContainer, false);
        String numTx;
        int type = info.getType();
        if (type == PropItem.TYPE_HOME_ANIM || type == PropItem.TYPE_DRAW_BOARD ||
                type == PropItem.TYPE_HEAD_DECORATION || info.getItemId() == PropItem.PROP_ID_VISITORS) {
            numTx = ResUtil.getStr(R.string.task_receive_dialog_view_2, ResUtil.getStr(R.string.x_number, num));
        } else {
            numTx = ResUtil.getStr(R.string.x_number, num);
        }
        updateNumTxt(((TextView) v.findViewById(R.id.receive_num)), numTx);
        if (isVipReward) {
            updateNameTxt(((TextView) v.findViewById(R.id.receive_item_name)), getString(R.string.task_receive_dialog_view_3));
        } else {
            updateNameTxt(((TextView) v.findViewById(R.id.receive_item_name)), info.getName());
        }
        ImageLoaderUtil.loadNormalImage(info.getMediaUrl(), (ImageView) v.findViewById(R.id.receive_res), ImageLoadInfo.getGiftInfo());
        itemContainer.addView(v);
    }
}
