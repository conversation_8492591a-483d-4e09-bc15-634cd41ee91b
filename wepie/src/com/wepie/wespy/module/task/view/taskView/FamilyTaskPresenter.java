package com.wepie.wespy.module.task.view.taskView;

import android.content.Context;
import android.view.View;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.family.FamilyRewardList;
import com.wepie.wespy.model.entity.family.FamilySimpleInfo;
import com.wepie.wespy.model.entity.task.TaskListInfo;
import com.wepie.wespy.model.event.family.FamilyMainRefreshEvent;
import com.wepie.wespy.module.common.jump.JumpCommon;
import com.wepie.wespy.module.family.dialogs.FamilyActiveAwardDialog;
import com.wepie.wespy.module.family.main.mine.task.FamilyNewcomerMeetingGiftDialog;
import com.wepie.wespy.module.shop.dialogs.PropInfoConfig;
import com.wepie.wespy.module.shop.dialogs.PropInfoDialog;
import com.wepie.wespy.module.task.DetailTaskInfo;
import com.wepie.wespy.net.http.api.FamilyApi;
import com.wepie.wespy.net.http.api.TaskApi;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class FamilyTaskPresenter {
    private final FamilyTaskView taskView;

    public FamilyTaskPresenter(FamilyTaskView tabTaskView) {
        this.taskView = tabTaskView;
    }

    public void register() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    public void unregister() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    public void init() {
        refreshList();
        refreshBaseInfo();
    }

    public void getFamilyInfo() {
        FamilyApi.getInfo(new LifeDataCallback<FamilyMainInfo>(taskView) {
            @Override
            public void onSuccess(Result<FamilyMainInfo> result) {
                taskView.updateFamilyTipsVisibleState(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    public void refreshBaseInfo() {
        FamilyApi.simpleInfo(new LifeDataCallback<FamilySimpleInfo>(taskView) {
            @Override
            public void onSuccess(Result<FamilySimpleInfo> result) {
                taskView.refreshMyFamilyActive(result.data);
            }

            @Override
            public void onFail(int i, String s) {

            }
        });
    }

    public void refreshList() {
        TaskApi.getTaskListNew(TaskListInfo.FAMILY_TASK, new LifeDataCallback<TaskListInfo>(taskView) {
            @Override
            public void onSuccess(Result<TaskListInfo> result) {
                taskView.updateTaskList(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    private void requestActiveReward(final Context context, final TaskListInfo.ActiveStageInfo activeStageInfo) {
        TaskApi.recvStage(activeStageInfo.stageId, TaskListInfo.FAMILY_TASK, new LifeDataCallback<Object>(taskView) {
            @Override
            public void onSuccess(Result<Object> result) {
                FamilyActiveAwardDialog.showAward(context,
                        FamilyRewardList.formData(activeStageInfo.rewardIds, activeStageInfo.rewardValues).getDataList(), true);
                refreshList();
                refreshBaseInfo();
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    public void clickActiveItem(Context mContext, TaskListInfo.ActiveStageInfo activeStageInfo) {
        if (activeStageInfo.canReceive()) {
            requestActiveReward(mContext, activeStageInfo);
        } else if (activeStageInfo.isOpen()) {
            ToastUtil.show(ResUtil.getStr(R.string.family_task_activity_already_get_tips));
        } else {
            FamilyActiveAwardDialog.showPreview(mContext,
                    FamilyRewardList.formData(activeStageInfo.rewardIds, activeStageInfo.rewardValues).getDataList(),
                    activeStageInfo.needActive, true);
        }
    }

    public void onRecvTask(final TaskListInfo.TaskInfo taskInfo, final List<Integer> rewardList, final List<Integer> valueList) {
        TaskApi.recvTask(taskInfo.taskId, new LifeDataCallback<Object>(taskView) {
            @Override
            public void onSuccess(Result<Object> result) {
                if (rewardList != null && valueList != null) {
                    List<Integer> totalRewardValues = new ArrayList<>(valueList.size());
                    for (Integer value : valueList) {
                        int totalValue = value * taskInfo.remainRewardNum;
                        totalRewardValues.add(totalValue);
                    }

                    FamilyActiveAwardDialog.showAward(taskView.getContext(),
                            FamilyRewardList.formData(rewardList, totalRewardValues).getDataList(), true);
                }
                refreshList();
                refreshBaseInfo();
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    public void gotoOtherPager(Context context, String jumpUrl) {
        JumpCommon.gotoOtherPager(context, jumpUrl, TrackButtonName.TASK, null);
    }

    public void onRecvTask(final DetailTaskInfo taskInfo, boolean isMeetingGiftTask) {
        TaskApi.recvTask(taskInfo.getTaskId(), new LifeDataCallback<Object>(taskView) {
            @Override
            public void onSuccess(Result<Object> result) {
                List<Integer> valueList = taskInfo.getRewardValues();
                List<Integer> totalRewardValues = new ArrayList<>(valueList.size());
                for (Integer value : valueList) {
                    int totalValue = value * taskInfo.getRemainRewardNum();
                    totalRewardValues.add(totalValue);
                }
                if (isMeetingGiftTask && taskView.getContext() != null) {
                    FamilyNewcomerMeetingGiftDialog.show(taskView.getContext(), FamilyNewcomerMeetingGiftDialog.rewardsFrom(taskInfo, totalRewardValues));
                } else if (null != taskView.getContext()) {
                    FamilyActiveAwardDialog.showAward(taskView.getContext(),
                            FamilyRewardList.formData(taskInfo.getRewardIds(), totalRewardValues).getDataList(), true);
                }
                refreshList();
                refreshBaseInfo();
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handlerFamilyInfoChange(FamilyMainRefreshEvent event) {
        refreshList();
        refreshBaseInfo();
    }

    public void showRewardPreviewDialog(View v, int rewardId) {
        PropInfoConfig config = PropInfoConfig.fromOutOfShop(rewardId);
//        config.setPreview(true);
        PropInfoDialog.show(v.getContext(), config, null);
    }
}
