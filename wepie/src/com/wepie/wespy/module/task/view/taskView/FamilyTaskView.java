package com.wepie.wespy.module.task.view.taskView;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.ConcatAdapter;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.IFunctionShieldApi;
import com.huiwan.user.LoginHelper;
import com.huiwan.widget.ViewUtilKtKt;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.redDotHelper.RedDotNode;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.family.FamilySimpleInfo;
import com.wepie.wespy.model.entity.task.TaskListInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.family.FamilyUiConst;
import com.wepie.wespy.module.family.main.mine.family.FamilyActiveItem;
import com.wepie.wespy.module.family.main.mine.family.FamilyActiveValueView;
import com.wepie.wespy.module.family.main.mine.task.FamilyTaskActiveCallback;
import com.wepie.wespy.module.family.main.mine.task.ITaskDetailOperate;
import com.wepie.wespy.module.task.DetailTaskInfo;
import com.wepie.wespy.module.task.FamilyMeetingTaskInfoKt;
import com.wepie.wespy.module.task.FamilyNewcomerTaskAdapter;
import com.wepie.wespy.module.task.TaskListAdapter;
import com.wepie.wespy.module.task.view.TaskPagerAdapter;
import com.wepie.wespy.module.task.view.TaskViewNew;
import com.wepie.wespy.module.task.view.popup.TaskPopupDescriptionView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-10-25.
 */
public class FamilyTaskView extends RelativeLayout implements TaskOperator {

    private Context mContext;
    private FamilyActiveValueView activeValueView;
    private TextView activeTv;
    private TextView familyTaskRefreshTv;
    private List<FamilyActiveItem> familyActiveItemList = new ArrayList<>();
    private FamilyTaskActiveCallback activeCallback;
    private DecorHeadImgView headImgView;
    private TextView upgradeTipsTv;
    private TextView fundsTv, coinTv;
    private RecyclerView familyTaskRv;
    private TaskListAdapter adapter;
    private FamilyNewcomerTaskAdapter familyNewcomerTaskAdapter;
    private TaskPopupDescriptionView descView;
    private ConstraintLayout joinFamilyTipsLay;
    private TaskViewNew.FamilyRedDotCallback familyRedDotCallback;
    private FamilyTaskPresenter presenter;
    private ImageView familyTipsIv;

    public FamilyTaskView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public FamilyTaskView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.family_task_view, this);
        descView = findViewById(R.id.family_task_desc_popup);
        activeValueView = findViewById(R.id.active_value_view);
        activeTv = findViewById(R.id.active_value_tv);
        familyTaskRefreshTv = findViewById(R.id.family_task_refresh_tv);
        familyActiveItemList.add(new FamilyActiveItem(findViewById(R.id.active_item1)));
        familyActiveItemList.add(new FamilyActiveItem(findViewById(R.id.active_item2)));
        familyActiveItemList.add(new FamilyActiveItem(findViewById(R.id.active_item3)));
        headImgView = findViewById(R.id.avatar_iv);
        upgradeTipsTv = findViewById(R.id.upgrade_tips_tv);
        fundsTv = findViewById(R.id.family_funds_tv);
        coinTv = findViewById(R.id.family_coin_tv);
        joinFamilyTipsLay = findViewById(R.id.join_family_tips_lay);
        joinFamilyTipsLay.setOnClickListener(v -> {
        });
        activeValueView.setTaskUi();
        activeValueView.refresh(0.0f);
        familyTipsIv = findViewById(R.id.family_tips_iv);

        familyTaskRv = findViewById(R.id.family_task_rv);
        familyTaskRv.setNestedScrollingEnabled(false);
        familyTaskRv.setLayoutManager(new LinearLayoutManager(mContext));
        if (descView == null) descView = new TaskPopupDescriptionView(mContext);
        presenter = new FamilyTaskPresenter(this);
        adapter = new TaskListAdapter(mContext, (taskInfo, rewardList, valueList) -> presenter.onRecvTask(taskInfo, rewardList, valueList), descView::addOrUpdatePopDesc);
        adapter.setTaskType(TaskPagerAdapter.FAMILY_TASK_PAGE);
        familyNewcomerTaskAdapter = new FamilyNewcomerTaskAdapter(new ITaskDetailOperate() {
            @Override
            public void onJumpOtherPage(@NonNull Context context, @NonNull String jumpUrl) {
                presenter.gotoOtherPager(context, jumpUrl);
            }

            @Override
            public void onClaimReward(@NonNull DetailTaskInfo task, boolean isMeetingGiftTask) {
                presenter.onRecvTask(task, isMeetingGiftTask);
            }

            @Override
            public void addOrUpdatePopupRewardDetail(@NonNull View v, int rewardId) {
                descView.addOrUpdatePopDesc(v, rewardId);
            }

            @Override
            public void onClickMeetingGiftReward(@NonNull View v, int rewardId) {
                presenter.showRewardPreviewDialog(v, rewardId);
            }
        });
        familyTaskRv.setAdapter(new ConcatAdapter(familyNewcomerTaskAdapter, adapter));
        presenter.refreshList();
        presenter.refreshBaseInfo();
        presenter.getFamilyInfo();
        setClickableListener();
        ImageView familyActionBarBg = findViewById(R.id.family_action_bar_bg);
        RelativeLayout actionBar = findViewById(R.id.action_bar);
        TaskViewCommonFunction.updateActionBarLp(familyActionBarBg, actionBar);
        familyTaskRefreshTv.setText(ResUtil.getStr(R.string.family_task_activity_refresh_tips, ApiService.of(IFunctionShieldApi.class).getTaskRefreshTimeZone()));
    }

    public void updateFamilyTipsVisibleState(FamilyMainInfo familyMainInfo) {
        if (familyMainInfo.isHasFamily()) {
            joinFamilyTipsLay.setVisibility(GONE);
        } else {
            joinFamilyTipsLay.setVisibility(VISIBLE);
            ViewUtilKtKt.fixTo375dp(familyTipsIv);
            WpImageLoader.load(ConfigHelper.getInstance().getConstConfig().familyIntroduction.familyTaskPage, familyTipsIv);
        }
    }

    private void setClickableListener() {
        familyTipsIv.setOnClickListener(v -> {
            JumpUtil.gotoFamilyMainActivity(mContext);
            TrackUtil.appClick(TrackScreenName.TASK_FAMILY_TASK, TrackButtonName.JOIN_FAMILY);
        });
    }

    public void updateTaskList(TaskListInfo taskListInfo) {
        adapter.update(taskListInfo.getFamilyTaskInfo());
        familyNewcomerTaskAdapter.refresh(FamilyMeetingTaskInfoKt.loadFamilyNewcomerTaskInfoFrom(taskListInfo));
        refreshActiveData(taskListInfo, activeStageInfo -> presenter.clickActiveItem(mContext, activeStageInfo));
        refreshTips(taskListInfo.note);
    }

    @SuppressLint("SetTextI18n")
    public void refreshActiveData(TaskListInfo taskListInfo, final FamilyTaskActiveCallback callback) {
        List<TaskListInfo.ActiveStageInfo> dataList = taskListInfo.activeStageInfos;
        this.activeCallback = callback;

        for (int i = 0; i < familyActiveItemList.size(); i++) {
            final FamilyActiveItem familyActiveItem = familyActiveItemList.get(i);
            if (i < dataList.size()) {
                final TaskListInfo.ActiveStageInfo activeStageInfo = dataList.get(i);
                final float rate = activeStageInfo.needActive * 1f / taskListInfo.getMaxActive();
                familyActiveItem.refresh(activeStageInfo, i, true, false, new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (activeCallback != null) activeCallback.clickItem(activeStageInfo);
                    }
                });
                post(new Runnable() {
                    @Override
                    public void run() {
                        int width = (int) (activeValueView.getWidth() * rate - familyActiveItem.getRootView().getWidth() / 2 + ScreenUtil.dip2px(20));
                        ViewUtil.setLeftMargins(familyActiveItem.getRootView(), width);
                    }
                });
            }
        }

        activeValueView.refresh(taskListInfo.active * 1f / taskListInfo.getMaxActive());
        LoginHelper.getHeadUrl();
        headImgView.updateDecoration(LoginHelper.getHeadUrl(), 0);
        if (familyRedDotCallback != null) {
            boolean showFamilyRedDot = taskListInfo.hasFamilyTaskRedDot();
            familyRedDotCallback.showFamilyRedDot(showFamilyRedDot);
            // 家族那边的红点同步去掉
            if (!showFamilyRedDot && RedDotUtil.get().hasRedDot(RedDotNode.NODE_D_F_TASK)) {
                RedDotUtil.get().reportRead(RedDotInfo.RED_DOT_FAMILY_TASK);
            }
        }
    }

    public void refreshTips(String tip) {
        if (TextUtils.isEmpty(tip)) {
            upgradeTipsTv.setVisibility(GONE);
        } else {
            upgradeTipsTv.setText(tip);
            upgradeTipsTv.setVisibility(VISIBLE);
        }
    }

    public void refreshMyFamilyActive(FamilySimpleInfo simpleInfo) {
        activeTv.setText(String.valueOf(simpleInfo.getMemberDailyActiveScore()));
        coinTv.setText(String.valueOf(simpleInfo.getFamilyCoin()));
        fundsTv.setText(FamilyUiConst.getFundsText(simpleInfo.getFunds()));
    }

    public void setFamilyRedDotCallback(TaskViewNew.FamilyRedDotCallback familyRedDotCallback) {
        this.familyRedDotCallback = familyRedDotCallback;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        presenter.register();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        clearAnim();
        presenter.unregister();
    }

    private void clearAnim() {
        for (FamilyActiveItem familyActiveItem : familyActiveItemList) {
            familyActiveItem.clearAnim();
        }
    }

    @Override
    public void bind(TaskListInfo taskListInfo) {
    }

    @Override
    public void initView() {
    }
}