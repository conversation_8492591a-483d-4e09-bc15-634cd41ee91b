package com.wepie.wespy.module.task.share_task;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.lib.api.ApiService;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.User;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.share.IShareApi;
import com.wepie.lib.api.plugins.share.ShareCallback;
import com.wepie.lib.api.plugins.share.ShareInfo;
import com.wepie.lib.api.plugins.share.ShareType;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.task.ShareCodeTaskInfo;
import com.wepie.wespy.net.http.api.TaskApi;

import java.util.Locale;

/**
 * Created by bigwen on 2016/5/23.
 */
public class ShareEarnCoinActivity extends BaseActivity {

    private TextView urlTv;
    private TextView todayCoin;
    private TextView totalCoin;
    private RelativeLayout shareBt;
    private String TAG = "ShareEarnCoinActivity";
    private String shareCode = "";
    private TextView shareExplainTv;
    private String share_url;
    private BaseWpActionBar actionBar;

    public static final String SHARE_CODE_CONNECT = "share/invite?code=";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_share_earn_coin);
        init();
        initData();
        initShareUrl();
    }

    private void initData() {
        TaskApi.getShareCodeTaskInfo(new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<ShareCodeTaskInfo> result) {
                setData(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    private void setData(ShareCodeTaskInfo shareCodeTaskInfo) {
        todayCoin.setText(ResUtil.getStr(R.string.activity_share_earn_coin_today_coin_x, shareCodeTaskInfo.today));
        totalCoin.setText(ResUtil.getStr(R.string.activity_share_earn_coin_total_coin_x, shareCodeTaskInfo.total));
    }

    private void initShareUrl() {
        User user = LoginHelper.getLoginUser();
        if (user != null) {
            shareCode = user.uid2code();
            shareCode = shareCode.toLowerCase(Locale.US);
        }
        String domain = ConfigHelper.getInstance().getMyShareUrl();
        share_url = domain + SHARE_CODE_CONNECT + shareCode;
        urlTv.setText(share_url);
    }

    private void init() {
        actionBar = findViewById(R.id.action_bar);
        urlTv = (TextView) findViewById(R.id.activity_share_earn_coin_myurl);
        todayCoin = (TextView) findViewById(R.id.activity_share_earn_coin_today_coin);
        totalCoin = (TextView) findViewById(R.id.activity_share_earn_coin_total_coin);
        shareBt = (RelativeLayout) findViewById(R.id.activity_share_earn_coin_url_copy);
        shareExplainTv = (TextView) findViewById(R.id.activity_share_earn_coin_explainb);
        shareExplainTv.setText(ConfigHelper.getInstance().getTaskShareDec());

        actionBar.addTitleAndBack(R.string.activity_share_earn_coin_title);

        shareBt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showInviteDialog();
            }
        });
    }

    private void showInviteDialog() {
        final String share_title = ConfigHelper.getInstance().getMyShareTitle();
        final String share_desc = ConfigHelper.getInstance().getMyShareDesc();
        final String share_icon = ConfigHelper.getInstance().getShareIconUrl();
        final String share_url = this.share_url;

        ShareInfo shareInfo = new ShareInfo();
        shareInfo.setTitle(share_title);
        shareInfo.setContent(share_desc);
        shareInfo.setLink(shareInfo.getLinkIntercept(share_url));
        shareInfo.setBitmapPath(share_icon);

        //添加分享的打点信息
        shareInfo.setShareContentType(ShareInfo.SHARE_CONTENT_TYPE_H5);
        shareInfo.screenName = TrackScreenName.SHARE_PAGE;
        shareInfo.scene = TrackString.SCENE_INVITE_FRIEND;
        shareInfo.gameType = -1;

        shareInfo.addTripartiteShareType();
        shareInfo.addShareTypes(ShareType.android, ShareType.copyLink);

        ApiService.of(IShareApi.class).showShareDialog(this, shareInfo, new ShareCallback() {
        });
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
    }
}
