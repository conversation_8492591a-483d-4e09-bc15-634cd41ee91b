package com.wepie.wespy.module.task

import com.wepie.wespy.model.entity.task.TaskListInfo
import com.wepie.wespy.module.family.main.mine.task.TaskRewardStatus


data class FamilyMeetingTaskInfo(
    val totalTaskCount: Int = 0,
    val finishedTaskCount: Int = 0,
    val meetingBenefitIntroContent: String = "",
    val meetingTaskInfo: DetailTaskInfo = DetailTaskInfo(),
    val taskDetails: List<TaskInfo> = emptyList()
)

sealed interface TaskInfo

data class DetailTaskInfo(
    val rewardStatus: TaskRewardStatus = TaskRewardStatus.NOT_FINISH,
    val taskId: Int = 0,
    val jumpUrl: String = "",
    val title: String = "",
    val desc: String = "",
    val remainRewardNum: Int = 0,
    val rewardIds: List<Int> = emptyList(),
    val rewardValues: List<Int> = emptyList()
) : TaskInfo

data object AllTaskFinish : TaskInfo
data object TodayTaskFinish : TaskInfo

private fun TaskListInfo.TaskInfo.toTaskDetailIfo(): DetailTaskInfo {
    return DetailTaskInfo(
        rewardStatus = TaskRewardStatus.from(rewardStatus),
        taskId = taskId,
        jumpUrl = jumpUrl,
        title = title,
        desc = desc,
        remainRewardNum = remainRewardNum,
        rewardIds = rewardIds,
        rewardValues = rewardValues,
    )
}

fun loadFamilyNewcomerTaskInfoFrom(taskListInfo: TaskListInfo): List<FamilyMeetingTaskInfo> {
    val familyMeetingTaskInfo = taskListInfo.familyNewcomerGiftTask ?: return emptyList()
    familyMeetingTaskInfo.meetingGiftTask ?: return emptyList()
    val familyNewComerTaskDetails =
        taskListInfo.taskTypeInfos.firstOrNull { it.taskType == TaskListInfo.FAMILY_NEWCOMER_BENEFIT_TASK }?.taskList
            ?: emptyList()
    val detailTaskInfo = familyNewComerTaskDetails.map { it.toTaskDetailIfo() }
    val taskInfos = if (familyMeetingTaskInfo.finishedCount >= familyMeetingTaskInfo.total) {
        listOf(AllTaskFinish)
    } else if (detailTaskInfo.all { it.rewardStatus == TaskRewardStatus.RECEIVED_REWARD }) {
        listOf(TodayTaskFinish)
    } else detailTaskInfo
    return ArrayList<FamilyMeetingTaskInfo>().also {
        it.add(
            FamilyMeetingTaskInfo(
                totalTaskCount = familyMeetingTaskInfo.total,
                finishedTaskCount = familyMeetingTaskInfo.finishedCount,
                meetingBenefitIntroContent = familyMeetingTaskInfo.meetingBenefitIntroDesc,
                meetingTaskInfo = familyMeetingTaskInfo.meetingGiftTask?.toTaskDetailIfo()
                    ?: DetailTaskInfo(),
                taskDetails = taskInfos
            )
        )
    }
}