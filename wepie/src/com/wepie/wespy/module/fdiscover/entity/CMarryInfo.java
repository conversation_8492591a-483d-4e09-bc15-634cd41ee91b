package com.wepie.wespy.module.fdiscover.entity;

import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

public class CMarryInfo {
    @SerializedName("marry_id")
    public int marry_id;
    @SerializedName("ring_type")
    public int ring_type;
    @SerializedName("room_name")
    public String room_name;
    @SerializedName("to_friends")
    public String to_friends;
    @SerializedName("start_time")
    public long start_time;
    @SerializedName("uid")
    public int uid = 0;
    @SerializedName("nickname")
    public String nickname = "";
    @SerializedName("headimgurl")
    public String headimgurl = "";
    @SerializedName("gender")
    public int gender = 0;
    @SerializedName("mate_uid")
    public int mate_uid = 0;
    @SerializedName("mate_nickname")
    public String mate_nickname = "";
    @SerializedName("mate_headimgurl")
    public String mate_headimgurl = "";
    @SerializedName("mate_gender")
    public int mate_gender = 0;

    public static CMarryInfo parseFromJson(JsonObject json) {
        CMarryInfo marry = new CMarryInfo();
        try {
            if (json.has("marry_id")) marry.marry_id = json.get("marry_id").getAsInt();
            if (json.has("ring_type")) marry.ring_type = json.get("ring_type").getAsInt();
            if (json.has("room_name")) marry.room_name = json.get("room_name").getAsString();
            if (json.has("to_friends")) marry.to_friends = json.get("to_friends").getAsString();
            if (json.has("start_time")) marry.start_time = json.get("start_time").getAsLong();

            if (json.has("uid")) marry.uid = json.get("uid").getAsInt();
            if (json.has("nickname")) marry.nickname = json.get("nickname").getAsString();
            if (json.has("headimgurl")) marry.headimgurl = json.get("headimgurl").getAsString();
            if (json.has("gender")) marry.gender = json.get("gender").getAsInt();
            if (json.has("mate_uid")) marry.mate_uid = json.get("mate_uid").getAsInt();
            if (json.has("mate_nickname"))
                marry.mate_nickname = json.get("mate_nickname").getAsString();
            if (json.has("mate_headimgurl"))
                marry.mate_headimgurl = json.get("mate_headimgurl").getAsString();
            if (json.has("mate_gender")) marry.mate_gender = json.get("mate_gender").getAsInt();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return marry;
    }
}
