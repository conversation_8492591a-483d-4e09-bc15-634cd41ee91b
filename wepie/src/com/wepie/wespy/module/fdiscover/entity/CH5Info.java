package com.wepie.wespy.module.fdiscover.entity;

import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class CH5Info implements Serializable {
    @SerializedName("title")
    private String title;
    @SerializedName("desc")
    private String desc;
    @SerializedName("icon")
    private String icon;
    @SerializedName("link")
    private String link;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    @Override
    public String toString() {
        return "CH5Info{" +
                "title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                ", icon='" + icon + '\'' +
                ", link='" + link + '\'' +
                '}';
    }

    public static CH5Info parseFromJson(JsonObject json){
        CH5Info h5Info = new CH5Info();
        try {
            if (json.has("h5_info") && json.get("h5_info").isJsonObject()){
                JsonObject jsonObject = json.getAsJsonObject("h5_info");
                if (jsonObject == null) return h5Info;

                if (jsonObject.has("title") && jsonObject.get("title").isJsonPrimitive()) {
                    h5Info.setTitle(jsonObject.get("title").getAsString());
                }
                if (jsonObject.has("desc") && jsonObject.get("desc").isJsonPrimitive()) {
                    h5Info.setDesc(jsonObject.get("desc").getAsString());
                }
                if (jsonObject.has("icon") && jsonObject.get("icon").isJsonPrimitive()) {
                    h5Info.setIcon(jsonObject.get("icon").getAsString());
                }
                if (jsonObject.has("link") && jsonObject.get("link").isJsonPrimitive()) {
                    h5Info.setLink(jsonObject.get("link").getAsString());
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }

        return h5Info;
    }
}
