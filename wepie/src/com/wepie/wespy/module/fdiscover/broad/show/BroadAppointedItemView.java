package com.wepie.wespy.module.fdiscover.broad.show;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.VocSongInfoShare;
import com.wepie.wespy.model.entity.broad.BroadInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.fdiscover.view.VocShareItem;
import com.huiwan.base.util.TimeUtil;

import java.util.Calendar;

/**
 * date 2018/3/9
 * email <EMAIL>
 *
 * <AUTHOR>
 */

public class BroadAppointedItemView extends LinearLayout {

    private final TextView timeTv;
    private final TextView contentTv;
    private final VocShareItem vocShareItem;
    private final View editIcon;

    public BroadAppointedItemView(@NonNull Context context){
        super(context);
        LayoutInflater.from(context).inflate(R.layout.broadcast_appointed_item_view, this);
        setLayoutParams(new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));

        timeTv = findViewById(R.id.broadcast_appointed_item_appoint_time_tv);
        contentTv = findViewById(R.id.broadcast_appointed_item_content_tv);
        vocShareItem = findViewById(R.id.broadcast_appointed_item_voc_item);
        editIcon = findViewById(R.id.broadcast_appointed_item_edit_iv);
    }

    public void setFromBroadInfo(BroadInfo broadInfo){
        setFromData(broadInfo.broadcast_id, broadInfo.appointment_time, broadInfo.appointment_end_time,
                broadInfo.message, broadInfo.getSongShareInfo());
    }

    public void setFromData(final int broadId, final long appointTime, long endTime,
                            @NonNull final String content, @Nullable final VocSongInfoShare songInfoShare){
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(appointTime);
        Calendar cEnd = Calendar.getInstance();
        cEnd.setTimeInMillis(endTime);

        int dayTime = TimeUtil.getDayTime(appointTime, System.currentTimeMillis());
        String day;
        if (dayTime == 0) {
            day = ResUtil.getStr(R.string.today);
        } else if (dayTime == 1) {
            day = ResUtil.getStr(R.string.tomorrow);
        } else {
            day = formatDay(c);
        }

        timeTv.setText(ResUtil.getStr(R.string.broad_appointed_time, day, formatHourMinute(c), formatHourMinute(cEnd)));
        contentTv.setText(content);

        if (songInfoShare == null){
            vocShareItem.setVisibility(GONE);
        } else {
            vocShareItem.setVisibility(VISIBLE);
            vocShareItem.update(songInfoShare);
        }
        editIcon.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                JumpUtil.gotoEditBroadActivity(getContext(), broadId, appointTime,
                        songInfoShare == null ? 0 : songInfoShare.song_id, content);
            }
        });
    }

    private String formatDay(Calendar c){
        return String.format(LibBaseUtil.getTimeFormatLocal(), "%d-%02d-%02d", c.get(Calendar.YEAR), c.get(Calendar.MONTH) + 1, c.get(Calendar.DATE));
    }

    private String formatHourMinute(Calendar c){
        return String.format(LibBaseUtil.getTimeFormatLocal(), "%02d:%02d", c.get(Calendar.HOUR_OF_DAY), c.get(Calendar.MINUTE));
    }

}
