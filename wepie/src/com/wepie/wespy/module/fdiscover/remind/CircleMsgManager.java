package com.wepie.wespy.module.fdiscover.remind;

import android.text.TextUtils;

import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.LifeDataCallbackProxy;
import com.three.http.callback.Result;
import com.huiwan.store.PrefConfig;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.helper.broadcast.BroadcastHelper;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.store.database.WPStore;
import com.wepie.wespy.model.entity.DiscoverReminds;
import com.wepie.wespy.model.entity.FriendCircleRemind;
import com.huiwan.store.database.WPModel;
import com.wepie.wespy.net.http.api.DiscoverApi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/4/8.
 * email <EMAIL>
 */
public class CircleMsgManager {
    private static final long REFRESH_INTERVAL = 3000;
    private static CircleMsgManager sInstance;
    private static final long RESUME_REFRESH_INTERVAL = 10 * 60 * 1000;

    private List<FriendCircleRemind> remindList = new ArrayList<>();
    private long lastRefreshTime = 0;

    public static CircleMsgManager getInstance(){
        if(sInstance == null){
            synchronized (CircleMsgManager.class){
                if(sInstance == null){
                    sInstance = new CircleMsgManager();
                }
            }
        }
        return sInstance;
    }

    private CircleMsgManager(){
        String sql = "select * from "+ FriendCircleRemind.TABLE_NAME + " order by "+FriendCircleRemind.TIME+" desc";
        List<WPModel> modelList = WPStore.getInstance().fetchListSync(new FriendCircleRemind(), sql);
        remindList.clear();
        for(WPModel model : modelList) remindList.add((FriendCircleRemind) model);
    }

    public void checkResumeRefresh() {
        if (System.currentTimeMillis() - lastRefreshTime > RESUME_REFRESH_INTERVAL) {
            doRefresh();
            lastRefreshTime = System.currentTimeMillis();
        }
    }

    public void refresh(){
        if (System.currentTimeMillis() - lastRefreshTime > REFRESH_INTERVAL){
            doRefresh();
            lastRefreshTime = System.currentTimeMillis();
        }
    }

    public void forceRefresh(){
        doRefresh();
        lastRefreshTime = System.currentTimeMillis();
    }

    private void doRefresh() {
        DiscoverApi.getRemindData(new LifeDataCallback<DiscoverReminds>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<DiscoverReminds> result) {
                onRemind(result.data.reminds);
            }

            @Override
            public void onFail(int i, String s) {
                HLog.d("Circle", "refresh remind error: " + s);
            }
        });
    }

    public int getSize(){
        return remindList.size();
    }

    public FriendCircleRemind getItem(int index){
        if(index >= remindList.size()) return null;
        return remindList.get(index);
    }

    private void onRemind(List<FriendCircleRemind> serverRemindList){
        for (FriendCircleRemind remind:serverRemindList){
            remind.remind_id = remind.discover_id + "" + remind.from_uid + "" + remind.time;
        }
        HashMap<String, FriendCircleRemind> remindMap = new HashMap<>();
        for(FriendCircleRemind remind : remindList){
            remindMap.put(remind.remind_id, remind);
        }
        List<WPModel> newRemindList = new ArrayList<>();
        for(FriendCircleRemind remind : serverRemindList){
            if(!remindMap.containsKey(remind.remind_id)){
                remindList.add(0, remind);
                newRemindList.add(remind);
            }
        }
        if(newRemindList.size() > 0){
            WPStore.getInstance().saveListByTransaction(newRemindList);
        }
        Collections.sort(remindList, new Comparator<FriendCircleRemind>() {
            @Override
            public int compare(FriendCircleRemind lhs, FriendCircleRemind rhs) {
                if(lhs.time == rhs.time){
                    return 0;
                }else{
                    return lhs.time > rhs.time ? -1 : 1;
                }
            }
        });
        addRemindNumber(newRemindList.size());
    }

    public void deleteItem(FriendCircleRemind friendCircleRemind){
        if (!TextUtils.isEmpty(friendCircleRemind.remind_id)){
            remindList.remove(friendCircleRemind);
            WPStore.getInstance().removeSync(friendCircleRemind);
        }
    }

    public void clearAllRemind(){
        remindList.clear();
        WPStore.getInstance().execSql("delete from " + FriendCircleRemind.TABLE_NAME);
        clearRemindNumber();
    }

    public static void clearRemindNumber(){
        PrefUserUtil.getInstance().setInt(PrefConfig.KEY_FRIEND_CIRCLE_REMIND_NUMBER, 0);
        BroadcastHelper.sendCircleRemindNumChange();
    }

    public static int getRemindNumber(){
        return PrefUserUtil.getInstance().getInt(PrefConfig.KEY_FRIEND_CIRCLE_REMIND_NUMBER, 0);
    }

    private void addRemindNumber(int number){
        int remindNumber = getRemindNumber() + number;
        PrefUserUtil.getInstance().setInt(PrefConfig.KEY_FRIEND_CIRCLE_REMIND_NUMBER, remindNumber);
        BroadcastHelper.sendCircleRemindNumChange();
    }

    public List<FriendCircleRemind> getUnReadRemain(){
        int unRead = getRemindNumber();
        List<FriendCircleRemind> reminds = new ArrayList<>();
        if (getSize() < unRead) return reminds;
        for (int i = 0;i < unRead;i++){
            reminds.add(remindList.get(i));
        }
        return reminds;
    }

    public List<FriendCircleRemind> getAllRemain(){
        return remindList;
    }

    public static void clear(){
        sInstance = null;
    }

}
