package com.wepie.wespy.module.match.start;

import android.content.Context;
import android.os.CountDownTimer;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.dialog.DialogCallback;
import com.wepie.wespy.module.firstcharge.util.CountdownUtil;

public class AudioMatchBanDialog extends RelativeLayout {

    private Context context;
    private TextView banSecondsTv;
    private DialogCallback callback;
    private CountDownTimer timer;

    public AudioMatchBanDialog(Context context) {
        super(context);
        this.context = context;
        initView();
    }

    public AudioMatchBanDialog(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        initView();
    }

    public void setCallback(DialogCallback callback) {
        this.callback = callback;
    }

    private void initView() {
        LayoutInflater.from(context).inflate(R.layout.audio_match_ban_view, this);
        banSecondsTv = findViewById(R.id.ban_seconds_tv);
        findViewById(R.id.enter_tv).setOnClickListener(v -> {
            if (callback != null) callback.onCancel();
        });
    }

    public void refreshView(int banSeconds) {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        long deadlineOffset = 1000L * banSeconds;
        timer = new CountDownTimer(deadlineOffset, 1000L) {
            @Override
            public void onTick(long millisUntilFinished) {
                String hh = CountdownUtil.getHour(millisUntilFinished);
                String mm = CountdownUtil.getMinute(millisUntilFinished);
                String ss = CountdownUtil.getSecond(millisUntilFinished);
                String s = ResUtil.getResource().getString(R.string.time_hh_mm_ss, hh, mm, ss);
                banSecondsTv.setText(s);
            }

            @Override
            public void onFinish() {
                if (callback != null) callback.onCancel();
            }
        };
        timer.start();
    }



    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    public static void showDialog(Context context, int banSeconds) {
        AudioMatchBanDialog dialogView = new AudioMatchBanDialog(context);
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(dialogView);
        dialogView.refreshView(banSeconds);
        dialogView.setCallback(new DialogCallback() {
            @Override
            public void onCancel() {
                dialog.dismiss();
            }

            @Override
            public void onEnter() {
            }
        });
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.show();
    }
}
