package com.wepie.wespy.base;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.anim.schedule.Scheduler;
import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.BaseConfig;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.DrawableRecycleUtil;
import com.huiwan.base.util.IMMHelper;
import com.huiwan.base.util.LeakRecyclerRecord;
import com.huiwan.base.util.LeakRefWatcher;
import com.huiwan.base.util.ProcessUtil;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.IGiftApi;
import com.huiwan.configservice.modelAbTest.AbTest;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.ITeenModeApi;
import com.huiwan.lib.api.plugins.IWebProxyApi;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.voiceservice.VoiceManager;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshFooter;
import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.DefaultRefreshFooterCreator;
import com.scwang.smart.refresh.layout.listener.DefaultRefreshHeaderCreator;
import com.three.http.core.HttpDnsConfig;
import com.wejoy.weplay.helper.view.WejoyRefreshHeader;
import com.wepie.deeplinkbase.DeepLinkUtil;
import com.wepie.lib.api.uploader.IUploaderApi;
import com.wepie.lib.api.uploader.UploaderConfig;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.skynet.apm.Apm;
import com.wepie.startup.Initializer;
import com.wepie.startup.InitializerManager;
import com.wepie.wespy.BuildConfig;
import com.wepie.wespy.R;
import com.wepie.wespy.base.appfyers.AppsFlyerUtil;
import com.wepie.wespy.base.startup.AbsInitializer;
import com.wepie.wespy.base.startup.ApiServiceInitializer;
import com.wepie.wespy.base.startup.CustomPushInitializer;
import com.wepie.wespy.base.startup.DeviceInitializer;
import com.wepie.wespy.base.startup.DynInitializer;
import com.wepie.wespy.base.startup.FactoryInitializer;
import com.wepie.wespy.base.startup.FirebaseAnalyticsInitializer;
import com.wepie.wespy.base.startup.FirebaseAppInitializer;
import com.wepie.wespy.base.startup.FirebaseCrashlyticsInitializer;
import com.wepie.wespy.base.startup.GlobalServerBoosterInitializer;
import com.wepie.wespy.base.startup.InitializerParam;
import com.wepie.wespy.base.startup.InitializerUtils;
import com.wepie.wespy.base.startup.LogInitializer;
import com.wepie.wespy.base.startup.LoggedInitializer;
import com.wepie.wespy.base.startup.MainNetworkInitializer;
import com.wepie.wespy.helper.PropItemExtraHelper;
import com.wepie.wespy.helper.view.DefaultLoadMoreFooter;
import com.wepie.wespy.module.abtest.AbTestManager;
import com.wepie.wespy.module.game.game.activity.TextSpanUtil;
import com.wepie.wespy.module.login.launch.InstallReferrer;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.member.VoiceRoomMemberUtil;
import com.wepie.wespy.utils.PackageUtil;
import com.wepie.wespy.utils.RegionImageLoaderHelper;
import com.wepie.wpdd.DeviceIdInitializer;
import com.wepie.wpdd.WpDdUtil;
import com.wp.s.SecService;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.util.Map;

/**
 * Created by bigwen on 2020/11/18.
 * <p>
 * 统一处理第三方sdk初始化逻辑，没有弹隐私弹窗前，不初始化其他SDK
 */
public class SDKInitUtil {

    //正常的初始化
    public static void initInAllProcesses(InitializerManager manager) {
        manager.addInitializer(new ApiServiceInitializer());
        manager.addInitializer(new LogInitializer());
        manager.addInitializer(new DeviceInitializer());
        manager.addInitializer(new DynInitializer());
        manager.addInitializer(new FactoryInitializer());
        manager.addInitializer(new AbsInitializer(InitializerParam.createMain().filterFlag(InitializerManager.FILTER_THREE)
                .tag("SmartRefreshLayout")) {
            @Override
            protected void create(@NonNull Context context) {
                super.create(context);
                SmartRefreshLayout.setDefaultRefreshHeaderCreator(new WejoyRefreshHeaderCreator());
                SmartRefreshLayout.setDefaultRefreshFooterCreator(new WejoyRefreshFooterCreator());
                JKPullToRefreshIndicator.setDefault();
            }
        });
        manager.addInitializer(new CustomPushInitializer());
        manager.addInitializer(new AbsInitializer(InitializerParam.createBackground()
                .filterFlag(InitializerManager.FILTER_TWO)
                .dependencies(ApiServiceInitializer.class)
                .tag("AbTestManagerPreload")) {
            @Override
            protected void create(@NotNull Context context) {
                super.create(context);
                AbTestManager.getInstance();
                AbTestManager.addObserver(abTestConfig -> {
                    AbTest abTest = abTestConfig.abTest;
                    // AbTest在用户登录后刷新配置，新用户注册的场景AliOssUploader取不到AbTest配置，拿到配置后需要更新
                    if (ApiService.isRegistered(IUploaderApi.class)) {
                        UploaderConfig uploaderConfig = new UploaderConfig();
                        uploaderConfig.setUseWebp(abTest.useWebp);
                        uploaderConfig.setCompressLimitSize(abTest.compressLimitSize);
                        ApiService.of(IUploaderApi.class).init(uploaderConfig);
                    }
                    if (ApiService.isRegistered(IWebProxyApi.class)) {
                        ApiService.of(IWebProxyApi.class).setOpen(abTest.proxyH5Request);
                    }
                    HttpDnsConfig.setUseHttpDns(abTest.isUseHttpDns);
                    initLeakWatcher(abTest);
                    ApiService.of(WebApi.class).openMessageQueue(abTest.getIntFromSwitch("webview_open_message_queue_rate"));
                });
                LeakRefWatcher.registerLeakCallback(new LeakRefWatcher.ILeakCallback() {
                    @Override
                    public void onLeak(@NonNull Object any) {
                        SDKInitUtil.onLeak(any);
                    }

                    @Override
                    public void onRecycleTooLate(@NonNull Object any) {
                        SDKInitUtil.onRecycleTooLate(any);
                    }
                });
                RegionImageLoaderHelper.init();
            }
        });
        manager.addInitializer(new AbsInitializer(InitializerParam.createMain().filterFlag(InitializerManager.FILTER_FOUR)
                .tag("initInAllProcesses1")) {
            @Override
            public void create(@NonNull Context context) {
                VoiceManager.getInstance().setOnJoinChannelListener(new OnJoinChannel());
                GiftAnimUtil.setGiftApi(ApiService.of(IGiftApi.class));
//                FixRoomStateManager.getInstance().setOperator(new FixRoomOperatorImpl());
                observerForAnimScheduler();
            }
        });
    }

    public static BaseConfig initBaseConfig(Context context, String processName, boolean mainProcess) {
        int defaultServer;
        if (BuildConfig.DEBUG) {
            defaultServer = 1;
        } else {
            defaultServer = 2;
        }
        int serverConfig = PrefUtil.getInstance().getInt(PrefUtil.SERVICECINFIG, defaultServer);
        boolean isDebugServer = serverConfig == 1;
        boolean isGooglePc = PackageUtil.isGooglePc();
        return new BaseConfig(PackageUtil.getVersionName(),
                PackageUtil.getVersionCode(),
                PackageUtil.getCommonVersionName(),
                PackageUtil.getCommonVersionCode(),
                PackageUtil.getChannel(),
                isDebugServer,
                BuildConfig.DEBUG,
                processName,
                mainProcess,
                context.getResources().getString(R.string.app_name),
                isGooglePc);
    }

    public static void initInMainProgressesNormal(InitializerManager manager) {
        Initializer eventBusInitializer = new AbsInitializer(InitializerParam.createBackground()
                .filterFlag(InitializerManager.FILTER_TWO)
                .tag("EventBus")) {
            @Override
            protected void create(@NonNull Context context) {
                super.create(context);
                initEventBus();
            }
        };
        manager.addInitializer(eventBusInitializer);
        manager.addInitializer(new AbsInitializer(InitializerParam.createBackground()
                .filterFlag(InitializerManager.FILTER_TWO)
                .dependencies(ApiServiceInitializer.class)
                .tag("AppsFlyer")) {

            @Override
            public void create(@NonNull Context context, @NonNull Map<String, ?> attach) {
                Apm.recordPeriod("AppsFlyerUtil#init", true);
                AppsFlyerUtil.init(context, InitializerUtils.getLoginUid(attach));
                Apm.recordPeriod("AppsFlyerUtil#init", false);
            }
        });
//        manager.addInitializer(new MainUnityInitializer());
        manager.addInitializer(new AbsInitializer(InitializerParam.createBackground()
                .filterFlag(InitializerManager.FILTER_MAX).dependencies(DeviceIdInitializer.class)
                .tag("initInMainProgressesNormal1")) {
            @Override
            protected void create(@NonNull Context context) {
                super.create(context);
                PropItemExtraHelper.register();
                VoiceRoomMemberUtil.initSpeakerAnim();
                TextSpanUtil.init();
            }
        });

        manager.addInitializer(new AbsInitializer(InitializerParam.createBackground().filterFlag(InitializerManager.FILTER_MAX)
                .dependencies(eventBusInitializer.tag(), DeviceInitializer.class).tag("initInMainProgressesNormal2")) {
            @Override
            protected void create(@NonNull Context context) {
                DeepLinkUtil.setCallback(EventDispatcher::postDeepLinkEvent);
                WpDdUtil.getInstance().doDidPendingTask();
                SecService.setDeps(SecB.INSTANCE, LibBaseUtil.buildDebug());
            }
        });
        manager.addInitializer(new LoggedInitializer());
        manager.addInitializer(new FirebaseAppInitializer());
        manager.addInitializer(new FirebaseCrashlyticsInitializer());
        manager.addInitializer(new FirebaseAnalyticsInitializer());
        manager.addInitializer(InstallReferrer.INSTANCE);
        manager.addInitializer(new AbsInitializer(InitializerParam.createBackground()
                .filterFlag(InitializerManager.FILTER_THREE)
                .dependencies("AbTestManagerPreload", MainNetworkInitializer.class)) {
            @Override
            protected void create(@NonNull Context context) {
                super.create(context);
                AbTestManager.addObserver(abTestConfig -> {
                    ApiService.of(ITeenModeApi.class).update(abTestConfig.abTest.isTeenMode());
                });
            }
        });
    }

    //未同意隐私权限延迟初始化
    public static void initInMainProcessesLimit(InitializerManager manager) {
        manager.addInitializer(new GlobalServerBoosterInitializer());
        manager.addInitializer(new MainNetworkInitializer());
    }

    private static void initEventBus() {
        boolean isInstall = false;
        Field instanceField = null;
        try {
            Field[] fields = EventBus.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.getType() == EventBus.class) {
                    instanceField = field;
                }
            }
            if (instanceField != null) {
                instanceField.setAccessible(true);
                synchronized (EventBus.class) {
                    Object obj = instanceField.get(EventBus.class);
                    if (obj != null) {
                        instanceField.set(EventBus.class, null);
                        EventBus.builder()
                                .sendNoSubscriberEvent(false).installDefaultEventBus();
                        isInstall = true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!isInstall) {
            try {
                EventBus.builder()
                        .sendNoSubscriberEvent(false).installDefaultEventBus();
            } catch (Exception e) {
                FLog.e(new IllegalStateException("instanceField: " + instanceField, e));
            }
        }
    }

    private static void initLeakWatcher(AbTest abTest) {
        int uid = LoginHelper.getLoginUid();
        if (uid <= 0) {
            return;
        }
        int defaultLeakMaxCount = 0;
        long defaultLeakCheckDuration = 0L;
        if (LibBaseUtil.buildDebug()) {
            defaultLeakMaxCount = 5;
            defaultLeakCheckDuration = 5000L;
        }
        int leakMaxCount = 0;
        int rate = abTest.getIntFromSwitch("activity_leak_open_rate", 0);
        if (rate > 0 && (uid % 100) < rate) {
            leakMaxCount = abTest.getIntFromSwitch("activity_leak_max_count", defaultLeakMaxCount);
        }
        long leakCheckDuration = 0;
        rate = abTest.getIntFromSwitch("activity_leak_open_check_rate", 0);
        if (rate > 0 && (uid % 100) < rate) {
            leakCheckDuration = abTest.getLongFromSwitch("activity_leak_check_duration", defaultLeakCheckDuration);
        }
        LeakRefWatcher.install(leakMaxCount, leakCheckDuration);
    }

    /**
     * GC后并未被回收掉,这里就先将图片等资源释放掉
     */
    private static void onRecycleTooLate(Object obj) {
        LeakRecyclerRecord record = new LeakRecyclerRecord(obj);
        Map<String, String> map = new ArrayMap<>();
        View view = null;
        if (obj instanceof Activity) {
            view = ViewUtil.getRootView((Activity) obj);
        } else if (obj instanceof View) {
            view = (View) obj;
            map.put("path", ViewUtil.getXPath(view));
        }
        if (view != null) {
            long memSize = onRecycleView(view);
            map.put("memSize", String.valueOf(memSize));
            record.add((int) memSize);
        }
        record.end();
        HLog.aliPerformance(obj.getClass().getName(), "RecycleTooLate", true,
                record.getDuration(), obj.toString(), map);
        if (LibBaseUtil.buildDebug()) {
            HLog.e("LeakRefWatcher", HLog.USR, "onRecycleTooLate=" + record);
        }
    }

    private static long onRecycleView(View view) {
        if (view == null) {
            return 0;
        }
        long memSize = 0;
        if (view instanceof ViewGroup) {
            ViewGroup group = ((ViewGroup) view);
            for (int i = 0; i < group.getChildCount(); i++) {
                View child = group.getChildAt(i);
                memSize += onRecycleView(child);
            }
            group.removeAllViewsInLayout();
            if (view instanceof RecyclerView) {
                RecyclerView rv = ((RecyclerView) view);
                rv.setAdapter(null);
                rv.getRecycledViewPool().clear();
            }
        }

        memSize += DrawableRecycleUtil.recyclerDrawable(view, false);
        return memSize;
    }

    /**
     * 确认出现了内存泄漏
     */
    private static void onLeak(Object obj) {
        long current = System.currentTimeMillis();
        Map<String, String> map = new ArrayMap<>();
        View view = null;
        if (obj instanceof Activity) {
            view = ViewUtil.getRootView((Activity) obj);
        } else if (obj instanceof View) {
            view = (View) obj;
        }
        if (view != null) {
            IMMHelper.fixInputMethodManagerLeak(view);
        }
        HLog.aliPerformance(obj.getClass().getName(), "Leak", true,
                (System.currentTimeMillis() - current), obj.toString(), map);
        if (LibBaseUtil.buildDebug()) {
            HLog.e("LeakRefWatcher", HLog.USR, "onLeak=" + obj);
        }
    }

    public static class BaseLogger implements LibBaseUtil.Logger {
        @Override
        public void logInfo(String tag, boolean writeToFile, String fmt, Object... args) {
            HLog.d(tag, writeToFile ? HLog.USR : HLog.CLR, fmt, args);
        }

        @Override
        public void logErr(String tag, boolean writeToFile, String fmt, Object... args) {
            HLog.e(tag, writeToFile ? HLog.USR : HLog.CLR, fmt, args);
        }

        @Override
        public void flogErr(Throwable throwable) {
            FLog.e(throwable);
        }

        @Override
        public void log(@NonNull Activity activity, String action, boolean isStart) {
            Apm.recordPeriod(activity.getClass().getSimpleName() + "#" + action, isStart);
        }

        @Override
        public void log(@NonNull Activity activity, String action, ActivityTaskManager.ActivityTaskListener listener) {
            Apm.recordTimePoint(listener + "#" + action);
        }
    }

    private static void initWebview(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WebView.setDataDirectorySuffix(ProcessUtil.getCurrentProcessName(context));
        }
    }

    private static void observerForAnimScheduler() {
        // 跳转过程中可能会停顿一下。
        ActivityTaskManager.getInstance().registerActivityTaskListener(new ActivityTaskManager.ActivityTaskListener() {
            // 记录当前 resume 了的 activity
            private int resumeCount = 0;

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                super.onActivityResumed(activity);
                resumeCount++;
                Scheduler.resumeAll();
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
                super.onActivityPaused(activity);
                resumeCount--;
                if (resumeCount < 0) {
                    // 注入实际问题可能会导致没有resume 的计数但触发了 pause
                    // 修正一下。
                    resumeCount = 0;
                }
                if (resumeCount == 0) {
                    Scheduler.pauseAll();
                }
            }
        });
    }

    private static class WejoyRefreshHeaderCreator implements DefaultRefreshHeaderCreator {

        @NonNull
        @Override
        public RefreshHeader createRefreshHeader(@NonNull Context context, @NonNull RefreshLayout layout) {
            return new WejoyRefreshHeader(context);
        }
    }

    private static class WejoyRefreshFooterCreator implements DefaultRefreshFooterCreator {

        @NonNull
        @Override
        public RefreshFooter createRefreshFooter(@NonNull Context context, @NonNull RefreshLayout layout) {
            return new DefaultLoadMoreFooter(context);
        }
    }
}