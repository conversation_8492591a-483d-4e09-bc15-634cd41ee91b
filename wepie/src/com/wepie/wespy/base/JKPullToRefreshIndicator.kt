package com.wepie.wespy.base

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.wejoy.weplay.composewidget.AbsPullToRefreshIndicator
import com.wejoy.weplay.composewidget.WpImage
import com.wejoy.weplay.composewidget.WpPullToRefreshIndicator
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R

object JKPullToRefreshIndicator : AbsPullToRefreshIndicator() {
    @Composable
    override fun downPull() {
        Image(
            painter = painterResource(R.drawable.wejoy_refresh_down_default_icon),
            contentDescription = "refresh",
            colorFilter = ColorFilter.tint(
                color = colorResource(R.color.color_accent),
                blendMode = BlendMode.SrcIn
            ),
            modifier = Modifier
                .size(32.dp)
                .padding(4.dp),
        )
    }

    @Composable
    override fun holding() {
        Image(
            painter = painterResource(R.drawable.jk_refresh_loading_default_icon),
            contentDescription = null,
            modifier = Modifier
                .height(32.dp)
                .wrapContentWidth()
        )
    }

    @Composable
    override fun loadingAnim() {
        WpImage(
            model = WpImageLoader.getAssetUri("svga/jk_loading.svga"),
            contentDescription = null,
            modifier = Modifier
                .width(76.dp)
                .height(32.dp)
        )
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @JvmStatic
    fun setDefault() {
        WpPullToRefreshIndicator = { isRefreshing, state ->
            JKPullToRefreshIndicator(isRefreshing, state)
        }
    }
}