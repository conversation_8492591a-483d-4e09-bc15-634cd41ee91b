package com.wepie.wespy.base.popview.style

import android.animation.Animator
import android.animation.ValueAnimator
import android.graphics.Matrix
import android.graphics.Rect
import android.view.Gravity
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AccelerateInterpolator

/**
 * Created by lsy on 2022/8/29.
 */
class RectToRectScareStyle(val oriRect: Rect) : BaseStyle() {

    val during = 220L
    var dstRect: Rect? = null
    val location = IntArray(2)
    var alpScare = 1.1f
    val matrix by lazy {
        Matrix()
    }

    init {
        gravity = Gravity.CENTER
    }

    override fun getStartAnim(view: View): Animator {
        val anim = ValueAnimator.ofFloat(0.0f, 1.0f).apply {
            duration = during
            interpolator = AccelerateInterpolator()
        }
        view.run {
            alpha = 0.0f
            pivotX = 0f
            pivotY = 0f
            scaleX = 1f
            scaleY = 1f
            translationX = 0f
            translationY = 0f
        }
        anim.addUpdateListener {
            if (view.width != 0 && view.height != 0) {
                if (dstRect == null) {
                    view.getLocationOnScreen(location)
                    dstRect = Rect(location[0],
                            location[1], location[0] + view.width, location[1] + view.height
                    )
                }
                val fl = it.animatedValue as Float
                val dstWidth = dstRect!!.width().toFloat()
                val dstHeight = dstRect!!.height().toFloat()

                view.scaleX = oriRect.width() / dstWidth + ((dstWidth - oriRect.width()) / dstWidth) * fl
                view.scaleY = oriRect.height() / dstHeight + ((dstHeight - oriRect.height()) / dstHeight) * fl
                if (dstWidth * view.scaleX < oriRect.width() * alpScare) {
                    val alpAnim = (dstWidth * view.scaleX - oriRect.width()) / oriRect.width()
                    view.alpha = alpAnim
                } else {
                    view.alpha = 1f
                }
                val dstY = dstRect!!.top.toFloat()
                val dstX = dstRect!!.left.toFloat()
                view.translationY = oriRect.top + (dstY - oriRect.top) * fl
                view.translationX = oriRect.left + (dstX - oriRect.left) * fl
            }
        }
        return anim
    }

    override fun getEndAnim(view: View): Animator {
        val anim = ValueAnimator.ofFloat(1.0f, 0.0f).apply {
            duration = during
            interpolator = AccelerateDecelerateInterpolator()
        }
        view.run {
            alpha = 1.0f
            pivotX = 0f
            pivotY = 0f
        }
        anim.addUpdateListener {
            if (view.width != 0 && view.height != 0) {
                if (dstRect == null) {
                    view.getLocationOnScreen(location)
                    dstRect = Rect(location[0],
                            location[1], location[0] + view.width, location[1] + view.height
                    )
                }
                val fl = it.animatedValue as Float
                val dstWidth = dstRect!!.width().toFloat()
                val dstHeight = dstRect!!.height().toFloat()

                view.scaleX = oriRect.width() / dstWidth + ((dstWidth - oriRect.width()) / dstWidth) * fl
                view.scaleY = oriRect.height() / dstHeight + ((dstHeight - oriRect.height()) / dstHeight) * fl
                if (dstWidth * view.scaleX < oriRect.width() * alpScare) {
                    val alpAnim = (dstWidth * view.scaleX - oriRect.width()) / oriRect.width()
                    view.alpha = alpAnim
                } else {
                    view.alpha = 1f
                }
                val dstY = dstRect!!.top.toFloat()
                val dstX = dstRect!!.left.toFloat()
                view.translationY = oriRect.top + (dstY - oriRect.top) * fl
                view.translationX = oriRect.left + (dstX - oriRect.left) * fl
            }
        }
        return anim
    }
}