package com.wepie.wespy.helper.dialog.coin

import android.content.Context
import com.huiwan.constants.GameType
import com.huiwan.user.LoginHelper
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog

/**
 * 游戏的充值页面
 */
object PublicCoinPayDialogFactory {
    const val TAG = "PublicCoinPayDialogFactory"
    fun createAndShow(context: Context, portrait: <PERSON>ole<PERSON>, scene: String, gameType: Int) {
        if (portrait) {
            PublicCoinPayBottomDialogView.showBottomDialog(context)
            if (gameType != GameType.GAME_TYPE_UN_DEFINED) {
                trackViewScreen(gameType, scene, TrackScreenName.PAY_DIALOG)
            }
        } else {
            PublicCoinPayHorizontalView.showBottomDialog(context)
            trackViewScreen(gameType, scene, TrackScreenName.PAY_PAGE)
        }
    }

    private fun trackViewScreen(gameType: Int, scene: String, screenName: String) {
        val user = LoginHelper.getLoginUser()
        if (user == null) {
            HLog.d(TAG, HLog.USR, "getLoginUse=null, gameType={}, scene={}", gameType, scene)
            FLog.e(Throwable("getLoginUse=null, gameType=" + gameType + "scene=" + scene))
            return
        }
        val map = mapOf(
            "game_type" to gameType,
            "scene" to scene,
            "account_coin" to user.getCoin(),
            "is_vip" to user.isVip,
        )
        TrackUtil.appViewScreen(screenName, map)
    }
}