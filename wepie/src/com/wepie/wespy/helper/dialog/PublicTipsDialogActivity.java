package com.wepie.wespy.helper.dialog;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.wepie.wespy.R;

/**
 * Created by three on 15/11/9.
 */
public class PublicTipsDialogActivity extends Activity implements DialogActivity {
    public static final String TIPS_KEY = "tips_key";
    private RelativeLayout container;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.public_dialog_tips_activity);
        container = (RelativeLayout) findViewById(R.id.public_tips_container);
        String tips = getIntent().getStringExtra(TIPS_KEY);
        setView(tips);
    }

    private void setView(String tipContent) {
        final View view = LayoutInflater.from(this).inflate(R.layout.global_dialog_single_bt_lay, null);
        TextView tipsTx = (TextView) view.findViewById(R.id.global_single_dialog_content_tx);
        TextView sureBt = (TextView) view.findViewById(R.id.global_single_dialog_sure_bt);
        tipsTx.setText(TextUtils.isEmpty(tipContent) ? "" : tipContent);

        sureBt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                doFinish();
            }
        });

        int width = BaseFullScreenDialog.getDialogWidth();
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                width, RelativeLayout.LayoutParams.WRAP_CONTENT);
        params.addRule(RelativeLayout.CENTER_IN_PARENT);
        container.addView(view, params);
    }

    private void doFinish() {
        finish();
        overridePendingTransition(R.anim.dialog_activity_scale_out, R.anim.dialog_activity_scale_in);
    }

    @Override
    public void onBackPressed() {
        doFinish();
    }

}
