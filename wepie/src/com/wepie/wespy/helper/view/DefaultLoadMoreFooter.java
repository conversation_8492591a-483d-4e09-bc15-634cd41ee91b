package com.wepie.wespy.helper.view;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.anim.schedule.FrameAnimateDrawable;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.widget.CustomTextView;
import com.scwang.smart.refresh.layout.api.RefreshFooter;
import com.scwang.smart.refresh.layout.api.RefreshKernel;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.constant.SpinnerStyle;
import com.scwang.smart.refresh.layout.simple.SimpleComponent;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;

public class DefaultLoadMoreFooter extends SimpleComponent implements RefreshFooter {
    private final TextView tipTv;
    private final ImageView loadingIv;

    private final Drawable mLoadingImageDrawable;
    private FrameAnimateDrawable animationDrawable;

    public DefaultLoadMoreFooter(Context context) {
        this(context, null);
    }

    public DefaultLoadMoreFooter(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DefaultLoadMoreFooter(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        LinearLayout linearLayout = new LinearLayout(context);
        LayoutParams containerLp = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        containerLp.addRule(CENTER_IN_PARENT);
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        linearLayout.setGravity(Gravity.CENTER);

        ImageView imgView = new ImageView(context);
        LinearLayout.LayoutParams imgLp = new LinearLayout.LayoutParams(ScreenUtil.dip2px(76), ScreenUtil.dip2px(32));
        imgView.setScaleType(ImageView.ScaleType.CENTER);
        imgView.setLayoutParams(imgLp);
        mLoadingImageDrawable = ResUtil.getDrawable(R.drawable.jk_refresh_loading_default_icon);
        imgView.setImageDrawable(mLoadingImageDrawable);
        loadingIv = imgView;

        TextView textView = new CustomTextView(context);
        textView.setTextColor(ResUtil.getColor(R.color.color_primary));
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10);
        textView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        textView.setText(ResUtil.getStr(R.string.loading));
        tipTv = textView;

        linearLayout.addView(imgView);
        linearLayout.addView(textView);
        addView(linearLayout, containerLp);

        WpImageLoader.load(WpImageLoader.getAssetUri("svga/jk_loading.svga"), null, ImageLoadInfo.newInfo().owner(this), new WpImageLoadListener<>() {
            @Override
            public boolean onComplete(String model, Drawable drawable) {
                if (drawable instanceof FrameAnimateDrawable) {
                    animationDrawable = (FrameAnimateDrawable) drawable;
                }
                return true;
            }

            @Override
            public boolean onFailed(String model, Exception e) {
                return true;
            }
        });
    }

    /**
     * 【仅限框架内调用】设置数据全部加载完成，将不能再次触发加载功能
     *
     * @param noMoreData 是否有更多数据
     * @return true 支持全部加载完成的状态显示 false 不支持
     */
    @Override
    public boolean setNoMoreData(boolean noMoreData) {
        return false;
    }

    /**
     * 获取实体视图
     *
     * @return 实体视图
     */
    @NonNull
    @Override
    public View getView() {
        return this;
    }

    /**
     * 获取变换方式 {@link SpinnerStyle} 必须返回 非空
     *
     * @return 变换方式
     */
    @NonNull
    @Override
    public SpinnerStyle getSpinnerStyle() {
        return SpinnerStyle.Translate;
    }

    /**
     * 【仅限框架内调用】设置主题颜色
     *
     * @param colors 对应Xml中配置的 srlPrimaryColor srlAccentColor
     */
    @Override
    public void setPrimaryColors(int... colors) {
        if (colors == null || colors.length == 0) {
            return;
        }
        if (colors.length == 1) {
            setBackgroundColor(colors[0]);
        } else {
            for (int color : colors) {
                if (color != 0) {
                    setBackgroundColor(color);
                    break;
                }
            }
        }

    }

    /**
     * 【仅限框架内调用】尺寸定义完成 （如果高度不改变（代码修改：setHeader），只调用一次, 在RefreshLayout#onMeasure中调用）
     *
     * @param kernel        RefreshKernel
     * @param height        HeaderHeight or FooterHeight
     * @param maxDragHeight 最大拖动高度
     */
    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {

    }

    /**
     * 【仅限框架内调用】手指拖动下拉（会连续多次调用，添加isDragging并取代之前的onPulling、onReleasing）
     *
     * @param isDragging    true 手指正在拖动 false 回弹动画
     * @param percent       下拉的百分比 值 = offset/footerHeight (0 - percent - (footerHeight+maxDragHeight) / footerHeight )
     * @param offset        下拉的像素偏移量  0 - offset - (footerHeight+maxDragHeight)
     * @param height        高度 HeaderHeight or FooterHeight (offset 可以超过 height 此时 percent 大于 1)
     * @param maxDragHeight 最大拖动高度 offset 可以超过 height 参数 但是不会超过 maxDragHeight
     */
    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {

    }

    /**
     * 【仅限框架内调用】释放时刻（调用一次，将会触发加载）
     *
     * @param refreshLayout RefreshLayout
     * @param height        高度 HeaderHeight or FooterHeight
     * @param maxDragHeight 最大拖动高度
     */
    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {

    }

    /**
     * 【仅限框架内调用】开始动画
     *
     * @param refreshLayout RefreshLayout
     * @param height        HeaderHeight or FooterHeight
     * @param maxDragHeight 最大拖动高度
     */
    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        if (animationDrawable != null) {
            animationDrawable.stop();
            loadingIv.setImageDrawable(animationDrawable);
            animationDrawable.start();
        }
    }

    /**
     * 【仅限框架内调用】动画结束
     *
     * @param refreshLayout RefreshLayout
     * @param success       数据是否成功刷新或加载
     * @return 完成动画所需时间 如果返回 Integer.MAX_VALUE 将取消本次完成事件，继续保持原有状态
     */
    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        if (animationDrawable != null) {
            animationDrawable.stop();
        }
        return 0;
    }

    /**
     * 【仅限框架内调用】水平方向的拖动
     *
     * @param percentX  下拉时，手指水平坐标对屏幕的占比（0 - percentX - 1）
     * @param offsetX   下拉时，手指水平坐标对屏幕的偏移（0 - offsetX - LayoutWidth）
     * @param offsetMax 最大的偏移量
     */
    @Override
    public void onHorizontalDrag(float percentX, int offsetX, int offsetMax) {

    }

    /**
     * 是否支持水平方向的拖动（将会影响到onHorizontalDrag的调用）
     *
     * @return 水平拖动需要消耗更多的时间和资源，所以如果不支持请返回false
     */
    @Override
    public boolean isSupportHorizontalDrag() {
        return false;
    }

    /**
     * 【仅限框架内调用】状态改变事件 {@link RefreshState}
     *
     * @param refreshLayout RefreshLayout
     * @param oldState      改变之前的状态
     * @param newState      改变之后的状态
     */
    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        loadingIv.setImageDrawable(mLoadingImageDrawable);
        switch (newState) {
            case None:
            case LoadReleased:
            case PullUpToLoad:
            case ReleaseToLoad:
                tipTv.setText(R.string.release_to_load);
                break;
            case Loading:
                tipTv.setText(R.string.loading);
                break;
        }
    }
}
