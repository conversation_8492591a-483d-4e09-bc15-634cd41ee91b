package com.wepie.wespy.helper.seletefriend

import com.huiwan.user.UserInterface
import com.wepie.wespy.module.chat.presenter.IFriendChooseItem

interface IChoosePresenter {
    fun getUserData()
    fun setFilter(keyWords: String)
    fun onClickItem(userInterface: UserInterface, iFriendChooseItem: IFriendChooseItem)
    fun isDefaultChoose(uid: Int): Boolean
    fun refreshChoose(userInterface: UserInterface, iFriendChooseItem: IFriendChooseItem)
}