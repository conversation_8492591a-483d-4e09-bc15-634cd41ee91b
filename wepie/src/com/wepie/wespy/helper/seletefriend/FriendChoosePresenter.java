package com.wepie.wespy.helper.seletefriend;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.model.RoomLease;
import com.huiwan.constants.IntentConfig;
import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.LifeUserListSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserInterface;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.user.http.FriendApi;
import com.huiwan.user.http.callback.FriendGetTwoWayCallback;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.ILifeOwner;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.FriendChooseComparator;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.family.FamilyMembersRsp;
import com.wepie.wespy.model.entity.group.GroupInfo;
import com.wepie.wespy.module.chat.dataservice.group.GroupService;
import com.wepie.wespy.module.chat.presenter.IFriendChooseItem;
import com.wepie.wespy.module.family.FamilyManager;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.FamilyApi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by bigwen on 2017/9/9.
 */

public class FriendChoosePresenter implements IChoosePresenter {

    public interface IChooseActivity extends ILifeOwner {
        void showLoading();

        void hideLoading();

        void refreshFriendList(List<UserInterface> friendInfoList, boolean empty);

        void refresh(int fromType, List<UserInterface> userInterfaces);

        void refreshAdapter();

        void clearSearch();

        void setTitle(String title);

        void finish(List<Integer> list);

    }

    public static final String ACTION_RS_CREATE_ROOM = "game_rs_room_create";

    private IChooseActivity mInterface;
    private List<UserInterface> userList = new ArrayList<>();
    private String keyWords = "";
    private ArrayList<Integer> defaultUidList = new ArrayList<>();
    private int gid;
    private int fromType = IntentConfig.TYPE_CREATE_GROUP;
    private int rid;
    private List<UserInterface> friendsNeed = new ArrayList<>();
    private int maxSize = Integer.MAX_VALUE;

    //建房
    private int gameType;
    private String roomName;
    private int gamerNum;
    private int allowEnter;
    @Nullable
    private RoomLease roomLease;

    //婚礼请帖邀请
    private int[] excludeUid = new int[]{};

    public FriendChoosePresenter(IChooseActivity mInterface) {
        this.mInterface = mInterface;
    }

    @Override
    public void getUserData() {
        mInterface.showLoading();
        if (fromType == IntentConfig.TYPE_FAMILY_ROOM_ADD_ADMIN) {
            FamilyApi.members(new LifeDataCallback<FamilyMembersRsp>(mInterface.getLife()) {
                @Override
                public void onSuccess(Result<FamilyMembersRsp> result) {
                    getUserList2(result.data.getNoAdminList());
                }

                @Override
                public void onFail(int i, String s) {
                    mInterface.hideLoading();
                    ToastUtil.show(s);
                }
            });
            getUserList2(FamilyManager.getInstance().getFamilyMembers());
        } else if (fromType == IntentConfig.TYPE_GROUP_ADMIN) {
            GroupInfo groupInfo = GroupService.getInstance().getGroupInfo(gid);
            List<Integer> list = new ArrayList<>();
            for (Integer integer : groupInfo.getAllMemberUid()) {
                if (groupInfo.owner != integer) {
                    list.add(integer);
                }
            }
            getUserList2(list);
        } else if (fromType == IntentConfig.TYPE_GROUP_ADMIN_DELETE) {
            GroupInfo groupInfo = GroupService.getInstance().getGroupInfo(gid);
            List<Integer> list = new ArrayList<>();
            for (Integer integer : groupInfo.getAllMemberUid()) {
                if (groupInfo.isAdmin(integer)) {
                    list.add(integer);
                }
            }
            getUserList2(list);
        } else if (fromType == IntentConfig.TYPE_GROUP_TRANSFER_OWNER) {
            GroupInfo groupInfo = GroupService.getInstance().getGroupInfo(gid);
            List<Integer> list = new ArrayList<>();
            for (Integer integer : groupInfo.getAllMemberUid()) {
                if (groupInfo.owner != integer) {
                    list.add(integer);
                }
            }
            getUserList2(list);
        } else if (fromType == IntentConfig.KICK_TYPE_GROUP) {
            GroupInfo groupInfo = GroupService.getInstance().getGroupInfo(gid);
            List<Integer> list = new ArrayList<>();
            for (Integer integer : groupInfo.getAllMemberUid()) {
                //如果我是群主,除了自己,都能踢
                if (groupInfo.isSelfOwner()) {
                    //过滤群主
                    if (groupInfo.owner != integer) {
                        list.add(integer);
                    }
                }
                //如果我是管理员,只能踢群成员
                if (groupInfo.isSelfAdmin()) {
                    //管理不能踢群主,管理,但是要加入进去,不让选
                    List<Integer> dflist = new ArrayList<>();
                    dflist.add(groupInfo.owner);
                    dflist.addAll(groupInfo.getAdminList());
                    int[] df = new int[dflist.size()];
                    for (int i = 0; i < dflist.size(); i++) {
                        df[i] = dflist.get(i);
                    }
                    setDefaultUidList(df);
                    //除了自己都加进去,包括群主,只是不能选中
                    if (integer != LoginHelper.getLoginUid()) {
                        list.add(integer);
                    }
                    //过滤群主和管理员
                    if (groupInfo.owner != integer && !groupInfo.isAdmin(integer)) {

                    }
                }
            }
            getUserList2(list);
        } else {
            FriendApi.getTwoWayFriend(mInterface.getLife(), new FriendGetTwoWayCallback() {
                @Override
                public void onSuccess(ArrayList<Integer> uids) {
                    getFriendList(uids);
                }

                @Override
                public void onFail(String msg) {
                    mInterface.hideLoading();
                    ToastUtil.show(msg);
                }
            });
        }
    }

    private void getUserList2(final List<Integer> data) {
        UserService.get().getCacheSimpleUserList(filterExcludeUid(data), new LifeUserListSimpleInfoCallback(mInterface.getLife()) {
            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfos) {
                List<UserInterface> userList = new ArrayList<>();
                for (UserInterface userInterface : userSimpleInfos) {
                    if (data.contains(userInterface.getUid())) {
                        userList.add(userInterface);
                    }
                }
                Collections.sort(userList, new FriendChooseComparator());
                friendsNeed = userList;

                //过滤关键词
                List<UserInterface> filter = filterFriendInfo(keyWords, userList);
                mInterface.refreshFriendList(filter, true);
                mInterface.hideLoading();
            }

            @Override
            public void onUserInfoFailed(String description) {
                mInterface.hideLoading();
            }
        });
    }

    private void getFriendList(final List<Integer> data) {
        mInterface.hideLoading();
        List<UserInterface> userList = new ArrayList<>();
        for (UserInterface userInterface : FriendInfoCacheManager.getInstance().getFriendList()) {
            if (data.contains(userInterface.getUid())) {
                userList.add(userInterface);
            }
        }
        Collections.sort(userList, new FriendChooseComparator());
        friendsNeed = userList;

        //过滤关键词
        List<UserInterface> filter = filterFriendInfo(keyWords, userList);
        mInterface.refreshFriendList(filter, true);
    }

    private List<UserInterface> filterFriendInfo(String keyWords, List<UserInterface> friendInfos) {
        if (TextUtils.isEmpty(keyWords)) return friendInfos;
        List<UserInterface> filter = new ArrayList<>();
        for (UserInterface friendInfo : friendInfos) {
            keyWords = keyWords.toLowerCase();
            if (friendInfo.getRemarkName().toLowerCase().contains(keyWords)) {
                filter.add(friendInfo);
            } else if (friendInfo.getNameFirstLetter().toLowerCase().contains(keyWords)) {
                filter.add(friendInfo);
            }
        }
        return filter;
    }

    private List<Integer> filterExcludeUid(final List<Integer> data) {
        List<Integer> list = new ArrayList<>();
        for (int uid : data) {
            if (!isExcludeUid(uid)) {
                list.add(uid);
            }
        }
        return list;
    }

    private boolean isExcludeUid(int uid) {
        if (excludeUid == null) return false;
        for (int exclude : excludeUid) {
            if (exclude == uid) return true;
        }
        return false;
    }

    public void removeChoose(UserInterface userInterface) {
        userList.remove(userInterface);
        mInterface.refresh(fromType, userList);
        mInterface.refreshAdapter();
    }

    public char[] getFriendFirstChars(List<UserInterfaceWithTag> userInterfaceWithTags) {
        ArrayList<String> nameCharList = new ArrayList();
        for (int i = 0; i < userInterfaceWithTags.size(); i++) {
            UserInterfaceWithTag userInterfaceWithTag = userInterfaceWithTags.get(i);
            if (userInterfaceWithTag.getUseSlideIndex() && !nameCharList.contains(userInterfaceWithTag.getTag())) {
                nameCharList.add(userInterfaceWithTag.getTag());
            }
        }
        int len = nameCharList.size();
        char[] chars = new char[len];
        for (int i = 0; i < len; i++) {
            chars[i] = nameCharList.get(i).charAt(0);
        }
        return chars;
    }

    @Override
    public void refreshChoose(UserInterface userInterface, IFriendChooseItem iFriendChooseItem) {
        if (userList.contains(userInterface)) {
            iFriendChooseItem.refreshChoose(true);
        } else {
            if (defaultUidList.contains(userInterface.getUid())) {
                iFriendChooseItem.refreshDefaultChoose(true);
            } else {
                iFriendChooseItem.refreshChoose(false);
            }
        }
    }

    @Override
    public void onClickItem(UserInterface userInterface, IFriendChooseItem iFriendChooseItem) {
        if (fromType == IntentConfig.TYPE_GROUP_TRANSFER_OWNER) {
            //群聊转让只能转给4级以上的用户
            if (userInterface.getLevel() < 4) {
                ToastUtil.show(ResUtil.getStr(R.string.group_transfer_level_tips, 4));
                return;
            }
        }
        if (userList.size() >= maxSize) {
            if (userList.contains(userInterface)) {
                //实现反选,如果选了,再次点击就变成不选
                userList.remove(userInterface);
                iFriendChooseItem.refreshChoose(false);
            } else {
                //如果是一个人,则清除所有,然后刷新列表,实现其他人都不选中的效果
                if (maxSize == 1) {
                    userList.clear();
                    userList.add(userInterface);
                    mInterface.refreshAdapter();
                } else {
                    //否则提示最多选多少个人
                    ToastUtil.show(ResUtil.getResource().getString(R.string.people_max_select_num, maxSize));
                    return;
                }
            }
        } else {
            if (userList.contains(userInterface)) {
                userList.remove(userInterface);
                iFriendChooseItem.refreshChoose(false);
            } else {
                userList.add(userInterface);
                iFriendChooseItem.refreshChoose(true);
                if (!TextUtils.isEmpty(keyWords)) {
                    mInterface.clearSearch();
                    setFilter("");
                }
            }
        }
        mInterface.refresh(fromType, userList);
    }

    public void onEnter() {
        final List<Integer> integers = new ArrayList<>();
        for (UserInterface userInterface : userList) {
            integers.add(userInterface.getUid());
        }
        if (fromType == IntentConfig.TYPE_CREATE_GROUP) {
            integers.addAll(defaultUidList);
            if (integers.size() == 0) return;
            FriendChooseEnterUtil.createGroup((FriendChooseActivity) mInterface, integers);
        } else if (fromType == IntentConfig.TYPE_GROUP_ADD) {
            FriendChooseEnterUtil.addGroupUser((FriendChooseActivity) mInterface, integers, gid);
        } else if (fromType == IntentConfig.KICK_TYPE_GROUP) {
            FriendChooseEnterUtil.kickGroupUser((FriendChooseActivity) mInterface, integers, gid);
        } else if (fromType == IntentConfig.TYPE_GROUP_ADMIN) {
            FriendChooseEnterUtil.addGroupAdmin(mInterface,integers, gid);
        } else if (fromType == IntentConfig.TYPE_GROUP_ADMIN_DELETE) {
            FriendChooseEnterUtil.deleteGroupAdmin(mInterface,integers, gid);
        } else if (fromType == IntentConfig.TYPE_GROUP_TRANSFER_OWNER) {
            FriendChooseEnterUtil.transferOwner((FriendChooseActivity) mInterface, integers, gid);
        } else if (fromType == IntentConfig.TYPE_MARRY_INVITATION) {
            FriendChooseEnterUtil.invitationFriend((FriendChooseActivity) mInterface, integers);
        } else if (fromType == IntentConfig.TYPE_GIFT_TO_VIP) {
            FriendChooseEnterUtil.giftToVip((FriendChooseActivity) mInterface, integers);
        } else if (fromType == IntentConfig.TYPE_VOICE_ROOM_ADD_ADMIN || fromType == IntentConfig.TYPE_FAMILY_ROOM_ADD_ADMIN) {
            FriendChooseEnterUtil.addVoiceRoomAdmin((FriendChooseActivity) mInterface, integers, rid);
        }
    }

    @Override
    public boolean isDefaultChoose(int uid) {
        return defaultUidList.contains(uid);
    }

    @Override
    public void setFilter(String keyWords) {
        this.keyWords = keyWords;
        List<UserInterface> filter = filterFriendInfo(keyWords, friendsNeed);
        Collections.sort(filter, new FriendChooseComparator());
        mInterface.refreshFriendList(filter, TextUtils.isEmpty(keyWords));
    }

    public void setDefaultUidList(int[] uids) {
        defaultUidList.clear();
        if (uids == null) return;
        for (int uid : uids) {
            if (FriendInfoCacheManager.getInstance().isFriend(uid)) {
                defaultUidList.add(uid);
            } else if (fromType == IntentConfig.TYPE_FAMILY_ROOM_ADD_ADMIN) {
                defaultUidList.add(uid);
            } else if (fromType == IntentConfig.TYPE_GROUP_ADMIN) {
                defaultUidList.add(uid);
            } else if (fromType == IntentConfig.KICK_TYPE_GROUP) {
                defaultUidList.add(uid);
            }
        }
    }

    public void finishCreateGroupActivity() {
        EventDispatcher.postFinishEventFromCreateGroup();
    }

    public void setIntentData(Intent intentData) {
        fromType = intentData.getIntExtra(IntentConfig.CHOOSE_FRIEND_TYPE, IntentConfig.TYPE_CREATE_GROUP);
        setTitleByFromType();
        gid = intentData.getIntExtra(IntentConfig.GROUP_ID, 0);
        rid = intentData.getIntExtra(IntentConfig.ROOM_RID, 0);
        maxSize = intentData.getIntExtra(IntentConfig.CHOOSE_FRIEND_NUM, Integer.MAX_VALUE);

        //创建房间
        gameType = intentData.getIntExtra(IntentConfig.GAME_TYPE, 0);
        roomName = intentData.getStringExtra(IntentConfig.ROOM_NAME);
        gamerNum = intentData.getIntExtra(IntentConfig.GAMER_NUM, 0);
        allowEnter = intentData.getIntExtra(IntentConfig.ALLOW_ENTER, RoomInfo.ROOM_ALLOW_ENTER);
        roomLease = (RoomLease) intentData.getSerializableExtra(IntentConfig.ROOM_LEASE);

        //婚礼邀请
        int[] excludeUid = intentData.getIntArrayExtra(IntentConfig.EXCLUDE_UID);
        if (excludeUid != null) {
            this.excludeUid = excludeUid;
        }
    }

    public void setData( int id, int type, int maxNum) {
        fromType = type;
        setTitleByFromType();
        gid =  id;
        maxSize = maxNum;
    }

    public int getFromType() {
        return fromType;
    }

    /**
     * 在有需要的情况下可以自定义标题
     */
    public void setTitleByFromType() {
        switch (fromType) {
            case IntentConfig.KICK_TYPE_GROUP:
                mInterface.setTitle(ResUtil.getStr(R.string.group_member_del));
                break;
            case IntentConfig.TYPE_GROUP_ADMIN:
                mInterface.setTitle(ResUtil.getStr(R.string.interest_group_setting_add_manager));
                break;
            case IntentConfig.TYPE_GROUP_ADMIN_DELETE:
                mInterface.setTitle(ResUtil.getStr(R.string.interest_group_setting_remove_manager));
                break;
            default:
                break;
        }
    }
}