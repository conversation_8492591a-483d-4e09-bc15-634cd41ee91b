package com.wepie.wespy.helper.redDotHelper;

import android.util.SparseArray;

/**
 * 红点结点，每个会显示红点的地方，定义一个结点。
 * 在 {@link RedDotNode#constructNode()} 中添加结构层次
 * 在 {@link RedDotUtil} 中 markRead 的时候，向上遍历，当前结点及父结点
 */
public class RedDotNode {
    /** 游戏 */
    public static final int NODE_GAME = 0x01000000;
    public static final int NODE_G_ACTIVITY = 0x01100000;
    public static final int NODE_G_BATTLE_PASS = 0x01200000;
    /** 发现 */
    public static final int NODE_DISCOVER = 0x08000000;
    public static final int NODE_D_DISCOVER = 0x08010000;
    public static final int NODE_D_VOICE = 0x08020000;
    public static final int NODE_D_FAMILY           = 0x08040000;
    public static final int NODE_D_F_MAIN           = 0x08040100;
    public static final int NODE_D_F_M_UPGRADE      = 0x08040101;
    public static final int NODE_D_F_M_WEEK_ACTIVE  = 0x08040102;
    public static final int NODE_D_F_M_WHEEL        = 0x08040104;
    public static final int NODE_D_F_TASK           = 0x08040200;
    public static final int NODE_D_F_MEMBER         = 0x08040400;
    public static final int NODE_D_F_POST           = 0x08040800;
    public static final int NODE_D_F_MANAGE         = 0x08041000;
    public static final int NODE_D_F_BENEFIT         = 0x08041002;
    public static final int NODE_D_F_MANAGE_APPLY   = 0x08041001;
    public static final int NODE_D_AUDIO_MATCH      = 0x08080000;

    /**
     * 师徒
     */
    public static final int NODE_D_MENTOR = 0x08100000;//师徒
    public static final int NODE_D_MENTOR_TEACHER = 0x08100001;//拜师
    public static final int NODE_D_MENTOR_STUDENT = 0x08100002;//收徒
    public static final int NODE_D_MENTOR_APPLY = 0x08100004;//申请通过
    public static final int NODE_D_MENTOR_DES = 0x08100008;//收徒宣言
    public static final int NODE_D_MENTOR_REWARD = 0x08100009;//领取奖励
    public static final int NODE_D_MENTOR_DEL = 0x08100010;//删除师徒关系


    /** 我的 */
    public static final int NODE_ME         = 0x10000000;
    public static final int NODE_M_SHOP     = 0x10010000;
    public static final int NODE_M_S_BAG    = 0x10010100;
    public static final int NODE_M_S_B_RP   = 0x10010101;
    public static final int NODE_M_S_RING   = 0x10010200;
    public static final int NODE_M_S_DECOR  = 0x10010400;
    public static final int NODE_M_S_PROP   = 0x10010800;
    public static final int NODE_ME_VIP =            0x10020000;
    public static final int NODE_ME_VIP_DISCOUNT =   0x10020100;
    public static final int NODE_ME_VIP_UPGRADE =    0x10020200;
    public static final int NODE_ME_VIP_DOWNGRADE =  0x10020400;
    public static final int NODE_ME_VIP_EXPIRE =     0x10020800;
    public static final int NODE_ME_VIP_MSG_ROAMING =0x10021000;
    public static final int NODE_M_LOOKME = 0x10040000;
    public static final int NODE_M_CONTRIB = 0x10080000;//贡献题目
    public static final int NODE_M_C_MUSICHUM = 0x10080100;//嗨歌抢唱贡献
    public static final int NODE_M_C_M_WAITSING = 0x10080101;//我的待唱
    public static final int NODE_M_MANUAL_SERVICE = 0x10090000;//客服系统
//    public static final int NODE_M_INVITE_NEW = 0x10100000;
    //20开头，用做独立的红点，5个tab之外的
    public static final int NODE_FRIST_CHARGE = 0x20010000;//首充奖励
    public static final int NODE_INVALID = 0;

    public static final int NODE_DISCOVER_DEEPLINK_ENTRANCE = 0x80000000;
    public static final int NODE_ME_DEEPLINK_ENTRANCE = 0x90000000;

    public static final int NODE_GROUP_APPLY = 0x20040000;// 群聊申请通知

    /** 根节点的 parent 是自己 */
    public final RedDotNode parent;
    public final int value;
    private boolean show = false;

    public RedDotNode(int value) {
        this.parent = this;
        this.value = value;
    }

    public RedDotNode(RedDotNode parent, int value) {
        this.parent = parent;
        this.value = value;
    }

    void showSelfAndParent() {
        RedDotNode cur = this;
        while (cur.parent != cur && !cur.show) {
            cur.show = true;
            cur = cur.parent;
        }
    }

    void hide() {
        show = false;
    }

    boolean isShowing() {
        return show;
    }
    
    static SparseArray<RedDotNode> constructNode() {
        SparseArray<RedDotNode> redDotTree = new SparseArray<>();
        redDotTree.put(RedDotNode.NODE_INVALID, new RedDotNode(RedDotNode.NODE_INVALID));

        appendNode(redDotTree, RedDotNode.NODE_INVALID, RedDotNode.NODE_GAME);
        appendNode(redDotTree, RedDotNode.NODE_GAME, RedDotNode.NODE_G_ACTIVITY);
        appendNode(redDotTree, RedDotNode.NODE_GAME, RedDotNode.NODE_G_BATTLE_PASS);

        appendNode(redDotTree, RedDotNode.NODE_INVALID, RedDotNode.NODE_DISCOVER);
        appendNode(redDotTree, RedDotNode.NODE_DISCOVER, RedDotNode.NODE_D_AUDIO_MATCH);
        appendNode(redDotTree, RedDotNode.NODE_DISCOVER, RedDotNode.NODE_D_DISCOVER);
        appendNode(redDotTree, RedDotNode.NODE_DISCOVER, RedDotNode.NODE_D_FAMILY);
        appendNode(redDotTree, RedDotNode.NODE_D_FAMILY, RedDotNode.NODE_D_F_MAIN);
        appendNode(redDotTree, RedDotNode.NODE_D_F_MAIN, RedDotNode.NODE_D_F_M_UPGRADE);
        appendNode(redDotTree, RedDotNode.NODE_D_F_MAIN, RedDotNode.NODE_D_F_M_WEEK_ACTIVE);
        appendNode(redDotTree, RedDotNode.NODE_D_F_MAIN, RedDotNode.NODE_D_F_M_WHEEL);
        appendNode(redDotTree, RedDotNode.NODE_D_FAMILY, RedDotNode.NODE_D_F_MANAGE);
        appendNode(redDotTree, RedDotNode.NODE_D_F_MANAGE, RedDotNode.NODE_D_F_MANAGE_APPLY);
        appendNode(redDotTree, RedDotNode.NODE_D_FAMILY, RedDotNode.NODE_D_F_TASK);
        appendNode(redDotTree, RedDotNode.NODE_D_FAMILY, RedDotNode.NODE_D_F_MEMBER);
        appendNode(redDotTree, RedDotNode.NODE_D_FAMILY, RedDotNode.NODE_D_F_POST);
        appendNode(redDotTree, RedDotNode.NODE_D_FAMILY, RedDotNode.NODE_D_F_BENEFIT);
        appendNode(redDotTree, RedDotNode.NODE_DISCOVER, RedDotNode.NODE_D_VOICE);

        appendNode(redDotTree, RedDotNode.NODE_INVALID, RedDotNode.NODE_ME);

        appendNode(redDotTree, RedDotNode.NODE_ME, RedDotNode.NODE_M_SHOP);

        appendNode(redDotTree, RedDotNode.NODE_M_SHOP, RedDotNode.NODE_M_S_BAG);
        appendNode(redDotTree, RedDotNode.NODE_M_S_BAG, RedDotNode.NODE_M_S_B_RP);
        appendNode(redDotTree, RedDotNode.NODE_M_SHOP, RedDotNode.NODE_M_S_RING);
        appendNode(redDotTree, RedDotNode.NODE_M_SHOP, RedDotNode.NODE_M_S_DECOR);
        appendNode(redDotTree, RedDotNode.NODE_M_SHOP, RedDotNode.NODE_M_S_PROP);

        appendNode(redDotTree, RedDotNode.NODE_ME, RedDotNode.NODE_ME_VIP);
        appendNode(redDotTree, RedDotNode.NODE_ME_VIP, RedDotNode.NODE_ME_VIP_DISCOUNT);
        appendNode(redDotTree, RedDotNode.NODE_ME_VIP, RedDotNode.NODE_ME_VIP_UPGRADE);
        appendNode(redDotTree, RedDotNode.NODE_ME_VIP, RedDotNode.NODE_ME_VIP_DOWNGRADE);
        appendNode(redDotTree, RedDotNode.NODE_ME_VIP, RedDotNode.NODE_ME_VIP_EXPIRE);
        appendNode(redDotTree, RedDotNode.NODE_ME_VIP, RedDotNode.NODE_ME_VIP_MSG_ROAMING);

        appendNode(redDotTree, RedDotNode.NODE_ME, RedDotNode.NODE_M_LOOKME);

        appendNode(redDotTree, RedDotNode.NODE_ME, RedDotNode.NODE_M_CONTRIB);
        appendNode(redDotTree, RedDotNode.NODE_M_CONTRIB, RedDotNode.NODE_M_C_MUSICHUM);
        appendNode(redDotTree, RedDotNode.NODE_M_C_MUSICHUM, RedDotNode.NODE_M_C_M_WAITSING);

//        appendNode(redDotTree, RedDotNode.NODE_ME, RedDotNode.NODE_M_INVITE_NEW);
        appendNode(redDotTree, RedDotNode.NODE_ME, RedDotNode.NODE_M_MANUAL_SERVICE);

        appendNode(redDotTree, RedDotNode.NODE_DISCOVER, RedDotNode.NODE_D_MENTOR);
        appendNode(redDotTree, RedDotNode.NODE_D_MENTOR, RedDotNode.NODE_D_MENTOR_TEACHER);
        appendNode(redDotTree, RedDotNode.NODE_D_MENTOR, RedDotNode.NODE_D_MENTOR_STUDENT);
        appendNode(redDotTree, RedDotNode.NODE_D_MENTOR, RedDotNode.NODE_D_MENTOR_APPLY);
        appendNode(redDotTree, RedDotNode.NODE_D_MENTOR, RedDotNode.NODE_D_MENTOR_REWARD);
        appendNode(redDotTree, RedDotNode.NODE_D_MENTOR, RedDotNode.NODE_D_MENTOR_DEL);
        appendNode(redDotTree, RedDotNode.NODE_D_MENTOR_TEACHER, RedDotNode.NODE_D_MENTOR_DES);

        appendNode(redDotTree, RedDotNode.NODE_INVALID, RedDotNode.NODE_FRIST_CHARGE);
        appendNode(redDotTree, RedDotNode.NODE_INVALID, RedDotNode.NODE_GROUP_APPLY);

        return redDotTree;
    }

    public static void appendNode(SparseArray<RedDotNode> redDotTree, int parent, int current) {
        redDotTree.append(current, new RedDotNode(redDotTree.get(parent), current));
    }
}
