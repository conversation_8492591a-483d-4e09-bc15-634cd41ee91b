package com.wepie.wespy.helper.redDotHelper;

import android.util.SparseArray;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.SdkBaseStore;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.store.file.FileConfig;
import com.huiwan.user.LoginHelper;
import com.three.http.callback.EmptyDataCallback;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wejoy.weplay.module.me.ManualServiceRedDotRetriever;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.RedDotInfoExt;
import com.wepie.wespy.module.family.lottery.FamilyLotteryRedDotUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.RedDotApi;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

// Created by bigwen on 2018/5/30.
public class RedDotUtil {
    private static final String FILE_NAME = FileCacheName.RED_DOT_VERSION_FILE;

    private static final RedDotUtil INSTANCE = new RedDotUtil();
    private static final long UPDATE_INTERVAL = 1000 * 60 * 10;
    private long lastUpdateTime = 0;
    private SparseArray<RedDotInfo> serverRedDots = new SparseArray<>();
    private SparseArray<RedDotInfoExt> localRedDots = new SparseArray<>();
    private List<RedDotRetriever> redDotRetrievers = new ArrayList<>();

    private final SparseArray<RedDotNode> redDotTree = RedDotNode.constructNode();
    // 发现和我Tab，deeplink红点id到node的映射
    private final Map<Integer, Integer> discoverDeepLinkRedDotIdMap = new HashMap<>();
    private final Map<Integer, Integer> meDeepLinkRedDotIdMap = new HashMap<>();

    private RedDotUtil() {
        registerRetriever(new FamilyLotteryRedDotUtil());
        registerRetriever(new ManualServiceRedDotRetriever());
    }

    public static RedDotUtil get() {
        return INSTANCE;
    }

    public void clearAll() {
        serverRedDots.clear();
        localRedDots.clear();
        lastUpdateTime = 0;
        hideAllRedDot();
    }

    public void reportRead(@RedDotInfo.AllIdInt int redDotId) {
        reportRead(redDotId, -1, new EmptyDataCallback("report_util"));
    }

    public void reportReadWithCb(@RedDotInfo.AllIdInt int redDotId, LifeDataCallback<Object> callback) {
        reportRead(redDotId, -1, callback);
    }

    public void reportRead(@RedDotInfo.AllIdInt int redDotId, int index, LifeDataCallback<Object> callback) {
        for (RedDotRetriever retriever : redDotRetrievers) {
            if (retriever.getRedDotId() == redDotId) {
                retriever.reportRead(index);
                return;
            }
        }

        RedDotInfo info = serverRedDots.get(redDotId);
        if (info != null) {
            RedDotInfoExt ext = localRedDots.get(redDotId);
            boolean updated = false;
            if (ext == null) {
                ext = RedDotInfoExt.fromRedDotInfo(info, false);
            } else {
                if (ext.version != info.version) {
                    updated = true;
                }
                ext.version = info.version;
            }
            updated |= updateReadNodeIndex(ext, index);
            if (updated) {
                localRedDots.put(redDotId, ext);
                checkIfHasNew();
                saveLocal();
                if (RedDotInfo.needReport2Server(redDotId)) {
                    RedDotApi.reportRead(redDotId, callback);
                }
            }
        }
    }

    public void update(RedDotInfo redDotInfo) {
        serverRedDots.put(redDotInfo.id, redDotInfo);
        checkIfHasNew();
    }

    @Nullable
    public List<Integer> getUpdatedData(@RedDotInfo.AllIdInt int redDotId) {
        RedDotInfo s = serverRedDots.get(redDotId);
        RedDotInfoExt l = localRedDots.get(redDotId);
        if (s == null) {
            return null;
        }
        if (l == null) {
            return s.updateData;
        }
        if (l.version < s.version) {
            return s.updateData;
        }
        return null;
    }

    public void check2UpdateRedDot() {
        check2UpdateRedDot(true);
    }

    public void check2UpdateRedDot(boolean checkLastUpdate) {
        if (checkLastUpdate) {
            if (System.currentTimeMillis() - lastUpdateTime > UPDATE_INTERVAL) {
                getAllRedDot();
            } else {
                checkIfHasNew();
            }
        } else {
            getAllRedDot();
        }
    }

    private String getUserFileFullPath() {
        return FileConfig.getUserFileFullPath(SdkBaseStore.getUid(), FILE_NAME);
    }

    private void getAllRedDot() {
        lastUpdateTime = System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(1);
        final List<RedDotInfoExt> localList = new ArrayList<>();
        String filePath = getUserFileFullPath();
        ThreadUtil.runOnUiThread(() -> {
            if (localRedDots.size() > 0) {
                latch.countDown();
                return;
            }
            ThreadUtil.runInOtherThread(() -> {
                List<RedDotInfoExt> redDotInfoList = FileUtil.loadEntityListFromFile(filePath, RedDotInfoExt.class);
                if (redDotInfoList != null) {
                    localList.addAll(redDotInfoList);
                }
                latch.countDown();
            });
        });
        RedDotApi.getAllRedDot(new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(final Result<List<RedDotInfo>> result) {
                Iterator<RedDotInfo> ite = result.data.iterator();
                serverRedDots.clear();
                while (ite.hasNext()) {
                    RedDotInfo r = ite.next();
                    serverRedDots.put(r.id, r);
                }
                try {
                    latch.await(3, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    //ignore
                }
                if (localRedDots.size() == 0 && !localList.isEmpty()) {
                    for (RedDotInfoExt redDotInfo : localList) {
                        if (redDotInfo != null) {
                            localRedDots.put(redDotInfo.id, redDotInfo);
                        }
                    }
                }
                checkIfHasNew();
            }

            @Override
            public void onFail(int i, String s) {
                TimeLogger.msg("update red dot error: " + s);
            }
        });
    }

    private void checkIfHasNew() {
        try {
            initFromLocalIfNeed();
            Set<RedDotInfoExt> redDotInfoSet = new HashSet<>();
            int size = serverRedDots.size();
            for (int i = 0; i < size; i++) {
                RedDotInfo s = serverRedDots.valueAt(i);
                RedDotInfoExt l = localRedDots.get(s.id);
                if (s.isRead()) {
                    localRedDots.put(s.id, RedDotInfoExt.fromRedDotInfo(s, true));
                } else if (l == null) {
                    redDotInfoSet.add(RedDotInfoExt.fromRedDotInfo(s, false));
                } else if (l.version < s.version) {
                    if (RedDotInfo.needReport2Server(s.id)) {
                        updatePushRedDot(s, l, redDotInfoSet);
                    } else if (s.id == RedDotInfo.RED_DOT_ACTIVITY_SELF) {
                        redDotInfoSet.add(RedDotInfoExt.fromRedDotInfo(s, s.isRead()));
                    } else {
                        updateEditionRedDot(s, l, redDotInfoSet);
                    }
                } else if (l.version == s.version) {
                    for (RedDotInfoExt.DotData dotData : l.updateData) {
                        if (!dotData.read) {
                            redDotInfoSet.add(l);
                            break;
                        }
                    }
                    //家族福利红点比较特殊，服务器不好处理version增加逻辑，这里走version不变的逻辑，添加ID特殊判断处理
                    if (s.id == RedDotInfo.RED_DOT_FAMILY_BENEFIT) {
                        redDotInfoSet.add(RedDotInfoExt.fromRedDotInfo(s, s.isRead()));
                    }
                }
            }
            for (RedDotRetriever retriever : redDotRetrievers) {
                if (retriever.hasRedDot()) {
                    redDotInfoSet.add(RedDotInfoExt.simpleUnRead(retriever.getRedDotId()));
                }
            }
            hideAllRedDot();
            addNodeByInfo(redDotInfoSet);
            if (!redDotInfoSet.isEmpty()) {
                EventDispatcher.postRedDotUpdateEvent();
            }
        } catch (Exception e) {
            TimeLogger.msg(e.getLocalizedMessage());
        }
    }

    public void updateLocalRedDot() {
        checkIfHasNew();
    }

    private void initFromLocalIfNeed() {
        if (localRedDots.size() == 0) {
            List<RedDotInfoExt> redDotInfoList = FileUtil.loadEntityListFromFile(getUserFileFullPath(), RedDotInfoExt.class);
            if (redDotInfoList != null && !redDotInfoList.isEmpty()) {
                for (RedDotInfoExt redDotInfo : redDotInfoList) {
                    if (redDotInfo != null) {
                        localRedDots.put(redDotInfo.id, redDotInfo);
                    }
                }
            }
        }
    }

    private void updatePushRedDot(RedDotInfo s, RedDotInfoExt l, Set<RedDotInfoExt> redDotInfoSet) {
        // 该类道具情况以服务器当前状态为准，如背包可能出现时长更新
        for (Integer updatedId : s.updateData) {
            boolean find = false;
            for (RedDotInfoExt.DotData dotData : l.updateData) {
                if (updatedId == dotData.id) {
                    dotData.read = false;
                    find = true;
                    break;
                }
            }
            if (!find) {
                RedDotInfoExt.DotData dotData = new RedDotInfoExt.DotData();
                dotData.id = updatedId;
                dotData.read = false;
                l.updateData.add(dotData);
            }
        }
        // push 有版本更新即有红点，不再要求 update data 中有数据
        // 如果服务器更新背包却不带item 时可能导致背包上有红点，但没有 new item。
        l.read = s.isRead();
        if (!l.read) {
            redDotInfoSet.add(l);
        }
    }

    private void updateEditionRedDot(RedDotInfo s, RedDotInfoExt l, Set<RedDotInfoExt> redDotInfoSet) {
        // 服务器有本地没有，则添加到本地未读，服务器没有本地有，则本地移除
        // 服务器有本地有，不进行处理
        // 这里先取交集，再分别处理
        Set<Integer> mergedId = new HashSet<>();
        for (RedDotInfoExt.DotData dotData : l.updateData) {
            if (s.updateData.contains(dotData.id)) {
                mergedId.add(dotData.id);
            }
        }
        boolean hasRedDot = false;
        Iterator<RedDotInfoExt.DotData> ite = l.updateData.iterator();
        while (ite.hasNext()) {
            RedDotInfoExt.DotData dot = ite.next();
            if (!mergedId.contains(dot.id)) {
                ite.remove();
            } else if (!dot.read) {
                hasRedDot = true;
            }
        }
        for (Integer id : s.updateData) {
            if (!mergedId.contains(id)) {
                RedDotInfoExt.DotData dotData = new RedDotInfoExt.DotData();
                dotData.id = id;
                dotData.read = false;
                hasRedDot = true;
                l.updateData.add(dotData);
            }
        }
        if (hasRedDot) {
            redDotInfoSet.add(l);
        }
    }

    private void saveLocal() {
        int total = localRedDots.size();
        List<RedDotInfoExt> list = new ArrayList<>(total);
        for (int i = 0; i < total; i++) {
            list.add(localRedDots.valueAt(i));
        }
        FileUtil.writeEntityAsync(getUserFileFullPath(), list);
    }

    public boolean hasRedDot(int nodeId) {
        RedDotNode node = redDotTree.get(nodeId);
        return node != null && node.isShowing();
    }

    public static void setMsgNum(TextView text, int msgNum) {
        if (msgNum == 0) {
            text.setVisibility(View.GONE);
        } else {
            String strNum = msgNum > 99 ? "..." : "" + msgNum;
            text.setText(strNum);
            text.setVisibility(View.VISIBLE);
        }
    }

    public static void setMsgNumSmall(ImageView text, int msgNum) {
        if (msgNum == 0) {
            text.setVisibility(View.GONE);
        } else {
            text.setVisibility(View.VISIBLE);
        }
    }


    private static boolean updateReadNodeIndex(RedDotInfoExt ext, int index) {
        boolean updated = false;
        if (ext.updateData.isEmpty()) {
            ext.read = true;
            updated = true;
        } else {
            for (RedDotInfoExt.DotData data : ext.updateData) {
                if (index == -1 || ext.id != RedDotInfo.RED_DOT_SHOP) {
                    if (!data.read) {
                        updated = true;
                    }
                    data.read = true;
                } else {
                    PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(data.id);
                    if (item != null) {
                        if (item.isRing()) {
                            if (!data.read && index == 0) {
                                updated = true;
                                data.read = true;
                            }
                        } else if (item.getType() == PropItem.TYPE_DRAW_BOARD ||
                                item.getType() == PropItem.TYPE_HEAD_DECORATION ||
                                item.getType() == PropItem.TYPE_HOME_ANIM) {
                            if (!data.read && index == 1) {
                                updated = true;
                                data.read = true;
                            }
                        } else if (index == 2) {
                            if (!data.read) {
                                updated = true;
                                data.read = true;
                            }
                        }
                    } else {
                        if (!data.read) {
                            updated = true;
                            data.read = true;
                        }
                    }
                }

            }
        }
        return updated;
    }

    private void addNodeByInfo(Set<RedDotInfoExt> infoSet) {
        for (RedDotInfoExt info : infoSet) {
            if (info.updateData.isEmpty()) {
                if (!info.read) {
                    addNodes(info.id, 0);
                }
            } else {
                for (RedDotInfoExt.DotData data : info.updateData) {
                    if (!data.read) {
                        addNodes(info.id, data.id);
                    }
                }
            }
        }
    }

    public void registerRetriever(RedDotRetriever redDotRetriever) {
        redDotRetrievers.add(redDotRetriever);
    }

    private void addNodes(int infoId, int dataId) {
        switch (infoId) {
            case RedDotInfo.RED_DOT_ACTIVITY_SELF:
            case RedDotInfo.RED_DOT_ACTIVITY: {
                markShowDot(RedDotNode.NODE_G_ACTIVITY);
                break;
            }
            case RedDotInfo.RED_DOT_BATTLE_PASS:
            case RedDotInfo.RED_DOT_BATTLE_PASS_COMMON: {
                markShowDot(RedDotNode.NODE_G_BATTLE_PASS);
                break;
            }
            case RedDotInfo.RED_DOT_BAG: {
                markShowDot(RedDotNode.NODE_M_S_BAG);
                break;
            }
            case RedDotInfo.RED_DOT_BAG_RP: {
                markShowDot(RedDotNode.NODE_M_S_B_RP);
                break;
            }
            case RedDotInfo.RED_DOT_VOICE: {
                markShowDot(RedDotNode.NODE_D_VOICE);
                break;
            }
            case RedDotInfo.RED_DOT_AUDIO_MATCH: {
                markShowDot(RedDotNode.NODE_D_AUDIO_MATCH);
                break;
            }
            case RedDotInfo.RED_DOT_SHOP: {
                PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(dataId);
                if (item != null) {
                    if (item.isRing()) {
                        markShowDot(RedDotNode.NODE_M_S_RING);
                    } else if (item.getType() == PropItem.TYPE_DRAW_BOARD ||
                            item.getType() == PropItem.TYPE_HEAD_DECORATION ||
                            item.getType() == PropItem.TYPE_HOME_ANIM) {
                        markShowDot(RedDotNode.NODE_M_S_DECOR);
                    } else {
                        markShowDot(RedDotNode.NODE_M_S_PROP);
                    }
                }
                break;
            }
            case RedDotInfo.RED_DOT_VIP_DISCOUNT:
                markShowDot(RedDotNode.NODE_ME_VIP_DISCOUNT);
                break;
            case RedDotInfo.RED_DOT_VIP_DOWNGRADE:
                markShowDot(RedDotNode.NODE_ME_VIP_DOWNGRADE);
                break;
            case RedDotInfo.RED_DOT_VIP_UPGRADE:
                markShowDot(RedDotNode.NODE_ME_VIP_UPGRADE);
                break;
            case RedDotInfo.RED_DOT_VIP_EXPIRE:
                markShowDot(RedDotNode.NODE_ME_VIP_EXPIRE);
                break;
            case RedDotInfo.RED_DOT_VIP_MSG_ROAMING:
                if (LoginHelper.getVipLevel() > 0) {
                    markShowDot(RedDotNode.NODE_ME_VIP_MSG_ROAMING);
                }
                break;
            case RedDotInfo.RED_DOT_FAMILY_APPLY:
                markShowDot(RedDotNode.NODE_D_F_MANAGE_APPLY);
                break;
            case RedDotInfo.RED_DOT_FAMILY_TASK:
                markShowDot(RedDotNode.NODE_D_F_TASK);
                break;
            case RedDotInfo.RED_DOT_FAMILY_BENEFIT:
                markShowDot(RedDotNode.NODE_D_F_BENEFIT);
                break;
            case RedDotInfo.RED_DOT_FAMILY_UPGRADE:
                markShowDot(RedDotNode.NODE_D_F_M_UPGRADE);
                break;
            case RedDotInfo.RED_DOT_FAMILY_WEEK_ACTIVE:
                markShowDot(RedDotNode.NODE_D_F_M_WEEK_ACTIVE);
                break;
            case RedDotInfo.RED_DOT_FAMILY_WHEEL:
                markShowDot(RedDotNode.NODE_D_F_M_WHEEL);
                break;
            case RedDotInfo.RED_DOT_LOOK_WE:
                markShowDot(RedDotNode.NODE_M_LOOKME);
                break;
            case RedDotInfo.RED_DOT_MUSIC_HUM_USER_CARD:
                markShowDot(RedDotNode.NODE_M_C_M_WAITSING);
                break;
            case RedDotInfo.RED_DOT_FIRST_CHARGE:
                markShowDot(RedDotNode.NODE_FRIST_CHARGE);
                break;
            case RedDotInfo.RED_DOT_INVITE_NEW:
//                markShowDot(RedDotNode.NODE_M_INVITE_NEW);
                break;
            case RedDotInfo.RED_DOT_MANUAL_SERVICE:
                markShowDot(RedDotNode.NODE_M_MANUAL_SERVICE);
                break;
            case RedDotInfo.RED_DOT_TEACHER_MENTORSHIP:
                markShowDot(RedDotNode.NODE_D_MENTOR_TEACHER);
                break;
            case RedDotInfo.RED_DOT_STUDENT_MENTORSHIP:
                markShowDot(RedDotNode.NODE_D_MENTOR_STUDENT);
                break;
            case RedDotInfo.RED_DOT_MENTOR_APPLY_PASS:
                markShowDot(RedDotNode.NODE_D_MENTOR_APPLY);
                break;
            case RedDotInfo.RED_DOT_MENTOR_DES:
                markShowDot(RedDotNode.NODE_D_MENTOR_DES);
                break;
            case RedDotInfo.RED_DOT_MENTOR_REWARD:
                markShowDot(RedDotNode.NODE_D_MENTOR_REWARD);
                break;
            case RedDotInfo.RED_DOT_MENTOR_DEL:
                markShowDot(RedDotNode.NODE_D_MENTOR_DEL);
                break;
            case RedDotInfo.RED_DOT_GROUP_APPLY:
                markShowDot(RedDotNode.NODE_GROUP_APPLY);
                break;
            default: {
                break;
            }
        }

        if (meDeepLinkRedDotIdMap.containsKey(infoId)) {
            markShowDot(meDeepLinkRedDotIdMap.get(infoId));
        } else if (discoverDeepLinkRedDotIdMap.containsKey(infoId)) {
            markShowDot(discoverDeepLinkRedDotIdMap.get(infoId));
        } else {
            markShowDot(RedDotNode.NODE_INVALID);
        }
    }

    public static int getRedDotNode(int redDotId) {
        switch (redDotId) {
            case RedDotInfo.RED_DOT_TEACHER_MENTORSHIP:
                return RedDotNode.NODE_D_MENTOR_TEACHER;
            case RedDotInfo.RED_DOT_STUDENT_MENTORSHIP:
                return RedDotNode.NODE_D_MENTOR_STUDENT;
            case RedDotInfo.RED_DOT_MENTOR_APPLY_PASS:
                return RedDotNode.NODE_D_MENTOR_APPLY;
            case RedDotInfo.RED_DOT_MENTOR_DES:
                return RedDotNode.NODE_D_MENTOR_DES;
            case RedDotInfo.RED_DOT_MENTOR_REWARD:
                return RedDotNode.NODE_D_MENTOR_REWARD;
            case RedDotInfo.RED_DOT_MENTOR_DEL:
                return RedDotNode.NODE_D_MENTOR_DEL;
            default: {
                if (RedDotUtil.get().meDeepLinkRedDotIdMap.containsKey(redDotId)) {
                    return RedDotUtil.get().meDeepLinkRedDotIdMap.get(redDotId);
                } else if (RedDotUtil.get().discoverDeepLinkRedDotIdMap.containsKey(redDotId)) {
                    return RedDotUtil.get().discoverDeepLinkRedDotIdMap.get(redDotId);
                } else {
                    return RedDotNode.NODE_INVALID;
                }
            }
        }
    }

    /**
     * 不要直接从这里取数据，先判断是否有红点
     *
     * @param redDotId
     * @return
     */
    @Nullable
    public RedDotInfo getRedDotInfo(int redDotId) {
        return serverRedDots.get(redDotId);
    }

    private void markShowDot(int nodeId) {
        markShowDot(redDotTree.get(nodeId));
    }

    private void markShowDot(RedDotNode node) {
        if (node != null) {
            node.showSelfAndParent();
        }
    }

    private void hideAllRedDot() {
        int size = redDotTree.size();
        for (int i = 0; i < size; i++) {
            redDotTree.valueAt(i).hide();
        }
    }

    public void addMeDeeplinkNode2Tree(List<Integer> idList) {
        if (idList == null || idList.isEmpty()) {
            return;
        }

        if (!meDeepLinkRedDotIdMap.isEmpty()) {
            return;
        }

        for (int i = 0; i < idList.size(); i++) {
            int id = idList.get(i);
            if (!meDeepLinkRedDotIdMap.containsKey(id)) {
                meDeepLinkRedDotIdMap.put(id, RedDotNode.NODE_ME_DEEPLINK_ENTRANCE + i);
                RedDotNode.appendNode(redDotTree, RedDotNode.NODE_ME, RedDotNode.NODE_ME_DEEPLINK_ENTRANCE + i);
            }
        }
    }

    public void addDiscoverDeeplinkNode2Tree(List<Integer> idList) {
        if (idList == null || idList.isEmpty()) {
            return;
        }

        if (!discoverDeepLinkRedDotIdMap.isEmpty()) {
            return;
        }

        for (int i = 0; i < idList.size(); i++) {
            int id = idList.get(i);
            if (!discoverDeepLinkRedDotIdMap.containsKey(id)) {
                discoverDeepLinkRedDotIdMap.put(id, RedDotNode.NODE_DISCOVER_DEEPLINK_ENTRANCE + i);
                RedDotNode.appendNode(redDotTree, RedDotNode.NODE_DISCOVER, RedDotNode.NODE_DISCOVER_DEEPLINK_ENTRANCE + i);
            }
        }
    }

}
