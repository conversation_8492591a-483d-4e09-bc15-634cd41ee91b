package com.wepie.wespy.helper;

import com.huiwan.user.entity.FriendInfo;
import com.huiwan.base.util.StringUtil;

import java.util.Comparator;

/**
 * Created by geeksammao on 24/01/2018.
 */

public class FriendPinyinComparator implements Comparator<FriendInfo> {

    @Override
    public int compare(FriendInfo o1, FriendInfo o2) {

        String str1 = o1.getNameFirstLetter();
        String str2 = o2.getNameFirstLetter();

        if (!StringUtil.isCharacter(str1)) {
            str1 = "#";
        }

        if (!StringUtil.isCharacter(str2)) {
            str2 = "#";
        }

        if (str1.equals(str2)) return String.valueOf(o1.getUid()).compareTo(String.valueOf(o2.getUid()));

        if (str2.equals("#")) {
            return -1;
        } else if (str1.equals("#")) {
            return 1;
        } else {
            return str1.compareTo(str2);
        }

    }
}
