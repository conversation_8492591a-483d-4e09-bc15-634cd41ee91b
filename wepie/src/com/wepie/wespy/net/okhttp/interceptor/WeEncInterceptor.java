package com.wepie.wespy.net.okhttp.interceptor;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.Base64;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.store.PrefUserUtil;
import com.three.http.interceptor.EncInterceptor;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.module.abtest.AbTestNormalManager;
import com.wepie.wespy.utils.PackageUtil;
import com.wp.s.SecService;

import java.util.HashMap;
import java.util.Map;

public class WeEncInterceptor implements EncInterceptor {
    @Override
    public byte[] encrypt(byte[] reqBody) {
        return SecService.encS6(reqBody);
    }

    @Override
    public byte[] decrypt(byte[] rsp) {
        byte[] decRsp = SecService.decS6(rsp);
        if (decRsp == null) {
            String ori = Base64.encode(rsp);
            Map<String, String> map = new HashMap<>();
            map.put("ori_data", ori);
            map.put("rsk", "0x" + Integer.toHexString(SecService.getSimpleRiskMask()));
            PackageUtil.dumpSigInfo(map);
            HLog.aliLog(AliNetLogUtil.PORT.httpFail, AliNetLogUtil.TYPE.err, "error_dec_data", map);
        }

        return decRsp;
    }

    public boolean needEnc(String url) {
        boolean superAdmin = ConfigHelper.getInstance().superAdminDevice() || LibBaseUtil.buildDebug();
        boolean flag = AbTestNormalManager.getInstance().needEncrypt();
        if (superAdmin) {
            boolean userSwitch = PrefUserUtil.getInstance().getBoolean(PrefUserUtil.USER_ENCRYPT_SWITCH, false);
            if (userSwitch) {
                flag = PrefUserUtil.getInstance().getBoolean(PrefUserUtil.ENCRYPT_SWITCH, true);
            }
        }
        return flag;
    }

    @Override
    public void addEncHeader(Map<String, String> headerMap) {
        headerMap.put("X-ENCRYPTED-VERSION", "250210");
    }
}
