package com.wepie.wespy.net.http.api;

import com.three.http.callback.LifeDataCallback;
import com.three.http.core.HttpUtil;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.model.entity.HotActivityInfo;
import com.wepie.wespy.model.entity.NavigationInfo;
import com.wepie.wespy.model.entity.VoiceRoomCenterConfigInfo;

/**
 * Created by dsc on 20/4/1.
 */
public class ActivityTabApi {
    public static void getNavigation(String version, LifeDataCallback<NavigationInfo> callback) {
        HttpUtil.newBuilder().addParam("version_num", version)
                .uri(UrlConfig.ACTIVITY_V1_GET_NAVIGATION)
                .build().post(callback);
    }

    public static void getHotActivity(int uid, String lang, int page, int pageSize,
                                      LifeDataCallback<HotActivityInfo> callback) {
        HttpUtil.newBuilder()
                .addParam("uid", String.valueOf(uid))
                .addParam("lang", lang)
                .addParam("page", String.valueOf(page))
                .addParam("page_size", String.valueOf(pageSize))
                .uri(UrlConfig.GET_HOT_ACTIVITIES)
                .build().post(callback);
    }

    public static void getConfig(int uid, String lang, LifeDataCallback<VoiceRoomCenterConfigInfo> callback) {
        HttpUtil.newBuilder()
                .addParam("uid", String.valueOf(uid))
                .addParam("lang", lang)
                .uri(UrlConfig.VOICE_ROOM_CENTER_GET_CONFIG)
                .build().post(callback);
    }
}
