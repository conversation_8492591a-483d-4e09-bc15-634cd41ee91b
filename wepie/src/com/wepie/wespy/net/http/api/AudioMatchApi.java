package com.wepie.wespy.net.http.api;

import com.huiwan.user.LoginHelper;
import com.three.http.callback.LifeDataCallback;
import com.three.http.core.HttpUtil;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.model.entity.AudioMatchAvatarsInfo;
import com.wepie.wespy.model.entity.AudioMatchInfo;
import com.wepie.wespy.model.entity.AudioMatchTips;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.module.match.help.AudioMatchSelectInfo;

/**
 * Created by dsc on 20/4/1.
 */
public class AudioMatchApi {

    public static void matchInfo(LifeDataCallback<AudioMatchInfo> callback) {
        HttpUtil.newBuilder()
                .addParam("uid", String.valueOf(LoginHelper.getLoginUid()))
                .addParam("game_type", String.valueOf(RoomInfo.GAME_TYPE_AUDIO_MATCH))
                .uri(UrlConfig.AUDIO_MATCH_MATCH_INFO)
                .build().post(callback);
    }

    public static void submitFeedBack(int star, int uid, LifeDataCallback<Object> callback) {
        HttpUtil.newBuilder()
                .addParam("target_uid", String.valueOf(uid))
                .addParam("star", String.valueOf(star))
                .addParam("game_type", String.valueOf(RoomInfo.GAME_TYPE_AUDIO_MATCH))
                .uri(UrlConfig.AUDIO_MATCH_SUBMIT_FEED_BACK)
                .build().post(callback);
    }

    public static void getMatchingPlayers(LifeDataCallback<AudioMatchAvatarsInfo> callback) {
        HttpUtil.newBuilder()
                .uri(UrlConfig.AUDIO_MATCH_MATCHING_PLAYERS)
                .build().post(callback);
    }

    public static void getTips(int gender, LifeDataCallback<AudioMatchTips> callback) {
        HttpUtil.newBuilder()
                .addParam("gender", String.valueOf(gender))
                .uri(UrlConfig.AUDIO_MATCH_TIPS)
                .build().post(callback);
    }

    public static void setAudioMatchInfo(AudioMatchSelectInfo info, LifeDataCallback<Object> callback) {
        HttpUtil.newBuilder()
                .uri(UrlConfig.SET_VOICE_MATCH_SETTING)
                .addParam("prefer_gender", String.valueOf(info.getPreferGender()))
                .addParam("spy_16_state", String.valueOf(info.getSixteenState()))
                .addParam("gender", String.valueOf(info.getGender()))
                .build()
                .post(callback);
    }

    public static void getAudioMatchInfo(LifeDataCallback<AudioMatchSelectInfo> callback) {
        HttpUtil.newBuilder()
                .uri(UrlConfig.GET_VOICE_MATCH_SETTING)
                .build()
                .post(callback);
    }

    public static void notMatchUser(int uid, boolean isBlock, LifeDataCallback<Object> callback) {
        HttpUtil.newBuilder()
                .addParam("target_uid", String.valueOf(uid))
                .addParam("block", String.valueOf(isBlock))
                .uri(UrlConfig.AUDIO_MATCH_BLOCK)
                .build().post(callback);
    }
}
