package com.wepie.wespy.net.http.api;

import com.three.http.callback.LifeDataCallback;
import com.three.http.core.HttpUtil;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.model.entity.task.ShareCodeTaskInfo;
import com.wepie.wespy.model.entity.task.TaskListInfo;

/**
 * Created by three on 16/1/18.
 */
public class TaskApi {
    public static void recvTask(int task_id,  LifeDataCallback<Object> callback) {
        HttpUtil.newBuilder().uri(UrlConfig.TASK_API_RECV_REWARD)
                .addParam("task_id", String.valueOf(task_id))
                .build().post(callback);
    }

    public static void recvStage(int stage_id,  LifeDataCallback<Object> callback) {
        HttpUtil.newBuilder().uri(UrlConfig.RECV_STAGE_REWARD)
                .addParam("stage_id", String.valueOf(stage_id))
                .build().post(callback);
    }

    /**
     * @param task_type 加了一个参数task_type. 3表示家族任务，不填就默认的。领取任务奖励的接口和之前的一样
     */
    public static void recvStage(int stage_id, int task_type, LifeDataCallback<Object> callback) {
        HttpUtil.newBuilder().uri(UrlConfig.RECV_STAGE_REWARD)
                .addParam("stage_id", String.valueOf(stage_id))
                .addParam("task_type", String.valueOf(task_type))
                .build().post(callback);
    }

    public static void getShareCodeTaskInfo(LifeDataCallback<ShareCodeTaskInfo> callback){
        HttpUtil.newBuilder().uri(UrlConfig.TASK_API_GET_SHARE_CODE_TASK_INFO).build().post(callback);
    }

    public static void getTaskListNew(LifeDataCallback<TaskListInfo> callback) {
        HttpUtil.newBuilder()
                .uri(UrlConfig.GET_TASK_LIST_API)
                .build().post(callback);
    }

    /**
     * @param task_type 加了一个参数task_type. 3表示家族任务，不填就默认的。领取任务奖励的接口和之前的一样
     */
    public static void getTaskListNew(int task_type, LifeDataCallback<TaskListInfo> callback) {
        HttpUtil.newBuilder()
                .uri(UrlConfig.GET_TASK_LIST_API)
                .addParam("task_type", String.valueOf(task_type))
                .build().post(callback);
    }
}
