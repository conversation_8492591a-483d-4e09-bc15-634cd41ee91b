package com.wepie.wespy.net.http.api;

import com.huiwan.user.LoginHelper;
import com.three.http.callback.LifeDataCallback;
import com.three.http.core.HttpUtil;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.model.entity.AllowRecommendInfo;

/**
 * Created by three on 15/12/19.
 */
public class NearbyApi {

    /**
     * @param option 选项，所有人可见=1，好友可见=2，不可见=3
     */
    public static void switchGameStateVisible(int option, LifeDataCallback<Object> callback) {
        HttpUtil.newBuilder().addParam("option", String.valueOf(option))
                .uri(UrlConfig.NEARBY_API_SWITCH_USER_GAME_STATE_VISIBLE).build().post(callback);
    }

    /**
     * 所有人可见=1，好友可见=2，不可见=3
     */
    public static void getGameStateVisible(LifeDataCallback<Integer> callback) {
        HttpUtil.newBuilder().uri(UrlConfig.NEARBY_API_GET_USER_GAME_STATE_VISIBLE).build().post(callback);
    }

    public static void setRecommend(boolean allowRecommend, LifeDataCallback<Object> callback) {
        HttpUtil.newBuilder()
                .addParam("uid", String.valueOf(LoginHelper.getLoginUid()))
                .addParam("need_recommend", allowRecommend ? "1" : "0")
                .uri(UrlConfig.NEARBY_API_SET_RECOMMEND)
                .build().post(callback);
    }

    public static void getRecommend(LifeDataCallback<AllowRecommendInfo> callback) {
        HttpUtil.newBuilder().uri(UrlConfig.NEARBY_API_GET_RECOMMEND).build().post(callback);
    }

}
