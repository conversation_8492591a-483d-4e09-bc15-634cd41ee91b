package com.wepie.wespy.net.http.api;

import com.three.http.callback.DataCallback;
import com.three.http.core.HttpUtil;
import com.wepie.wespy.config.BaseConfig;
import com.wepie.wespy.config.UrlConfig;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.wepie.wespy.model.entity.GiftResultData;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.wepie.wespy.model.entity.marry.NestPageInfo;

/**
 * Created by <PERSON>wen on 2016/5/25.
 */
public class NestAPI {

    public static void getNestPage(int target_uid, DataCallback<NestPageInfo> callback) {
        HttpUtil.newBuilder()
                .uri(UrlConfig.NEST_API_PAGE)
                .addParam("target_uid", String.valueOf(target_uid))
                .build()
                .post(callback);
    }

    public static void sendGift(int userMarryId, GiftSendInfo sendInfo, DataCallback<GiftResultData> callback){
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(sendInfo.giftId);
        HttpUtil.newBuilder()
                .uri(UrlConfig.NEST_API_SEND_GIFT)
                .addParam("user_marry_id", String.valueOf(userMarryId))
                .addParam("gift_id", String.valueOf(sendInfo.giftId))
                .addParam("number", String.valueOf(sendInfo.giftNum))
                .addParam("is_public", String.valueOf(sendInfo.isPrivate ? BaseConfig.SERVER_NO : BaseConfig.SERVER_YES))
                .addParam("gift_type", String.valueOf(gift == null ? 0 : gift.getGiftType()))
                .addParam("is_gift_card", String.valueOf(sendInfo.isGiftCard ? 1 : 0))
                .build().post(callback);
    }

    public static void setGiftAuth(int giftAuth, DataCallback<String> callback) {
        HttpUtil.newBuilder()
                .uri(UrlConfig.NEST_API_SET_GIFT_AUTH)
                .addParam("gift_auth", giftAuth + "")
                .build()
                .post(callback);
    }
}
