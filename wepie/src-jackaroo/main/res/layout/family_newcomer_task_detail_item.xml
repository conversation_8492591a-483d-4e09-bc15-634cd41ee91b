<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_ffffff_corner6"
    android:paddingHorizontal="16dp"
    android:paddingVertical="10dp">

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/task_title_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:gravity="start"
        android:textColor="@color/color_text_accent_dark"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/task_detail_state_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:sizeAndFont="tajawal_bold|Body2"
        tools:text="Open Family Gacha 1 time" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/task_desc_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        android:gravity="start"
        android:textColor="@color/color_text_primary_ex"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/task_detail_state_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/task_title_tv"
        app:sizeAndFont="tajawal|Body3"
        tools:text="Open Family Gacha 1 time" />

    <LinearLayout
        android:id="@+id/reward_lay"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginTop="9dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/task_desc_tv" />

    <TextView
        android:id="@+id/task_detail_state_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/tajawal"
        android:paddingHorizontal="18dp"
        android:paddingVertical="4dp"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>