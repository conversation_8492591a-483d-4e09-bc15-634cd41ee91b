<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/benefit_big_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:sizeAndFont="tajawal_bold|H4"
        tools:text="Newcomer Benefits" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/benefit_desc_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/white_alpha70"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/benefit_big_title_tv"
        app:sizeAndFont="tajawal|Body2"
        tools:text="finish newcomer task,acquire benefit!" />

    <com.wepie.wespy.module.family.main.mine.task.Pager2HostRvFrameLayout
        android:id="@+id/newcomer_benefit_gift_preview_rv_container"
        android:layout_width="wrap_content"
        android:layout_height="64dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/benefit_desc_tv"
        app:layout_constraintWidth_max="209dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/newcomer_benefit_gift_preview_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.wepie.wespy.module.family.main.mine.task.Pager2HostRvFrameLayout>


    <View
        android:id="@+id/rv_fade_bg"
        android:layout_width="8dp"
        android:layout_height="64dp"
        app:layout_constraintEnd_toEndOf="@id/newcomer_benefit_gift_preview_rv_container"
        app:layout_constraintTop_toTopOf="@id/newcomer_benefit_gift_preview_rv_container" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/newcomer_meeting_reward_state_btn"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:gravity="center_vertical"
        android:maxWidth="88dp"
        android:maxLines="1"
        android:paddingHorizontal="22dp"
        android:textSize="14sp"
        android:textStyle="bold"
        app:autoSizeMaxTextSize="14sp"
        app:autoSizeMinTextSize="8sp"
        app:autoSizeStepGranularity="1sp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="@id/newcomer_benefit_gift_preview_rv_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/newcomer_benefit_gift_preview_rv_container"
        tools:text="geggeggeggeggeg" />
</androidx.constraintlayout.widget.ConstraintLayout>