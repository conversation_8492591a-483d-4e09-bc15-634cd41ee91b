<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="android.widget.FrameLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:paddingHorizontal="12dp"
        android:paddingTop="12dp"
        android:paddingBottom="4dp"
        tools:background="#FF9716">

        <ImageView
            android:id="@+id/bg_star"
            android:layout_width="146dp"
            android:layout_height="72dp"
            android:layout_marginTop="-12dp"
            android:layout_marginEnd="-12dp"
            android:src="@drawable/family_welcome_bg_star"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/help_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="-4dp"
            android:layout_marginEnd="-4dp"
            android:tint="@color/white_alpha70"
            android:tintMode="src_in"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/action_bar_icon_help" />

        <include
            android:id="@+id/newcomer_meeting_task_lay"
            layout="@layout/family_newcomer_meeting_task_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <View
            android:id="@+id/divider_start"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@drawable/gradient_80ffffff_00ffffff"
            android:rotation="180"
            app:layout_constraintBottom_toBottomOf="@id/divider_title_tv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/divider_title_tv"
            app:layout_constraintTop_toTopOf="@id/divider_title_tv" />

        <com.huiwan.base.ui.WPUIText
            android:id="@+id/divider_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:layout_marginTop="12dp"
            android:textColor="@color/white"
            app:layout_constraintLeft_toRightOf="@id/divider_start"
            app:layout_constraintRight_toLeftOf="@id/divider_end"
            app:layout_constraintTop_toBottomOf="@id/newcomer_meeting_task_lay"
            app:sizeAndFont="tajawal|Body2"
            tools:text="has finish(2/4)" />

        <View
            android:id="@+id/divider_end"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@drawable/gradient_80ffffff_00ffffff"
            app:layout_constraintBottom_toBottomOf="@id/divider_title_tv"
            app:layout_constraintLeft_toRightOf="@id/divider_title_tv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/divider_title_tv" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/task_details_container_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider_title_tv"
            tools:layout_height="200dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>