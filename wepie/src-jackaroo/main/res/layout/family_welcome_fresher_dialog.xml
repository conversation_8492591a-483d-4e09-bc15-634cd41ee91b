<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:paddingHorizontal="24dp"
    android:paddingTop="16dp">

    <View
        android:id="@+id/simple_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#FFD979" />

    <ImageView
        android:layout_width="146dp"
        android:layout_height="72dp"
        android:src="@drawable/family_welcome_bg_star"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/bg_gift_iv"
        android:layout_width="132dp"
        android:layout_height="82dp"
        android:layout_marginTop="-16dp"
        android:layout_marginEnd="5dp"
        android:src="@drawable/family_welcome_bg_gift"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/simple_bg" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/title_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:gravity="start"
        android:textColor="@color/white"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/bg_gift_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:sizeAndFont="tajawal_bold|H2"
        tools:text="Welcome!" />

    <View
        android:id="@+id/intro_bg"
        android:layout_width="match_parent"
        android:layout_height="92dp"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/shape_ffffff_corner8"
        app:layout_constraintTop_toBottomOf="@id/title_tv" />

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/family_head_iv"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="26dp"
        app:layout_constraintBottom_toBottomOf="@id/intro_bg"
        app:layout_constraintStart_toStartOf="@id/intro_bg"
        app:layout_constraintTop_toTopOf="@id/intro_bg" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/family_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="18dp"
        android:ellipsize="end"
        android:maxWidth="147dp"
        android:textColor="@color/color_text_accent_gray"
        app:layout_constraintStart_toEndOf="@id/family_head_iv"
        app:layout_constraintTop_toTopOf="@id/intro_bg"
        app:sizeAndFont="tajawal_bold|Body1"
        tools:text="Family" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/leader_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxWidth="147dp"
        android:textColor="@color/color_text_primary_ex"
        app:layout_constraintStart_toStartOf=" @id/family_name_tv"
        app:layout_constraintTop_toBottomOf="@id/family_name_tv"
        app:sizeAndFont="tajawal_bold|Body3"
        tools:text="Leader:BB" />

    <com.wepie.wespy.module.family.FamilyLevelIconView
        android:id="@+id/level_iv"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        app:layout_constraintBottom_toBottomOf="@id/place_signal"
        app:layout_constraintStart_toStartOf="@id/family_name_tv"
        app:layout_constraintTop_toTopOf="@id/place_signal" />

    <View
        android:id="@+id/place_signal"
        android:layout_width="1dp"
        android:layout_height="22dp"
        android:layout_marginTop="2dp"
        app:layout_constraintBottom_toBottomOf="@id/intro_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/leader_tv"
        app:layout_constraintVertical_bias="0" />

    <com.huiwan.widget.FamilyLightView
        android:id="@+id/family_light"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/place_signal"
        app:layout_constraintStart_toEndOf="@id/level_iv"
        app:layout_constraintTop_toTopOf="@id/place_signal" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/welcome_desc_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="12dp"
        android:textColor="@color/white"
        app:layout_constraintTop_toBottomOf="@id/intro_bg"
        app:sizeAndFont="tajawal|Body1" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/got_btn"
        android:layout_width="183dp"
        android:layout_height="40dp"
        android:layout_marginVertical="24dp"
        android:gravity="center"
        android:text="@string/voice_room_members_do_task_title"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/welcome_desc_tv"
        app:sizeAndFont="tajawal_bold|H4" />
</androidx.constraintlayout.widget.ConstraintLayout>