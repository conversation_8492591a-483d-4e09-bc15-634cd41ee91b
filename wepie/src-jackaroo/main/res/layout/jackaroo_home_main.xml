<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#42181C"
    android:clipChildren="false">

    <include
        android:id="@+id/jackaroo_home_bg_lay1"
        layout="@layout/jackaroo_home_body_bg_lay"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/head_iv" />

    <include
        android:id="@+id/jackaroo_home_bg_lay2"
        layout="@layout/jackaroo_home_body_bg_lay"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/head_iv" />

    <View
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/jackaroo_home_bg_shade"
        android:scaleY="-1"
        app:layout_constraintTop_toTopOf="@id/jackaroo_home_bg_lay2" />

    <View
        android:layout_width="match_parent"
        android:layout_height="38dp"
        android:background="@drawable/jackaroo_home_bg_shade"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/home_head_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="-24dp"
        android:scaleType="fitXY"
        android:src="@drawable/jackaroo_home_nav_bg"
        app:layout_constraintBottom_toBottomOf="@id/head_iv"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/status_bar_view"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/head_iv"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/status_bar_view" />

    <com.huiwan.decorate.JackarooLevelView
        android:id="@+id/level_view"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginStart="14dp"
        android:scaleX="1.4"
        android:scaleY="1.4"
        app:layout_constraintBottom_toBottomOf="@id/head_iv"
        app:layout_constraintStart_toEndOf="@id/head_iv"
        app:layout_constraintTop_toTopOf="@id/head_iv" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coin_lay"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="@id/head_iv"
        app:layout_constraintStart_toEndOf="@id/level_view"
        app:layout_constraintTop_toTopOf="@id/head_iv">

        <TextView
            android:id="@+id/coin_tv"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="10dp"
            android:background="@color/color_transparent30"
            android:gravity="center"
            android:includeFontPadding="false"
            android:minWidth="37dp"
            android:paddingStart="16dp"
            android:paddingEnd="14dp"
            android:textColor="@color/jackaroo_res_txt_color"
            android:textFontWeight="600"
            android:textSize="11dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/coin_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@drawable/game_chip"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/coin_add_iv"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:background="@drawable/jackaroo_coin_add"
            android:textFontWeight="600"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/diamond_lay"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginStart="8dp"
        app:layout_constraintBottom_toBottomOf="@id/head_iv"
        app:layout_constraintStart_toEndOf="@id/coin_lay"
        app:layout_constraintTop_toTopOf="@id/head_iv">

        <TextView
            android:id="@+id/diamond_tv"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:background="@color/color_transparent30"
            android:gravity="center"
            android:includeFontPadding="false"
            android:minWidth="37dp"
            android:paddingStart="16dp"
            android:paddingEnd="14dp"
            android:textColor="@color/jackaroo_res_txt_color"
            android:textFontWeight="600"
            android:textSize="11dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/diamond_iv"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:background="@drawable/wejoy_coin_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/diamond_add_iv"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:background="@drawable/jackaroo_coin_add"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageView
        android:id="@+id/event_iv"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="20dp"
        android:scaleType="centerInside"
        android:src="@drawable/jackaroo_event"
        app:layout_constraintBottom_toBottomOf="@id/head_iv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/head_iv" />

    <com.huiwan.widget.HWEffectTextView
        android:id="@+id/event_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="0dp"
        android:lineSpacingMultiplier="1"
        android:text="@string/home_user_view_5"
        android:textColor="@color/white"
        android:textFontWeight="700"
        android:textSize="12dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/event_iv"
        app:layout_constraintEnd_toEndOf="@id/event_iv"
        app:layout_constraintStart_toStartOf="@id/event_iv"
        app:stroke_color="@color/jackaroo_txt_stroke"
        app:stroke_width="2" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/jackaroo_home_config_vp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="12dp"
        app:layout_constraintBottom_toTopOf="@id/jackaroo_home_indicator_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/jackaroo_home_bg_lay1"
        app:layout_goneMarginBottom="32dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/jackaroo_home_indicator_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/jackaroo_home_config_vp" />

    <include
        android:id="@+id/qualifying_entry_lay"
        layout="@layout/jackaroo_home_qualifying_entry"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/home_head_bg_iv" />

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/friends_iv"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:src="@drawable/jackaroo_friend"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/qualifying_entry_lay" />

    <com.huiwan.widget.HWEffectTextView
        android:id="@+id/friends_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-14dp"
        android:gravity="center"
        android:lineSpacingExtra="0dp"
        android:lineSpacingMultiplier="1"
        android:maxWidth="64dp"
        android:text="@string/jackaroo_home_friends"
        android:textColor="@color/white"
        android:textFontWeight="700"
        android:textSize="12dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/friends_iv"
        app:layout_constraintStart_toStartOf="@id/friends_iv"
        app:layout_constraintTop_toBottomOf="@id/friends_iv"
        app:stroke_color="@color/jackaroo_txt_stroke"
        app:stroke_width="2" />

    <ImageView
        android:id="@+id/task_iv"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:src="@drawable/jackaroo_task"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/friends_tv" />

    <View
        android:id="@+id/task_rd"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/small_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/task_iv"
        app:layout_constraintTop_toTopOf="@id/task_iv"
        tools:visibility="visible" />

    <com.huiwan.widget.HWEffectTextView
        android:id="@+id/task_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:maxWidth="64dp"
        android:padding="0dp"
        android:text="@string/home_user_view_8"
        android:textColor="@color/white"
        android:textFontWeight="700"
        android:textSize="12dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/task_iv"
        app:layout_constraintStart_toStartOf="@id/task_iv"
        app:layout_constraintTop_toTopOf="@id/task_iv"
        app:stroke_color="@color/jackaroo_txt_stroke"
        app:stroke_width="2" />

    <ViewStub
        android:id="@+id/battle_pass_vs"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout="@layout/jackaroo_home_battle_pass_lay"
        app:layout_constraintEnd_toEndOf="@id/task_iv"
        app:layout_constraintStart_toStartOf="@+id/task_iv"
        app:layout_constraintTop_toBottomOf="@id/task_tv" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/qualifying_entry_lay">

        <ViewStub
            android:id="@+id/recommend_activity_vs"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout="@layout/jackaroo_home_recommend_activity_lay"
            android:visibility="gone" />

        <ViewStub
            android:id="@+id/down_recommend_activity_vs"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout="@layout/jackaroo_home_recommend_activity_lay"
            android:visibility="gone" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>