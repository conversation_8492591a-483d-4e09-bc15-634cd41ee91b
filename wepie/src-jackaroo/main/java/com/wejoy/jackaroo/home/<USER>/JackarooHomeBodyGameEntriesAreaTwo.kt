package com.wejoy.jackaroo.home.gameentry

import android.view.LayoutInflater
import android.view.ViewGroup
import com.wejoy.jackaroo.home.GameAreaType
import com.wepie.wespy.databinding.JackarooHomeBodyGameEntriesTwoBinding

class JackarooHomeBodyGameEntriesAreaTwo(
    parent: ViewGroup
) : JackarooHomeBodyGameEntriesArea {
    override val type: GameAreaType = GameAreaType.TWO_ENTRIES
    private val binding = JackarooHomeBodyGameEntriesTwoBinding.inflate(
        LayoutInflater.from(parent.context), parent, true
    )

    override val holders: List<GameEntryViewHolder> = listOf(
        GameEntryViewHolder(binding.nameTv1, binding.bgIv1),
        GameEntryViewHolder(binding.nameTv2, binding.bgIv2),
    )

}