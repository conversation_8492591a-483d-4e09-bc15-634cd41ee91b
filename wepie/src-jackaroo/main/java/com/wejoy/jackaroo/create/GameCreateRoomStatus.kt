package com.wejoy.jackaroo.create

import com.huiwan.configservice.constentity.JkGameModeInfo
import com.huiwan.configservice.editionentity.GameConfig.JACKAROO_MODE_COMPLEX
import com.huiwan.configservice.editionentity.GameConfig.MatchInfo
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.welib.alinetlog.AliNetLogUtil
import com.welib.alinetlog.AliNetLogUtil.PORT
import com.wepie.liblog.main.HLog


data class GameBetEntryItem(
    val gameMode: Int = 0,
    val iconUrl: String = "",
    val modeName: String = "",

    )

data class EntryFeeDate(
    //底分
    val baseScore: Int? = null,
    val coin: Int = 0,
    //经验倍数
    val expMultiplier: String = "",
)

data class GameCreateRoomStatus(
    val gameType: Int = 0,
    val vipMode: Boolean = false,
    val coinNum: Long = 0,
    val selectedMode: Int = JACKAROO_MODE_COMPLEX,
    val allowObserve: Boolean = true,
    val betLevelIndex: Int = 0,
    val entryEntryList: List<GameBetEntryItem> = emptyList(),
    val entryFeeData: EntryFeeDate = EntryFeeDate(),
    val vipBgData: VipBgData = VipBgData()
)

sealed class GameCreateIntent {
    class ChangeObserver(val allow: Boolean) : GameCreateIntent()
    class ChangeGameMode(val gameMode: Int) : GameCreateIntent()
    data object Create : GameCreateIntent()
    data object JoinBtnAction : GameCreateIntent()
    data object ChargeCoinAction : GameCreateIntent()
    data object IncEntryFeeAction : GameCreateIntent()
    data object DecEntryFeeAction : GameCreateIntent()
    data object ShowHelpAction : GameCreateIntent()
}

data class VipBgData(
    val bgUrl: String = "",
    val titleIconUrl: String = ""
)

interface JackarooCreateIntentHandler {
    fun onIntent(intent: GameCreateIntent)
}

sealed class JackarooCreateAction {
    data object ShowLoading : JackarooCreateAction()
    data object HideLoading : JackarooCreateAction()
    class JumpToWait(val vip: Boolean, val gameType: Int) : JackarooCreateAction()
    class CreateTmpVoiceRoom(val matchInfo: MatchInfo, val gameType: Int) : JackarooCreateAction()
    class ShowError(
        val code: Int, val desc: String, val info: LittleGameSimpleInfo,
        val next: () -> Unit
    ) : JackarooCreateAction()

    class ShowToast(val msg: String) : JackarooCreateAction()
}

internal fun JkGameModeInfo.toGameBetEntryItem(isVip: Boolean) =
    GameBetEntryItem(
        gameMode = gameMode.convertToVipGameModeIfNeed(isVip),
        iconUrl = icon,
        modeName = modeName
    )

/**
 * 历史原因， 获取vip的 gameMode 由客户端完成， 值为 普通的 gameMode  + 100
 */
private fun Int.convertToVipGameModeIfNeed(isVip: Boolean): Int {
    return if (isVip) {
        this + 100
    } else {
        this
    }
}

internal fun MatchInfo.toEntryFeeData(showBottomScore: Boolean): EntryFeeDate {
    return EntryFeeDate(
        baseScore = if (showBottomScore) score else null,
        coin = coin,
        expMultiplier = expMultiply,
    )
}

internal fun VsCenterLittleGameConfig.toVipBgData(gameType: Int): VipBgData {
    return if (vipRoomConfig.topBgUrl.isEmpty() || vipRoomConfig.titleImage.isEmpty()) {
        HLog.aliLog(
            PORT.performance,
            AliNetLogUtil.TYPE.err,
            "JackarooRoomCreateViewModel empty bgDataInfo,gameType:$gameType,vipRoomConfig:${vipRoomConfig}"
        )
        VipBgData()
    } else {
        VipBgData(vipRoomConfig.topBgUrl, vipRoomConfig.titleImage)
    }
}