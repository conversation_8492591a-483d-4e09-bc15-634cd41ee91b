package com.wejoy.jackaroo.create

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.material3.MaterialTheme
import androidx.lifecycle.lifecycleScope
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.constants.GameType
import com.huiwan.constants.IntentConfig
import com.huiwan.lib.api.ApiService
import com.huiwan.littlegame.util.LittleGameUtil
import com.wejoy.littlegame.EXCHANGE_TYPE_ENTER
import com.wejoy.littlegame.GAME_SCENE_LITTLE_GAME_MAIN
import com.wejoy.littlegame.ILittleGameApi
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.wespy.cocosnew.match.main.ar285.LittleGameEventHandler
import com.wepie.wespy.module.common.jump.JumpUtil
import kotlinx.coroutines.launch

class JackarooRoomCreateActivity : BaseActivity() {

    private val vm: JackarooRoomCreateViewModel by viewModels<JackarooRoomCreateViewModel>()
    private var gameType: Int = GameType.GAME_TYPE_JACKAROO

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val isVipMode = intent.getBooleanExtra(IS_VIP, false)
        gameType = intent.getIntExtra(IntentConfig.GAME_TYPE, GameType.GAME_TYPE_JACKAROO)
        setContent {
            MaterialTheme(
                typography = createRoomTypography(),
            ) {
                JackarooRoomCreatePage(vm) { finish() }
            }
        }
        vm.init(isVipMode, gameType)
        lifecycleScope.launch {
            vm.action.collect {
                handleAction(it)
            }
        }
        val screenName: String = if (isVipMode) {
            TrackSource.CREATE_ROOM_VIP
        } else {
            TrackSource.CREATE_ROOM_NORMAL
        }
        TrackUtil.appViewScreen(screenName, mapOf<String, Any>("game_type" to gameType))
    }


    private fun handleAction(action: JackarooCreateAction) {
        when (action) {
            JackarooCreateAction.HideLoading -> hideProgressDialog()
            JackarooCreateAction.ShowLoading -> showProgressDialogDelay()
            is JackarooCreateAction.JumpToWait -> {
                val source: String = if (action.vip) {
                    TrackSource.CREATE_ROOM_VIP
                } else {
                    TrackSource.CREATE_ROOM_NORMAL
                }
                JumpUtil.gotoIceBallCreateRoomActivity(
                    this, action.gameType,
                    true, false,
                    source
                )
            }

            is JackarooCreateAction.CreateTmpVoiceRoom -> {
                lifecycleScope.launch {
                    LittleGameEventHandler.createVoiceGameRoom(
                        this@JackarooRoomCreateActivity,
                        action.matchInfo,
                        action.gameType
                    )
                }
            }

            is JackarooCreateAction.ShowError -> handleError(action)
            is JackarooCreateAction.ShowToast -> ToastUtil.show(action.msg)
        }
    }

    private fun handleError(error: JackarooCreateAction.ShowError) {
        LittleGameUtil.trackCreateGameError(error.code, error.info)
        val scene = GAME_SCENE_LITTLE_GAME_MAIN or EXCHANGE_TYPE_ENTER
        val hasHandled= ApiService.of(ILittleGameApi::class.java)
            .handleEnterError(this, error.code, scene, error.info, error.next)
        if (!hasHandled) {
            ToastUtil.show(error.desc)
        }
    }

    companion object {
        private const val IS_VIP = "__key_is_vip"
        fun go(context: Context, vip: Boolean, gameType: Int) {
            val intent = Intent(context, JackarooRoomCreateActivity::class.java)
            intent.putExtra(IS_VIP, vip)
            intent.putExtra(IntentConfig.GAME_TYPE, gameType)
            context.startActivity(intent)
        }
    }

}