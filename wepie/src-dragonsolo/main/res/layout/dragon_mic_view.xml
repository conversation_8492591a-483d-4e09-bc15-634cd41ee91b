<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <include
        android:id="@+id/orange_life_view"
        layout="@layout/dragon_life_value_orange"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="130dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/blue_life_view"
        layout="@layout/dragon_life_value_blue"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="130dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/battle_record_iv"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginTop="126dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/icon_battle_field_record" />

    <TextView
        android:id="@+id/dragon_battle_detail_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="22dp"
        android:text="@string/dragon_battle_detail_title"
        android:textColor="@color/white"
        android:textSize="8dp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/battle_record_iv"
        app:layout_constraintStart_toStartOf="@id/battle_record_iv"
        app:layout_constraintTop_toTopOf="@id/battle_record_iv" />

    <ImageView
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="-2dp"
        android:layout_marginEnd="-3dp"
        android:src="@drawable/dragon_orange_seat_bg"
        app:layout_constraintEnd_toEndOf="@+id/orange_seat3_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/owner_seat_view" />

    <ImageView
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="-3dp"
        android:layout_marginTop="-2dp"
        android:src="@drawable/dragon_blue_seat_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/blue_seat3_view"
        app:layout_constraintTop_toTopOf="@+id/owner_seat_view" />

    <com.wepie.wespy.voiceroom.dragonsolo.DragonSeatView
        android:id="@+id/orange_seat1_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="13dp"
        app:layout_constraintEnd_toStartOf="@+id/orange_seat2_view"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/owner_seat_view"
        app:seat_num="2" />

    <com.wepie.wespy.voiceroom.dragonsolo.DragonSeatView
        android:id="@+id/orange_seat2_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/orange_seat3_view"
        app:layout_constraintStart_toEndOf="@+id/orange_seat1_view"
        app:layout_constraintTop_toTopOf="@+id/owner_seat_view"
        app:seat_num="3" />

    <com.wepie.wespy.voiceroom.dragonsolo.DragonSeatView
        android:id="@+id/orange_seat3_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/owner_seat_view"
        app:layout_constraintStart_toEndOf="@+id/orange_seat2_view"
        app:layout_constraintTop_toTopOf="@+id/owner_seat_view"
        app:seat_num="4" />

    <com.wepie.wespy.voiceroom.dragonsolo.DragonSeatView
        android:id="@+id/owner_seat_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toTopOf="@+id/battle_record_iv"
        app:layout_constraintEnd_toStartOf="@+id/blue_seat3_view"
        app:layout_constraintStart_toEndOf="@+id/orange_seat3_view"
        app:seat_num="1" />

    <com.wepie.wespy.voiceroom.dragonsolo.DragonSeatView
        android:id="@+id/blue_seat3_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/blue_seat2_view"
        app:layout_constraintStart_toEndOf="@+id/owner_seat_view"
        app:layout_constraintTop_toTopOf="@+id/owner_seat_view"
        app:seat_num="5" />

    <com.wepie.wespy.voiceroom.dragonsolo.DragonSeatView
        android:id="@+id/blue_seat2_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/blue_seat1_view"
        app:layout_constraintStart_toEndOf="@+id/blue_seat3_view"
        app:layout_constraintTop_toTopOf="@+id/owner_seat_view"
        app:seat_num="6" />

    <com.wepie.wespy.voiceroom.dragonsolo.DragonSeatView
        android:id="@+id/blue_seat1_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/blue_seat2_view"
        app:layout_constraintTop_toTopOf="@+id/owner_seat_view"
        app:seat_num="7" />

</androidx.constraintlayout.widget.ConstraintLayout>