package com.wepie.wespy.voiceroom.dragonsolo;

import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.libtcp.huiwan.HuiwanPacketHandlerInterface;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.wespy.net.tcp.handler.NewPacketHandler;
import com.wepie.wespy.net.tcp.packet.HeadPackets;
import com.wepie.wespy.net.tcp.packet.MsgBattlePackets;
import com.wepie.wespy.net.tcp.packet.MsgBattlePushPackets;
import com.wepie.wespy.net.tcp.packet.PushPackets;
import com.wepie.wespy.voiceroom.dragonsolo.api.BattleCallItem;
import com.wepie.wespy.voiceroom.dragonsolo.api.BattleGameInfo;
import com.wepie.wespy.voiceroom.dragonsolo.base.DragonCallWarriorEvent;
import com.wepie.wespy.voiceroom.dragonsolo.base.DragonSyncEvent;
import com.wepie.wespy.voiceroom.dragonsolo.base.DragonUserJoinEvent;
import com.wepie.wespy.voiceroom.dragonsolo.base.DragonUserPointEvent;

import org.greenrobot.eventbus.EventBus;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * author: tsp
 * Date: 2023/5/12 16:55
 * Des:
 */
public class BattleHandler implements HuiwanPacketHandlerInterface {

    @Override
    public boolean invokeHandleMethod(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        if (rspHeadInfo.type == PushPackets.PushType.SEND_MSG_BATTLE_VALUE) {
            if (rspHeadInfo.command == HeadPackets.CommandType.PUSH_VALUE) {
                handlePushMsg(rspHeadInfo, message);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean onPacketBody(RspHeadInfo rspHeadInfo, InputStream stream) {
        if (rspHeadInfo.command == HeadPackets.CommandType.MsgBattle_VALUE) {
            try {
                MsgBattlePackets.MsgBattleRspBody battleRspBody = MsgBattlePackets.MsgBattleRspBody.parseFrom(stream);
                rspHeadInfo.message = getBattleBodyMsg(battleRspBody, rspHeadInfo.type);
            } catch (IOException e) {
                return false;
            }
            return true;
        }
        return false;
    }

    private static GeneratedMessageLite<?, ?> getBattleBodyMsg(MsgBattlePackets.MsgBattleRspBody rspBody, int type) {
        GeneratedMessageLite<?, ?> generatedMessageLite = null;
        switch (type) {
            case MsgBattlePackets.MsgBattleOpType.SyncGame_VALUE:
                generatedMessageLite = rspBody.getSyncGame();
                break;
            case MsgBattlePackets.MsgBattleOpType.JoinGroup_VALUE:
                generatedMessageLite = rspBody.getJoinGroup();
                break;
            case MsgBattlePackets.MsgBattleOpType.AdjustView_VALUE:
                generatedMessageLite = rspBody.getAdjustView();
                break;
            case MsgBattlePackets.MsgBattleOpType.ShowResult_VALUE:
                generatedMessageLite = rspBody.getShowResult();
                break;
            case MsgBattlePackets.MsgBattleOpType.SwitchNext_VALUE:
                generatedMessageLite = rspBody.getSwitchNext();
                break;
            case MsgBattlePackets.MsgBattleOpType.CallWarrior_VALUE:
                generatedMessageLite = rspBody.getCallWarrior();
                break;
            case MsgBattlePackets.MsgBattleOpType.StartGame_VALUE:
                generatedMessageLite = rspBody.getStartGame();
                break;
            case MsgBattlePackets.MsgBattleOpType.RealTimeRank_VALUE:
                generatedMessageLite = rspBody.getRealTimeRank();
                break;
            case MsgBattlePackets.MsgBattleOpType.KillDetails_VALUE:
                generatedMessageLite = rspBody.getKillDetails();
                break;
            default: {
                TimeLogger.notifyIfDebug("unknown msg in getBattleBodyMsg");
            }
        }
        return generatedMessageLite;
    }

    public static void handlePushMsg(RspHeadInfo rspHeadInfo, GeneratedMessageLite<?, ?> message) {
        MsgBattlePushPackets.MsgBattlePushBody push = (MsgBattlePushPackets.MsgBattlePushBody) message;
        if (push != null) {
            int pushTyp = push.getTyp();
            if (pushTyp == MsgBattlePushPackets.MsgBattlePushType.BattlePushSync_VALUE) {
                EventBus.getDefault().post(new DragonSyncEvent(push.getSync().getRid(), push.getSync().getVersion()));
            } else if (pushTyp == MsgBattlePushPackets.MsgBattlePushType.BattlePushPoint_VALUE) {
                List<BattleGameInfo.StreakUser> orangePoint = BattleGameInfo.StreakUser.parseList(push.getPoint().getLeftPoint());
                List<BattleGameInfo.StreakUser> blueStreak = BattleGameInfo.StreakUser.parseList(push.getPoint().getRightPoint());
                EventBus.getDefault().post(new DragonUserPointEvent(orangePoint, blueStreak));
            } else if (pushTyp == MsgBattlePushPackets.MsgBattlePushType.BattlePushCall_VALUE) {
                MsgBattlePushPackets.PuCallWarrior callWarrior = push.getCall();
                if (callWarrior != null) {
                    List<BattleCallItem> battleCallItems = BattleCallItem.paresList(callWarrior.getCallListList());
                    EventBus.getDefault().post(new DragonCallWarriorEvent(battleCallItems));
                }
            } else if (pushTyp == MsgBattlePushPackets.MsgBattlePushType.BattlePushJoin_VALUE) {
                MsgBattlePushPackets.PuJoinGroup join = push.getJoin();
                if (join == null) {
                    return;
                }
                EventBus.getDefault().post(new DragonUserJoinEvent(join.getUid(), join.getGroup(), join.getRank(), join.getEnterEffectId()));
            } else if (pushTyp == MsgBattlePushPackets.MsgBattlePushType.BattlePushWarriorSkin_VALUE) {

            } else {
                ToastUtil.debugShow("unKnow push type in BattlePush");
            }
        }
    }

    public static void handleMsg(GeneratedMessageLite<?, ?> message, RspHeadInfo rspHeadInfo) {
        NewPacketHandler.invokeSuccess(rspHeadInfo, message);
    }


}
