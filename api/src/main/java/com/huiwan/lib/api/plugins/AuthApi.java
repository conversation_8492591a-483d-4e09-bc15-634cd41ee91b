package com.huiwan.lib.api.plugins;

import android.content.Context;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import com.huiwan.lib.api.Api;
import com.huiwan.lib.api.Callback;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public interface AuthApi extends Api {
    int SCENE_NONE = 0;

    int SCENE_CHARGE = 1;
    int SCENE_CREATE_VOICE_ROOM = 2;
    int SCENE_ENTER_VOICE_ROOM = 3;
    int SCENE_SIT = 4;
    int SCENE_EV = 5; // 脱单神器
    int SCENE_NEAR_BY = 6;
    int SCENE_CIRCLE_CAMERA = 7; // 玩友圈
    int SCENE_CIRCLE_SQUARE = 8; // 广场
    int SCENE_VOICE_SAY = 9; // 语音房公屏
    int SCENE_BUY_PACKET = 10;
    int SCENE_BUY_VISITOR = 11; // ios
    int SCENE_VOW = 12;
    int SCENE_CIRCLE_COMMENT = 13;
    int SCENE_ROOM_BROAD_GIFT = 14;
    int SCENE_APP_BROAD_GIFT = 15;
    int SCENE_SEND_BROAD_CAST = 16;
    int SCENE_GAME_QUICK_MATCH = 17;
    int SCENE_LITTLE_GAME_QUICK_MATCH = 18;
    int SCENE_MARRY = 19; // 戒指，结婚
    int SCENE_CREATE_GROUP_CHAT = 20;
    int SCENE_CREATE_GAME_ROOM = 21;
    int SCENE_BUY_VIP = 22;
    int SCENE_CREATE_FAMILY = 23;

    //<editor-fold desc="5.9.28 新增场景">
    int SCENE_BUY_HEAD_DECOR = 24;
    int SCENE_BUY_HOME_ANIM = 25;
    int SCENE_BUY_DRAW_BOARD = 26;
    int SCENE_BUY_VOICE_ENTER_ANIM = 27;
    int SCENE_BUY_CHAT_BUBBLE = 28;
    int SCENE_BUY_VOICE_BUBBLE = 29;
    int SCENE_BUY_DISCOVER_DECOR = 30;
    int SCENE_BUY_VOICE_BG = 31;
    int SCENE_BUY_VOICE_MIC = 32;
    int SCENE_BUY_FUNCTION = 33;
    int SCENE_BUY_FAMILY_BOX = 34;
    int SCENE_BUY_GIFT_CARD = 35;
    int SCENE_BUY_FAMILY_DECOR = 36;
    int SCENE_SEND_GIFT = 37;
    int SCENE_SEND_RED_PACKET = 38;
    int SCENE_SEND_RED_PACKET_VOICE = 39;
    int SCENE_RECEIVE_RED_PACKET = 40;
    int SCENE_RECEIVE_RED_PACKET_VOICE = 41;
    int SCENE_AUDIO_MATCH = 42;
    int SCENE_XROOM_AUTH = 44;
    int SCENE_NEW_USER_AUTH_DIALOG = 45;

    //是否可跳过
    int AUTH_CODE_NONE = 1;
    int AUTH_CODE_OPTIONAL = 2;
    int AUTH_CODE_FORCE = 3;

    //用什么信息认证
    int AUTH_CHECK_TYPE_ID_CARD = 1;
    int AUTH_CHECK_TYPE_ID_OR_PHONE = 2;
    int AUTH_CHECK_TYPE_ID_AND_FACE = 3;
    /**
     *  可以理解为AUTH_CHECK_TYPE_ID_CARD的子集；
     *  如果未认证身份证，本地弹窗一个不同于AUTH_CHECK_TYPE_ID_CARD的弹窗；
     *  目前的需要的场景下，如果已经认证身份证且小于18岁，由服务器接口抛出异常，弹出toast；
     */
    int AUTH_CHECK_TYPE_ID_AND_EIGHTEEN = 4;

    int AUTH_CHECK_TYPE_PHONE = 5;

    void gotoCertificateActivity(Context context, boolean needFaceAuth);

    void gotoFaceAuthPreActivity(Context context);

    void forceRefreshConfigs(String payload);

    void init();

    void showNeedAuthDialog(int code);

    void clear();

    String getListStringConfig();

    void doTaskOrShowNeedCertificate(@NonNull final Context context, @AuthApi.IdScene final int scene,
                                @NonNull final Callback callback);

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({SCENE_CHARGE, SCENE_CREATE_VOICE_ROOM, SCENE_ENTER_VOICE_ROOM, SCENE_SIT, SCENE_EV, SCENE_NEAR_BY,
            SCENE_CIRCLE_CAMERA, SCENE_CIRCLE_SQUARE, SCENE_VOICE_SAY, SCENE_BUY_PACKET,
            SCENE_BUY_VISITOR, SCENE_VOW, SCENE_CIRCLE_COMMENT, SCENE_ROOM_BROAD_GIFT,
            SCENE_APP_BROAD_GIFT, SCENE_SEND_BROAD_CAST, SCENE_GAME_QUICK_MATCH,
            SCENE_LITTLE_GAME_QUICK_MATCH, SCENE_MARRY, SCENE_CREATE_GROUP_CHAT,
            SCENE_CREATE_GAME_ROOM, SCENE_BUY_VIP, SCENE_CREATE_FAMILY,

            SCENE_BUY_HEAD_DECOR, SCENE_BUY_HOME_ANIM, SCENE_BUY_DRAW_BOARD, SCENE_BUY_VOICE_ENTER_ANIM,
            SCENE_BUY_CHAT_BUBBLE, SCENE_BUY_VOICE_BUBBLE, SCENE_BUY_DISCOVER_DECOR, SCENE_BUY_VOICE_BG,
            SCENE_BUY_VOICE_MIC, SCENE_BUY_FUNCTION, SCENE_BUY_FAMILY_BOX, SCENE_BUY_GIFT_CARD, SCENE_BUY_FAMILY_DECOR,

            SCENE_SEND_GIFT, SCENE_SEND_RED_PACKET, SCENE_SEND_RED_PACKET_VOICE, SCENE_RECEIVE_RED_PACKET, SCENE_RECEIVE_RED_PACKET_VOICE,
            SCENE_AUDIO_MATCH,

            SCENE_NONE
    })
    @interface IdScene {}
}
