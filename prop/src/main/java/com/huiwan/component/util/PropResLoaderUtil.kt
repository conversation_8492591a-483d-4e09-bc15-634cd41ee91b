package com.huiwan.component.util

import android.widget.ImageView
import com.huiwan.store.file.FileManager
import com.wejoy.weplay.ex.ILife
import com.wejoy.weplay.ex.view.toLife
import com.wepie.download.DownloadUtil
import com.wepie.download.LifeDownloadCallback
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoadListener
import com.wepie.libimageloader.WpImageLoader

object PropResLoaderUtil {
    fun loadResForDownload(
        url: String, localPath: String, view: ImageView,
        loadInfo: ImageLoadInfo? = null
    ) {
        loadResForDownload(url, localPath, view, view.toLife(), null, false, loadInfo)
    }

    fun loadResForDownload(
        url: String, localPath: String, view: ImageView, addH265Support: Boolean,
        loadInfo: ImageLoadInfo? = null
    ) {
        loadResForDownload(url, localPath, view, view.toLife(), null, addH265Support, loadInfo)
    }

    fun loadResForDownload(
        url: String, localPath: String, view: ImageView?, life: ILife,
        listener: WpImageLoadListener<Any>?,
        loadInfo: ImageLoadInfo? = null
    ) {
        loadResForDownload(url, localPath, view, life, listener, false, loadInfo)
    }

    fun loadResForDownload(
        url: String, localPath: String, view: ImageView?, life: ILife,
        listener: WpImageLoadListener<Any>?, addH265Support: Boolean,
        loadInfo: ImageLoadInfo? = null
    ) {
        val imgLoadInfo = loadInfo ?: ImageLoadInfo.newInfo()
        imgLoadInfo.isNoneCache = true
        val file = FileManager.getFile(localPath)
        if (file.exists() && file.length() > 0) {
            WpImageLoader.loadExt(url, localPath, view, imgLoadInfo, listener)
            return
        }
        view?.tag = url

        DownloadUtil.downloadFileWithRetry(url, localPath, 3,
            true, true, addH265Support, object : LifeDownloadCallback(life) {
                override fun onSuccess(url: String?, path: String?) {
                    if ((view == null) || (view.tag == url)) {
                        WpImageLoader.loadExt(url, localPath, view, imgLoadInfo, listener)
                    }
                }

                override fun onFail(msg: String?) {
                    if ((view == null) || (view.tag == url)) {
                        WpImageLoader.loadExt(url, localPath, view, imgLoadInfo, listener)
                    }
                }
            })
    }

    fun loadTitleResForDownload(
        url: String, localPath: String, view: ImageView,
        loadInfo: ImageLoadInfo? = null
    ) {
        loadResForDownload(url, localPath, view, view.toLife(), null, false, loadInfo)
    }
}