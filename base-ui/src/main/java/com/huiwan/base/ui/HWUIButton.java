package com.huiwan.base.ui;

import android.animation.Animator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.util.FontUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;

/**
 * UI基础组件库的按钮组件<p>
 * 支持定义的属性包括<p>
 * 是否有图片{@link HWUIButton#hasIv}<p>
 * 图片资源{@link HWUIButton#imgRes} 非res资源图片可以通过{@link HWUIButton#drawable}设置<p>
 * 按钮文字{@link HWUIButton#text}<p>
 * 按钮样式{@link HWUIButton#style} 属性参考{@link HWUIButton#BIG_BUTTON_STYLE_MAIN}<p>
 * last update date 2022/04/24
 */
public class HWUIButton extends FrameLayout {
    public static final int BIG_BUTTON_STYLE_MAIN = 0;
    public static final int BIG_BUTTON_STYLE_SUB = 1;
    public static final int BIG_BUTTON_STYLE_WEAK = 2;
    public static final int BIG_BUTTON_STYLE_GREY = 3;
    public static final int BIG_BUTTON_STYLE_ROSE = 4;
    public static final int BIG_BUTTON_STYLE_ORANGE = 5;
    public static final int MID_BUTTON_STYLE_MAIN = 6;
    public static final int MID_BUTTON_STYLE_SUB = 7;
    public static final int MID_BUTTON_STYLE_WEAK = 8;
    public static final int MID_BUTTON_STYLE_GREY = 9;
    public static final int MID_BUTTON_STYLE_ROSE = 10;
    public static final int MID_BUTTON_STYLE_ORANGE = 11;
    public static final int SMALL_BUTTON_STYLE_MAIN = 12;
    public static final int SMALL_BUTTON_STYLE_SUB = 13;
    public static final int SMALL_BUTTON_STYLE_WEAK = 14;
    public static final int SMALL_BUTTON_STYLE_GREY = 15;
    public static final int SMALL_BUTTON_STYLE_ROSE = 16;
    public static final int SMALL_BUTTON_STYLE_ORANGE = 17;
    public static final int MINI_BUTTON_STYLE_MAIN = 18;
    public static final int MINI_BUTTON_STYLE_SUB = 19;
    public static final int MINI_BUTTON_STYLE_WEAK = 20;
    public static final int MINI_BUTTON_STYLE_GREY = 21;
    public static final int MINI_BUTTON_STYLE_ROSE = 22;
    public static final int MINI_BUTTON_STYLE_ORANGE = 23;
    private ViewGroup rootLay;
    private TextView centerTv;
    private ImageView leftIv;

    private int style = 0;
    private boolean hasIv = false;
    private int imgRes = R.drawable.default_iv;
    private Drawable drawable = null;
    private CharSequence text = "";

    private static final int DEFAULT_ANIM_TIME = 500;

    public HWUIButton(@NonNull Context context) {
        super(context);
        init();
    }

    public HWUIButton(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HWUIButton(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public HWUIButton(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.HWUIButton, defStyleAttr, defStyleRes);
        style = a.getInt(R.styleable.HWUIButton_hwButtonStyle, BIG_BUTTON_STYLE_MAIN);
        imgRes = a.getInt(R.styleable.HWUIButton_hwButtonSrc, R.drawable.default_iv);
        hasIv = a.getBoolean(R.styleable.HWUIButton_hwButtonHasImg, false);
        text = a.getText(R.styleable.HWUIButton_hwButtonText);
        if (text == null) {
            text = "";
        }
        a.recycle();
        init();
    }

    private void init() {
        LayoutInflater.from(getContext()).inflate(R.layout.hwui_button, this);
        rootLay = findViewById(R.id.root_lay);
        centerTv = findViewById(R.id.hw_btn_tv);
        leftIv = findViewById(R.id.hw_btn_iv);
        refreshView();
    }

    private void refreshView() {
        switch (style) {
            case BIG_BUTTON_STYLE_MAIN:
            case MID_BUTTON_STYLE_MAIN:
                rootLay.setBackgroundResource(R.drawable.btn_00ccf9);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                break;
            case BIG_BUTTON_STYLE_SUB:
            case MID_BUTTON_STYLE_SUB:
                rootLay.setBackgroundResource(R.drawable.btn_white_stroke_30d395);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_brand_default : R.color.color_brand_disabled));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                break;
            case BIG_BUTTON_STYLE_WEAK:
            case MID_BUTTON_STYLE_WEAK:
                rootLay.setBackgroundResource(R.drawable.btn_weak_stroke_dedee0);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_text_secondary :  R.color.color_gray_border));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                break;
            case BIG_BUTTON_STYLE_GREY:
            case MID_BUTTON_STYLE_GREY:
                rootLay.setBackgroundResource(R.drawable.btn_f7f8fa);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_text_secondary : R.color.color_text_default));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                break;
            case BIG_BUTTON_STYLE_ROSE:
            case MID_BUTTON_STYLE_ROSE:
                rootLay.setBackgroundResource(R.drawable.btn_fe6484);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                //修正视觉上的不居中
                centerTv.setPadding(0, 0, 0, ScreenUtil.dip2px(2));
                break;
            case BIG_BUTTON_STYLE_ORANGE:
            case MID_BUTTON_STYLE_ORANGE:
                rootLay.setBackgroundResource(R.drawable.btn_ff683b);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                break;
            case SMALL_BUTTON_STYLE_MAIN:
                rootLay.setBackgroundResource(R.drawable.btn_00ccf9);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                break;
            case SMALL_BUTTON_STYLE_SUB:
                rootLay.setBackgroundResource(R.drawable.btn_white_stroke_30d395);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_brand_default : R.color.color_brand_disabled));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                break;
            case SMALL_BUTTON_STYLE_WEAK:
                rootLay.setBackgroundResource(R.drawable.btn_weak_stroke_dedee0);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_text_secondary : R.color.color_gray_border));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                break;
            case SMALL_BUTTON_STYLE_GREY:
                rootLay.setBackgroundResource(R.drawable.btn_f7f8fa);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_text_secondary : R.color.color_gray_border));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                break;
            case SMALL_BUTTON_STYLE_ROSE:
                rootLay.setBackgroundResource(R.drawable.btn_fe6484);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                break;
            case SMALL_BUTTON_STYLE_ORANGE:
                rootLay.setBackgroundResource(R.drawable.btn_ff683b);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                break;
            case MINI_BUTTON_STYLE_MAIN:
                rootLay.setBackgroundResource(R.drawable.btn_00ccf9);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
                break;
            case MINI_BUTTON_STYLE_SUB:
                rootLay.setBackgroundResource(R.drawable.btn_white_stroke_30d395);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_brand_default : R.color.color_brand_disabled));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
                break;
            case MINI_BUTTON_STYLE_WEAK:
                rootLay.setBackgroundResource(R.drawable.btn_weak_stroke_dedee0);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_text_secondary : R.color.color_gray_border));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
                break;
            case MINI_BUTTON_STYLE_GREY:
                rootLay.setBackgroundResource(R.drawable.btn_f7f8fa);
                centerTv.setTextColor(getResources().getColor(isEnabled() ? R.color.color_text_secondary : R.color.color_gray_border));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
                break;
            case MINI_BUTTON_STYLE_ROSE:
                rootLay.setBackgroundResource(R.drawable.btn_fe6484);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
                break;
            case MINI_BUTTON_STYLE_ORANGE:
                rootLay.setBackgroundResource(R.drawable.btn_ff683b);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
                break;
            default:
                ToastUtil.debugShow("HWUIButton未设置样式，请检查代码");
                rootLay.setBackgroundResource(R.drawable.btn_00ccf9);
                centerTv.setTextColor(getResources().getColor(R.color.color_white));
                centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                break;
        }
        leftIv.setVisibility(hasIv ? VISIBLE : GONE);
        leftIv.setImageResource(imgRes);
        centerTv.setText(text);
    }

    public void setBgResource(int resId, int textColor, int textSize) {
        rootLay.setBackgroundResource(resId);
        centerTv.setTextColor(getResources().getColor(textColor));
        centerTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, textSize);
    }

    public void setBgResource(int resId) {
        rootLay.setBackgroundResource(resId);
    }

    /**
     * 设置字体加粗
     */
    public void setCenterTvBold() {
        FontUtil.setTextStyle(centerTv, Typeface.BOLD);
    }

    public boolean isHasIv() {
        return hasIv;
    }

    public void setHasIv(boolean hasIv) {
        this.hasIv = hasIv;
        leftIv.setVisibility(hasIv ? VISIBLE : GONE);
    }

    public CharSequence getText() {
        return text;
    }

    public void setText(CharSequence text) {
        this.text = text;
        centerTv.setText(text);
    }

    public void setImgRes(@DrawableRes int res) {
        this.imgRes = res;
        leftIv.setImageResource(imgRes);
    }

    public Drawable getDrawable() {
        return drawable;
    }

    public void setDrawable(Drawable drawable) {
        this.drawable = drawable;
        leftIv.setImageDrawable(drawable);
    }

    public int getStyle() {
        return style;
    }

    /**
     * 属性参考{@link HWUIButton#BIG_BUTTON_STYLE_MAIN}
     *
     * @param style 预设样式
     */
    public void setStyle(int style) {
        this.style = style;
        refreshView();
    }

    /**
     * 设置旋转的进度条，若设置了img，旋转时会隐藏之前设置的图标
     *
     * @param on true表示旋转，false表示隐藏
     */
    public void setProgressOn(boolean on) {
        if (on) {
            leftIv.setVisibility(VISIBLE);
            leftIv.setImageResource(R.drawable.abc_vector_test);
            loopLeftIvAnim();
        } else {
            leftIv.animate().setListener(null).cancel();
            leftIv.setVisibility(hasIv ? VISIBLE : GONE);
            leftIv.setImageResource(imgRes);
        }
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        rootLay.setEnabled(enabled);
    }

    private void loopLeftIvAnim() {
        leftIv.animate().rotationBy(360).setDuration(DEFAULT_ANIM_TIME).setListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                loopLeftIvAnim();
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        }).start();
    }

    @Override
    protected void onDetachedFromWindow() {
        leftIv.animate().setListener(null).cancel();
        super.onDetachedFromWindow();
    }
}
