package com.wepie.wespy.module.emotionalvoice.main;

import android.animation.FloatEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;

import com.wepie.wespy.R;

/**
 * date 2018/2/3
 * email <EMAIL>
 *
 * <AUTHOR>
 */

public class EvMainAnimView extends FrameLayout {
    private static final float MIN_SCALE = 1.0F;
    private static final float DELTA_SCALE = 0.42F;
    private static final float MAX_SCALE = MIN_SCALE + DELTA_SCALE;
    private static final long DURATION = 2000;

    private View animView0;
    private View animView1;
    private View animView2;

    private ValueAnimator animator;
    private float step1 = 0.3f;
    private float step2 = 0.6f;


    public EvMainAnimView(Context context){
        this(context, null);
    }

    public EvMainAnimView(Context context, AttributeSet attrs){
        this(context, attrs, 0);
    }

    public EvMainAnimView(Context context, AttributeSet attrs, int defStyleAttr){
        super(context, attrs, defStyleAttr);
        LayoutInflater.from(context).inflate(R.layout.ev_anim_view, this);
        animView0 = findViewById(R.id.ev_anim_v0);
        animView1 = findViewById(R.id.ev_anim_v1);
        animView2 = findViewById(R.id.ev_anim_v2);

        step1 = DELTA_SCALE / 3;
        step2 = step1 * 2;

        updateView(animView0, MIN_SCALE);
        updateView(animView1, MIN_SCALE + step1);
        updateView(animView2, MIN_SCALE + step2);
    }


    private void initAnimatorIfNeed(){
        if (animator != null){
            return;
        }
        animator = ValueAnimator.ofObject(new FloatEvaluator(), MIN_SCALE, MAX_SCALE);
        animator.setDuration(DURATION);
        animator.setInterpolator(new LinearInterpolator());
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float s0 = (float)animation.getAnimatedValue();
                float s1 = s0 + step1;
                float s2 = s0 + step2;
                if (s1 > MAX_SCALE){
                    s1 = s1 - DELTA_SCALE;
                }
                if (s2 > MAX_SCALE){
                    s2 = s2 - DELTA_SCALE;
                }
                updateView(animView0, s0);
                updateView(animView1, s1);
                updateView(animView2, s2);
            }
        });
        animator.setRepeatMode(ValueAnimator.RESTART);
        animator.setRepeatCount(ValueAnimator.INFINITE);
    }

    private void updateView(View v, float it){
        v.setScaleX(it);
        v.setScaleY(it);
        v.setAlpha((MAX_SCALE - it)/DELTA_SCALE);
    }

    public void startAnim(){
        initAnimatorIfNeed();
        if (animator != null){
            animator.start();
        }
    }

    public void reset(){
        if (animator == null){
            return;
        }
        animator.cancel();
        updateView(animView0, MIN_SCALE);
        updateView(animView1, MIN_SCALE + step1);
        updateView(animView2, MIN_SCALE + step2);
        invalidate();
    }
}
