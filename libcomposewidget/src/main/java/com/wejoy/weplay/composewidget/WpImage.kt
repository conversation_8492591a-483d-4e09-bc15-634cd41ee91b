package com.wejoy.weplay.composewidget

import android.graphics.drawable.Drawable
import android.text.TextUtils
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.layout.ContentScale
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.UnitTransformation
import com.bumptech.glide.load.resource.bitmap.Rotate
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.signature.ObjectKey
import com.huiwan.store.PrefUtil
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.loader.GlideLottieDrawable
import com.wepie.libimageloader.loader.GlideSvgaDrawable
import com.wepie.libimageloader.loader.GlideTransformFadeLTR
import com.wepie.u.w.c as GlideWebpDrawable


@Composable
fun <T> DecorationImageInternal(modifier: Modifier, provider: ModelProvider<T>) {
    val decorImgUri: MutableState<T?> = remember { mutableStateOf(null) }
    LaunchedEffect(provider.key) {
        decorImgUri.value = provider.provide()
    }
    WpImage(decorImgUri.value, modifier = modifier)
}

interface ModelProvider<T> {
    /**
     *  用作 launch effect key,唯一标识一个 decoration uri
     */
    val key: Any?

    /**
     * @return decoration uri, null 代表失败
     */
    suspend fun provide(): T?
}

/**
 * 对于Image组件的封装，推荐项目内Image使用这个
 * @sample DecorationImageInternal
 */
@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun <T> WpImage(
    modifier: Modifier,
    modelProvider: ModelProvider<T>,
    imageLoadInfo: ImageLoadInfo = ImageLoadInfo.newInfo(),
    contentScale: ContentScale = ContentScale.Fit,
    contentDescription: String? = null,
    alignment: Alignment = Alignment.Center,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
) {
    val modelState: MutableState<T?> = remember { mutableStateOf(null) }
    LaunchedEffect(modelProvider.key) {
        modelState.value = modelProvider.provide()
    }
    GlideImage(
        modelState.value,
        contentDescription = contentDescription,
        modifier = modifier,
        contentScale = contentScale,
        alignment = alignment,
        alpha = alpha,
        colorFilter = colorFilter,
        requestBuilderTransform = { old ->
            old.also {
                it.apply(modelState.value, imageLoadInfo)
                //这里添加一个 GlideSvgaDrawable 的 Transformation，是因为GlideImage 会强制加一些Transformation，而 GlideSVGADrawable
                // 在Transformation 中被强制转BitmapDrawable会失败，这里添加一个 UnitTransformation，来pass这个流程
                it.transformations[GlideSvgaDrawable::class.java] =
                    UnitTransformation.get<GlideSvgaDrawable>()
                it.transformations[GlideWebpDrawable::class.java] =
                    UnitTransformation.get<GlideWebpDrawable>()
//                当从本地文件播放svga失败时 检查diskCacheStrategy 是否为DiskCacheStrategy.DATA
//                添加 这个是因为GlideSvgaDecoder还没实现InputStream -> GlideSvgaDrawable
//               it.diskCacheStrategy(DiskCacheStrategy.DATA)
            }
        })

}

/**
 * Fun:完善GlideImage缺省参数
 * @sample WpImageSample1
 * @sample WpImageSample2
 */
@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun WpImage(
    model: Any?,
    modifier: Modifier,
    imageLoadInfo: ImageLoadInfo = ImageLoadInfo.newInfo(),
    contentScale: ContentScale = ContentScale.Fit,
    contentDescription: String? = null,
    alignment: Alignment = Alignment.Center,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
) {
    GlideImage(
        model,
        contentDescription,
        contentScale = contentScale,
        modifier = modifier,
        alignment = alignment,
        alpha = alpha,
        colorFilter = colorFilter,
        requestBuilderTransform = { old ->
            old.also {
                it.apply(model, imageLoadInfo)
                //这里添加一个 GlideSvgaDrawable 的 Transformation，是因为GlideImage 会强制加一些Transformation，而 GlideSVGADrawable
                // 在Transformation 中被强制转BitmapDrawable会失败，这里添加一个 UnitTransformation，来pass这个流程
                it.transformations[GlideSvgaDrawable::class.java] =
                    UnitTransformation.get<GlideSvgaDrawable>()
                it.transformations[GlideWebpDrawable::class.java] =
                    UnitTransformation.get<GlideWebpDrawable>()
//                当从本地文件播放svga失败时 检查diskCacheStrategy 是否为DiskCacheStrategy.DATA
//                添加 这个是因为GlideSvgaDecoder还没实现InputStream -> GlideSvgaDrawable
//               it.diskCacheStrategy(DiskCacheStrategy.DATA)
            }
        })
}


private fun <T> RequestBuilder<T>.apply(model: Any?, loadInfo: ImageLoadInfo) {
    val noneCache =
        PrefUtil.getInstance().getBoolean(PrefUtil.NONE_CACHE_KEY, false) || loadInfo.isNoneCache
    if (loadInfo.isDisableNetwork) {
        onlyRetrieveFromCache(true)
    }
    if (!TextUtils.isEmpty(loadInfo.signature)) {
        signature(ObjectKey(loadInfo.signature))
    }
    //非svga动画时可使用 transform
    if (loadInfo.cornerPixel > 0) {
        transform(RoundedCorners(loadInfo.cornerPixel))
    }
    if (loadInfo.isFadeTransform) {
        transform(GlideTransformFadeLTR())
    }
    if (loadInfo.isDisableAnim) {
        dontAnimate()
    }
    if (loadInfo.placeholderDrawable != null) {
        placeholder(loadInfo.placeholderDrawable)
    } else if (loadInfo.placeholderRes != ImageLoadInfo.INVALID_RES) {
        placeholder(loadInfo.placeholderRes)
    }
    if (loadInfo.errorDrawable != null) {
        error(loadInfo.errorDrawable)
    } else if (loadInfo.errorRes != ImageLoadInfo.INVALID_RES) {
        error(loadInfo.errorRes)
    }
    if (loadInfo.width > 0 && loadInfo.height > 0) {
        override(loadInfo.width, loadInfo.height)
    }
    if (loadInfo.isUseOriginalSize) {
        override(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL)
    }
    if (loadInfo.rotation > 0) {
        transform(Rotate(loadInfo.rotation))
    }
    if (null != loadInfo.options) {
        apply(loadInfo.options)
    }
    val isNormalDrawable = model is Drawable && model !is GlideSvgaDrawable
            && model !is GifDrawable && model !is GlideLottieDrawable
    when {
        isNormalDrawable || model is Int -> DiskCacheStrategy.AUTOMATIC
        noneCache -> diskCacheStrategy(
            DiskCacheStrategy.NONE
        )

        else -> diskCacheStrategy(DiskCacheStrategy.DATA)
    }
}