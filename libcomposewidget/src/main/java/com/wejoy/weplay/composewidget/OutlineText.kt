package com.wejoy.weplay.composewidget

import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.constrainHeight
import androidx.compose.ui.unit.constrainWidth
import androidx.compose.ui.unit.dp

@Composable
fun OutlineText(
    text: String,
    textStyle: TextStyle,
    strokeWidth: Dp,
    strokeColor: Color,
    fillColor: Color,
) {
    var textWidth by remember { mutableIntStateOf(0) }
    var textHeight by remember { mutableIntStateOf(0) }
    val textMeasurer = rememberTextMeasurer()
    Box(
        modifier = Modifier
            .outlineText(
                text = text,
                fillColor = fillColor,
                strokeColor = strokeColor,
                strokeWidth = strokeWidth,
                style = textStyle,
                onSizeMeasured = { w, h ->
                    textWidth = w
                    textHeight = h
                }
            )
            .layout { measurable, constraints ->
                val textLayoutResult = textMeasurer.measure(
                    text = text,
                    style = textStyle
                )
                val contentWidth = textLayoutResult.size.width
                val contentHeight = textLayoutResult.size.height
                // 确保不超过父容器的约束
                val width = constraints.constrainWidth(contentWidth)
                val height = constraints.constrainHeight(contentHeight)
                val placeable = measurable.measure(
                    constraints.copy(
                        minWidth = width,
                        maxWidth = width,
                        minHeight = height,
                        maxHeight = height
                    )
                )
                layout(width, height) {
                    placeable.place(0, 0)
                }
            }
    )
}

@Composable
fun Modifier.outlineText(
    text: String,
    strokeWidth: Dp = 2.dp,
    strokeColor: Color = Color.Black,
    fillColor: Color = Color.White,
    onSizeMeasured: (Int, Int) -> Unit,
    style: TextStyle = TextStyle()
) = Modifier.composed {
    val textMeasurer: TextMeasurer = rememberTextMeasurer()
    val strokeMeasureResult = remember(textMeasurer, text, style, strokeColor) {
        textMeasurer.measure(
            text = text,
            style = style.copy(color = strokeColor)
        )
    }
    val strokeWith = with(LocalDensity.current) {
        strokeWidth.toPx()
    }
    val fillMeasureResult = remember(textMeasurer, text, style, fillColor) {
        textMeasurer.measure(
            text = text,
            style = style.copy(color = fillColor)
        )
    }

    drawWithContent {
        onSizeMeasured(strokeMeasureResult.size.width, strokeMeasureResult.size.height)
        drawText(
            textLayoutResult = strokeMeasureResult,
            drawStyle = Stroke(width = strokeWith),
        )
        drawText(
            textLayoutResult = fillMeasureResult,
            drawStyle = Fill,
        )
    }
}