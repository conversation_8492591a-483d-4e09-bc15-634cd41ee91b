package com.wejoy.weplay.composewidget

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp

@Preview
@Composable
fun WpTextSample() {
    Column {
        //  e.g. 设计稿上 Text为 16--标题-H4/常规  文字色/一级-Text_primary
        // 常规对应 字重 normal
        //此时直接预览字体 和 颜色不一致，是因为外部还未使用WpTheme 包裹使用
        H4("Demo text", color = MaterialTheme.colorScheme.onPrimary, fontWeight = FontWeight.Normal)
        N1("Demo text", color = Color.Blue)
        N2("Demo text", color = Color.Green)
        //当设计稿上未标注 字体，或未按设计稿字体设计，可使用 BasicText 自定义
        BasicText("Demo text", color = Color.Red, fontSize = 22.sp, fontWeight = FontWeight.Normal)
        BasicText("Demo text", color = Color.Red, fontSize = 22.sp, fontWeight = FontWeight.Bold)
        H4(buildAnnotatedString {
            append("click ")
            pushStringAnnotation(tag = "URL", annotation = "https://developer.android.com")
            withStyle(style = SpanStyle(color = Color.Blue, textDecoration = TextDecoration.Underline)) {
                append("here")
            }
            pop()
            append(" jump to doc")
        })
    }
}