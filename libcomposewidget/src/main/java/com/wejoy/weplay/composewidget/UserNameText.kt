package com.wejoy.weplay.composewidget

import android.graphics.ComposeShader
import android.graphics.Matrix
import android.graphics.PorterDuff
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.LinearGradientShader
import androidx.compose.ui.graphics.ShaderBrush
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.graphics.Shader
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun UserNameText(
    userName: String,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = TextUnit.Unspecified,
    color: Color = Color.Black,
    textAlign: TextAlign? = null,
    colors: UserNameColorsConfig = UserNameColorsConfig(),
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium
) {
    val flashWidth = with(LocalDensity.current) { 14.dp.toPx() }
    val flashSpeed = with(LocalDensity.current) { (1.3).dp.toPx() }
    val x1 = -flashWidth

    var offset by remember { mutableFloatStateOf(0f) }
    val scope = rememberCoroutineScope()
    LaunchedEffect(Unit) {
        scope.launch {
            while (true) {
                offset += flashSpeed
                delay(33)// 30fps & break point
            }
        }
    }

    val mulShader by remember(colors, color) {
        mutableStateOf(object : ShaderBrush() {
            private val animShader by lazy {
                LinearGradientShader(
                    colors = colors.blend.map { Color(it) },
                    from = Offset(x1, 0f),
                    to = Offset(x1 + flashWidth, flashWidth * 0.2f),
                    tileMode = TileMode.Clamp
                )
            }
            private var baseShader: Shader? = null
            private var baseShaderSize: Size? = null

            fun getBaseShader(size: Size): Shader {
                val a = baseShader
                return if (baseShaderSize == size && a != null) {
                    a
                } else LinearGradientShader(
                    colors = colors.base.map { Color(it) }
                        .ifEmpty {
                            listOf(colors.textColor?.let { Color(it) } ?: color,
                                colors.textColor?.let { Color(it) } ?: color)
                        },
                    from = Offset(0f, 0f),
                    to = Offset(size.width, size.height),
                    tileMode = TileMode.Clamp
                ).apply {
                    baseShaderSize = size
                    baseShader = this
                }
            }

            override fun createShader(size: Size): Shader {
                val matrix = Matrix().apply {
                    postTranslate(offset % (size.width + flashWidth), 0f)//trigger re-compose
                }
                return if (colors.blend.isEmpty()) getBaseShader(size) else ComposeShader(
                    getBaseShader(size),
                    animShader.apply {
                        setLocalMatrix(matrix)
                    },
                    PorterDuff.Mode.SRC_OVER,
                )
            }
        })
    }

    Text(
        text = userName,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        style = textStyle.merge(TextStyle(fontSize = fontSize, brush = mulShader))
    )
}


/**
 * 如果当前布局方向为 RTL（从右到左），则反转列表顺序。
 * 否则，保持原始列表顺序。
 */
@Composable
fun <T> List<T>.rtlReverse(): List<T> {
    val layoutDirection = LocalLayoutDirection.current
    if (layoutDirection == LayoutDirection.Rtl) {
        return this.asReversed()
    }
    return this
}

@Composable
@Preview(showBackground = true)
fun UserNameTextPreview() {
    Column {
        UserNameText("非会员")
        UserNameText("Vip TEST", colors = UserNameColorsConfig.TEST)
        UserNameText("Vip Level 3", colors = UserNameColorsConfig.COLOR_GOLD)
        UserNameText("Vip Level 2", colors = UserNameColorsConfig.GOLD)
        UserNameText("Vip Level 1", colors = UserNameColorsConfig.RED)
    }
}

data class UserNameColorsConfig(
    val base: List<Long> = emptyList(),//底层颜色
    val blend: List<Long> = emptyList(),//混合颜色
    val textColor: Long? = null,//文本颜色
) {
    companion object {
        val COLOR_GOLD = UserNameColorsConfig(
            // 最炫酷的
            blend = listOf(0x00FFFFFF, 0xFFFFFF00, 0xFFFFFF00, 0xFFFFFF00, 0x00FFFFFF),
            base = listOf(0xFFFD023D, 0xFFFFAC00, 0xFFFF2E8D, 0xFF8F00FF),
        )
        val GOLD = UserNameColorsConfig(
            blend = listOf(
                0xFFC27B00,
                0xFFD8A721,
                0xFFFFF000,
                0xFFFFF000,
                0xFFFFF000,
                0xFFD8A721,
                0xFFC27B00
            )
        )
        val GOLD_DARK = UserNameColorsConfig(
            blend = listOf(0xFFFFBB00, 0xFFFFFF00, 0xFFFFFF00, 0xFFFFFF00, 0xFFFFBB00)
        )
        val RED = UserNameColorsConfig(
            textColor = 0xFFF35524
        )
        val RED_ANIM = UserNameColorsConfig(//特定场景触发
            blend = listOf(0xFFFFFFFF, 0xFFFF9D9D, 0xFFFF9D9D, 0xFFFF9D9D, 0xFFFFFFFF),
            textColor = 0xFFF35524
        )
        val TEST = UserNameColorsConfig(
            blend = listOf(0x00FFFFFF, 0xFFFF00FF, 0x00FFFFFF),
            base = listOf(0xFFFF0000, 0xFF00FF00, 0xFF0000FF),
            textColor = 0xFFFFFF00,// YELLOW
        )
    }
}
