package com.wejoy.weplay.composewidget

import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ProvideTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit

/**
 * @sample WpTextSample
 */
abstract class WpTextEnum {
    @Composable
    operator fun invoke(
        text: String,
        modifier: Modifier = Modifier,
        color: Color = Color.Black,
        textStyle: TextStyle = textStyle(),
        fontWeight: FontWeight = textStyle.fontWeight ?: FontWeight.Normal,
        fontSize: TextUnit = textStyle.fontSize,
        lineHeight: TextUnit = textStyle.lineHeight,
        letterSpacing: TextUnit = TextUnit.Unspecified,
        textDecoration: TextDecoration? = null,
        textAlign: TextAlign? = null,
        overflow: TextOverflow = TextOverflow.Clip,
        softWrap: Boolean = true,
        maxLines: Int = Int.MAX_VALUE,
        minLines: Int = 1,
        onTextLayout: ((TextLayoutResult) -> Unit)? = null,
    ) {
        ProvideTextStyle(
            textStyle.copy(
                color = color,
                fontSize = fontSize,
                fontWeight = fontWeight,
                lineHeight = lineHeight
            )
        ) {
            Text(
                text = text,
                modifier = modifier,
                letterSpacing = letterSpacing,
                textDecoration = textDecoration,
                textAlign = textAlign,
                overflow = overflow,
                softWrap = softWrap,
                maxLines = maxLines,
                minLines = minLines,
                onTextLayout = onTextLayout,
            )
        }
    }

    @Composable
    operator fun invoke(
        text: AnnotatedString,
        modifier: Modifier = Modifier,
        color: Color = Color.Black,
        textStyle: TextStyle = textStyle(),
        fontWeight: FontWeight = textStyle.fontWeight ?: FontWeight.Normal,
        fontSize: TextUnit = textStyle.fontSize,
        lineHeight: TextUnit = textStyle.lineHeight,
        letterSpacing: TextUnit = TextUnit.Unspecified,
        textDecoration: TextDecoration? = null,
        textAlign: TextAlign? = null,
        overflow: TextOverflow = TextOverflow.Clip,
        softWrap: Boolean = true,
        maxLines: Int = Int.MAX_VALUE,
        minLines: Int = 1,
        inlineContent: Map<String, InlineTextContent> = mapOf(),
        onTextLayout: ((TextLayoutResult) -> Unit) = {},
    ) {
        ProvideTextStyle(
            textStyle.copy(
                color = color,
                fontSize = fontSize,
                fontWeight = fontWeight,
                lineHeight = lineHeight
            )
        ) {
            Text(
                text = text,
                modifier = modifier,
                letterSpacing = letterSpacing,
                textDecoration = textDecoration,
                textAlign = textAlign,
                overflow = overflow,
                softWrap = softWrap,
                maxLines = maxLines,
                minLines = minLines,
                inlineContent = inlineContent,
                onTextLayout = onTextLayout,
            )
        }
    }

    @Composable
    abstract fun textStyle(): TextStyle
}

object BasicText : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.bodyLarge
    }
}

object H1 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.displayMedium
    }
}

object H2 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.headlineLarge
    }
}


object H3 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.headlineMedium
    }
}

object H4 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.headlineSmall
    }
}

object Body1 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.bodyLarge
    }
}

object Body2 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.bodyMedium
    }
}

object Body3 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.bodySmall
    }
}

object N1 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.titleLarge
    }
}

object N2 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.titleMedium
    }
}

object N3 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.titleSmall
    }
}

object N4 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.labelLarge
    }
}

object N5 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.labelMedium
    }
}

object N6 : WpTextEnum() {
    @Composable
    override fun textStyle(): TextStyle {
        return MaterialTheme.typography.labelSmall
    }
}
