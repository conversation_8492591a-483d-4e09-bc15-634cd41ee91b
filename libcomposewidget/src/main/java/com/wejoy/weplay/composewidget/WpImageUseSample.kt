package com.wejoy.weplay.composewidget

import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.weojoy.weplay.libcomposewidget.R
import com.wepie.libimageloader.ImageLoadInfo

/**
 * 示例1 最简易使用，加载 URL，本地drawable
 */
@Composable
private fun WpImageSample1() {
    val model1 = "https://res.weplayapp.com/conf/4822F873-E42E-4120-930A-BA977D672FA2.png"
    val model2 = R.drawable.game_chip
    WpImage(model1, Modifier.size(20.dp))
}

/**
 * 示例2 利用 ImageLoadInfo 设置 默认图
 *  ImageLoadInfo 还有其他属性
 */
@Composable
private fun WpImageSample2() {
    val model1 = "https://res.weplayapp.com/conf/4822F873-E42E-4120-930A-BA977D672FA2.png"
    WpImage(model1, Modifier.size(20.dp), ImageLoadInfo().placeholder(R.drawable.home_coin_add))
}


