package com.wejoy.weplay.composewidget

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ProvideTextStyle
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshState
import androidx.compose.material3.pulltorefresh.pullToRefresh
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weojoy.weplay.libcomposewidget.R

@OptIn(ExperimentalMaterial3Api::class)
abstract class AbsPullToRefreshIndicator {
    @Composable
    operator fun invoke(
        isRefreshing: Boolean,
        state: PullToRefreshState,
        downText: Int = R.string.common_text_pull_to_refresh,
        loadingText: Int = R.string.common_text_loading_refresh,
    ) {
        Box(
            Modifier
                .height(60.dp)
                .fillMaxWidth(),
            contentAlignment = Alignment.Center,
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
            ) {
                var text: Int
                if (isRefreshing) {
                    loadingAnim()
                    text = R.string.common_text_loading_refresh
                } else if (state.distanceFraction >= 1.0) {
                    holding()
                    text = R.string.common_text_pull_to_refresh
                } else {
                    downPull()
                    text = R.string.common_text_pull_to_refresh
                }
                ProvideTextStyle(TextStyle.Default) {
                    Text(
                        stringResource(text),
                        fontSize = 10.sp,
                        color = colorResource(R.color.color_primary)
                    )
                }
            }
        }
    }

    @Composable
    abstract fun downPull()

    @Composable
    abstract fun holding()

    @Composable
    abstract fun loadingAnim()
}

// 主题中提供的默认 PullToRefresh indicator 实现
object DefaultPullToRefreshIndicator : AbsPullToRefreshIndicator() {
    @Composable
    override fun downPull() {
        Image(
            painter = painterResource(R.drawable.wejoy_refresh_down_default_icon),
            contentDescription = null,
            modifier = Modifier.size(24.dp)
        )
    }

    @Composable
    override fun holding() {
        Image(
            painter = painterResource(R.drawable.wejoy_refresh_loading_default_icon),
            contentDescription = null,
            modifier = Modifier.size(24.dp)
        )
    }

    @Composable
    override fun loadingAnim() {
        val infiniteTransition = rememberInfiniteTransition(label = "rotation")
        val rotation by infiniteTransition.animateFloat(
            initialValue = 0f,
            targetValue = 360f,
            animationSpec = infiniteRepeatable(
                animation = tween(1000, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "rotation"
        )
        Image(
            painter = painterResource(R.drawable.wejoy_refresh_loading_default_icon),
            contentDescription = null,
            modifier = Modifier
                .size(24.dp)
                .rotate(rotation)
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
var WpPullToRefreshIndicator: @Composable (isRefreshing: Boolean, state: PullToRefreshState) -> Unit =
    { isRefreshing, state ->
        DefaultPullToRefreshIndicator(isRefreshing, state)
    }

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WpPullToRefreshColumn(
    isRefreshing: Boolean,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier,
    state: PullToRefreshState = rememberPullToRefreshState(),
    indicator: @Composable BoxScope.() -> Unit = {
        // 使用主题中提供的 indicator 实现
        WpPullToRefreshIndicator(isRefreshing, state)
    },
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier.pullToRefresh(
            state = state,
            isRefreshing = isRefreshing,
            onRefresh = onRefresh,
            threshold = 60.dp
        ),
    ) {
        if (state.isAnimating || state.distanceFraction > 0) {
            Box(modifier = Modifier.height((state.distanceFraction * 60).dp)) {
                indicator()
            }
        }
        content()
    }
}