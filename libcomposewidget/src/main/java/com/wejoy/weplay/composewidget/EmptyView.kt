package com.wejoy.weplay.composewidget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun EmptyView(
    icon: Int = 0,
    text: String = "",
    textColor: Color = TextColorTertiary,
    buttonText: String = "",
    buttonClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.align(Alignment.Center)
        ) {
            if (icon != 0) {
                WpImage(
                    model = icon,
                    contentDescription = "empty icon",
                    modifier = Modifier
                        .width(140.dp)
                        .height(140.dp),
                )
            }
            Body1(text, color = textColor)
            if (buttonText.isNotBlank()) {
                Spacer(
                    modifier = Modifier
                        .height(16.dp)
                        .fillMaxWidth(),
                )

                Box(
                    modifier = Modifier
                        .wrapContentWidth()
                        .widthIn(min = 88.dp)
                        .height(32.dp)
                        .background(ColorAccent, RoundedCornerShape(16.dp))
                        .clickableComplex(onClick = buttonClick),
                    contentAlignment = Alignment.Center
                ) {
                    BasicText(
                        text = buttonText,
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(horizontal = 8.dp)
                    )
                }

            }
        }
    }
}