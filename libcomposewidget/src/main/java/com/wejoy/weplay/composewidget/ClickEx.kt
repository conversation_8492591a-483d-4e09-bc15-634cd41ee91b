package com.wejoy.weplay.composewidget

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.LayoutDirection

/**
 *  设置点击防抖动 和 水波纹
 */
fun Modifier.clickableComplex(
    enabled: Boolean = true,
    onClickLabel: String? = null,
    role: Role? = null,
    internal: Long = 500L,
    rippleConfig: RippleConfig = RippleConfig(),
    onClick: () -> Unit
) = composed {
    val lastClickTime = remember { mutableStateOf(0L) }
    this.clickable(
        interactionSource = remember { MutableInteractionSource() },
        enabled = enabled,
        onClickLabel = onClickLabel,
        role = role,
        indication = if (!rippleConfig.enableRipple) {
            null
        } else {
            ripple(color = rippleConfig.rippleColor)
        }
    ) {
        val cur = System.currentTimeMillis()
        if ((cur - lastClickTime.value) > internal) {
            lastClickTime.value = cur
            onClick()
        }
    }
}

class RippleConfig(
    val enableRipple: Boolean = false,
    val rippleColor: Color = Color.Black
)

@Composable
fun Modifier.fitLayoutDirection() = composed {
    val scaleX = if (LocalLayoutDirection.current == LayoutDirection.Rtl) -1f else 1f
    graphicsLayer {
        this.scaleX = scaleX
    }
}

@Composable
fun Modifier.noRippleClickable(
    enabled: Boolean = true,
    onClick: () -> Unit
): Modifier {
    return this.clickable(
        enabled = enabled,
        indication = null,
        interactionSource = remember { MutableInteractionSource() },
        onClick = onClick
    )
}