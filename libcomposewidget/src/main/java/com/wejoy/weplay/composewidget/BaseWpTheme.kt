package com.wejoy.weplay.composewidget

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Typography
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp

/**
 * Typography migrate from [com.huiwan.base.ui.WPUIText]
 *
 *  虽然将 titleLarge ~ labelSmall 映射到 N1~N6 不符合 Typography定义，但为了将组件库 N1~N6放入Typography，暂先这么做了
 */
@Composable
fun BaseWpTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    fontFactory: APPFontFactory,
    content: @Composable () -> Unit,
) {
    val defaultColor = if (darkTheme) Color(0xFF000000) else Color(0xFFFFFFFF)
    val fontFamily = FontFamily(fontFactory.createNormalFont(), fontFactory.createBoldFont())
    val colorScheme = MaterialTheme.colorScheme.copy(
        onPrimary = TextColorPrimary,
        onSecondary = TextColorSecondary,
        onTertiary = TextColorTertiary,
        background = ColorAccent
    )
    MaterialTheme(
        typography = createTypography(defaultColor, fontFamily),
        content = content,
        colorScheme = colorScheme
    )
}

private fun createTypography(defaultColor: Color, fontFamily: FontFamily): Typography {
    return Typography(
        displayLarge = TextStyle(
            fontSize = 56.sp,
            lineHeight = 68.sp,
            fontFamily = fontFamily,
            color = defaultColor,
            fontWeight = FontWeight.W700,
        ),
        //组件库 H1
        displayMedium = TextStyle(
            fontSize = 28.sp,
            lineHeight = 34.sp,
            fontFamily = fontFamily,
            color = defaultColor,
            fontWeight = FontWeight.W700,
        ),
        displaySmall = TextStyle(
            fontSize = 10.sp,
            lineHeight = 13.sp,
            fontFamily = fontFamily,
            color = defaultColor,
            fontWeight = FontWeight.W400,
        ),
        //组件库 H2
        headlineLarge = TextStyle(
            fontSize = 20.sp,
            lineHeight = 24.sp,
            fontFamily = fontFamily,
            color = defaultColor,
            fontWeight = FontWeight.W700,
        ),
        //组件库 H3
        headlineMedium = TextStyle(
            fontSize = 18.sp,
            lineHeight = 22.sp,
            fontFamily = fontFamily,
            color = defaultColor,
            fontWeight = FontWeight.W700,
        ),
        //组件库 H4
        headlineSmall = TextStyle(
            fontSize = 16.sp,
            lineHeight = 20.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W700,
            color = defaultColor,
            textAlign = TextAlign.Center,
        ),
        //组件库 Body1
        bodyLarge = TextStyle(
            fontSize = 14.sp,
            lineHeight = 18.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W400,
            color = defaultColor,
            textAlign = TextAlign.Center,
        ),
        //组件库 Body2
        bodyMedium = TextStyle(
            fontSize = 12.sp,
            lineHeight = 16.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W400,
            color = defaultColor,
            textAlign = TextAlign.Center,
        ),
        //组件库 Body3
        bodySmall = TextStyle(
            fontSize = 10.sp,
            lineHeight = 12.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W400,
            color = defaultColor,
            textAlign = TextAlign.Center,
        ),
        //组件库 N1
        titleLarge = TextStyle(
            fontSize = 56.sp,
            lineHeight = 68.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W700,
        ),
        //组件库 N2
        titleMedium = TextStyle(
            fontSize = 30.sp,
            lineHeight = 36.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W700,
        ),
        //组件库 N3
        titleSmall = TextStyle(
            fontSize = 24.sp,
            lineHeight = 30.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W700,
            color = defaultColor,
            textAlign = TextAlign.Center,
        ),
        //组件库 N4
        labelLarge = TextStyle(
            fontSize = 18.sp,
            lineHeight = 22.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W700,
            color = defaultColor,
            textAlign = TextAlign.Center,
        ),
        //组件库 N5
        labelMedium = TextStyle(
            color = defaultColor,
            fontSize = 14.sp,
            lineHeight = 18.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W400,
        ),
        //组件库 N6
        labelSmall = TextStyle(
            fontSize = 12.sp,
            lineHeight = 16.sp,
            fontFamily = fontFamily,
            fontWeight = FontWeight.W400,
            color = defaultColor,
            textAlign = TextAlign.Center,
        ),
    )
}

interface APPFontFactory {
    fun createBoldFont(): Font

    fun createNormalFont(): Font
}