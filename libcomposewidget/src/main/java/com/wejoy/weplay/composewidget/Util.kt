package com.wejoy.weplay.composewidget

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color

/**
 * 在 Composable 环境 中使用
 * 通过该方式打印日志，不会造额外的成外部重组从而避免影响性能分析
 *
 * @sample actInComposeSample
 *
 *
 * 当点击Box 时，会看到输出
 * ```
 * observe int :1
 * observe int :2
 * ```
 * 而不会有 out recompose 的输出
 */
@Composable
fun actInCompose(block: () -> Unit) {
    block()
}

@Composable
private fun actInComposeSample() {
    Column(Modifier.fillMaxSize()) {
        var curInt by remember {
            mutableIntStateOf(0)
        }
        actInCompose {
            if (curInt > 0) {
                Log.d("ddd", "observe int :curInt")
            }
        }

        Log.d("ddd", "out recompose")
        Box(
            Modifier
                .background(color = Color.Red)
                .clickable {
                    curInt += 1
                }) {}
    }
}
