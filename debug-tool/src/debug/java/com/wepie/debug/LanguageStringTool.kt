package com.wepie.debug

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import com.google.gson.JsonObject
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.baseservice.HwServiceManager
import com.huiwan.baseservice.IHwService
import com.huiwan.platform.ThreadUtil
import com.huiwan.store.PrefUtil
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.text.StringEscapeUtils
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.ConcurrentHashMap

object LanguageStringTool {

    private const val KEY_TRANSLATE_ID = "KEY_TRANSLATE_ID"

    private const val HOST = "https://translate.weplayapp.com/api/public/export_trans_multi"

    private const val KEY_LANGUAGE_TRANSLATE = "KEY_LANGUAGE_TRANSLATE"

    private val languageStringCache = ConcurrentHashMap<Int, String>()
    private val languageStringArrayCache = ConcurrentHashMap<Int, Array<String>>()
    private var local = LibBaseUtil.getLocale()

    fun init() {
        local = LibBaseUtil.getLocale()
        ResUtil.addResourceFilter { resources ->
            LanguageStringResources(resources, languageStringCache, languageStringArrayCache)
        }
        HwServiceManager.getServiceManager().registerService(object : IHwService {
            override fun languageChangeClear() {
                super.languageChangeClear()
                loadFromFile()
            }
        })
        loadFromFile()
    }

    fun genView(context: Context, activity: AppCompatActivity): View {
        val composeView = ComposeView(context)
        composeView.setContent {
            Column {
                LanguageIdInput()
            }
        }

        val lay = LinearLayout(context)
        lay.orientation = LinearLayout.VERTICAL
        val et = EditText(context)
        et.hint = "输入翻译ID"
        et.inputType = EditorInfo.TYPE_CLASS_NUMBER
        et.setText(PrefUtil.getInstance().getString(KEY_TRANSLATE_ID, BuildConfig.TRANSLATION_ID))
        lay.addView(
            et,
            LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )

        val btLay = LinearLayout(context)
        btLay.orientation = LinearLayout.HORIZONTAL
        lay.addView(
            btLay,
            LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )


        val loadBt = Button(context)
        loadBt.text = "加载翻译"
        loadBt.setOnClickListener {
            val id = et.text?.toString()
            if (id.isNullOrEmpty()) {
                return@setOnClickListener
            }
            loadBt.isEnabled = false
            PrefUtil.getInstance().setString(KEY_TRANSLATE_ID, id)
            CoroutineScope(Dispatchers.IO).launch {
                if (!loadFromServer(id)) {
                    return@launch
                }
                ToastUtil.debugShow("加载成功,正重启当前页面")
                ThreadUtil.runOnUiThreadDelay(100) {
                    activity.recreate()
                }
            }
        }

        btLay.addView(
            loadBt, LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )

        val resetBt = Button(context)
        resetBt.text = "恢复默认"
        resetBt.setOnClickListener {
            clear()
            recreate(activity)
        }

        btLay.addView(
            resetBt, LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )

        val recreateBt = Button(context)
        recreateBt.text = "重建当前页面"
        recreateBt.setOnClickListener {
            recreate(activity)
        }

        btLay.addView(
            recreateBt, LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )

        return composeView
    }

    suspend fun loadFromServer(id: String): Boolean {
        val rsp = post(id) ?: return false
        PrefUtil.getInstance().setString(KEY_LANGUAGE_TRANSLATE, rsp)
        loadStrings(rsp)
        return true
    }

    fun loadFromFile(autoRefresh: Boolean = true) {
        val pref = PrefUtil.getInstance()
        val s = pref.getString(KEY_LANGUAGE_TRANSLATE, "")
        loadStrings(s)
        if (autoRefresh && s.isNullOrEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                val id = pref.getString(KEY_TRANSLATE_ID, BuildConfig.TRANSLATION_ID)
                loadFromServer(id)
            }
        }
    }

    fun clear() {
        languageStringCache.clear()
        languageStringArrayCache.clear()
        PrefUtil.getInstance().clear(KEY_LANGUAGE_TRANSLATE)
    }

    private fun recreate(activity: Activity) {
//        activity.recreate()
        val intent = activity.intent
        activity.finishAfterTransition()
        activity.startActivity(intent)
    }

    private suspend fun post(id: String): String? {
        val url = URL(HOST + "?file_id=${id}&file_type=5&operator_email=panrunqiu%40wepie.com")
        val connection = (url.openConnection() as? HttpURLConnection) ?: return null
        connection.setRequestMethod("POST")
        connection.setConnectTimeout(10000) // 连接超时
        connection.setReadTimeout(15000) // 读取超时
        connection.setDoOutput(true) // 允许输出
        connection.setDoInput(true) // 允许输入
//        connection.setRequestProperty("Content-Type", "application/json") // 设置请求头

        val response = StringBuilder()
        val responseCode: Int = connection.getResponseCode()
        if (responseCode != HttpURLConnection.HTTP_OK) {
            // 处理错误码
            HLog.e("NetworkError", "Response Code: $responseCode")
            return null
        }
        connection.getInputStream().use { inputStream ->
            val reader = BufferedReader(InputStreamReader(inputStream))
            var line: String?
            while ((reader.readLine().also { line = it }) != null) {
                response.append(line)
            }
        }
        return response.toString()
    }

    private fun getLanguageMap(obj: JsonObject, langName: String): Map<String, String> {
        val languageObj = obj.getAsJsonObject(langName) ?: return emptyMap()
        val map = mutableMapOf<String, String>()
        languageObj.asMap().forEach { (k, v) ->
            map.put(k, v.asString)
        }
        return map
    }

    private fun replaceFormat(old: String, hans: String): String {
        var s = old
        var i = 1
        var startIndex = hans.indexOf("{#")
        do {
            val endIndex = hans.indexOf("#}", startIndex)
            if (endIndex < 0) {
                return old
            }
            val format = hans.substring(startIndex, endIndex + 2)
            val newFormat = "%$i\$s"
            s = s.replace(format, newFormat)
            i++
            startIndex = hans.indexOf("{#", endIndex)
        } while (startIndex >= 0)
        return s
    }

    private fun loadStrings(rsp: String?) {
        if (rsp.isNullOrEmpty()) {
            return
        }
        val obj = JsonUtil.toObject(rsp)
        val langMap = getLanguageMap(obj, LibBaseUtil.getLang().name)
        val hansMap = getLanguageMap(obj, "zh-Hans")
        val map = mutableMapOf<Int, String>()
        val res = ResUtil.getResource()

        val stringModifyList = listOf(
            StringFormatModify(hansMap),
            UnescapeModify()
        )

        val packageName = LibBaseUtil.getApplication().packageName
        langMap.entries.forEach { (k, v) ->
            val id = res.getIdentifier(k, "string", packageName)
            if (id == 0) {
                return@forEach
            }
            var newS = v
            for (modify in stringModifyList) {
                newS = modify.modify(k, newS)
            }
            map[id] = newS
        }
        languageStringCache.putAll(map)
    }

    interface IStringModify {
        fun modify(key: String, value: String): String
    }

    class StringFormatModify(private val hansMap: Map<String, String>) : IStringModify {
        override fun modify(key: String, value: String): String {
            if (value.contains("{#")) {
                val hans = hansMap[key] ?: return value
                return replaceFormat(value, hans)
            }
            return value
        }

    }

    class UnescapeModify : IStringModify {
        override fun modify(key: String, value: String): String {
            try {
                return StringEscapeUtils.unescapeJava(value)
            } catch (thr: Throwable) {
                thr.printStackTrace()
                return value.replace("\\n", "\n").replace("\\t", "\t")
            }
        }
    }
}

@Composable
fun LanguageIdInput() {

}

@Composable
fun LanguageDisposeTools() {

}