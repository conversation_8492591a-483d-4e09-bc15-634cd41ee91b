package com.wepie.module.rank.activity;

import com.huiwan.base.str.ResUtil;
import com.wepie.module.rank.R;

public class NewRankConst {
    public static final String DEFAULT_AREA = "Global";
    public static final String DEFAULT_AREA_NAME = ResUtil.getStr(R.string.rank_popularity_default_area);
    public static final String DEFAULT_PROVINCE = ResUtil.getStr(R.string.rank_popularity_default_province);
    public static final int TYPE_FLOWER_DAILY = 1;
    public static final int TYPE_FLOWER_YESTERDAY = 2;
    public static final int TYPE_FLOWER_STAR = 3;
    public static final int TYPE_FLOWER_YEAR = 17;
    public static final int TYPE_VIP_DAILY = 4;
    public static final int TYPE_VIP_YESTERDAY = 5;
    public static final int TYPE_VIP_STAR = 6;
    public static final int TYPE_VIP_YEAR = 18;
    public static final int TYPE_CP_WEEK = 7;
    public static final int TYPE_CP_TOTAL = 8;
    public static final int TYPE_CP_BLESS = 9;
    public static final int TYPE_VOICE_ROOM_DAILY = 10;
    public static final int TYPE_VOICE_ROOM_WEEK = 11;
    public static final int TYPE_VOICE_ROOM_TOTAL = 12;
    public static final int TYPE_FAMILY_WEEK = 13;
    public static final int TYPE_FAMILY_TOTAL = 14;
    public static final int TYPE_AVATAR_MALE = 15;
    public static final int TYPE_AVATAR_FEMALE = 16;
    public static final int TYPE_JK_GAME_EARN = 17;
    public static final int TYPE_JK_GAME_WIN = 18;

//    public static final String LOVE_RANK_SELF_TYPE_LOVE_WEEK = "weekly";
//    public static final String LOVE_RANK_SELF_TYPE_LOVE_TOTAL = "general";
//    public static final String LOVE_RANK_SELF_TYPE_BLESS_TOTAL = "bless";
//
//    public static final String VOICE_ROOM_RANK_SELF_TYPE_DAY = "daily";
//    public static final String VOICE_ROOM_RANK_SELF_TYPE_WEEK = "weekly";
//    public static final String VOICE_ROOM_RANK_SELF_TYPE_TOTAL = "general";

    public static int getRankIcon(int rank) {
        if (rank == 1) {
            return R.drawable.family_new_rank_icon1;
        }
        if (rank == 2) {
            return R.drawable.family_new_rank_icon2;
        }
        if (rank == 3) {
            return R.drawable.family_new_rank_icon3;
        }
        return R.drawable.transparent_background;
    }
}
