package com.wepie.module.rank.view.couple;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.wepie.module.rank.R;
import com.wepie.module.rank.activity.NewRankConst;
import com.wepie.module.rank.bean.CoupleRankInfo;
import com.wepie.module.rank.view.base.BaseRankDetailWithBottomFragment;
import com.wepie.module.rank.view.base.RankRecycleViewItemAdapter;
import com.wepie.module.rank.viewmodel.CoupleRankViewModel;

public class CoupleRankDetailFragment extends BaseRankDetailWithBottomFragment<CoupleRankInfo> {

    private CoupleRankItemVH selfHolder;
    private View selfEmptyView;
    private CoupleRankViewModel model;
    @Override
    protected void registerViewModel() {
        CoupleRankViewModel model = getViewModel();
        switch (rankType) {
            case NewRankConst.TYPE_CP_WEEK:
                model.weekRankDataList.observe(getViewLifecycleOwner(), rankUsers -> refreshList(rankUsers, true));
                break;
            case NewRankConst.TYPE_CP_TOTAL:
                model.totalRankDataList.observe(getViewLifecycleOwner(), coupleRankModel -> refreshList(coupleRankModel.getRankDataList(), coupleRankModel.isEnd()));
                break;
            case NewRankConst.TYPE_CP_BLESS:
                model.blessRankDataList.observe(getViewLifecycleOwner(), coupleRankModel -> refreshList(coupleRankModel.getRankDataList(), coupleRankModel.isEnd()));
                break;
        }
        LiveData<CoupleRankInfo> selfData = getSelfLiveData();
        if (selfData != null) {
            selfData.observe(getViewLifecycleOwner(), this::updateSelfData);
        }
    }

    private CoupleRankViewModel getViewModel() {
        if (model == null) {
            model = new ViewModelProvider(requireActivity()).get(CoupleRankViewModel.class);
        }
        return model;
    }

    @Override
    protected LiveData<CoupleRankInfo> getSelfLiveData() {
        if (rankType == NewRankConst.TYPE_CP_WEEK) {
            return getViewModel().selfWeekData;
        } else if (rankType == NewRankConst.TYPE_CP_TOTAL) {
            return getViewModel().selfTotalData;
        } else if (rankType == NewRankConst.TYPE_CP_BLESS) {
            return getViewModel().selfBlessData;
        }
        return null;
    }

    @Nullable
    @Override
    protected View convertBottomSelfView(View origenView, CoupleRankInfo data) {
        if (data == null || !data.isMarried()) {
            if (selfEmptyView == null) {
                selfEmptyView = LayoutInflater.from(getContext()).inflate(R.layout.rank_empty_bottom, bottomLayout, false);
                TextView actBtn = selfEmptyView.findViewById(R.id.rank_bottom_empty_btn);
                actBtn.setText(R.string.couple_rank_item_goto_wedding);
                actBtn.setTextColor(ResUtil.getColor(R.color.rank_white_color));
                actBtn.setBackgroundResource(R.drawable.couple_rank_bottom_empty_btn_bg);
                actBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ApiService.of(HwApi.class).gotoPropose2Activity(getContext(), 0);
                    }
                });
            }
            return selfEmptyView;
        } else {
            if (selfHolder == null) {
                View selfRankView = LayoutInflater.from(getContext()).inflate(R.layout.couple_rank_item_layout, bottomLayout, false);
                selfHolder = new CoupleRankItemVH(selfRankView, rankType);
            }
            selfHolder.bind(data, data.getRankNum(), false);
            return selfHolder.itemView;
        }
    }

    @Override
    protected void initView(View root) {
        super.initView(root);
        headStub.setLayoutResource(R.layout.rank_list_couple_head);
        View view = headStub.inflate();
        headHolder = new RankHeadCpHolder(view);
    }

    @Override
    protected RankRecycleViewItemAdapter<CoupleRankInfo, ? extends RecyclerView.ViewHolder> createAdapter() {
        return new CoupleRankItemAdapter(getContext(), rankType);
    }

    @Override
    protected RecyclerView.LayoutManager createLayoutManager() {
        return new LinearLayoutManager(getContext(), RecyclerView.VERTICAL, false);
    }

    @Override
    protected int getBottomGradientRGBColor() {
        return 0x1a0008;
    }

    @Override
    protected RecyclerView.RecycledViewPool createRecyclerViewPool() {
        return model.getPool();
    }
}
