package com.wepie.module.rank.view.vip;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.huiwan.widget.rv.RVHolder2;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.module.rank.R;
import com.wepie.module.rank.bean.RankUser;
import com.wepie.module.rank.view.base.RankHofTitleTimeItemHolder;
import com.wepie.module.rank.view.base.RankRecycleViewItemAdapter;
import com.wepie.module.rank.view.base.RankHofUserItemHolder;

public class VIPHofRankItemAdapter extends RankRecycleViewItemAdapter<RankUser, RVHolder2<RankUser>> {
    private final LayoutInflater layoutInflater;

    public VIPHofRankItemAdapter(Context context) {
        this.layoutInflater = LayoutInflater.from(context);
    }

    @NonNull
    @Override
    public RVHolder2<RankUser> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == RankUser.RANK_INFO_TYPE_TIME_TITLE) {
            View view = layoutInflater.inflate(R.layout.hof_rank_time_item_layout, parent, false);
            return new RankHofTitleTimeItemHolder(view, R.drawable.rank_month_title_vip_bg);
        } else {
            View view = layoutInflater.inflate(R.layout.hof_gride_item_view, parent, false);
            return new RankHofUserItemHolder(view, TrackScreenName.VIP_RANK);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RVHolder2<RankUser> holder, int position) {
        holder.bind(dataList.get(position), position);
    }

    @Override
    public int getItemViewType(int position) {
        return dataList.get(position).getRankInfoType();
    }
}
