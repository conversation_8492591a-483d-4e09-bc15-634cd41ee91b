package com.wepie.module.rank.bean;

import com.google.gson.annotations.SerializedName;
import com.wepie.wespy.model.entity.qualifying.QualifyingUserInfo;


public class RankJkItemInfo {

    @SerializedName("uid")
    public int uid = 0;

    @SerializedName("score")
    public long score = 0;

    @SerializedName("headimgurl")
    public String headimgurl = "";

    @SerializedName("nickname")
    public String nickname = "";

    @SerializedName("active_level")
    public int activeLevel = 0;

    @SerializedName("qualifying_info")
    public QualifyingUserInfo qualifyInfo = new QualifyingUserInfo();
}

