package com.wepie.module.rank.view.family;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.widget.FamilyLightView;
import com.huiwan.widget.rv.RVHolder;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.module.rank.R;
import com.wepie.module.rank.bean.FamilyNewRankInfo;
import com.wepie.module.rank.view.base.RankRecycleViewItemAdapter;

public class FamilyNewRankItemAdapter extends RankRecycleViewItemAdapter<FamilyNewRankInfo, FamilyNewRankItemAdapter.ViewHolder> {

    private final LayoutInflater layoutInflater;

    public FamilyNewRankItemAdapter(Context context) {
        this.layoutInflater = LayoutInflater.from(context);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(layoutInflater.inflate(R.layout.family_new_rank_item, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull FamilyNewRankItemAdapter.ViewHolder holder, int position) {
        FamilyNewRankInfo info = dataList.get(position);
        holder.refresh(info, info.getRank(), info.getActiveValue(), true);
    }

    public static class ViewHolder extends RVHolder {
        private final TextView rankTv;
        private final DecorHeadImgView familyAvatarIv;
        private final TextView nameTv;
        private final TextView activeValueTv;
        private final RankFamilyLevelIconView levelIv;
        private final FamilyLightView familyLightView;

        public ViewHolder(View view) {
            super(view);
            rankTv = view.findViewById(R.id.rank_tv);
            familyAvatarIv = view.findViewById(R.id.rank_cover_iv);
            nameTv = view.findViewById(R.id.rank_name_tv);
            activeValueTv = view.findViewById(R.id.value_num_tv);
            levelIv = view.findViewById(R.id.rank_level_iv);
            familyLightView = view.findViewById(R.id.family_light_view);
        }

        @SuppressLint("SetTextI18n")
        public void refresh(final FamilyNewRankInfo object, int rank, int active, boolean isList) {
            if (rank <= 0) {
                rankTv.setVisibility(View.VISIBLE);
                rankTv.setText("-");
            } else if (rank <= 999) {
                rankTv.setText(String.valueOf(rank));
            } else {
                rankTv.setText("999+");
            }
            nameTv.setText(object.getFamilyName());
            activeValueTv.setText("" + active);
            levelIv.refresh(object.getLevel());
            familyAvatarIv.updateDecoration(object.getFamilyAvatar(), object.getAvatarFrameId());
            View.OnClickListener jumpClick = v -> {
                if (v == itemView) {
                    ApiService.of(HwApi.class).gotoFamilyDetailActivity(itemView.getContext(), object.getFamilyId(), TrackScreenName.FAMILY_RANK_PAGE);
                }
            };

            if (isList) {
                itemView.setOnClickListener(jumpClick);
            } else {
                itemView.setOnClickListener(null);
            }

            object.getFamilyLightInfo().initPropInfo();
            familyLightView.updateFamilyLightView(object.getFamilyLightInfo(), false);

        }
    }
}
