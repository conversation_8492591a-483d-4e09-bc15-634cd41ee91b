package com.wepie.module.rank.view.jackaroo

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.widget.rv.RVHolder
import com.wepie.module.rank.R
import com.wepie.module.rank.activity.NewRankConst
import com.wepie.module.rank.view.base.BaseRankDetailWithBottomFragment
import com.wepie.module.rank.view.base.RankRecycleViewItemAdapter
import com.wepie.module.rank.viewmodel.JackarooRankItem
import com.wepie.module.rank.viewmodel.JackarooRankType
import com.wepie.module.rank.viewmodel.RankJackarooViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class RankJkGameDetailFragment : BaseRankDetailWithBottomFragment<JackarooRankItem>() {
    private val model: RankJackarooViewModel by lazy {
        ViewModelProvider(requireActivity())[RankJackarooViewModel::class.java]
    }
    private var selfHolder: RankJkGameItemAdapter.ViewHolder? = null
    private val listLiveData: MutableLiveData<List<JackarooRankItem>> = MutableLiveData()
    private val selfLiveData = MutableLiveData<JackarooRankItem>()

    override fun getLiveData(): LiveData<List<JackarooRankItem>> {
        return listLiveData
    }

    override fun getSelfLiveData(): LiveData<JackarooRankItem> {
        return selfLiveData
    }

    override fun convertBottomSelfView(origenView: View?, data: JackarooRankItem): View {
        var selfHolder = this.selfHolder
        if (selfHolder == null) {
            val selfRankView = LayoutInflater.from(context)
                .inflate(R.layout.rank_jk_game_rank_item, bottomLayout, false)
            selfHolder = RankJkGameItemAdapter.ViewHolder(selfRankView)
        }
        selfHolder.refresh(data)
        return selfHolder.itemView
    }

    override fun initView(root: View?) {
        super.initView(root)
        headStub.layoutResource = R.layout.rank_list_jk_game_head
        val view = headStub.inflate()
        headHolder = RankHeadJkGameHolder(view)
        val lp = emptyView.layoutParams as ViewGroup.MarginLayoutParams
        lp.topMargin = ScreenUtil.dip2px(64f)

        // viewModel 的 scope 跟着 activity 走，但这里 fragment 可能提前销毁，应当用 fragment 的 lifecycleScope
        lifecycleScope.launch {
            val collectStat = if (rankType == NewRankConst.TYPE_JK_GAME_WIN) {
                model.winStat
            } else {
                model.earnStat
            }
            collectStat.collectLatest {
                refresh(it)
            }
        }
    }

    private fun refresh(rankType: JackarooRankType) {
        listLiveData.value = rankType.rank
        selfLiveData.value = rankType.mine
    }

    override fun onResume() {
        super.onResume()
        model.refreshIfEmpty(rankType)
    }

    override fun createAdapter(): RankRecycleViewItemAdapter<JackarooRankItem, out RVHolder> {
        return RankJkGameItemAdapter(requireContext())
    }

    override fun createLayoutManager(): RecyclerView.LayoutManager {
        return LinearLayoutManager(context)
    }

    override fun getBottomGradientRGBColor(): Int {
        return if (rankType == NewRankConst.TYPE_JK_GAME_WIN) {
            0x011426
        } else {
            0x170302
        }
    }

    override fun createRecyclerViewPool(): RecyclerView.RecycledViewPool {
        return model.getPool()
    }
}