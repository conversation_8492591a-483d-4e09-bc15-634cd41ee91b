package com.wepie.module.rank.fragment;

import static com.wepie.module.rank.activity.NewRankConst.DEFAULT_AREA;

import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.View;
import android.view.ViewStub;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.huiwan.base.str.ResUtil;
import com.huiwan.constants.ActivityResultCode;
import com.huiwan.constants.IntentConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.module.rank.R;
import com.wepie.module.rank.activity.IRankFullBg;
import com.wepie.module.rank.activity.NewRankActivity;
import com.wepie.module.rank.activity.NewRankConst;
import com.wepie.module.rank.view.base.BaseRankDetailFragment;
import com.wepie.module.rank.view.popularity.PopularityHofRankDetailFragment;
import com.wepie.module.rank.view.popularity.PopularityRankDetailFragment;
import com.wepie.module.rank.viewmodel.BaseRankViewModel;
import com.wepie.module.rank.viewmodel.PopularityRankViewModel;

public class RankPopularityFragment extends BaseRankFragment {
    private TextView provinceTv;
    private ActivityResultLauncher<Intent> activityResultLauncher;

    private String area;
    private PopularityRankViewModel rankViewModel;

    @Override
    protected void initView(View rootView) {
        super.initView(rootView);
        tab = NewRankActivity.POPULARITY_TAB_INDEX;
        ViewStub stub = rootView.findViewById(R.id.rank_sub_area_stub);
        View stubView = stub.inflate();
        provinceTv = stubView.findViewById(R.id.area_tv);
        provinceTv.setOnClickListener(v -> {
            if (activityResultLauncher != null) {
                Intent intent = new Intent(requireActivity(), ApiService.of(HwApi.class).getLocationSelectActivityClass());
                intent.putExtra(IntentConfig.ACTIVITY_CODE, ActivityResultCode.PROVINCE_SELECT_REQUEST_CODE);
                activityResultLauncher.launch(intent);
            }
        });
        initSubLabelViewTheme(0xff200b36, R.drawable.rank_sub_label_popular);
        setProvinceName(DEFAULT_AREA, ResUtil.getStr(R.string.rank_popularity_default_area));
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityResultContracts.StartActivityForResult contracts = new ActivityResultContracts.StartActivityForResult();
        activityResultLauncher = registerForActivityResult(contracts, this::handleOnActivityResult);
    }

    @Override
    protected void initSubLabelList() {
        subLabelList.clear();
        subLabelList.add(ResUtil.getString(R.string.rank_popularity_sub_text_1));
        subLabelList.add(ResUtil.getString(R.string.rank_popularity_sub_text_2));
        subLabelList.add(ResUtil.getString(R.string.rank_popularity_sub_text_3));
        subLabelList.add(ResUtil.getString(R.string.rank_popularity_sub_text_4));
    }

    @Override
    protected BaseRankViewModel subscribeViewModel() {
        rankViewModel = new ViewModelProvider(requireActivity()).get(PopularityRankViewModel.class);
        return rankViewModel;
    }

    @Override
    protected Fragment createRankDetailFragment(int position) {
        BaseRankDetailFragment<?> fragment = null;
        int type = NewRankConst.TYPE_FLOWER_DAILY;
        switch (position) {
            case 0:
                fragment = new PopularityRankDetailFragment();
                type = NewRankConst.TYPE_FLOWER_DAILY;
                break;
            case 1:
                fragment = new PopularityRankDetailFragment();
                type = NewRankConst.TYPE_FLOWER_YESTERDAY;
                break;
            case 2:
                fragment = new PopularityHofRankDetailFragment();
                type = NewRankConst.TYPE_FLOWER_STAR;
                break;
            case 3:
                fragment = new PopularityRankDetailFragment();
                type = NewRankConst.TYPE_FLOWER_YEAR;
                break;
            default:
        }
        if (fragment != null) {
            Bundle bundle = new Bundle();
            bundle.putInt(BaseRankDetailFragment.EXTRA_TYPE, type);
            bundle.putSerializable(BaseRankDetailFragment.EXTRA_MODEL_CLASS, rankViewModel.getClass());
            fragment.setArguments(bundle);
        }
        return fragment;
    }

    public void setProvinceName(String areaStr, String areaNameStr) {
        area = areaStr;
        provinceTv.setText(areaNameStr);

        if (rankViewModel != null) {
            rankViewModel.requestData(area);
        }
    }

    @Override
    protected void onShowFullBg(IRankFullBg fullBg) {
        fullBg.showRankBg(R.drawable.rank_bg_popular);
    }

    @Override
    protected Drawable getSubLabelFrameBgDrawable() {
        return ContextCompat.getDrawable(requireActivity(), R.drawable.rank_sub_label_bg_pop);
    }

    public void handleOnActivityResult(ActivityResult activityResult) {
        if (activityResult.getResultCode() == Activity.RESULT_OK) {
            Intent data = activityResult.getData();
            if (data == null) {
                return;
            }
            String area = data.getStringExtra(IntentConfig.AREA);
            String areaName = data.getStringExtra(IntentConfig.AREA_NAME);
            setProvinceName(area, areaName);
            TrackUtil.appClick(TrackScreenName.RANK_SELECT_AREA_PAGE, areaName);
        }
    }

    @Override
    protected int getDefaultIndex() {
        return 1;
    }
}
