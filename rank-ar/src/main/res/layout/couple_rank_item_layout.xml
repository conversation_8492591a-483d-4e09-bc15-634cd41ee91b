<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="68dp"
    android:clipChildren="false"
    tools:background="#FFFFFF">

    <RelativeLayout
        android:id="@+id/rank_couple_avatar_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="44dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.huiwan.decorate.DecorHeadImgView
            android:id="@+id/rank_groom_avatar_iv"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:src="@drawable/default_head_icon"
            app:border_end_color="#FFC28C59"
            app:border_star_color="#FFFAF0D4"
            app:gradiant_area_size="48dp"
            app:head_border_width="1dp"
            app:use_gradiant_border="true" />

        <com.huiwan.decorate.DecorHeadImgView
            android:id="@+id/rank_bride_avatar_iv"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:layout_toEndOf="@id/rank_groom_avatar_iv"
            android:src="@drawable/default_head_icon"
            app:border_end_color="#FFC28C59"
            app:border_star_color="#FFFAF0D4"
            app:gradiant_area_size="48dp"
            app:head_border_width="1dp"
            app:use_gradiant_border="true" />

    </RelativeLayout>


    <TextView
        android:id="@+id/rank_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/rank_couple_avatar_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:onlyNum="true"
        tools:text="4" />

    <ImageView
        android:id="@+id/rank_couple_ring_iv"
        android:layout_width="112dp"
        android:layout_height="112dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/rank_couple_avatar_layout"
        app:layout_constraintLeft_toLeftOf="@+id/rank_couple_avatar_layout"
        app:layout_constraintRight_toRightOf="@+id/rank_couple_avatar_layout"
        app:layout_constraintStart_toStartOf="@+id/rank_couple_avatar_layout"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/content_lay"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/rank_couple_avatar_layout"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/rank_name_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="5dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <com.huiwan.decorate.NameTextView
                android:id="@+id/groom_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="16dp"
                app:ntv_scene="rank"
                tools:text="望月阁111111111" />

            <com.huiwan.decorate.NameTextView
                android:id="@+id/bride_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="16dp"
                app:ntv_scene="rank"
                tools:text="望月阁111111111" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/value_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/value_num_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:onlyNum="true"
                tools:text="42798" />

            <TextView
                android:id="@+id/value_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:textColor="#99ffffff"
                android:textSize="11sp"
                tools:text="@string/couple_rank_item_love_value" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>