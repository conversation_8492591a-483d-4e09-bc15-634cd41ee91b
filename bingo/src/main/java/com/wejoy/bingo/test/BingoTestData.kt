package com.wejoy.bingo.test

import com.huiwan.user.LoginHelper
import com.wejoy.bingo.business.data.BingoViewModel
import com.wepie.wespy.net.tcp.packet.BingoPacket.SimplePlayerInfo
import com.wepie.wespy.net.tcp.packet.BingoPacket.WinnerInfo

/**
 * 测试数据
 * <AUTHOR>
 * @since 2023/8/29 20:44
 */
object BingoTestData {


    fun initData(model: BingoViewModel) {
        model.gameInfo.realInfo =
            model.gameInfo.realInfo.toBuilder().addAllPlayers(getBuyCardPlayerList()).build()
    }


    fun getBuyCardPlayerList(): List<Int> {
        return listOf(
            20002596,
            20002596,
            20002596,
            20002596,
            20002652,
            20002652,
            20002596,
            20002596,
            20002596
        )
    }

    fun getSimplePlayerList(): List<SimplePlayerInfo> {
        val player1 = SimplePlayerInfo.newBuilder()
            .setPlayerId(LoginHelper.getLoginUid())
            .setCardNum(2)
            .build()
        val player2 = SimplePlayerInfo.newBuilder()
            .setPlayerId(20002596)
            .setCardNum(2)
            .build()
        val player3 = SimplePlayerInfo.newBuilder()
            .setPlayerId(20002596)
            .setCardNum(1)
            .build()
        val list: MutableList<SimplePlayerInfo> = mutableListOf(player1, player2, player3)
        for (i in 0..10) {
            val player = SimplePlayerInfo.newBuilder()
                .setPlayerId(20002596)
                .setCardNum(1)
                .build()
            list.add(player)
        }
        return list
    }



    fun getWinnerInfoList(): List<WinnerInfo> {
        val winnerInfo1 = WinnerInfo.newBuilder()
            .setUid(LoginHelper.getLoginUid())
            .setAwardNum(3000)
            .setRanking(1)
            .build()
        val winnerInfo2 = WinnerInfo.newBuilder()
            .setUid(20002652)
            .setAwardNum(2000)
            .setRanking(2)
            .build()
        val winnerInfo3 = WinnerInfo.newBuilder()
            .setUid(0)
            .setAwardNum(0)
            .setRanking(3)
            .build()
        val list: List<WinnerInfo> = listOf(winnerInfo1, winnerInfo2, winnerInfo3)
        return list
    }


    private var index = 0
    val list = byteArrayOf(1, 4, 3, 15, 16, 17, 28, 34, 56, 7, 36, 29, 67, 75, 33, 32, 14)

    val hasGenList: MutableList<Byte> = ArrayList()

    fun getAllNumList(): List<Byte> {
        return list.toList()
    }

    fun getNumList(): List<Byte> {
        return listOf()
    }


    fun genNum(): Byte {
        index++
        if (index > list.size) {
            index = 0
        }
        return list[index]
    }


    fun getWatchListInfo(): List<SimplePlayerInfo> {
        return getSimplePlayerList()
    }
}