package com.wejoy.bingo

import com.huiwan.lib.api.plugins.bingo.IBingoApi
import com.huiwan.lib.api.plugins.bingo.IBingoCore
import com.huiwan.lib.api.plugins.bingo.IBingoDepends
import com.wejoy.bingo.business.BingoIntent

/**
 * bingo 核心类, 提供给外部使用
 * <AUTHOR>
 * @since 2023/7/13 17:06
 */
class BingoCore(bingoData: IBingoApi.BingoData, bingoDepends: IBingoDepends) : IBingoCore {


    private val bingoIntent = BingoIntent(bingoData, bingoDepends)

    override fun checkBingoMutual(bingoOpen: Boolean): Boolean {
        return bingoIntent.checkBingoMutual(bingoOpen)
    }


    override fun openBingo(
        memeOpen: Boolean,
        drawOpen: Boolean,
        winOpen: Boolean,
        bingoOpen: Boolean,
    ) {
        bingoIntent.openBingo(memeOpen, drawOpen, winOpen, bingoOpen)
    }


    override fun isPlayer(uid: Int): Bo<PERSON>an {
        return false
    }


    override fun releaseBingo() {
        bingoIntent.release()
    }


    override fun roomInfoUpdate(ownUid: Int) {
        bingoIntent.roomInfoUpdate()
    }

}
