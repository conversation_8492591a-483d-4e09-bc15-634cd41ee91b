package com.wejoy.bingo.common.util

import com.wejoy.bingo.business.ui.item.card.bigcard.CardNum
import com.wepie.liblog.main.FLog

/**
 * bingo 位置转换
 *
 * 这样的一个二维数组
 * [
 *     [1,16,31,46,61],
 *     [2,17,32,47,62],
 *     [3,18,33,48,63],
 *     [4,19,34,49,64],     19 x=3 y=2
 *     [5,20,35,50,65]
 * ]
 *     [1 ,2, 3, 4, 5  | 16,  17, 18, 19, 20]
 * 按列拼接起来后，转换为
 *     [1,2,3,4,5,16,17,18,19,20,31,32,33,34,35,46,47,48,49,50,61,62,63,64,65]
 * 使用这个数组的下标+1表示标记的位置，转换公式为
 * 原卡中第x行y列的数字(x,y从0开始)对应一维数组中第 y*5+x+1 个数字（从1开始计算）
 *
 *    比如 19  x = 1 y = 4   index = 1 * 5 + 4
 *    要转换成：x = 4 y = 1   index = 3) * 5 + 2
 *
 * <AUTHOR>
 * @since 2023/9/7 18:10
 */
object BingoPosition {


    private const val tag = "Position"


    /**
     * 通过数字，获取对应的 坐标
     * @param num 数字
     * @param cardList 卡片数据
     */
    fun getPositionByNum(num: CardNum, cardList: List<CardNum>): Int {
        if (cardList.isEmpty()) {
            return -1
        }
        val oldIndex = cardList.indexOf(num)
        val position = oldIndex + 1
        log("getPosition index=$oldIndex position=$position num=$num")
        return position
    }


    /**
     * 通过坐标获取对应的数字
     * @param num 数字
     * @param cardList 卡片数据
     */
    fun getNumByPosition(position: Int, cardList: List<CardNum>): CardNum? {
        val index = position - 1
        val num = cardList.getOrNull(index)
        if (num == null) {
            FLog.e(Throwable("bingo getNumByPosition, position=$position, cardList=$cardList"))
        }
        return num
    }


    fun log(msg: String) {
        BingoLog.e(tag, msg)
    }
}