package com.wejoy.bingo.business.ui.floating

import android.text.TextUtils
import android.widget.TextView
import com.wejoy.bingo.R
import com.wejoy.bingo.business.BingoUiManager
import com.wejoy.bingo.business.data.event.BingoEvent
import com.huiwan.base.ktx.fontNumSemiBold

/**
 *  买卡阶段
 *
 * <AUTHOR>
 * @since 2023/8/21 19:56
 */
class BingoBuyCardFloat(uiManager: BingoUiManager) : BingoBaseFloat(uiManager) {

    override var tag = "BingoBuyCardFloat"
    private lateinit var numberView: TextView

    init {
        initItem()
        lunch {
            gameVm.consumeEvents(BingoEvent.PlayerStatusEvent::class.java) {
                updateCount(it.push.currPlayerCount)
            }
        }
    }

    override fun getRootResId(): Int {
        return R.id.root_lay
    }


    override fun initView() {
        super.initView()
        numberView = getView().findViewById(R.id.bingo_float_buy_number)
        numberView.fontNumSemiBold()
    }


    override fun initData() {
        super.initData()
        updateCount(getRealGameInfo().playerCount)
    }


    private fun updateCount(count: Int) {
        val newStr = count.toString()
        val oldStr = numberView.text
        log("updateCount oldStr=$oldStr newStr=$newStr")
        if (TextUtils.equals(oldStr, newStr)) {
            return
        }
        numberView.text = count.toString()
    }


    /**
     * 获取布局ID
     */
    override fun getLayoutId(): Int {
        return R.layout.bingo_float_buy_card
    }


}