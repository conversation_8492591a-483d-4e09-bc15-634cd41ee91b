package com.wejoy.bingo.business.data.action

import com.wejoy.bingo.business.data.BingoGameInfo
import com.wejoy.bingo.business.data.BingoViewModel
import com.wejoy.bingo.business.data.event.BingoEvent
import com.wepie.wespy.net.tcp.packet.BingoPacket

/**
 * 随机数字播报 push
 * <AUTHOR>
 * @since 2023/7/10 14:58
 */
class GameGenNumAction(vm: BingoViewModel) : BaseAction(vm) {

    override fun onAction(gameInfo: BingoGameInfo, pushBody: BingoPacket.BingoPush) {

        val info = pushBody.genNum
        log("gen num = $info")

        // 处理数据
        gameInfo.realInfo = gameInfo.realInfo.toBuilder()
            .addAllBingoNums(info.bingoNumsList)
            .build()

        vm.emitEvents(BingoEvent.GenNumEvent(info))
    }

    override fun actionTypes(): List<Int> {
        return listOf(BingoPacket.BingoPushType.BingoPushTypeGenNum_VALUE)
    }
}

