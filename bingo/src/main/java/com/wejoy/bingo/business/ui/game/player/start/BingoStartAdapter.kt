package com.wejoy.bingo.business.ui.game.player.start

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.widget.inflate
import com.huiwan.widget.setExOnClickListener
import com.wejoy.bingo.R
import com.wejoy.bingo.business.BingoUiManager
import com.wejoy.bingo.business.ui.base.BingoBaseAdapter
import com.wejoy.bingo.common.util.BingoStore

/**
 * 开始游戏：选择金币
 * <AUTHOR>
 * @since 2023/8/28 17:40
 */
class BingoStartAdapter(uiManager: BingoUiManager) : BingoBaseAdapter<Int>(uiManager) {

    private var checkView: View? = null
    var selectGold = BingoStore.getStartGold()


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view: View = parent.inflate(R.layout.bingo_game_start_item, false)
        return DataHolder(view)
    }


    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is DataHolder) {
            val data = dataList[position]
            if (position == 0) {
                updateCheck(holder.rootView, data)
            }
            holder.goldNum.text = data.toString()
            holder.rootView.setExOnClickListener {
                updateCheck(it, data)
                BingoStore.saveStartGold(data)
            }
        }
    }


    private fun updateCheck(view: View, gold: Int) {
        if (view == checkView) {
            return
        }
        checkView?.background = ResUtil.getDrawable(R.drawable.bingo_start_bg_normal)
        view.background = ResUtil.getDrawable(R.drawable.bingo_start_bg_checked)
        checkView = view
        selectGold = gold
    }


    class DataHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val rootView: View = itemView.findViewById(R.id.bingo_start_item_root)
        val goldNum: TextView = itemView.findViewById(R.id.bingo_start_item_num)
    }


}