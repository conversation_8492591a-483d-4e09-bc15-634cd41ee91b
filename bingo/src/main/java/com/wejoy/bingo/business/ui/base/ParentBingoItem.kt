package com.wejoy.bingo.business.ui.base

import android.view.ViewGroup
import com.huiwan.base.ktx.invisible
import com.huiwan.base.ktx.visible
import com.wejoy.bingo.business.BingoUiManager

/**
 * 内部检查是否添加到父布局上
 * <AUTHOR>
 * @since 2023/8/30 20:44
 */
abstract class ParentBingoItem(uiManager: BingoUiManager, val parent: ViewGroup) :
    BaseBingoItem(uiManager) {


    /**
     * 添加到 父 view 上
     */
    override fun attachView() {
        super.attachView()
        if (!isAttach()) {
            parent.visible()
            parent.addView(getView())
        }
    }


    override fun hide() {
        super.hide()
        parent.invisible()
    }


    override fun show() {
        super.show()
        parent.visible()
    }
}