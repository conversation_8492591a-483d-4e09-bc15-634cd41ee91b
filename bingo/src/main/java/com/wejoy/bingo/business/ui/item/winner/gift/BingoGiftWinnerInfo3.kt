package com.wejoy.bingo.business.ui.item.winner.gift

import com.wejoy.bingo.R
import com.wejoy.bingo.business.BingoUiManager


/**
 * 礼物池样式3
 *     等级
 *     礼物icon x礼物数量
 * @author: lzf
 * @date: 2023/08/31 17:20
 */
class BingoGiftWinnerInfo3(uiManager: BingoUiManager) : BaseGiftWinnerInfo(uiManager) {

    init {
        initItem()
    }

    override fun getLayoutId(): Int {
        return R.layout.bingo_view_winner_list_item_gift3
    }


    override fun initView() {
        super.initView()
        tipsView = findViewById(R.id.bingo_winner_list3_tips)
        giftIcon1 = findViewById(R.id.bingo_winner_list3_gift1)
        giftIcon2 = findViewById(R.id.bingo_winner_list3_gift2)
        giftNum1 = findViewById(R.id.bingo_winner_list3_num1)
        giftNum2 = findViewById(R.id.bingo_winner_list3_num2)

        tipsView?.apply {
            setCrownSize(12F)
            setLevelSize(9F)
            setHeadSize(12F)
            setNickSize(9F, 12)
        }
    }


}