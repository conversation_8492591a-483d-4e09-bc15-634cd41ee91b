apply from: "../base_module.gradle"


dependencies {
    implementation project(':component:activity')
    implementation project(':component:decorate')
    implementation project(':component:gift')
    implementation project(path: ':component:music')

    implementation project(":lib:api")
    implementation project(':lib:libdialog')
    implementation project(':lib:baseutil')
    implementation project(':lib:libimageloader')
    implementation project(':lib:liblog')
    implementation project(':lib:store')
    implementation project(':lib:libwidget')
    implementation project(":lib:libhttp")
    implementation project(":lib:libproto")
    implementation project(":lib:libdownload")
    implementation project(':lib:api-plugin:track')
    implementation project(path: ':lib:hwconstants')
    implementation project(path: ':lib:libtcp')
    implementation project(path: ':lib:luban')
    implementation project(path: ':lib:api-plugin:uploader')
    implementation project(path: ':lib:JsBridge')
    implementation project(path: ':lib:libwebview')
    implementation project(':lib:startup')

    implementation project(':service:UserService')
    implementation project(":service:ConfigService")

    implementation project(path: ':module:hwroom')
    implementation project(path: ':module:webview')
    implementation project(':module:voiceroom:base')

    implementation(libs.eventBus)
    implementation project(':service:VoiceService')
    implementation project(':lib:api-plugin:voice-api')
}
