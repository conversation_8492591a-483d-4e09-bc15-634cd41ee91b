package com.wepie.libthirdparty.fbline;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.activity.ActivityTask;
import com.huiwan.base.activity.ActivityTaskBuilderHolder;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.twitter.sdk.android.core.DefaultLogger;
import com.twitter.sdk.android.core.Twitter;
import com.twitter.sdk.android.core.TwitterAuthConfig;
import com.twitter.sdk.android.core.TwitterConfig;
import com.wejoy.weplay.ex.ILife;
import com.wepie.liblog.main.HLog;
import com.wepie.libthirdparty.fbline.twitter.TwitterAuth;
import com.wepie.libthirdparty.fbline.twitter.TwitterAuth_V1;
import com.wepie.libthirdparty.fbline.twitter.WejoyAuthHandler;
import com.wepie.libthirdparty.fbline.twitter.WejoyTwitterAuthClient;

import java.lang.ref.SoftReference;
import java.util.Map;

public class TwitterLoginUtil {
    private static final String TAG = "TwitterLoginUtil";

    private final Activity activity;
    private WejoyTwitterAuthClient mTwitterAuthClient;

    public TwitterLoginUtil(Activity activity) {
        this.activity = activity;
        String key = "";
        String secret = "";
        Map<String, String> twitterConfig = GlobalConfigManager.getInstance().getTwitterConfig();
        if (twitterConfig != null) {
            key = twitterConfig.get("key");
            secret = twitterConfig.get("secret");
        }
        initTwitter(LibBaseUtil.getApplication(), key, secret);
        TwitterActivityTaskBuilder builder = new TwitterActivityTaskBuilder(this);
        ActivityTaskBuilderHolder.get().register(builder);
        ILife life = ContextUtil.getLife(activity);
        if (life != null) {
            life.onDestroy(() -> {
                ActivityTaskBuilderHolder.get().unregister(builder);
                return null;
            });
        }
    }

    public void login(boolean useCustomTab, TwitterLoginCallBack callBack) {
        mTwitterAuthClient = new WejoyTwitterAuthClient();
        mTwitterAuthClient.useCustomTab(useCustomTab);
        mTwitterAuthClient.authorize(activity, new WejoyAuthHandler.Callback() {
            @Override
            public void success(TwitterAuth auth) {
                String token = auth.getToken();
                String tokenSecret = "";
                if (auth instanceof TwitterAuth_V1) {
                    tokenSecret = ((TwitterAuth_V1) auth).getSession().getAuthToken().secret;
                }
                HLog.d(TAG, "authorize token:{} tokenSecret:{} authType:{}", token, tokenSecret, auth.getAuthType());

                if (callBack != null) {
                    callBack.loginSuccess(token, tokenSecret, auth.getAuthType());
                }
            }

            @Override
            public void failure(Exception exception) {
                exception.printStackTrace();
                HLog.d(TAG, "authorize.failure: {}", exception.getMessage());
                if (callBack != null) {
                    callBack.loginFail(exception.getMessage());
                }
            }
        });
    }

    private static void initTwitter(Context context, String key, String secret) {
        TwitterConfig config = new TwitterConfig.Builder(context)
                .logger(new DefaultLogger(Log.DEBUG))
                .twitterAuthConfig(new TwitterAuthConfig(key, secret))
                .debug(true)
                .build();
        Twitter.initialize(config);
    }

    public interface TwitterLoginCallBack {
        void loginSuccess(String token, String tokenSecret, int authType);

        void loginFail(String msg);
    }

    private static class TwitterActivityTaskBuilder implements ActivityTask.Builder {

        private final SoftReference<TwitterLoginUtil> reference;

        public TwitterActivityTaskBuilder(TwitterLoginUtil util) {
            reference = new SoftReference<>(util);
        }

        @Override
        public ActivityTask build(Activity activity) {
            return new ActivityTask() {
                @Override
                public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
                    ActivityTask.super.onActivityResult(activity, requestCode, resultCode, data);
                    TwitterLoginUtil util = reference.get();
                    if (util != null && util.mTwitterAuthClient != null) {
                        util.mTwitterAuthClient.onActivityResult(requestCode, resultCode, data);
                    }
                }
            };
        }
    }
}
