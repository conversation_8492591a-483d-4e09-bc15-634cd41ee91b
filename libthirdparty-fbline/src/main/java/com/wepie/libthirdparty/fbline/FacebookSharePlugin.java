package com.wepie.libthirdparty.fbline;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.widget.ImageView;
import android.widget.TextView;

import com.facebook.CallbackManager;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.facebook.share.model.ShareContent;
import com.facebook.share.model.ShareHashtag;
import com.facebook.share.model.ShareLinkContent;
import com.facebook.share.model.SharePhoto;
import com.facebook.share.model.SharePhotoContent;
import com.facebook.share.widget.ShareDialog;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.activity.ActivityTask;
import com.huiwan.base.activity.ActivityTaskBuilderHolder;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ToastUtil;
import com.wepie.lib.api.plugins.share.ShareCallback;
import com.wepie.lib.api.plugins.share.ShareInfo;
import com.wepie.lib.api.plugins.share.SharePlugin;
import com.wepie.lib.api.plugins.share.ShareResult;
import com.wepie.lib.api.plugins.share.ShareType;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.helper.dialog.progress.IProgressDialog;

public class FacebookSharePlugin implements SharePlugin {
    private boolean registered = false;
    private CallbackManager callbackManager;
    private boolean isMainProcess;
    private final ShareTag shareTag;

    public FacebookSharePlugin(ShareTag shareTag) {
        this.shareTag = shareTag;
        updateCallbackManager();
    }

    private void updateCallbackManager() {
        if (registered) {
            return;
        }
        registered = true;
        ActivityTaskBuilderHolder.get().register(activity -> new ActivityTask() {
            @Override
            public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
                if (callbackManager != null) {
                    callbackManager.onActivityResult(requestCode, resultCode, data);
                }
            }

            @Override
            public void onDestroy(Activity activity) {
                callbackManager = null;
            }
        });
    }

    @Override
    public ShareType getShareType() {
        return ShareType.facebook;
    }

    @Override
    public boolean isMainProcess() {
        return isMainProcess;
    }

    @Override
    public void setProcessInfo(boolean isMainProcess) {
        this.isMainProcess = isMainProcess;
    }


    @Override
    public void onShare(Context context, ShareInfo shareInfo, ShareCallback callback) {
//        updateCallbackManager();
        Activity activity = ContextUtil.getActivityFromContext(context);
        if (activity != null) {
            ShareDialog dialog = new ShareDialog(activity);
            callbackManager = CallbackManager.Factory.create();
            dialog.registerCallback(callbackManager, new FacebookCallback<Sharer.Result>() {
                @Override
                public void onSuccess(Sharer.Result result) {
                    tryHideLoading(activity);
                    if (callback != null) {
                        callback.onShare(new ShareResult(shareInfo, getShareType()));
                    }
                }

                @Override
                public void onCancel() {
                    tryHideLoading(activity);
                    if (callback != null) {
                        callback.onFailed(-1, ResUtil.getStr(R.string.cancel));
                    }
                }

                @Override
                public void onError(FacebookException error) {
                    tryHideLoading(activity);
                    if (callback != null) {
                        callback.onFailed(-1, error.getMessage());
                    }
                }
            });
            try {
                Bitmap bitmap = shareInfo.getBitmap();
                ShareContent<?, ?> shareContent;
                String hashTag = "";
                if (shareTag != null) {
                    hashTag = shareTag.getShareHashTag();
                } else {
                    hashTag = "#" + ResUtil.getStr(R.string.app_name);
                }
                if (hashTag == null) {
                    hashTag = "";
                }
                if (bitmap != null) {
                    SharePhoto photo = new SharePhoto.Builder().setBitmap(bitmap).build();
                    shareContent = new SharePhotoContent.Builder()
                            .addPhoto(photo)
                            .setShareHashtag(new ShareHashtag.Builder().setHashtag(hashTag).build())
                            .setPageId(BuildConfig.FB_PAGE_ID)
                            .build();
                } else {
                    HLog.d("FacebookSharePlugin", HLog.USR, "title={}, shareTag={}, content={}, desc={}, url={}, ",
                            shareInfo.getTitle(), hashTag, shareInfo.getContent(), shareInfo.getDesc(), shareInfo.getLink());
                    shareContent = new ShareLinkContent.Builder()
                            .setContentUrl(Uri.parse(shareInfo.getLink()))
                            .setQuote(shareInfo.getTitle() + "\n" + shareInfo.getContent())
                            .setShareHashtag(new ShareHashtag.Builder().setHashtag(hashTag).build())
                            .setPageId(BuildConfig.FB_PAGE_ID)
                            .build();
                }
                FacebookShareWrapper.share(activity, () -> dialog.canShow(shareContent, ShareDialog.Mode.NATIVE), code -> {
                    if (code > 0) {
                        tryShowLoading(activity);
                        dialog.show(shareContent, ShareDialog.Mode.NATIVE);
                    } else if (code == -1) {
                        toastNotInstall();
                        callback.onFailed(code, ResUtil.getStr(R.string.uninstall_facebook));
                    } else {
                        callback.onFailed(-2, ResUtil.getStr(R.string.share_failed));
                    }
                    return null;
                });
                return;
            } catch (Exception e) {
                LibBaseUtil.logInfo("Share", "error share fb {}", e);
                tryHideLoading(activity);
            }
        }
        if (callback != null) {
            callback.onFailed(-1, "ShareFailed");
        }
    }

    private void tryShowLoading(Object o) {
        if (o instanceof IProgressDialog) {
            ((IProgressDialog) o).showProgressDialogDelay();
        }
    }

    private void tryHideLoading(Object o) {
        if (o instanceof IProgressDialog) {
            ((IProgressDialog) o).hideProgressDialog();
        }
    }

    private void toastNotInstall() {
        ToastUtil.show(ResUtil.getStr(R.string.uninstall_facebook));
    }

    private boolean noFacebook(Context context) {
        try {
            PackageManager pm = context.getPackageManager();
            ApplicationInfo applicationInfo = pm.getApplicationInfo("com.facebook.katana", 0);
            return !applicationInfo.enabled;
        } catch (Exception ignored) {
            return true;
        }
    }

    @Override
    public void updateShareInfo(ImageView iconIv, TextView nameTv, boolean isPictureShare) {
        iconIv.setImageResource(R.drawable.ic_fb_share_full);
        nameTv.setTextColor(isPictureShare ? 0xffffffff : 0xff999CB4);
        nameTv.setText(R.string.facebook);
    }

    public interface ShareTag {
        String getShareHashTag();
    }
}
