package com.huiwan.barrage;

import android.content.Context;
import android.text.Html;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.wepie.emoji.view.EmojiHelper;

/**
 * Created by three on 16/4/15.
 */
public class BarrageItem extends LinearLayout {
    private Context mContext;
    private ImageView iconImage;
    private ImageView leftPadding;
    private TextView contentTx;

    public BarrageItem(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public BarrageItem(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.barrage_item_view, this);
        iconImage = (ImageView) findViewById(R.id.barrage_icon);
        leftPadding = (ImageView) findViewById(R.id.barrage_left_padding);
        contentTx = (TextView) findViewById(R.id.barrage_content_tx);
    }

    public void update(String content, int isGiftMsg, boolean fromUnity) {
        if(isGiftMsg == BarrageAnimView.TYPE_FAGUAN) {
            iconImage.setVisibility(View.VISIBLE);
            leftPadding.setVisibility(View.GONE);
            iconImage.setImageResource(R.drawable.judge_speak_icon);
        } else if(isGiftMsg == BarrageAnimView.TYPE_GIFT) {
            iconImage.setVisibility(View.VISIBLE);
            leftPadding.setVisibility(View.GONE);
            iconImage.setImageResource(R.drawable.room_item_flower_icon);
        }else {
            iconImage.setVisibility(View.GONE);
            leftPadding.setVisibility(View.INVISIBLE);
        }

        if (fromUnity) {
            contentTx.setText(EmojiHelper.parseEmojiAndHtml2Ssb(mContext, content, 14));
        } else {
            EmojiHelper.parseEmojis(mContext, contentTx, content, 22);
        }
        //contentTx.setText(content);
    }

}
