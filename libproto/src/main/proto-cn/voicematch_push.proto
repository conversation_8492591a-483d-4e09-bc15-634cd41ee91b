syntax = "proto3";
package push;

message VoiceMatchPush
{
    enum VMPushType
    {
        VM_PUSH_UNDEFINED = 0;
        MATCH_SUCCESS = 1;
        BOTH_LIKE = 2;
    }
    VMPushType push_type = 1;
    VMMatchSuccess match_success = 2;
    VMBothLike both_like = 3;
}

message VMMatchSuccess
{
    int32 target_uid = 1;
    string target_media_url = 2;
}

message VMBothLike
{
    int32 target_uid = 1;
}
 option java_package="com.wepie.wespy.net.tcp.packet"; option java_outer_classname = "VoiceMatchPushPackets";
