syntax = "proto3";
package landlords;

option go_package = "wespy_tcpgo/proto/landlords";

message LandlordsPush
{
    enum LLPushType
    {
        PUSH_UNDEFINED = 0;
        GAME_START = 1; 
        CALL_LOAD = 2;
        PUT_CARDS = 3;
        CANCEL_TRUST = 4;
        GAME_CANCEL = 5;
        TRUST = 6;
        DOUBLE = 7;
    }
    LLPushType push_type = 1;
    LLPushInfo push_info = 2;
}

message LLPushInfo
{
    int32 action_uid = 1;
    bool multiple_double = 2;
}
 option java_package="com.wepie.wespy.net.tcp.packet"; option java_outer_classname = "LandLordPushPackets";
