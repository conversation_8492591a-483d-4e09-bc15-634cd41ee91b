package com.wepie.download;


import android.text.TextUtils;

import com.wepie.download.chunks.ChunkInfo;
import com.wepie.download.chunks.ChunkData;

import java.io.File;

public class FileEntity {
    private static final String TAG = "FileEntity";

    private final String url;

    private final String cacheDirPath;
    private final String cacheFilePath;
    private boolean chunkDownload = false;
    public ChunkInfo chunkConfig;
    private long fileSize = -1;

    FileEntity(String url, String cacheDirPath, String cacheFilePath, boolean chunkDownload) {
        this.url = url;
        this.cacheDirPath = cacheDirPath;
        this.cacheFilePath = cacheFilePath;
        this.chunkDownload = chunkDownload;
    }

    public String getUrl() {
        return url;
    }

    public File tempFile() {
        String tempName = "";
        String tempDir = "";
        if (!TextUtils.isEmpty(cacheFilePath)) {//指定储存的文件路径
            File cacheFile = cacheFile();
            tempDir = cacheFile.getParent();
            tempName = cacheFile.getName() + ".temp";
        } else {
            tempDir = cacheDirPath;
            tempName = DownloadUtil.fileNameForUrl(url, true);
        }
        if (isChunkDownload()) {
            tempName = ChunkInfo.Companion.getTempFileName(url);
        }
        return new File(tempDir, tempName);
    }

    public boolean chunkDownloadSuccess() {
        boolean flag = false;
        if (null != chunkConfig) {
            long cur = 0;
            for (ChunkData data : chunkConfig.getChunks()) {
                cur += data.getBytesWritten();
            }
            flag = cur >= fileSize;
        }
        return flag;
    }

    public boolean isChunkDownload() {
        return chunkDownload;
    }

    public void setChunkDownload(boolean chunkDownload) {
        this.chunkDownload = chunkDownload;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public File cacheFile() {
        if (!TextUtils.isEmpty(cacheFilePath)) {//指定储存的文件路径
            return new File(cacheFilePath);
        }
        return DownloadUtil.getCacheFile(getUrl(), cachePath());
    }

    private String cachePath() {
        return cacheDirPath;
    }

    public void tempRename() {
        File file = tempFile();
        File newFile = cacheFile();
        boolean rename = file.renameTo(newFile);
    }
}
