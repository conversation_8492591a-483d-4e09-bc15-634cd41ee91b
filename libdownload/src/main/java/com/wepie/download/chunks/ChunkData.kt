package com.wepie.download.chunks

import com.google.gson.annotations.SerializedName

class ChunkData(@Transient val id: Int, @SerializedName("start") val start: Long, @SerializedName("end") val end: Long) {
    @SerializedName("downloadedLength")
    @Volatile
    var bytesWritten: Long = 0L
    @Transient
    var currentStart: Long = 0L

    constructor() : this(0, 0, 0)

    companion object {
        const val CHUNK_READ_SIZE = 1024
    }

    override fun toString(): String {
        return "ChunkData(id=$id, start=$start, end=$end, bytesWritten=$bytesWritten, currentStart=$currentStart)"
    }

}

fun ChunkData.isDownloaded(): <PERSON><PERSON>an {
    return (end - start + 1) == bytesWritten
}