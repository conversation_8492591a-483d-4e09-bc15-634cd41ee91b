package com.huiwan.module.authcheck;

import android.os.Bundle;
import android.widget.TextView;

import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.widget.actionbar.BaseWpActionBar;

/**
 * date 2020/5/12
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class FaceIdentityPreActivity extends BaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_face_identity_pre);
        ((BaseWpActionBar)findViewById(R.id.action_bar)).addTitleAndBack("授权声明");
        ((TextView)findViewById(R.id.tip_tv)).setText(ConfigHelper.getInstance().getConstV3Info().faceAuthTip);
        findViewById(R.id.ok_btn).setOnClickListener((v)-> WpFaceIdentity.start(this));
    }

    public void close() {
        finish();
        overridePendingTransition(0, 0);
    }
}
