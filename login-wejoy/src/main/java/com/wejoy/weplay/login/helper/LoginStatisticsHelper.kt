package com.wejoy.weplay.login.helper

import android.app.Activity
import com.huiwan.base.LibBaseUtil
import com.wepie.lib.api.plugins.track.LoginTraceHelper
import com.wepie.liblog.main.HLog

// 登录辅助相关
object LoginStatisticsHelper {
    private var isFaceBookWebLogin = false
    private const val TAG = "LoginStatisticsHelper"
    private const val FB_TAG = "FacebookLoginTAG"

    @JvmStatic
    fun updateFBWebTag(clear: Boolean, activity: Activity?) {
        if (clear) {
            isFaceBookWebLogin = false
        } else {
            activity?.let {
                val name = it.localClassName
                if (name.contains("CustomTab", true)) {
                    isFaceBookWebLogin = true
                }
            }
        }
    }

    @JvmStatic
    fun fbLoginReport(msg: String) {
        val isInstall = LibBaseUtil.isAPPInstalled(
            LibBaseUtil.getApplication(),
            LibBaseUtil.FACEBOOK_PACKAGE_NAME
        )
        val logMsg = "[$FB_TAG]， webLogin:$isFaceBookWebLogin, isInstall=$isInstall, msg:$msg"
        HLog.d(TAG, logMsg)
        LoginTraceHelper.aliLogTrace(logMsg)
    }
}