<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/password_input_lay"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layoutDirection="ltr"
    android:orientation="horizontal">

    <include
        android:id="@+id/dialog_secret_tx1"
        layout="@layout/login_input_number_cell_view"
        android:layout_width="48dp"
        android:layout_height="48dp" />

    <Space
        android:layout_width="12dp"
        android:layout_height="1dp" />

    <include
        android:id="@+id/dialog_secret_tx2"
        layout="@layout/login_input_number_cell_view"
        android:layout_width="48dp"
        android:layout_height="48dp" />

    <Space
        android:layout_width="12dp"
        android:layout_height="1dp" />

    <include
        android:id="@+id/dialog_secret_tx3"
        layout="@layout/login_input_number_cell_view"
        android:layout_width="48dp"
        android:layout_height="48dp" />

    <Space
        android:layout_width="12dp"
        android:layout_height="1dp" />

    <include
        android:id="@+id/dialog_secret_tx4"
        layout="@layout/login_input_number_cell_view"
        android:layout_width="48dp"
        android:layout_height="48dp" />

    <EditText
        android:id="@+id/dialog_secret_edittext"
        android:layout_width="1dp"
        android:layout_height="10dp"
        android:background="#0000"
        android:cursorVisible="false"
        android:inputType="number"
        android:maxLength="4"
        android:textAlignment="viewStart"
        android:textColor="#0000"
        android:textColorHint="#0000" />

</LinearLayout>