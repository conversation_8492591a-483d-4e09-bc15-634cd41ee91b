package com.wepie.emoji.view.model;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.store.PrefUtil;
import com.wepie.emoji.view.EmojiHelper;
import com.wepie.emoji.view.resource.EmojiItem;
import com.wepie.emoji.view.resource.EmojiRes;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

public class EmojiLatestUseInfo {
    public static final int NEW_USE_EMOJI_NUM = 7;

    @SerializedName("emojiList")
    public final List<EmojiItem> emojiList = new ArrayList<>();//原本只需要记7个，考虑到骰子在某些场景不展示所以需要记8个

    public static EmojiLatestUseInfo getInfo(boolean useNewEmoji) {
        String emojiLatestUseInfo = PrefUserUtil.getInstance().getString(PrefUtil.LATEST_USE_EMOJI, "");
        EmojiLatestUseInfo info = JsonUtil.parseJson(emojiLatestUseInfo, EmojiLatestUseInfo.class);
        if (null == info) {
            info = new EmojiLatestUseInfo();
        }
        ListIterator<EmojiItem> iterator = info.emojiList.listIterator();
        while (iterator.hasNext()) {
            EmojiItem i = iterator.next();
            int res = EmojiHelper.getEmojiRes(i.name);
            if (!useNewEmoji && i.type != EmojiRes.EMOJI_SYSTEM) {
                iterator.remove();
            } else if (res != 0) {
                i.res = res;
            } else {
                iterator.remove();
            }
        }
        return info;
    }

    public static void saveLastInfo(EmojiItem item) {
        EmojiLatestUseInfo info = getInfo(true);
        ListIterator<EmojiItem> iterator = info.emojiList.listIterator();
        while (iterator.hasNext()) {
            EmojiItem i = iterator.next();
            if (i.isSame(item)) {
                iterator.remove();
            }
        }
        if (info.emojiList.isEmpty()) {
            info.emojiList.add(item);
        } else {
            info.emojiList.add(0, item);
        }
        PrefUserUtil.getInstance().setString(PrefUtil.LATEST_USE_EMOJI, JsonUtil.toJsonString(info));
    }
}
