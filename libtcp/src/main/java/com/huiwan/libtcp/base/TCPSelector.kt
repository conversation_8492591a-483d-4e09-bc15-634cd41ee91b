package com.huiwan.libtcp.base

import android.util.Log
import com.huiwan.libtcp.TcpMainUtil
import java.io.IOException
import java.nio.channels.CancelledKeyException
import java.nio.channels.ClosedChannelException
import java.nio.channels.Selector
import java.util.concurrent.ExecutorService

class TCPSelector private constructor(service: ExecutorService) {

    private val register: ITCPRegister = try {
        val selector = Selector.open()
        SelectorTcpRegister(selector)
    } catch (e: IOException) {
        e.printStackTrace()
        NormalTcpRegister(service)
    }

    fun register(channel: TCPChannel) {
        synchronized(TCPSelector::class.java) {
            register.register(channel)
        }
    }

    @Throws(IOException::class)
    fun cancel(channel: TCPChannel) {
        synchronized(TCPSelector::class.java) {
            channel.close()
        }
    }

    companion object {

        const val TAG = "TCPSelector"

        @JvmStatic
        fun get(service: ExecutorService): TCPSelector {
            return TCPSelector(service)
        }
    }

    interface ITCPRegister {
        fun register(channel: TCPChannel)
    }

    private inner class SelectorTcpRegister(private var selector: Selector) : ITCPRegister,
        Runnable {

        @Volatile
        private var isRegistering = false

        override fun run() {
            try {
                select()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }

        private fun checkSelector() {
            val next = try {
                Selector.open()
            } catch (e: IOException) {
                e.printStackTrace()
                return
            }
            for (key in selector.keys()) {
                if (!key.isValid) {
                    key.cancel()
                    continue
                }
                val attach = key.attachment()
                val ops = key.interestOps()
                val channel = key.channel()
                try {
                    val k = channel.register(next, ops, attach)
                    if (attach is TCPChannel) {
                        attach.key = k
                    }
                } catch (e: ClosedChannelException) {
                    e.printStackTrace()
                    if (attach is TCPChannel) {
                        attach.notifyRead()
                    }
                } finally {
                    key.cancel()
                }
            }
            try {
                selector.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
            selector = next
        }

        private fun select() {
            var count = 0
            while (true) {
                val n: Int = try {
                    selector.select()
                } catch (e: CancelledKeyException) {
                    0
                }
                if (n <= 0) {
                    if (isRegistering) {
                        isRegistering = false
                        Thread.sleep(500)
                    }
                    count++
                    if (count > 20) {
                        synchronized(TCPSelector::class.java) {
                            checkSelector()
                        }
                        count = 0
                    }
                    continue
                }
                count = 0

                val iterator = selector.selectedKeys().iterator()
                while (iterator.hasNext()) {
                    val key = iterator.next()
                    try {
                        if (!key.isValid) {
                            continue
                        }
                        if (key.isReadable) {
                            val obj = key.attachment()
                            if (obj is TCPChannel) {
                                obj.notifyRead()
                            }
                        }
                    } catch (e: CancelledKeyException) {
                        key.cancel()
                    } finally {
                        iterator.remove()
                    }
                }
            }
        }

        override fun register(channel: TCPChannel) {
            try {
                isRegistering = true
                selector.wakeup()
                channel.register(selector)
            } catch (e: ClosedChannelException) {
                TcpMainUtil.error("SelectorTcpRegister register error=" + Log.getStackTraceString(e))
            }
        }

        init {
            Thread(this).start()
        }
    }

    private class NormalTcpRegister(private val executor: ExecutorService) :
        ITCPRegister {
        override fun register(channel: TCPChannel) {
            executor.submit {
                while (channel.isConnected()) {
                    channel.notifyRead()
                }
            }
        }
    }
}