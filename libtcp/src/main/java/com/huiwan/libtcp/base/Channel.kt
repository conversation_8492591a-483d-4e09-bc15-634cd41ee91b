package com.huiwan.libtcp.base

import android.util.Log
import com.huiwan.base.BufferFactory
import com.huiwan.base.BufferPool
import com.huiwan.libtcp.config.TCPConfig.PACKET_HEAD_LEN
import com.wepie.liblog.main.HLog
import java.io.IOException
import java.net.ConnectException
import java.net.Socket
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.channels.SelectionKey
import java.nio.channels.Selector
import java.nio.channels.SocketChannel
import java.util.*
import java.util.concurrent.LinkedBlockingDeque
import java.util.concurrent.TimeUnit

interface Channel {

    fun isConnected(): Boolean

    fun write(buffer: ByteBuffer): Boolean

    fun read(): ByteBuffer?

    fun hasNext(): Boolean

    fun release(contentBuffer: ByteBuffer?)
}

interface IStateCallback<T : Channel> {
    fun onChannelRead(channel: T)

    fun onChannelDisConnect()
}

abstract class BaseChannel : Channel {
    protected val headBuff = ByteArray(PACKET_HEAD_LEN)

    override fun release(contentBuffer: ByteBuffer?) {
        contentBuffer?.let {
            bufferPool.release(it)
        }
    }

    companion object {

        private const val POOL_SIZE = 10

        internal val bufferPool: BufferPool<ByteBuffer> =
            BufferPool(POOL_SIZE, object : BufferFactory<ByteBuffer> {
                override fun create(size: Int): ByteBuffer = ByteBuffer.allocate(size)

                override fun capacity(t: ByteBuffer): Int = t.capacity()

                override fun reset(t: ByteBuffer) {
//                    t.compact()
                    t.clear()
                }

            })
    }
}

class TCPChannel constructor(private val channel: SocketChannel) : BaseChannel() {
    var callback: IStateCallback<TCPChannel>? = null

    var key: SelectionKey? = null

    override fun isConnected(): Boolean = channel.isConnected

    fun notifyRead() {
        if (!channel.isOpen) {
            callback?.onChannelDisConnect()
        } else {
            callback?.onChannelRead(this)
        }
    }

    fun register(selector: Selector): SelectionKey? {
        channel.configureBlocking(false)
        val key: SelectionKey
        try {
            key = channel.register(selector, SelectionKey.OP_READ, this)
            this.key = key
        } catch (e: Exception) {
            HLog.e("TCPChannel", "register=" + Log.getStackTraceString(e))
            return null
        }
        return key
    }

    override fun write(buffer: ByteBuffer): Boolean {
        while (buffer.hasRemaining()) {
            if (!isConnected()) {
                return false
            }
            channel.write(buffer)
        }
        return true
    }

    @Throws(ConnectException::class)
    private fun doRead(buffer: ByteBuffer, size: Int): Boolean {
        while (buffer.hasRemaining()) {
            if (!channel.isOpen) {
                return false
            }
            val count = channel.read(buffer)
            if (count == -1) {
                throw ConnectException("read time out")
            }
        }
        return buffer.position() == size
    }

    @Throws(ConnectException::class)
    override fun read(): ByteBuffer? {
        val buff = ByteBuffer.wrap(headBuff)
        var ret = doRead(buff, PACKET_HEAD_LEN)
        if (!ret) {
            return null
        }
        buff.order(ByteOrder.LITTLE_ENDIAN)
        buff.flip()
        val bodyLen = buff.int
        if (bodyLen <= 0) {
            return null
        }
        val contentBuffer = bufferPool.acquire(bodyLen)
        contentBuffer.clear()
        contentBuffer.limit(bodyLen)
        ret = doRead(contentBuffer, bodyLen)
        if (!ret) {
            contentBuffer.compact()
            return null
        }
        return contentBuffer
    }

    override fun hasNext(): Boolean = false

    fun close() {
        if (isConnected()) {
            key?.cancel()
            channel.close()
            callback?.onChannelDisConnect()
        }
        callback = null
    }

    fun getSocket(): Socket = channel.socket()

    override fun toString(): String {
        return "{isConnect:${channel.isConnected},isOpen:${channel.isOpen}}"
    }
}

class KCPChannel constructor(
    internal val conn: KcpClient.Connection,
    private val thread: HWKCPSocketThread
) : BaseChannel(),
    KcpClient.IReadCallback {

    private val linkedBlockingDeque = LinkedBlockingDeque<ByteBuffer>()

    init {
        conn.setCallback(this)
    }

    override fun isConnected(): Boolean = conn.isConnect

    override fun write(buffer: ByteBuffer): Boolean {
        val ret = conn.write(buffer.array(), buffer.position(), buffer.remaining())
        if (!ret) {
            thread.onWriteFailed()
        }
        return ret
    }

    override fun read(): ByteBuffer? {
        if (linkedBlockingDeque.isEmpty()) {
            return null
        }
        if (!doReadBuffer(headBuff)) {
            throw IOException("can not get head")
        }
        val headBuffer = ByteBuffer.wrap(headBuff)
        headBuffer.order(ByteOrder.LITTLE_ENDIAN)
        val bodyLen = headBuffer.int
        if (bodyLen <= 8 || bodyLen > 10 * 1024 * 1024) {
            throw IOException("get head error bodyLen:$bodyLen")
        }
        val bodyBuffer = bufferPool.acquire(bodyLen)
        bodyBuffer.limit(bodyLen)
        if (doReadBuffer(bodyBuffer)) {
            bodyBuffer.flip()
        } else {
            bufferPool.release(bodyBuffer)
            throw IOException("get head error bodyLen:$bodyLen")
        }
        return bodyBuffer
    }

    private fun doReadBuffer(byteBuffer: ByteBuffer): Boolean {
        var temp: ByteBuffer?
        while (byteBuffer.hasRemaining()) {
            temp = linkedBlockingDeque.poll(10, TimeUnit.MINUTES)
            if (temp == null) {
                return false
            }
            if (temp.remaining() > byteBuffer.remaining()) {
                while (byteBuffer.hasRemaining()) {
                    byteBuffer.put(temp.get())
                }
                linkedBlockingDeque.addFirst(temp)
            } else {
                byteBuffer.put(temp)
                bufferPool.release(temp)
            }
        }
        return true
    }

    private fun doReadBuffer(byteArray: ByteArray): Boolean {
        return doReadBuffer(ByteBuffer.wrap(byteArray))
    }

    override fun hasNext(): Boolean = linkedBlockingDeque.isNotEmpty()

    override fun onDataReach(connection: KcpClient.Connection, buffer: ByteBuffer, size: Int) {
        val contentBuffer = bufferPool.acquire(size)
        contentBuffer.clear()
        contentBuffer.put(buffer)
        contentBuffer.flip()
        linkedBlockingDeque.addLast(contentBuffer)
        thread.onChannelRead(this)
    }

    override fun onDisconnect(connection: KcpClient.Connection) {
        thread.onChannelDisConnect()
    }
}