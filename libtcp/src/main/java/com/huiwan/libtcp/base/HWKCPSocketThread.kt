package com.huiwan.libtcp.base

import com.huiwan.base.common.GlobalConfig
import com.huiwan.base.util.TextUtil
import com.huiwan.libtcp.TcpMainUtil
import com.welib.alinetlog.AliNetLogUtil
import org.json.JSONObject
import java.net.ConnectException
import java.net.InetAddress

abstract class HWKCPSocketThread(
    tag: String,
    configItem: GlobalConfig.ConfigItem
) : BaseDisposer<KCPChannel>(tag, configItem) {

    private var kcpConnection: KcpClient.Connection? = null

    private var cacheIp: String? = null

    override fun start() {
        socketStatus = SOCKET_CONNECTING
        TCPThreadPool.get().execute {
            socketConnect()
        }
    }

    private fun getIp(): String? {
        try {
            val address = InetAddress.getByName(configItem.host)
            cacheIp = address.hostAddress
        } catch (e: Exception) {
            //ignore
        }
        return cacheIp
    }

    private fun socketConnect() {
        val conn: KcpClient.Connection? = try {
            val ip = configItem.ip ?: getIp()
            if (TextUtil.isEmpty(ip)) {
                null
            } else {
                client?.connect(ip, configItem.port)
            }
        } catch (e: ConnectException) {
            TcpMainUtil.upload(AliNetLogUtil.PORT.kcp, AliNetLogUtil.TYPE.err, e.toString())
            null
        }
        if (conn == null) {
            socketStatus = SOCKET_UN_CONNECTED
            onConnectFailed(configItem.host, configItem.port)
            return
        }
        kcpConnection = conn
        channel = KCPChannel(conn, this@HWKCPSocketThread)
        socketStatus = SOCKET_CONNECTED
        onConnected()
    }

    override fun socketStatusMsg(status: JSONObject) {
        val obj = JSONObject()
        try {
            obj.put("socket ", channel)
            obj.put("host", getHostMsg())
            obj.put("socketStatus", socketStatus)
            val address: InetAddress? = InetAddress.getByName(configItem.host)
            if (null != address) {
                try {
                    obj.put("reachable", address.isReachable(10 * 1000))
                } catch (e: Exception) {
                    obj.put("reachable error", e.toString())
                }
            }
        } catch (e: Exception) {
        }
        status.put("status", obj)
    }

    override fun disconnect() {
        channel?.conn?.unConnect()
        channel = null
        onDisconnected()
    }

    open fun onWriteFailed() {

    }

    open fun onConnectFailed(host: String, port: Int) {

    }

    override fun onChannelDisConnect() {
        channel?.conn?.unConnect()
        channel = null
        onDisconnected()
        socketConnect()
    }

    override fun handleError(e: Error) {
        TcpMainUtil.upload(AliNetLogUtil.PORT.kcp, AliNetLogUtil.TYPE.err, e.toString())
        reConnect()
    }

    override fun handleSocketIOException(e: Exception) {
        TcpMainUtil.upload(AliNetLogUtil.PORT.kcp, AliNetLogUtil.TYPE.err, e.toString())
        reConnect()
    }

    private fun reConnect() {
        channel?.conn?.unConnect()
        channel = null
        onDisconnected()
        socketConnect()
    }

    companion object {
        private var client = KcpClient.create()
    }
}