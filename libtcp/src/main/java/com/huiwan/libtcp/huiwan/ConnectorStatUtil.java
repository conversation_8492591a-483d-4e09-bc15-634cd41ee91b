package com.huiwan.libtcp.huiwan;

import static com.welib.alinetlog.AliNetLogUtil.PORT.netStat;

import com.huiwan.base.LibBaseUtil;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.net.tcp.packet.ConnectorPackets;
import com.wepie.wespy.net.tcp.packet.HeadPackets;

import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class ConnectorStatUtil {
    private static final String TAG = "ConnectorStatUtil";
    private static final int MAX_LOG_NUM = 30;
    private final long[] reqTimeArr = new long[MAX_LOG_NUM];
    private static final AtomicInteger reqTimes = new AtomicInteger(0);

    public static final int PROTOCOL_TCP = 1;
    public static final int PROTOCOL_KCP = 2;

    private int protocolType = 0;

    public static void logConnTime(long latency, boolean success) {
        HashMap<String, String> map = new HashMap<>();
        map.put("req_time_avg", String.valueOf(latency));
        map.put("req_failed", String.valueOf(success ? 0 : 1));
        map.put("req_success", String.valueOf(success ? 1 : 0));
        HLog.aliLog(netStat, AliNetLogUtil.TYPE.normal, "connector_establish", map);

        if (LibBaseUtil.buildDebug()) {
            HLog.d(TAG, "connector_establish success=" + success + ", spent=" + latency);
        }
    }

    public void logTime(long latencyMs, int seq, int command, int type, int code, int protocolType) {
        if (filterHeartBeat(command, type)) {
            return;
        }
        synchronized (ConnectorStatUtil.class) {
            int reqTime = reqTimes.getAndIncrement();
            reqTimeArr[reqTime] = latencyMs;
            if (this.protocolType == 0 || this.protocolType == protocolType) {
                if (reqTimes.get() >= MAX_LOG_NUM) {
                    logData(protocolType);
                    reqTimes.set(0);
                }
            } else {
                logData(protocolType);
                reqTimes.set(0);
            }
            this.protocolType = protocolType;
        }
        if (LibBaseUtil.buildDebug()) {
            HLog.d(TAG, "[" + seq + "," + command + "," + type + "] code=" + code + " --- spent " + latencyMs);
        }
    }

    /**
     * 过滤心跳包打点
     *
     * @param command
     * @param type
     * @return
     */
    private boolean filterHeartBeat(int command, int type) {
        //Connect 心跳
        if (command == HeadPackets.CommandType.CONNECTOR_VALUE && type == ConnectorPackets.ConnectorType.HEARTBEAT_VALUE) {
            return true;
        }

        return false;
    }

    private void logData(int protocolType) {
        int okTimes = 0;
        int failedTimes = 0;
        long sum = 0;
        long avg = -1;
        long dx = -1;
        long maxTime = 0;
        for (int i = 0; i < MAX_LOG_NUM; i++) {
            long time = reqTimeArr[i];
            if (time > 0) {
                okTimes++;
                sum += time;
                if (maxTime < time) {
                    maxTime = time;
                }
            } else {
                failedTimes++;
            }
        }
        if (okTimes > 0) {
            avg = (long) (sum / (float) okTimes);
            long ds = 0;
            for (int i = 0; i < MAX_LOG_NUM; i++) {
                long time = reqTimeArr[i];
                if (time > 0) {
                    long div = reqTimeArr[i] - avg;
                    ds += div * div;
                }
            }
            dx = ds / okTimes;
        }
        HLog.d(TAG, "tcp stat failedTimes={}, okTimes={} avg={}, max={}, dx={}, arr={}", failedTimes, okTimes, avg, maxTime, dx, Arrays.toString(reqTimeArr));
        HashMap<String, String> map = new HashMap<>();
        map.put("req_failed", String.valueOf(failedTimes));
        map.put("req_success", String.valueOf(okTimes));
        map.put("req_time_avg", String.valueOf(avg));
        map.put("req_time_max", String.valueOf(maxTime));
        map.put("req_time_dx", String.valueOf(dx));
        map.put("req_arr", Arrays.toString(reqTimeArr));
        map.put("req_protocol", getProtocolStr(protocolType));

        HLog.aliLog(netStat, AliNetLogUtil.TYPE.normal, "connector_stat", map);
    }

    private static String getProtocolStr(int type) {
        switch (type) {
            case PROTOCOL_KCP:
                return "KCP";
            case PROTOCOL_TCP:
            default:
                return "TCP";
        }
    }

}
