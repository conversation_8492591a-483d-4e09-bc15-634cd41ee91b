package com.huiwan.libtcp.zip

import androidx.collection.ArrayMap
import com.welib.alinetlog.AliNetLogUtil
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog

/**
 * 解压上报：
 *   1. 开启和不开启压缩情况下，http/tcp 整体耗时监控
 *   2. 开启和不开启压缩情况下，流量消耗监控
 *   3. 解压耗时监控
 *   4. 解压失败监控
 * <AUTHOR>
 * @since 2023/2/8 16:39
 */
object TcpZipReport {

    private const val tag = "TcpReport"

    // size 统计：记录 100 次上报一次
    const val MAX_SIZE_TIMES_UPLOAD = 200

    // 当前统计次数
    @Volatile
    var curSizeTimes = 0

    // 当前总共 size
    @Volatile
    var curTotalSize = 0L

    // 解压耗时大于 20 ms 则直接进行上报
    const val MAX_SPEND_TIME_UPLOAD = 20

    /**
     * 解压失败统计
     */
    fun zipErrorReport(error: String?, from: String, zip: String) {
        val map: MutableMap<String, String> = HashMap()
        map["name"] = "ZipError"
        map["errmsg"] = error ?: ""
        map["from"] = from
        map["req_zip"] = zip
        map["type"] = "tcp"
        debugLog("error:{} map={}", error ?: "", map)
        FLog.e(IllegalStateException(error))
        FLog.d(error)
        HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map)
    }


    /**
     * 解压耗时统计: 30 次上报一次
     */
    fun zipSuccessReport(seq: String, from: String, zip: String, spendTime: Long) {
        // 大于峰值直接上报
        if (spendTime > MAX_SPEND_TIME_UPLOAD) {
            val map: MutableMap<String, String> = ArrayMap()
            map["name"] = "ZipLong"
            map["type"] = "tcp"
            map["from"] = from
            map["req_zip"] = zip
            map["duration"] = spendTime.toString()
            HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "", map)
            debugLog("unzip long seq={} map={}", seq, map)
        }
    }

    private fun debugLog(format: String, vararg obj: Any) {
        HLog.d(tag, format, *obj)
    }

}