//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/29.
//

#include "KcpClient.h"
#include "utils.hpp"
#include <errno.h>
#include <sys/socket.h>
#include <fcntl.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>
#include <netdb.h>

static pthread_mutex_t client_lock = PTHREAD_RECURSIVE_MUTEX_INITIALIZER_NP;

static int is_run = 0;

KCNode *getKCNode(KClient *client) {
    struct IQUEUEHEAD *head = &client->queue_buff;
    if (IQUEUE_IS_EMPTY(head)) {
        return (KCNode *) malloc(sizeof(KClient));
    }
    KCNode *k_node = IQUEUE_ENTRY(head->next, KCNode, node);
    IQUEUE_DEL(&k_node->node);
    return k_node;
}

void releaseKCNode(KClient *client, KCNode *k_node) {
    struct IQUEUEHEAD *head = &client->queue_buff;
    IQUEUE_ADD(&k_node->node, head);
}

void add_un_connect(struct IQUEUEHEAD *head, KConn *conn) {
    struct IQUEUEHEAD *pos;
    KCNode *k_node;
    for (pos = head->next; pos != head; pos = pos->next) {
        k_node = IQUEUE_ENTRY(pos, KCNode, node);
        if (k_node->conn == conn) {
            return;
        }
    }
    k_node = malloc(sizeof(KCNode));
    k_node->conn = conn;
    k_node->state = -1;
    IQUEUE_ADD_TAIL(&k_node->node, head);
}

void release_all_connect(struct IQUEUEHEAD *head) {
    if (IQUEUE_IS_EMPTY(head)) {
        return;
    }
    LOGD("[release_all_connect]");
    KCNode *k_node;
    do {
        k_node = IQUEUE_ENTRY(head->next, KCNode, node);
        release_connect(k_node->conn);
        IQUEUE_DEL(&k_node->node);
    } while (!IQUEUE_IS_EMPTY(head));
}

void notify_kcp_state(KClient *client, KConn *conn, int state) {
    KCNode *tmp_node = getKCNode(client);
    tmp_node->conn = conn;
    tmp_node->state = state;
    IQUEUE_ADD_TAIL(&tmp_node->node, &client->recv_queue);
    pthread_cond_signal(&client->cond);
}

int on_kcp_data_reach(KSelect *select, void *user) {
    KConn *conn = user;
    return read_udp(conn);
}

int on_select(KSelect *select, int fd, int event, void *user) {
    KClient *client = select->user;
    if (fd <= 0) {
        update_client(client);
        return KCP_INTERVAL;
    }
    int state = 0;
    if (event & K_POLLIN) {
        state = STATE_HAS_DATA;
    } else if (event & K_POLLHUP || event & K_POLLNVAL) {
        state = STATE_HANG_UP;
        LOGD("[on_select] fd:%d event:%d conn:%p", fd, event, user);
    }
    if (state != 0) {
        KConn *conn = user;
        lock(&client_lock);
        notify_kcp_state(client, conn, state);
        unlock(&client_lock);
    }
    return (int) check_client(client, iclock());
}

/**
 * 创建Client
 * @param port udp端口
 * @return
 */
KClient *create_client() {
    KSelect *select = start_select();
    lock(&client_lock);
    KClient *client = (KClient *) malloc(sizeof(KClient));
    client->user = NULL;
    client->on_recv = NULL;
    select->on_event = on_select;
    select->on_data_reach = on_kcp_data_reach;
    select->user = client;
    client->select = select;
    atomic_store(&client->state, 1);
    IQUEUE_INIT(&client->recv_queue);
    IQUEUE_INIT(&client->queue_buff);
    is_run = 1;
    pthread_cond_init(&client->cond, NULL);
    unlock(&client_lock);
    return client;
}

/**
 * 释放Client
 * @param client
 */
void release_client(KClient *client) {
    lock(&client_lock);
    is_run = 0;
    KCNode *k_node;
    struct IQUEUEHEAD *head = &client->queue_buff;
    while (!IQUEUE_IS_EMPTY(head)) {
        k_node = IQUEUE_ENTRY(head->next, KCNode, node);
        IQUEUE_DEL(&k_node->node);
        free(k_node);
    }
    head = &client->recv_queue;
    while (!IQUEUE_IS_EMPTY(head)) {
        k_node = IQUEUE_ENTRY(head->next, KCNode, node);
        IQUEUE_DEL(&k_node->node);
        free(k_node);
    }
    if (client->select != NULL) {
        KEvent *event;
        kselect_stop_start(event, client->select);
            KConn *conn = event->user;
            release_connect(conn);
        kselect_stop_end(event, client->select);
        free_ptr(client->select);
        client->select = NULL;
    }

    client->buff = NULL;
    client->buff_size = 0;
    client->on_recv = NULL;
    free_ptr(client->user);
    client->user = NULL;
    atomic_store(&client->state, 0);
    pthread_cond_destroy(&client->cond);
    free_ptr(client);
    unlock(&client_lock);
}

void start_client(KClient *client) {
    KCNode *k_node = NULL;
    KConn *conn = NULL;
    struct IQUEUEHEAD un_conn_head;
    IQUEUE_INIT(&un_conn_head);
    while (is_run) {
        lock(&client_lock);
        struct IQUEUEHEAD *head = &client->recv_queue;
        if (IQUEUE_IS_EMPTY(head)) {
            release_all_connect(&un_conn_head);
            pthread_cond_wait(&client->cond, &client_lock);
        }
        if (IQUEUE_IS_EMPTY(head)) {
            unlock(&client_lock);
            break;
        }
        k_node = IQUEUE_ENTRY(head->next, KCNode, node);
        conn = k_node->conn;
        int state = k_node->state;
        IQUEUE_DEL(&k_node->node);
        releaseKCNode(client, k_node);
        if (conn == NULL) {
            unlock(&client_lock);
            continue;
        }
        int is_conn = is_connect(conn) == 0;
        unlock(&client_lock);
        if (state == STATE_DISCONNECT) {
            add_un_connect(&un_conn_head, conn);
            continue;
        }
        if (state == STATE_HANG_UP) {
            lock(&client_lock);
            if (client->select != NULL) {
                unregister_select(client->select, conn->fd);
            }
            unlock(&client_lock);
            KResult result = {0};
            result.conn = conn;
            result.kcp_state = KCP_DISCONNECT;
            result.size = 0;
            client->on_recv(client, result, client->user);
            continue;
        }
        if (!is_conn) {
            continue;
        }
        int kcp_state = STATE_HAS_DATA;
        int size = 0;
        if (state == STATE_HAS_DATA) {
            size = read_kcp(conn, client->buff, client->buff_size);
            if (size == KCP_WRITE_FAIL) {
                kcp_state = KCP_WRITE_FAIL;
                size = -1;
            }
        }
        if (client->on_recv != NULL && !(kcp_state == KCP_HAS_DATA && size <= 0)) {
            KResult result = {0};
            result.conn = conn;
            result.kcp_state = kcp_state;
            result.size = size;
            client->on_recv(client, result, client->user);
        }
    }
}

int is_client_running(KClient *client) {
    if (client == NULL || atomic_load(&client->state) <= 0) {
        return 0;
    }
    lock(&client_lock);
    KSelect *select = client->select;
    unlock(&client_lock);
    if (select == NULL) {
        return 0;
    }
    return 1;
}

IUINT32 check_client(KClient *client, IUINT32 current) {
    if (!is_client_running(client)) {
        return current + 1000;
    }
    long next_clock = current + KCP_INTERVAL * 2;
    lock(&client_lock);
    KEvent *event;
    kselect_foreach_start(event, client->select) {
        KConn *conn = event->user;
        IUINT32 check_clock = check_kcp(conn, current);
        if (check_clock < next_clock) {
            next_clock = check_clock;
        }
    }
    kselect_foreach_end();
    unlock(&client_lock);
    if (next_clock <= current) {
        return 1;
    }
    return next_clock - current;
}

void update_client(KClient *client) {
    if (!is_client_running(client) || client->select == NULL) {
        return;
    }
    KEvent *event;
    lock(&client_lock);
    kselect_foreach_start(event, client->select) {
        KConn *conn = event->user;
        if (update_connect(conn) > 0) {
            notify_kcp_state(client, conn, STATE_HAS_DATA);
        }
    }
    kselect_foreach_end();
    unlock(&client_lock);
}

int add_to_client(KClient *client, KConn *conn) {
    int ret = -1;
    lock(&client_lock);
    if (client->select != NULL) {
        ret = register_select(client->select, conn->fd, K_POLLIN | K_POLLHUP | K_POLLERR,
                              conn);
    }
    unlock(&client_lock);
    return ret;
}

int remove_from_client(KClient *client, KConn *conn) {
    lock(&client_lock);
    if (client->select != NULL) {
        LOGD("[remove_from_client] unregister_select fd:%d conn:%p", conn->fd, conn);
        unregister_select(client->select, conn->fd);
    }
    un_connect(conn);
    notify_kcp_state(client, conn, STATE_DISCONNECT);
    unlock(&client_lock);
    return 0;
}

int write_conn(KClient *client, KConn *connect, char *bytes, int len) {
    if (!is_client_running(client)) {
        return -1;
    }
    lock(&client_lock);
    int ret = write_kcp(connect, bytes, len);
    unlock(&client_lock);
    return ret;
}