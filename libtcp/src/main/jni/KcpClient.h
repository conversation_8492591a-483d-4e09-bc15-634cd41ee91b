//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/29.
//

#ifndef KCP_CLIENT_H
#define KCP_CLIENT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <arpa/inet.h>
#include "ikcp.h"
#include "KcpSelect.h"
#include "KcpConnect.h"

#define STATE_HAS_DATA KCP_HAS_DATA
#define STATE_DISCONNECT KCP_DISCONNECT
#define STATE_HANG_UP -8

typedef struct KcpResult {
    struct KcpConnect *conn;
    int size;
    int kcp_state;
} KResult;

typedef struct KcpConnectNode {
    struct IQUEUEHEAD node;
    KConn *conn;
    int state;
} KCNode;

typedef struct KcpClient {
    atomic_int state;

    KSelect *select;

    char *buff;
    long buff_size;

    pthread_cond_t cond;

    void *user;

    struct IQUEUEHEAD recv_queue, queue_buff;

    int (*on_recv)(struct KcpClient *client, KResult result, void *user);
} KClient;

KClient *create_client();

void release_client(KClient *client);

void start_client(KClient *client);

int is_client_running(KClient *client);

IUINT32 check_client(KClient *client, IUINT32 current);

void update_client(KClient *client);

int add_to_client(KClient *client, KConn *connect);

int remove_from_client(KClient *client, KConn *connect);

int write_conn(KClient *client, KConn *connect, char *bytes, int len);

#ifdef __cplusplus
}
#endif

#endif //KCP_CLIENT_H
