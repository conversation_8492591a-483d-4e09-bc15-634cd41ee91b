package com.huiwan.module.webview.web;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.module.webview.SimpleWebViewInterface;
import com.huiwan.module.webview.WebViewBridgeInterface;
import com.huiwan.module.webview.WespyWebView;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.webview.WPWebView;
import com.wepie.webview.WPWebViewClient;

import org.json.JSONObject;


/**
 * date 2020/6/29
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class WebDialogView extends FrameLayout {

    private static final int DEFAULT_HEIGHT = 445;
    private final FrameLayout contentLay;
    private final ImageView loadingIv;
    private WespyWebView webView;
    private String cacheUrl = "";
    private boolean contentShowing = false;
    private boolean webLoaded = false;
    private int h = ScreenUtil.dip2px(DEFAULT_HEIGHT);
    private androidx.core.view.OnApplyWindowInsetsListener listener;
    private static final String TAG = "WebDialogView";

    public WebDialogView(@NonNull Context context) {
        this(context, null);
    }

    public WebDialogView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        loadingIv = new ImageView(getContext());
        contentLay = new FrameLayout(context);
        resetNewWebView();
        contentLay.addView(loadingIv, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        LayoutParams contentLp = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        contentLp.gravity = Gravity.BOTTOM;
        addView(contentLay, contentLp);
        setBackgroundColor(0x44000000);
        setVisibility(INVISIBLE);
        loadingIv.setBackgroundColor(0xcc000000);
    }

    public void cacheUrlWithHeight(String url, WebDialogDelegate delegate, int height) {
        if (height >= ScreenUtil.getScreenHeight()) {
            this.h = ViewGroup.LayoutParams.MATCH_PARENT;
        } else {
            this.h = height;
        }
        loadingIv.setVisibility(GONE); //loadingIv加载会导致闪屏，设置webDialog高度的方法中将其隐藏
        cacheUrl(url, delegate);
    }

    public void cacheUrl(String url, WebDialogDelegate delegate) {
        Activity activity = ContextUtil.getActivityFromContext(getContext());
        if (activity == null) {
            return;
        }
        if (!TextUtils.isEmpty(url) && url.equals(cacheUrl)) {
            return;
        }
        if (!TextUtils.isEmpty(cacheUrl)) {
            resetNewWebView();
        }
        this.cacheUrl = url;
        loadingIv.setScaleType(ImageView.ScaleType.FIT_XY);
        updateWebViewWithUrl(webView, url, new WebDialogDelegateWrapper(delegate) {
            @Override
            public void onCloseWindow() {
                super.onCloseWindow();
                setVisibility(INVISIBLE);
                contentShowing = false;
                loadingIv.setAlpha(1.0f);
            }

            @Override
            public void onSendGift(JSONObject jsonObject) {
                super.onSendGift(jsonObject);
                setVisibility(INVISIBLE);
            }
        });

        webView.initWebView(url);
        WebViewBridgeInterface.registerStarBox(webView, delegate);
        webView.setWebViewInterface(new SimpleWebViewInterface() {
            @Override
            public void onPageFinished(WPWebView view, String url) {
                super.onPageFinished(view, url);
                webLoaded = true;
                ViewExKt.postAutoCancel(webView, () -> checkShowWebFromAnim(), 200);
                delegate.onPageViewFinished();
            }

            @Override
            public void onPageStarted(WPWebView view, String url, Bitmap favicon) {
                delegate.onPageViewStarted();
            }
        });
    }

    private void updateWebViewWithUrl(WespyWebView wespyWebView, String url, WebDialogDelegate delegate) {
        boolean useX5 = false;
        try {
            Uri uri = Uri.parse(url);
            String height = uri.getQueryParameter("height");
            boolean adjustResize = "1".equalsIgnoreCase(uri.getQueryParameter("adjustResize"));
            String use_x5 = uri.getQueryParameter("use_x5");
            if (height != null && height.length() > 0) {
                h = ScreenUtil.dip2px(Integer.parseInt(height));
            }
            useX5 = "true".equals(use_x5);
            updateGlobalListener(adjustResize);
        } catch (Exception e) {
            TimeLogger.msgNoStack("error parse url " + e.getMessage());
        }
        wespyWebView.initCore(useX5);
        updateLp(webView, h);
        updateLp(loadingIv, h);
        setOnClickListener(v -> delegate.onCloseWindow());
        String loadingUrl = delegate.getWebLoadingUrl();
        WpImageLoader.load(loadingUrl, loadingIv);
    }

    private void updateLp(View view, int height) {
        LayoutParams lp = (LayoutParams) view.getLayoutParams();
        lp.gravity = Gravity.BOTTOM;
        lp.height = height;
        view.requestLayout();
    }

    private void updateGlobalListener(boolean adjustResize) {
        if (listener != null) {
            ViewCompat.setAccessibilityDelegate(this, null);
            listener = null;
        }
        if (!adjustResize) {
            return;
        }
        listener = new androidx.core.view.OnApplyWindowInsetsListener() {
            final int paddingBottom = WebDialogView.this.getPaddingBottom();

            @NonNull
            @Override
            public WindowInsetsCompat onApplyWindowInsets(@NonNull View v, @NonNull WindowInsetsCompat insets) {
                Insets systemWindow = insets.getInsets(WindowInsetsCompat.Type.ime() |
                        WindowInsetsCompat.Type.displayCutout());
                WebDialogView.this.setPaddingRelative(WebDialogView.this.getPaddingStart(), v.getPaddingTop(), v.getPaddingEnd(), paddingBottom + systemWindow.bottom);
                return insets;
            }
        };
        ViewCompat.setOnApplyWindowInsetsListener(this, listener);
    }

    private void resetNewWebView() {
        if (webView != null) {
            webView.releaseRes();
            webView.destroy();
        }
        webLoaded = false;
        webView = new WespyWebView(getContext());
        contentLay.addView(webView, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
    }

    public void show() {
        setVisibility(VISIBLE);
        contentLay.setTranslationY(h);
        contentLay.animate().translationY(0).setDuration(200).setInterpolator(new AccelerateDecelerateInterpolator()).start();
        ViewExKt.postAutoCancel(contentLay, () -> {
            contentShowing = true;
            checkShowWebFromAnim();
        }, 300);
    }

    public void hideBackground() {
        setBackgroundColor(Color.TRANSPARENT); //hide current view background
        webView.hideBackground();       //hide webView background
    }

    public void close() {
        setVisibility(INVISIBLE);
        contentLay.clearAnimation();
    }

    public void goBack() {
        if (webView != null && webView.canGoBack()) {
            webView.goBack();
        }
    }

    public boolean canGoBack() {
        if (webView != null) {
            return webView.canGoBack();
        }
        return false;
    }

    public String getCurrentUrl() {
        String currentUrl = "";
        if (webView != null) {
            currentUrl = webView.getUrl();
        }
        return currentUrl;
    }

    public void loadUrl(String url) {
        webView.visitUrl(url, false);
    }

    public void setYoutubeWebViewClient(WPWebViewClient client, DataCallback<String> callback) {
        webView.setYoutubeWebViewClient(client, callback);
    }

    private void checkShowWebFromAnim() {
        if (contentShowing && webLoaded) {
            loadingIv.setAlpha(1.0f);
            loadingIv.animate().alpha(0).setDuration(300).start();
        }
    }

    public void destroy() {
        webView.releaseRes();
        webView.destroy();
    }
}
