package com.huiwan.platform;

import java.util.ServiceLoader;

class HwHolder {
    private static volatile Hw platform;

    static void set(Hw platform) {
        HwHolder.platform = platform;
    }

    static Hw get() {
        if (platform == null) {
            synchronized (HwHolder.class) {
                if (platform == null) {
                    ServiceLoader<Hw> loader = ServiceLoader.load(Hw.class);
                    for (Hw hw : loader) {
                        platform = hw;
                        Hw.set(hw);
                    }
                }
            }
        }
        return platform;
    }
}
