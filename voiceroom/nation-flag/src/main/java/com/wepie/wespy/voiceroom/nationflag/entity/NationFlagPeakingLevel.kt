package com.wepie.wespy.voiceroom.nationflag.entity

import com.wepie.wespy.net.tcp.packet.NationFlagPacket

class NationFlagPeakingLevel(val nationId: String, val value: Int) {

    companion object {
//        const val LEVEL_0 = NationFlagPacket.PeakingLevel.PeakingLevel0_VALUE
//        const val LEVEL_1 = NationFlagPacket.PeakingLevel.PeakingLevel1_VALUE
//        const val LEVEL_2 = NationFlagPacket.PeakingLevel.PeakingLevel2_VALUE
//        const val LEVEL_3 = NationFlagPacket.PeakingLevel.PeakingLevel3_VALUE
//        const val LEVEL_4 = NationFlagPacket.PeakingLevel.PeakingLevel4_VALUE

        fun parse(d: NationFlagPacket.PeakingItemOrBuilder): NationFlagPeakingLevel {
            return NationFlagPeakingLevel(d.nationId, d.peakingLevelValue)
        }

        fun parseList(list: List<NationFlagPacket.PeakingItemOrBuilder>): List<NationFlagPeakingLevel> {
            if (list.isNullOrEmpty()) {
                return emptyList()
            }
            val l = ArrayList<NationFlagPeakingLevel>(list.size)
            list.forEach {
                l.add(parse(it))
            }
            return l
        }
    }
}