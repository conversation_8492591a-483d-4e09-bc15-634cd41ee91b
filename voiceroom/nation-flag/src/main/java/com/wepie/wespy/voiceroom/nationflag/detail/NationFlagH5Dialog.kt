package com.wepie.wespy.voiceroom.nationflag.detail

import android.content.Context
import com.huiwan.base.util.ScreenUtil
import com.huiwan.module.webview.WebViewBridgeInterface
import com.huiwan.module.webview.WespyWebView
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.voiceroom.nationflag.R

class NationFlagH5Dialog(context: Context, val jumpUrl: String) :
    BaseFullScreenDialog(context, R.style.dialog_style_custom) {

    init {
        initView()
    }

    private fun initView() {
        val rootView = layoutInflater.inflate(R.layout.fragment_nation_h5_layout, null)
        setContentView(rootView)
        initBottomDialog()
        setCanceledOnTouchOutside(true)

        val dialogHeight = ScreenUtil.getScreenWidth() * DIALOG_RATION
        rootView.layoutParams.height = dialogHeight.toInt()

        val webView = rootView.findViewById<WespyWebView>(R.id.web_view)
        webView.apply {
            layoutParams.height = dialogHeight.toInt()
            initCore(false)
            hideBackground()
            initWebView(jumpUrl)
            this.webView?.registerHandler("closeWindow") { _, function ->
                dismiss()
                function.onCallBack(WebViewBridgeInterface.generateSuccessJson())
            }
        }
    }

    companion object {
        private const val DIALOG_RATION = 480 * 1.0f / 375
        fun showDialog(context: Context, url: String) {
            val dialog = NationFlagH5Dialog(context, url)
            dialog.show()
        }
    }
}