package com.wepie.wespy.voiceroom.nationflag

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.huiwan.base.lifecycle.SingleLiveData
import com.huiwan.base.util.ToastUtil
import com.wepie.wespy.net.tcp.packet.NationFlagPacket
import com.wepie.wespy.voiceroom.nationflag.api.NationFlagSender
import com.wepie.wespy.voiceroom.nationflag.entity.NationFlagUser
import kotlinx.coroutines.launch

class RecentUserViewModel : ViewModel() {

    var rid: Int = 0

    private val _recentUserListLiveData: MutableLiveData<List<NationFlagUser>> =
        SingleLiveData(emptyList())

    fun init(rid: Int) {
        this.rid = rid
    }

    fun getRecentUserListLiveData(): LiveData<List<NationFlagUser>> = _recentUserListLiveData

    fun getRecentInfo() {
        viewModelScope.launch {
            val rsp = NationFlagSender.getRecentInfo(rid)
            if (rsp.code != 200) {
                ToastUtil.show(rsp.desc)
                return@launch
            }
            (rsp.message as NationFlagPacket.RankingRecentRsp?)?.let {
                viewModelScope.launch {
                    _recentUserListLiveData.value = NationFlagUser.parseList(it.usersList)
                }
            }
        }
    }
}