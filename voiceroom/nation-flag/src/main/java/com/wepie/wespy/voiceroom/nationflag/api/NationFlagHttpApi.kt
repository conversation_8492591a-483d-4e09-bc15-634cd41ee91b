package com.wepie.wespy.voiceroom.nationflag.api

import com.huiwan.user.UserService
import com.three.http.core.HttpUtil
import com.three.http.core.postSuspend
import com.wepie.wespy.voiceroom.nationflag.entity.NationFlagReward

object NationFlagHttpApi {
    private const val NATION_FLAG_REWARDS_LIST = "/game/tmpwidget/nation_flag/rewards_list"

    suspend fun getRewardsInfo(gameId: String) =
        HttpUtil.newBuilder()
            .uri(NATION_FLAG_REWARDS_LIST)
            .addParam("uid", UserService.get().loginUid.toString())
            .addParam("game_id", gameId)
            .build().postSuspend<NationFlagReward>()
}