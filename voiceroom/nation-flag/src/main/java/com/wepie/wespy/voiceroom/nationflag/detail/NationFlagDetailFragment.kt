package com.wepie.wespy.voiceroom.nationflag.detail

import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.huiwan.base.str.ResUtil
import com.huiwan.configservice.ConfigHelper
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.voiceroom.giftgame.VerticalScrollableLinearLayout
import com.wepie.wespy.voiceroom.nationflag.NationFlagState
import com.wepie.wespy.voiceroom.nationflag.NationFlagUtils
import com.wepie.wespy.voiceroom.nationflag.NationFlagViewModel
import com.wepie.wespy.voiceroom.nationflag.R
import com.wepie.wespy.voiceroom.nationflag.progress.NationFlagSendViewHolder
import kotlin.math.abs
import kotlin.math.max

/**
 * 详情
 */
class NationFlagDetailFragment : Fragment() {

    private var viewModel: NationFlagViewModel? = null

    private lateinit var minuteTv: TextView
    private lateinit var secondTv: TextView
    private lateinit var tipsTv: TextView

    private lateinit var sendViewHolder: NationFlagSendViewHolder

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_nation_flag_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        initData()
    }

    private fun initView(view: View) {
        minuteTv = view.findViewById(R.id.nation_flag_count_down_minute_tv)
        secondTv = view.findViewById(R.id.nation_flag_count_down_second_tv)
        tipsTv = view.findViewById(R.id.nation_flag_tip_iv)
        sendViewHolder =
            NationFlagSendViewHolder(
                requireActivity(),
                view.findViewById(R.id.nation_flag_detail_send_iv)
            )

        val titleTv = view.findViewById<TextView>(R.id.nation_flag_title_tv)
        titleTv.post {
            val shaper = LinearGradient(
                0F,
                0F,
                0F,
                titleTv.measuredHeight.toFloat(),
                Color.parseColor("#E8FBFF"),
                Color.parseColor("#7CECFF"),
                Shader.TileMode.CLAMP
            )
            titleTv.paint.shader = shaper
            titleTv.invalidate()
        }
        view.setOnClickListener {
            //防穿透
        }
    }

    private fun initData() {
        viewModel = NationFlagViewModel.get(requireActivity())
        viewModel?.getLeftTimeLiveData()?.observe(viewLifecycleOwner) {
            minuteTv.text = NationFlagUtils.formatNum(it / 60)
            secondTv.text = NationFlagUtils.formatNum(it % 60)
        }
        viewModel?.getFlagListLiveData()?.observe(viewLifecycleOwner) {
            if (it.isEmpty()) {
                showFragment(NationFlagEmptyDetailFragment::class.java)
            } else {
                if (viewModel?.getStatus() == NationFlagState.AWARDING) {
                    showFragment(NationFlagAwardDetailFragment::class.java)
                } else {
                    showFragment(NationFlagRankDetailFragment::class.java)
                }
            }
        }
        viewModel?.getStateLiveData()?.observe(viewLifecycleOwner) {
            when (it.status) {
                NationFlagState.RAISING, NationFlagState.PEAKING -> {
                    tipsTv.text = ResUtil.getString(R.string.voice_room_nation_flag_send_tips)
                    sendViewHolder.setEnable(true)
                }
                NationFlagState.AWARDING, NationFlagState.FAIL -> {
                    sendViewHolder.setEnable(false)
                    tipsTv.text = ResUtil.getString(R.string.voice_room_nation_flag_no_send_tips)
                }
                else -> {
                    sendViewHolder.setEnable(false)
                    tipsTv.text = ResUtil.getString(R.string.voice_room_nation_flag_no_send_tips)
                }
            }
        }
    }

    private fun showFragment(cls: Class<out Fragment>) {
        val manager = try {
            childFragmentManager
        } catch (e: IllegalStateException) {
            return
        }
        val transaction = manager.beginTransaction()
        val fragment = cls.newInstance()
        transaction.replace(R.id.nation_flag_detail_container_lay, fragment)
        transaction.commitNowAllowingStateLoss()
    }
}

class NationFlagRankDetailFragment : Fragment(), INationFlagRankDetailCallback {

    private val adapter: NationFlagListAdapter by lazy {
        NationFlagListAdapter(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val context = requireContext()
        val ll = VerticalScrollableLinearLayout(context)
        val rv = RecyclerView(context)
        rv.layoutManager = LinearLayoutManager(context)
        rv.adapter = adapter
        ll.addView(
            rv,
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )
        return ll
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        NationFlagViewModel.get(requireActivity())?.let {
            it.getFlagListLiveData().observe(viewLifecycleOwner) { list ->
                adapter.refresh(list)
            }
        }
    }

    override fun onSelect(nationId: String) {
        val bundle = Bundle()
        bundle.putString("nationId", nationId)
        parentFragment?.parentFragmentManager?.setFragmentResult("rank", bundle)
    }
}

class NationFlagAwardDetailFragment : Fragment() {

    private lateinit var flagIv: ImageView
    private lateinit var heightTv: TextView
    private lateinit var resultTv: TextView
    private lateinit var resultLay: ViewGroup

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_nation_flag_award_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        flagIv = view.findViewById(R.id.nation_flag_icon_iv)
        heightTv = view.findViewById(R.id.nation_flag_height_result_tv)
        resultTv = view.findViewById(R.id.nation_flag_result_tv)
        resultLay = view.findViewById(R.id.nation_flag_height_result_lay)
        initData()
    }

    private fun initData() {
        val viewModel = NationFlagViewModel.get(requireActivity()) ?: return
        viewModel.getFlagListLiveData().observe(viewLifecycleOwner) {
            if (it.isNotEmpty()) {
                val status = it[0]
                WpImageLoader.load(status.getNationGiftUrl() ?: "", flagIv)
                heightTv.text = ResUtil.getStr(
                    R.string.voice_room_nation_flag_final_height,
                    status.height
                )
                resultTv.text = Html.fromHtml(
                    ResUtil.getStr(
                        R.string.voice_room_nation_flag_top_desc,
                        status.getArea()?.name
                    )
                )
                resultLay.setOnClickListener {
                    val bundle = Bundle()
                    bundle.putString("nationId", status.nationId)
                    parentFragment?.parentFragmentManager?.setFragmentResult("rank", bundle)
                }

            }
        }

    }
}

class NationFlagEmptyDetailFragment : Fragment() {

    private lateinit var flagViewPager: ViewPager2
    private val adapter = NationFlagEmptyListAdapter()
    private var selectedPage = 1

    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            scroll(selectedPage + 1)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_nation_flag_empty_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        flagViewPager = view.findViewById(R.id.nation_flag_list_vp)
        initViewPager()
        flagViewPager.adapter = adapter
        val data = ConfigHelper.getInstance().nationFlagConfig.list
            ?.filter { it.nationId.lowercase() != "global" }
            ?.map { it.giftUrl } ?: emptyList()
        adapter.refresh(data)
        if (data.isNotEmpty()) {
            val position = data.size / 2
            scroll(position, false)
        }
        view.findViewById<View>(R.id.nation_flag_list_lay).let {
            it.isHorizontalFadingEdgeEnabled = false
            it.isVerticalFadingEdgeEnabled = false
            it.setOnTouchListener { _, event ->
                flagViewPager.dispatchTouchEvent(event)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        handler.removeCallbacksAndMessages(null)
    }

    private fun initViewPager() {
        flagViewPager.isHorizontalFadingEdgeEnabled = false
        flagViewPager.isVerticalFadingEdgeEnabled = false
        flagViewPager.offscreenPageLimit = 2
        flagViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                if (state == ViewPager.SCROLL_STATE_IDLE) {
                    flagViewPager.setCurrentItem(selectedPage, false)
                }
            }

            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                selectedPage = getSelectedPage(position)
                handler.removeCallbacksAndMessages(null)
                handler.sendEmptyMessageDelayed(1, 3000L)
            }

            private fun getSelectedPage(position: Int): Int {
                return when (position) {
                    0 -> adapter.itemCount - 2
                    adapter.itemCount - 1 -> 1
                    else -> position
                }
            }
        })

        val minScale = 0.90F

        flagViewPager.setPageTransformer { page, position ->
            if (page.parent == null) {
                return@setPageTransformer
            }
            val scale = max(minScale, 1 - abs(position))
            page.visibility = View.VISIBLE
            page.scaleX = scale
            page.scaleY = scale
        }
    }

    private fun scroll(position: Int, smoothScroll: Boolean = true) {
        flagViewPager.setCurrentItem(position, smoothScroll)
        handler.sendEmptyMessageDelayed(1, 3000L)
    }

}