<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="52dp"
    android:paddingHorizontal="16dp"
    tools:background="@color/white">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/quality_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_accent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:checked="true"
        tools:text="低画质" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/quality_iv"
        android:layout_width="24dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_un_select"
        tools:checked="true" />

</androidx.constraintlayout.widget.ConstraintLayout>