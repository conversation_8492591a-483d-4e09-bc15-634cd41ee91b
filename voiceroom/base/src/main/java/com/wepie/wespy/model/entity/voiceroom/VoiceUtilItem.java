package com.wepie.wespy.model.entity.voiceroom;


import androidx.annotation.IntDef;
import androidx.annotation.StringRes;

import com.huiwan.base.str.ResUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * date 2019/3/28
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceUtilItem {
    public static final int ROOM_TYPE_VOICE_ROOM = 1;
    public static final int ROOM_TYPE_WEDDING_ROOM = 2;
    public static final int ROOM_TYPE_CP_ROOM = 3;
    public static final int ROOM_TYPE_AUCTION_ROOM = 4;
    public static final int ROOM_TYPE_LOVER_ROOM = 5;
    public static final int ROOM_TYPE_FAMILY_ROOM = 6;
    public static final int ROOM_TYPE_VIDEO_ROOM = 8;
    public static final int ROOM_TYPE_LIVE_BARRAGE = 9;
    public static final int ROOM_TYPE_GAME_ROOM = 10;

    public static final int ITEM_RP   = 1;
    public static final int ITEM_SLOT = 2;
    public static final int ITEM_PK   = 3;
    public static final int ITEM_A_G  = 4;
    public static final int ITEM_DRAW = 5;
    public static final int ITEM_MUSIC = 6;
    public static final int ITEM_AUDIO = 7;
    public static final int ITEM_MIC = 8;
    public static final int ITEM_B_WINNER = 9;
    public static final int ITEM_SCORE = 10;
    public static final int ITEM_PUZZLE = 11;
    public static final int ITEM_H5 = 12;
    public static final int ITEM_HOT = 13;
    public static final int ITEM_LUCKY_NUM = 14;
    public static final int ITEM_BINGO = 15;
    // 新版PK ID
    public static final int ITEM_PK_NEW = 19;
    // 语音房 跳转unity游戏的 通用控件
    public static final int ITEM_JUMP_GAME = 18;

    public static final int GAME_ENTERTAINMENT_UTIL_TYPE = 1;
    public static final int ROOM_UTIL_TYPE = 2;

    // 增加属性，需要适配VoiceUtilItem(VoiceUtilItem item) 构造方法
    private int id;
    private int nameResId;
    private int nameExtResId;
    private int iconRes;
    private int utilType;
    private String itemName = "";
    private String h5Link = "";
    private String itemIcon = "";
    private int h5Height;
    private boolean enable;
    private boolean useExtraName = false;
    private int familyLimitLevel = 0;
    //用于打点
    private String btnName ="";
    //需要跳转的unity游戏
    private int gameType = 0;
    public VoiceUtilItem(VoiceUtilItem item) {
        this.id = item.id;
        this.nameResId = item.nameResId;
        this.nameExtResId = item.nameExtResId;
        this.iconRes = item.iconRes;
        this.utilType = item.utilType;
        this.itemName = item.itemName;
        this.h5Link = item.h5Link;
        this.itemIcon = item.itemIcon;
        this.h5Height = item.h5Height;
        this.enable = item.enable;
        this.useExtraName = item.useExtraName;
        this.btnName = item.btnName;
        this.familyLimitLevel = item.familyLimitLevel;
    }

    public VoiceUtilItem(int id, @StringRes int name, int iconRes, int utilType) {
        this(id, name, com.wepie.wespy.voiceroom.base.R.string.empty, iconRes, utilType);
    }

    public VoiceUtilItem(int id, @StringRes int name, @StringRes int nameExt, int iconRes, int utilType) {
        this.id = id;
        this.nameResId = name;
        this.iconRes = iconRes;
        this.nameExtResId = nameExt;
        this.utilType = utilType;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return itemName.equals("") ? ResUtil.getStr(useExtraName ? nameExtResId : nameResId) : itemName;
    }

    public int getIconRes() {
        return iconRes;
    }

    public String getItemIcon() {
        return itemIcon;
    }

    public String getH5Link() {
        return h5Link;
    }

    public int getH5Height() {
        return h5Height;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setItemName(String itemName){
        this.itemName = itemName;
    }

    public void setH5Link(String h5Link){
        this.h5Link = h5Link;
    }

    public void setItemIcon(String itemIcon){
        this.itemIcon = itemIcon;
    }

    public void setH5Height(int h5Height){
        this.h5Height = h5Height;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public void setUseExtraName(boolean useExtraName) {
        this.useExtraName = useExtraName;
    }

    public int getUtilType(){ return utilType; }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }


    public String getBtnName() {
        return btnName;
    }

    public void setBtnName(String btnName) {
        this.btnName = btnName;
    }

    public int getFamilyLimitLevel() {
        return familyLimitLevel;
    }

    public void setFamilyLimitLevel(int familyLimitLevel) {
        this.familyLimitLevel = familyLimitLevel;
    }

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({ROOM_TYPE_VOICE_ROOM, ROOM_TYPE_WEDDING_ROOM, ROOM_TYPE_CP_ROOM, ROOM_TYPE_AUCTION_ROOM, ROOM_TYPE_LOVER_ROOM, ROOM_TYPE_VIDEO_ROOM})
    public @interface RoomType {}
}
