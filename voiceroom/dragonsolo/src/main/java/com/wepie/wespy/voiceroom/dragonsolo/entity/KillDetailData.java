package com.wepie.wespy.voiceroom.dragonsolo.entity;

import com.google.gson.annotations.SerializedName;
import com.wepie.wespy.net.tcp.packet.MsgBattlePackets;
import com.wepie.wespy.net.tcp.packet.MsgBattleUnityPackets;

import java.util.ArrayList;
import java.util.List;

public class KillDetailData {
    public static final int DRAGON_ID = -999;
    public static final int NO_DISPLAY = 0;
    public static final int SUC_MVP = 1;
    public static final int FAIL_MVP = 2;
    @SerializedName("total_score")
    public int totalScore = 0;
    @SerializedName("last_week_rank")
    public int lastWeekRank = 0;
    @SerializedName("last_week_rank_rate")
    public int lastWeekRankRate = 0; //加成
    @SerializedName("wins_num")
    public int winsNum = 0;
    @SerializedName("wins_num_rate")
    public int winsNumRate = 0;
    @SerializedName("mvp_type")
    public int mvpType = 0;  //0-不展示 1-胜方mvp 2-败方mvp
    @SerializedName("mvp_type_rate")
    public int mvpTypeRate = 0;
    @SerializedName("damage_to_dragon")
    public int damageToDragon = 0; //对龙伤害
    @SerializedName("kill_items")
    public List<RecordKillItem> killItems = new ArrayList<>(); //士兵击杀情况，已排序
    @SerializedName("group")
    public int group = 0;

    public static class RecordKillItem {
        @SerializedName("warrior_id")
        public int warriorId = 0;
        @SerializedName("num")
        public int num = 0;
        @SerializedName("is_elite")
        public boolean is_elite = false;
    }

    public static KillDetailData parseFromProto(MsgBattlePackets.KillDetailsRsp rsp){
        KillDetailData data = new KillDetailData();
        data.totalScore = rsp.getTotalScore();
        data.lastWeekRank = rsp.getLastWeekRank();
        data.lastWeekRankRate = rsp.getLastWeekRankRate();
        data.winsNum = rsp.getWinsNum();
        data.winsNumRate = rsp.getWinsNumRate();
        data.mvpType = rsp.getMvpType();
        data.mvpTypeRate = rsp.getMvpTypeRate();
        data.damageToDragon = rsp.getDamageToDragon();
        data.group = rsp.getGroup();
        ArrayList<RecordKillItem> itemArrayList = new ArrayList<>();
        for (MsgBattleUnityPackets.RecordKillItem item : rsp.getKillItemsList()) {
            RecordKillItem recordKillItem = new RecordKillItem();
            recordKillItem.warriorId = item.getWarriorId();
            recordKillItem.is_elite = item.getIsElite();
            recordKillItem.num = item.getNum();
            itemArrayList.add(recordKillItem);
        }
        data.killItems = itemArrayList;
        return data;
    }
}
