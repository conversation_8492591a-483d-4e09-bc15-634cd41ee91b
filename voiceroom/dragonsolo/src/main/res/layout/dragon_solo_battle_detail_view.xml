<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_lay"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_lay"
        android:layout_width="match_parent"
        android:layout_height="502dp"
        android:background="@drawable/shape_ffffff_topcorner12"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/return_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="22dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/icon_back_black" />

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:text="@string/dragon_battle_record_detail_title"
            android:textColor="@color/color_text_primary"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/close_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="22dp"
            android:layout_marginEnd="16dp"
            android:padding="5dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/icon_close"
            app:tint="#1B1D38" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_tv">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/user_lay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false"
                    app:layout_constraintTop_toBottomOf="@id/title_tv"
                    tools:background="@color/white">

                    <com.huiwan.decorate.DecorHeadImgView
                        android:id="@+id/head_iv"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="12dp"
                        android:clipChildren="false"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/team_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="6dp"
                        android:paddingTop="1dp"
                        android:paddingEnd="6dp"
                        android:paddingBottom="1dp"
                        android:textColor="@color/white"
                        android:textSize="8dp"
                        app:layout_constraintBottom_toBottomOf="@id/head_iv"
                        app:layout_constraintEnd_toEndOf="@id/head_iv"
                        app:layout_constraintStart_toStartOf="@id/head_iv" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="@id/head_iv"
                        app:layout_constraintStart_toEndOf="@id/head_iv"
                        app:layout_constraintTop_toTopOf="@id/head_iv">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.huiwan.decorate.NameTextView
                                android:id="@+id/name_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="12dp"
                                android:ellipsize="end"
                                android:maxWidth="110dp"
                                android:maxLines="1"
                                android:textColor="@color/color_text_primary"
                                android:textSize="16dp"
                                android:textStyle="bold"
                                tools:text="哈哈哈哈哈第三方哈哈哈哈" />

                            <ImageView
                                android:id="@+id/gender_iv"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_centerVertical="true"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="4dp"
                                app:layout_constraintBottom_toBottomOf="@id/name_tv"
                                app:layout_constraintStart_toEndOf="@id/name_tv"
                                app:layout_constraintTop_toTopOf="@id/name_tv" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/my_point_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="8dp"
                            android:gravity="center"
                            android:text="0"
                            android:textColor="@color/color_yellow_default"
                            android:textSize="16dp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="@id/name_tv"
                            app:layout_constraintTop_toBottomOf="@id/name_tv" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/buff_title_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="18dp"
                        android:text="@string/dragon_battle_buff_title"
                        android:textColor="@color/color_text_primary"
                        android:textSize="16dp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/head_iv" />

                    <TextView
                        android:id="@+id/buff_desc_tv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/dragon_battle_buff_desc"
                        android:textColor="@color/color_text_tertiary"
                        android:textSize="12dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/buff_title_tv" />

                    <androidx.constraintlayout.utils.widget.ImageFilterView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="#80FCF8EE"
                        app:layout_constraintBottom_toBottomOf="@id/buff_lay"
                        app:layout_constraintEnd_toEndOf="@id/buff_lay"
                        app:layout_constraintStart_toStartOf="@id/buff_lay"
                        app:layout_constraintTop_toTopOf="@id/buff_lay"
                        app:round="8dp" />

                    <LinearLayout
                        android:id="@+id/buff_lay"
                        android:layout_width="0dp"
                        android:layout_height="68dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="16dp"
                        android:orientation="horizontal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/buff_desc_tv">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/week_buff_lay"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">

                            <TextView
                                android:id="@+id/week_buff_percent_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:text="+0"
                                android:textColor="@color/color_text_primary"
                                android:textSize="24dp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toStartOf="@id/week_buff_percent_tv_end"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/week_buff_percent_tv_end"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="%"
                                android:textColor="@color/color_text_primary"
                                android:textSize="14dp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="@id/week_buff_percent_tv"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/week_buff_percent_tv" />

                            <TextView
                                android:id="@+id/week_point_des_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:layout_marginBottom="9dp"
                                android:gravity="center"
                                android:textColor="@color/color_text_secondary"
                                android:textSize="12dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/week_buff_percent_tv"
                                tools:text="上周第 99+ 名" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/suc_buff_lay"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">

                            <TextView
                                android:id="@+id/suc_buff_percent_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:text="+0"
                                android:textColor="@color/color_text_primary"
                                android:textSize="24dp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toStartOf="@id/suc_buff_percent_tv_end"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/suc_buff_percent_tv_end"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="%"
                                android:textColor="@color/color_text_primary"
                                android:textSize="14dp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="@id/suc_buff_percent_tv"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/suc_buff_percent_tv" />

                            <TextView
                                android:id="@+id/suc_point_des_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:layout_marginBottom="9dp"
                                android:gravity="center"
                                android:textColor="@color/color_text_secondary"
                                android:textSize="12dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/suc_buff_percent_tv"
                                tools:text="今日 0 连胜" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/mvp_buff_lay"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:visibility="visible"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/mvp_buff_percent_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:text="+0"
                                android:textColor="@color/color_text_primary"
                                android:textSize="24dp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toStartOf="@id/mvp_buff_percent_tv_end"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/mvp_buff_percent_tv_end"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="%"
                                android:textColor="@color/color_text_primary"
                                android:textSize="14dp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="@id/mvp_buff_percent_tv"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/mvp_buff_percent_tv" />

                            <TextView
                                android:id="@+id/mvp_point_des_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:layout_marginBottom="9dp"
                                android:gravity="center"
                                android:textColor="@color/color_text_secondary"
                                android:textSize="12dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/mvp_buff_percent_tv"
                                tools:text="胜方 MVP" />

                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </LinearLayout>

                    <TextView
                        android:layout_width="36dp"
                        android:layout_height="16dp"
                        android:background="@drawable/shape_80f3d892_tr8_rb8"
                        android:gravity="center"
                        android:text="buff"
                        android:textColor="#72573A"
                        android:textSize="12dp"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="@id/buff_lay"
                        app:layout_constraintTop_toTopOf="@id/buff_lay" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="22dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/kill_title_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="@string/dragon_battle_beat_stats"
                        android:textColor="@color/color_text_primary"
                        android:textSize="16dp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/user_lay" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycle_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="8dp"
                    android:overScrollMode="never"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/kill_title_tv"
                    tools:listitem="@layout/dragon_solo_battle_kill_item_view" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>