//package com.wepie.service.voice.zego.util.capture;
//
//import android.graphics.ImageFormat;
//import android.graphics.SurfaceTexture;
//import android.hardware.Camera;
//import android.opengl.GLES11Ext;
//import android.opengl.GLES20;
//import android.opengl.Matrix;
//import android.os.Build;
//import android.os.Handler;
//import android.os.HandlerThread;
//import android.os.SystemClock;
//import androidx.annotation.Nullable;
//import android.util.Log;
//import android.view.TextureView;
//import android.view.View;
//
//import com.wepie.libavatar.Accelerometer;
//import com.wepie.libavatar.WpAvatar;
//import com.wepie.libavatar.WpAvatarActionHelper;
//import com.wepie.libavatar.WpAvatarAvatarHelper;
//import com.huiwan.configservice.ConfigHelper;
//import com.huiwan.configservice.editionentity.AvatarConfig;
//import com.wepie.service.voice.zego.ZegoUtil;
//import com.wepie.service.voice.zego.util.ve_gl.EglBase;
//import com.wepie.service.voice.zego.util.ve_gl.EglBase14;
//import com.wepie.service.voice.zego.util.ve_gl.GlRectDrawer;
//import com.wepie.service.voice.zego.util.ve_gl.GlUtil;
//import com.huiwan.platform.ThreadUtil;
//import com.huiwan.base.util.log.TimeLogger;
//import com.huiwan.base.util.ToastUtil;
//import com.zego.zegoavkit2.ZegoVideoCaptureDevice;
//
//import java.io.IOException;
//import java.util.List;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.TimeUnit;
//import java.util.concurrent.atomic.AtomicBoolean;
//
///**
// * VideoCaptureFromCamera2
// * 实现从摄像头采集数据并传给ZEGO SDK，需要继承实现ZEGO SDK 的ZegoVideoCaptureDevice类
// * 采用SURFACE_TEXTURE方式传递数据，将client返回的SurfaceTexture对象转为EglSurface再进行图像绘制
// */
//
//public class VideoCaptureFromCamera2 extends ZegoVideoCaptureDevice implements
//        SurfaceTexture.OnFrameAvailableListener {
//    private static final String TAG = "ZegoCaptureFromCamera2";
//    private static final int NORMAL_TIMEOUT = 1000;
//    private static final int CAMERA_STOP_TIMEOUT_MS = 3000;
//    private static final int CAMERA_PREFERRED_SIZE = 500;
//
//    private AvatarDebugHelper debugHelper = AvatarDebugHelper.get();
//
//    private Camera mCam = null;
//    private Camera.CameraInfo mCamInfo = null;
//    // camera相关参数的初始值
//    private int mFront = Camera.CameraInfo.CAMERA_FACING_FRONT;
//    private int mCameraWidth = 640;
//    private int mCameraHeight = 640;
//    private int mCaptureWidth = 0;
//    private int mCaptureHeight = 0;
//    private int mViewMode = 0;//ScaleAspectFill==1 ScaleAspectFit==0
//    private int mFrameRate = 10;
//    private int mDisplayRotation = 0;
//    private int mImageRotation = 0;
//
//    // SDK 内部实现的、同样实现 ZegoVideoCaptureDevice.Client 协议的客户端，用于通知SDK采集结果
//    private ZegoVideoCaptureDevice.Client mClient = null;
//
//    // 用于帧缓冲区对象（FBO）的相关变量
//    private EglBase mDummyContext = null;
//    private GlRectDrawer mDummyDrawer = null;
//    private boolean mIsEgl14 = false;
//    private int mInputTextureId = 0;
//    private SurfaceTexture mInputSurfaceTexture = null;
//    private float[] mInputMatrix = new float[16];
//    private int mTextureId = 0;
//    private int mFrameBufferId = 0;
//
//    // 纹理变换矩阵
//    private float[] mIdentityMatrix = new float[]{1.0f, 0.0f, 0.0f, 0.0f,
//            0.0f, 1.0f, 0.0f, 0.0f,
//            0.0f, 0.0f, 1.0f, 0.0f,
//            0.0f, 0.0f, 0.0f, 1.0f};
//
//    private HandlerThread mThread = null;
//    private volatile Handler cameraThreadHandler = null;
//    private final AtomicBoolean isCameraRunning = new AtomicBoolean();
//    private final Object pendingCameraRestartLock = new Object();
//    private volatile boolean pendingCameraRestart = false;
//
//    // 用于向SDK传递SurfaceTexture的相关变量
//    private boolean mIsCapture = false;
//    private EglBase captureEglBase = null;
//    private GlRectDrawer captureDrawer = null;
//    private float[] mCaptureMatrix = new float[16];
//
//    //预览相关
//    private WPPreview wpPreviewSdk = new WPPreview();//与推流保持一致
//    private WPPreview wpPreviewCustom = new WPPreview();//本地自定义预览
//    private TextureView textureViewCustomTemp = null;
//    private TextureView.SurfaceTextureListener textureListener = new PreviewTextureListener(cameraThreadHandler, wpPreviewSdk);
//    private TextureView.SurfaceTextureListener textureListenerCustom = new PreviewTextureListener(cameraThreadHandler, wpPreviewCustom);
//
//    private WpAvatarActionHelper actionHelper = new WpAvatarActionHelper();
//    private WpAvatarAvatarHelper avatarHelper = new WpAvatarAvatarHelper();
//    private AvatarConfig avatarConfig = ConfigHelper.getInstance().getAvatarConfig();
//
//    //本地视频推流字段
//    private volatile boolean pushVideoSteam = false;//默认不推流
//
//    //商汤
//    private byte[] mImageData;
//    private final Object mImageDataLock = new Object();
//    private int bufferUpdateCount = 0;
//    private int frameUpdateCount = 0;
//
//    /**
//     * 初始化资源，必须实现
//     * @param client 通知ZEGO SDK采集结果的客户端
//     */
//    @Override
//    protected void allocateAndStart(ZegoVideoCaptureDevice.Client client) {
//        ZegoUtil.print(TAG+".allocateAndStart");
//        mClient = client;
//        mThread = new HandlerThread("camera-cap");
//        mThread.start();
//        // 创建camera异步消息处理handler
//        cameraThreadHandler = new Handler(mThread.getLooper());
//
//        final CountDownLatch barrier = new CountDownLatch(1);
//        cameraThreadHandler.post(new Runnable() {
//            @Override
//            public void run() {
//                bufferUpdateCount = 0;
//                frameUpdateCount = 0;
//                mDummyContext = EglBase.create(null, EglBase.CONFIG_PIXEL_BUFFER);
//                attachDriveMode();
//
//                try {
//                    // 创建Surface
//                    mDummyContext.createDummyPbufferSurface();
//                    // 绑定eglContext、eglDisplay、eglSurface
//                    mDummyContext.makeCurrent();
//                    // 创建绘制类，用于FBO
//                    mDummyDrawer = new GlRectDrawer();
//                } catch (RuntimeException e) {
//                    // Clean up before rethrowing the exception.
//                    mDummyContext.releaseSurface();
//                    e.printStackTrace();
//                    throw e;
//                }
//
//                mIsEgl14 = EglBase14.isEGL14Supported();
//                mInputTextureId = GlUtil.generateTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES);
//                mInputSurfaceTexture = new SurfaceTexture(mInputTextureId);
//                // 设置视频帧回调监听
//                mInputSurfaceTexture.setOnFrameAvailableListener(VideoCaptureFromCamera2.this);
//
//                barrier.countDown();
//            }
//        });
//        try {
//            barrier.await(NORMAL_TIMEOUT, TimeUnit.MILLISECONDS);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        setupTempTextureView();
//    }
//
//    private void attachDriveMode() {
//        debugHelper.licenseOk(WpAvatar.licenseIsOk());
//        if (avatarConfig.isFunctionModelAvailable(true)) {
//            debugHelper.functionInit(true, true);
//            actionHelper.initAsync(avatarConfig.getActionModel().localPath(), avatarConfig.getAvatarModel().localPath());
//        } else {
//            debugHelper.functionInit(avatarConfig.getActionModel().isDownloaded(), avatarConfig.getAvatarModel().isDownloaded());
//        }
//    }
//
//    /**
//     * 释放资源，必须实现
//     * 先停止采集任务再清理client对象，以保证ZEGO SDK调用stopAndDeAllocate后，没有残留的异步任务导致野指针crash
//     */
//    @Override
//    protected void stopAndDeAllocate() {
//        ZegoUtil.print(TAG+".stopAndDeAllocate");
//        stopCapture();
//
//        if (cameraThreadHandler != null) {
//            final CountDownLatch barrier = new CountDownLatch(1);
//            cameraThreadHandler.post(new Runnable() {
//                @Override
//                public void run() {
//                    // 销毁传递给ZEGO SDK的surface
//                    releaseCaptureSurface();
//                    frameUpdateCount = 0;
//                    bufferUpdateCount = 0;
//                    ignoreFrameCount = 0;
//
//                    if (captureEglBase != null) {
//                        captureEglBase.release();
//                        captureEglBase = null;
//                    }
//
//                    // 销毁用于屏幕显示的surface（预览）
//                    wpPreviewSdk.release(textureListener);
//                    wpPreviewCustom.release(textureListenerCustom);
//
//                    if (captureEglBase != null) {
//                        captureEglBase.release();
//
//                        captureEglBase = null;
//                    }
//
//
//                    mInputSurfaceTexture.release();
//                    mInputSurfaceTexture = null;
//
//                    // 绑定eglContext、eglDisplay、eglSurface
//                    mDummyContext.makeCurrent();
//                    avatarHelper.release();
//                    actionHelper.destroy();
//                    if (mInputTextureId != 0) {
//                        int[] textures = new int[] {mInputTextureId};
//                        GLES20.glDeleteTextures(1, textures, 0);
//                        mInputTextureId = 0;
//                    }
//
//                    checkDelOutTextureId();
//
//                    mDummyDrawer = null;
//                    mDummyContext.release();
//                    mDummyContext = null;
//
//                    barrier.countDown();
//                }
//            });
//            try {
//                barrier.await(NORMAL_TIMEOUT, TimeUnit.MILLISECONDS);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//        }
//
//        mThread.quit();
//        mThread = null;
//
//        mClient.destroy();
//        mClient = null;
//
//        cameraThreadHandler = null;
//    }
//
//    // 开始推流时，ZEGO SDK 调用 startCapture 通知外部采集设备开始工作，必须实现
//    @Override
//    protected int startCapture() {
//        ZegoUtil.print(TAG+".startCapture");
//        mIsCapture = true;
////        startCamera();
//        return 0;
//    }
//
//    // 停止推流时，ZEGO SDK 调用 stopCapture 通知外部采集设备停止采集，必须实现
//    @Override
//    protected int stopCapture() {
//        ZegoUtil.print(TAG+".stopCapture");
//        mIsCapture = false;
////        stopCamera(false);
//        return 0;
//    }
//
//    // 启动camera
//    private int startCamera() {
//        if (isCameraRunning.getAndSet(true)) {
//            ZegoUtil.print("Camera has already been started.");
//            return 0;
//        }
//
//        final boolean didPost = maybePostOnCameraThread(new Runnable() {
//            @Override
//            public void run() {
//                // * Create and Start Cam
//                createCamOnCameraThread();
//                startCamOnCameraThread();
//            }
//        });
//
//        return 0;
//    }
//
//    // 关闭camera
//    private int stopCamera(boolean force) {
//        if (cameraNeedOpen() && !force) {
//            return 0;
//        }
//
//        final CountDownLatch barrier = new CountDownLatch(1);
//        final boolean didPost = maybePostOnCameraThread(new Runnable() {
//            @Override public void run() {
//                stopCaptureOnCameraThread(true /* stopHandler */);
//                releaseCam();
//                barrier.countDown();
//            }
//        });
//        if (!didPost) {
//            ZegoUtil.print("Calling stopCapture() for already stopped camera.");
//            return 0;
//        }
//        try {
//            if (!barrier.await(CAMERA_STOP_TIMEOUT_MS, TimeUnit.MILLISECONDS)) {
//                ZegoUtil.print("Camera stop timeout");
//            }
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        ZegoUtil.print( "stopCapture done");
//
//        return 0;
//    }
//
//    private boolean cameraNeedOpen() {
//        return wpPreviewSdk.isPreview() || wpPreviewCustom.isPreview() || (mIsCapture && pushVideoSteam);
//    }
//
//    // 告知ZEGO SDK当前采集数据的类型，必须实现
//    @Override
//    protected int supportBufferType() {
//        // SurfaceTexture 类型
//        return PIXEL_BUFFER_TYPE_SURFACE_TEXTURE;
//    }
//
//    // 设置采集帧率
//    @Override
//    protected int setFrameRate(final int framerate) {
//        mFrameRate = framerate;
//
//        if (cameraThreadHandler != null) {
//            cameraThreadHandler.post(new Runnable() {
//                @Override
//                public void run() {
//                    updateRateOnCameraThread(framerate);
//                }
//            });
//        }
//
//        return 0;
//    }
//
//    // 设置视图宽高
//    @Override
//    protected int setResolution(int width, int height) {
//        ZegoUtil.print(TAG+".setResolution " + width + " " + height);
//        mCaptureWidth = width;
//        mCaptureHeight = height;
//        // 修改视图宽高后需要重启camera
//        restartCam();
//        return 0;
//    }
//
//    // 前后摄像头的切换
//    @Override
//    protected int setFrontCam(int bFront) {
//        mFront = bFront;
//        // 切换摄像头后需要重启camera
//        restartCam();
//        return 0;
//    }
//
//    // 设置展示视图
//    @Override
//    protected int setView(final View view) {
//        ZegoUtil.print(TAG+".setView");
//        if (view instanceof TextureView) {
//            setRendererView(wpPreviewSdk, (TextureView) view, textureListener);
//        }
//
//        return 0;
//    }
//
//    @Override
//    protected int setViewMode(int nMode) {
//        mViewMode = nMode;
//        return 0;
//    }
//
//    @Override
//    protected int setViewRotation(int nRotation) {
//        return 0;
//    }
//
//    // 设置采集时的旋转方向
//    @Override
//    protected int setCaptureRotation(int nRotation) {
//        mDisplayRotation = nRotation;
//        return 0;
//    }
//
//    // 启动预览
//    @Override
//    protected int startPreview() {
//        ZegoUtil.print(TAG+".startPreview");
//        wpPreviewSdk.setPreview(true);
//        wpPreviewCustom.setPreview(true);
//        return startCamera();
//    }
//
//    // 停止预览
//    @Override
//    protected int stopPreview() {
//        ZegoUtil.print(TAG+".stopPreview");
//        wpPreviewSdk.setPreview(false);
//        wpPreviewCustom.setPreview(false);
//        return stopCamera(false);
//    }
//
//    @Override
//    protected int enableTorch(boolean bEnable) {
//        return 0;
//    }
//
//    @Override
//    protected int takeSnapshot() {
//        return 0;
//    }
//
//    @Override
//    protected int setPowerlineFreq(int nFreq) {
//        return 0;
//    }
//
//    // 更新camera的采集帧率
//    private int updateRateOnCameraThread(final int framerate) {
//        checkIsOnCameraThread();
//        if (mCam == null) {
//            return 0;
//        }
//
//        mFrameRate = framerate;
//
//        Camera.Parameters parms = mCam.getParameters();
//        List<int[]> supported = parms.getSupportedPreviewFpsRange();
//
//        int[] fpsRange = getFpsRange(supported);
//        if (fpsRange != null) {
//            parms.setPreviewFpsRange(fpsRange[0], fpsRange[1]);
//        }
//
//        int[] realRate = new int[2];
//        parms.getPreviewFpsRange(realRate);
//        if (realRate[0] == realRate[1]) {
//            mFrameRate = realRate[0] / 1000;
//        } else {
//            mFrameRate = realRate[1] / 2 / 1000;
//        }
//
//        try {
//            mCam.setParameters(parms);
//        } catch (Exception ex) {
//            ZegoUtil.print("vcap: update fps -- set camera parameters error with exception\n");
//            ex.printStackTrace();
//        }
//        return 0;
//    }
//
//    private void checkIsOnCameraThread() {
//        if (cameraThreadHandler == null) {
//            ZegoUtil.print("Camera is not initialized - can't check thread.");
//        } else if (Thread.currentThread() != cameraThreadHandler.getLooper().getThread()) {
//            throw new IllegalStateException("Wrong thread");
//        }
//    }
//
//    private boolean maybePostOnCameraThread(Runnable runnable) {
//        return cameraThreadHandler != null && isCameraRunning.get()
//                && cameraThreadHandler.postAtTime(runnable, this, SystemClock.uptimeMillis());
//    }
//
//    // 创建camera
//    private int createCamOnCameraThread() {
//        checkIsOnCameraThread();
//        if (!isCameraRunning.get()) {
//            ZegoUtil.print("startCaptureOnCameraThread: Camera is stopped");
//            return 0;
//        }
//
//        ZegoUtil.print("board: " + Build.BOARD);
//        ZegoUtil.print("device: " + Build.DEVICE);
//        ZegoUtil.print("manufacturer: " + Build.MANUFACTURER);
//        ZegoUtil.print("brand: " + Build.BRAND);
//        ZegoUtil.print("model: " + Build.MODEL);
//        ZegoUtil.print("product: " + Build.PRODUCT);
//        ZegoUtil.print("sdk: " + Build.VERSION.SDK_INT);
//
//        // 获取欲设置camera的索引号
//        int nFacing = (mFront != 0) ? Camera.CameraInfo.CAMERA_FACING_FRONT : Camera.CameraInfo.CAMERA_FACING_BACK;
//
//        if (mCam != null) {
//            // 已打开camera
//            return 0;
//        }
//
//        mCamInfo = new Camera.CameraInfo();
//        // 获取camera的数目
//        int nCnt = Camera.getNumberOfCameras();
//        // 得到欲设置camera的索引号并打开camera
//        for (int i = 0; i < nCnt; i++) {
//            Camera.getCameraInfo(i, mCamInfo);
//            if (mCamInfo.facing == nFacing) {
//                try {
//                    mCam = Camera.open(i);
//                } catch (RuntimeException e) {
//                    mCam = null;
//                }
//                break;
//            }
//        }
//
//        // 没找到欲设置的camera
//        if (mCam == null) {
//            ZegoUtil.print("[WARNING] no camera found, try default\n");
//            // 先试图打开默认camera
//            try {
//                mCam = Camera.open();//crash Fail to connect to camera service
//            } catch (Exception e) {
//                ToastUtil.show("相机状态异常，请检查相机能否正常打开～");
//                e.printStackTrace();
//            }
//
//            if (mCam == null) {
//                ZegoUtil.e(VideoCaptureFromCamera2.class, "open camera failed, please check system camera status!");
//                ZegoUtil.print("[ERROR] no camera found\n");
//                return -1;
//            }
//        }
//
//        checkUpdateCameraPreviewSize();
//
//        Camera.Parameters parms = mCam.getParameters();
//        // 获取camera支持的帧率范围，并设置预览帧率范围
//        List<int[]> supported = parms.getSupportedPreviewFpsRange();
//        int[] fpsRange = getFpsRange(supported);
//        if (fpsRange != null) {
//            parms.setPreviewFpsRange(fpsRange[0], fpsRange[1]);
//        }
//
//        // 获取camera的实际帧率
//        int[] realRate = new int[2];
//        parms.getPreviewFpsRange(realRate);
//        if (realRate[0] == realRate[1]) {
//            mFrameRate = realRate[0] / 1000;
//        } else {
//            mFrameRate = realRate[1] / 2 / 1000;
//        }
//
//        parms.setPreviewFormat(ImageFormat.NV21);
//
//        // 设置camera的对焦模式
//        boolean bFocusModeSet = false;
//        for (String mode : parms.getSupportedFocusModes()) {
//            if (mode.compareTo(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO) == 0) {
//                try {
//                    parms.setFocusMode(mode);
//                    bFocusModeSet = true;
//                    break;
//                } catch (Exception ex) {
//                    ZegoUtil.print("[WARNING] vcap: set focus mode error (stack trace followed)!!!\n");
//                    ex.printStackTrace();
//                }
//            }
//        }
//        if (!bFocusModeSet) {
//            ZegoUtil.print("[WARNING] vcap: focus mode left unset !!\n");
//        }
//
//        try {
//            // 设置camera的参数
//            mCam.setParameters(parms);
//        } catch (Exception ex) {
//            ZegoUtil.print("vcap: set camera parameters error with exception\n");
//            ex.printStackTrace();
//        }
//
//        Camera.Parameters actualParm = mCam.getParameters();
//        mCameraWidth = actualParm.getPreviewSize().width;
//        mCameraHeight = actualParm.getPreviewSize().height;
//        ZegoUtil.print("[WARNING] vcap: focus mode " + actualParm.getFocusMode());
//
//        int result;
//        if (mCamInfo.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
//            result = (mCamInfo.orientation + mDisplayRotation) % 360;
//            result = (360 - result) % 360;  // compensate the mirror
//        } else {  // back-facing
//            result = (mCamInfo.orientation - mDisplayRotation + 360) % 360;
//        }
//        // 设置预览图像的转方向
//        mCam.setDisplayOrientation(result);
//        mImageRotation = result;
//
//        // 绑定eglContext、eglDisplay、eglSurface
//        mDummyContext.makeCurrent();;
//        ZegoUtil.print( "ImageRotation:" + mImageRotation);
//        checkDelOutTextureId();
//
//        // 销毁传递给ZEGO SDK的surface，如果原先已有EGLSurface的情况下
//        releaseCaptureSurface();
//
//        return 0;
//    }
//
//    @Nullable
//    private int[] getFpsRange(List<int[]> supported){
//        //取相近，大小差异一样
//        int fitMinDiff = Integer.MAX_VALUE;
//        int[] fitFpsRange = null;//范围内，帧率差距最小的一个
//        int extMinDiff = Integer.MAX_VALUE;
//        int[] extFpsRange = null;//范围外，满足帧率，最小的一个
//        for (int[] entry : supported) {
//            if (mFrameRate * 1000 >= entry[0] && mFrameRate * 1000 <= entry[1]) {
//                int diff = Math.abs(entry[0] - entry[1]);
//                if (diff < fitMinDiff) {
//                    fitMinDiff = diff;
//                    fitFpsRange = entry;
//                }
//            } else if (mFrameRate * 1000 < entry[0]) {
//                if (extMinDiff > entry[1]) {
//                    extMinDiff = entry[1];
//                    extFpsRange = entry;
//                }
//            }
//        }
//        if (fitFpsRange == null) return extFpsRange;
//        return fitFpsRange;
//    }
//
//    private void checkUpdateCameraPreviewSize() {
//        Camera.Parameters param = mCam.getParameters();
//        List<Camera.Size> sizeList = param.getSupportedPreviewSizes();
//        Camera.Size previewSize = mCam.new Size(CAMERA_PREFERRED_SIZE, CAMERA_PREFERRED_SIZE);
//        float previewScore = Integer.MAX_VALUE;
//        for (Camera.Size size:sizeList) {
//            float rateScore = 2 * CAMERA_PREFERRED_SIZE * Math.abs(size.width > size.height ? ((float)size.width)/size.height : ((float)size.height)/size.width);
//            float curScore = Math.abs(size.width - CAMERA_PREFERRED_SIZE) + Math.abs(size.height - CAMERA_PREFERRED_SIZE) + rateScore;
//            TimeLogger.msgNoStack("preview size({}, {}) rate score: {}, total score: {}", size.width, size.height, rateScore, curScore);
//            if (previewScore > curScore) {
//                previewScore = curScore;
//                previewSize = size;
//            }
//        }
//        param.setPreviewSize(previewSize.width, previewSize.height);
//        ToastUtil.debugShow("use preview size: ({}, {})", previewSize.width, previewSize.height);
//        try {
//            mCam.setParameters(param);
//        } catch (Exception e) {
//            TimeLogger.msgNoStack("error set preview size, {}", e);
//        }
//    }
//
//    // 启动camera
//    private int startCamOnCameraThread() {
//        checkIsOnCameraThread();
//        if (!isCameraRunning.get() || mCam == null) {
//            ZegoUtil.print("startPreviewOnCameraThread: Camera is stopped");
//            return -1;
//        }
//
//        // * mCam.setDisplayOrientation(90);
//        if (mInputSurfaceTexture == null) {
//            ZegoUtil.print("mInputSurfaceTexture == null");
//            return -1;
//        }
//
//        mImageData = new byte[mCameraWidth * mCameraHeight * ImageFormat.getBitsPerPixel(ImageFormat.NV21) / 8];
//        byte[] buffer = new byte[mImageData.length];
//        mCam.addCallbackBuffer(buffer);
//        mCam.setPreviewCallbackWithBuffer(new Camera.PreviewCallback() {
//            @Override
//            public void onPreviewFrame(byte[] data, Camera camera) {
//                synchronized (mImageDataLock) {
//                    System.arraycopy(data, 0, mImageData, 0, data.length);
//                }
//                bufferUpdateCount++;
//                mCam.addCallbackBuffer(data);
//            }
//        });
//        try {
//            // 设置预览SurfaceTexture
//            mCam.setPreviewTexture(mInputSurfaceTexture);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//        // 启动camera预览
//        mCam.startPreview();
//        ZegoUtil.print("startPreview success");
//
//        return 0;
//    }
//
//    // camera停止采集
//    private int stopCaptureOnCameraThread(boolean stopHandler) {
//        checkIsOnCameraThread();
//        ZegoUtil.print( "stopCaptureOnCameraThread");
//
//        if (stopHandler) {
//            // Clear the cameraThreadHandler first, in case stopPreview or
//            // other driver code deadlocks. Deadlock in
//            // android.hardware.Camera._stopPreview(Native Method) has
//            // been observed on Nexus 5 (hammerhead), OS version LMY48I.
//            // The camera might post another one or two preview frames
//            // before stopped, so we have to check |isCameraRunning|.
//            // Remove all pending Runnables posted from |this|.
//            isCameraRunning.set(false);
//            cameraThreadHandler.removeCallbacksAndMessages(this /* token */);
//        }
//
//        if (mCam != null) {
//            mCam.setPreviewCallbackWithBuffer(null);
//            // 停止camera预览
//            mCam.stopPreview();
//        }
//        return 0;
//    }
//
//    // 重启camera
//    private int restartCam() {
//        synchronized (pendingCameraRestartLock) {
//            if (pendingCameraRestart) {
//                // Do not handle multiple camera switch request to avoid blocking
//                // camera thread by handling too many switch request from a queue.
//                Log.w(TAG, "Ignoring camera switch request.");
//                return 0;
//            }
//            pendingCameraRestart = true;
//        }
//
//        final boolean didPost = maybePostOnCameraThread(new Runnable() {
//            @Override
//            public void run() {
//                stopCaptureOnCameraThread(false);
//                releaseCam();
//                createCamOnCameraThread();
//                startCamOnCameraThread();
//                synchronized (pendingCameraRestartLock) {
//                    pendingCameraRestart = false;
//                }
//            }
//        });
//
//        if (!didPost) {
//            synchronized (pendingCameraRestartLock) {
//                pendingCameraRestart = false;
//            }
//        }
//
//        return 0;
//    }
//
//    // 释放camera
//    private int releaseCam() {
//        // * release cam
//        if (mCam != null) {
//            mCam.release();
//            mCam = null;
//        }
//
//        // * release cam info
//        mCamInfo = null;
//        return 0;
//    }
//
//    private void setupTempTextureView() {
//        if (textureViewCustomTemp != null) {
//            setRendererView(wpPreviewCustom, textureViewCustomTemp, textureListenerCustom);
//            textureViewCustomTemp = null;
//        }
//    }
//
//    // 设置渲染视图
//    private int setRendererView(final WPPreview wpPreview, final TextureView view, final TextureView.SurfaceTextureListener textureListener) {
//        ZegoUtil.print("setRendererView");
//        if (cameraThreadHandler == null) {
//            // 设置Texture.SurfaceTextureListener回调监听
//            wpPreview.doSetRendererView(view, textureListener);
//        } else {
//            cameraThreadHandler.post(new Runnable() {
//                @Override
//                public void run() {
//                    wpPreview.doSetRendererView(view, textureListener);
//                }
//            });
//        }
//        return 0;
//    }
//
//
//    // 设置自定义的渲染视图
//    public boolean setCustomPreView(final TextureView view) {
//        ZegoUtil.print("setCustomPreView");
//        if (cameraThreadHandler == null) {
//            textureViewCustomTemp = view;
//        } else {
//            setRendererView(wpPreviewCustom, view, textureListenerCustom);
//        }
//        return true;
//    }
//
//    // 绘制图像数据到SDK提供的EglSurface上
//    private void drawToCapture(int textureId, int width, int height, float[] texMatrix, long timestamp_ns, boolean mosaic) {
//        if (captureEglBase == null) {
//            captureEglBase = EglBase.create(mDummyContext.getEglBaseContext(), EglBase.CONFIG_RECORDABLE);
//        }
//
//        if (!captureEglBase.hasSurface()) {
//            TimeLogger.msgNoStack("get sdk surface texture start");
//            SurfaceTexture temp = mClient.getSurfaceTexture();
//            TimeLogger.msgNoStack("get sdk surface texture success");
//            temp.setDefaultBufferSize(mCaptureWidth, mCaptureHeight);
//            try {
//                // 创建EGLSurface
//                captureEglBase.createSurface(temp);
//                // 绑定eglContext、eglDisplay、eglSurface
//                captureEglBase.makeCurrent();
//                // 创建绘制类
//                captureDrawer = new GlRectDrawer();
//            } catch (RuntimeException e) {
//                e.printStackTrace();
//                // Clean up before rethrowing the exception.
//                captureEglBase.releaseSurface();
//                return ;
//            }
//        }
//
//        try {
//            // 绑定eglContext、eglDisplay、eglSurface
//            captureEglBase.makeCurrent();
//
//            int scaleWidth = mCaptureWidth;
//            int scaleHeight = mCaptureHeight;
//            System.arraycopy(texMatrix, 0, mCaptureMatrix, 0, 16);
//            if (mViewMode == 0) {
//                if (mCaptureHeight * width <= mCaptureWidth * height) {
//                    scaleWidth = mCaptureHeight * width / height;
//                } else {
//                    scaleHeight = mCaptureWidth * height / width;
//                }
//            } else if (mViewMode == 1) {
//                if (mCaptureHeight * width <= mCaptureWidth * height) {
//                    scaleHeight = mCaptureWidth * height / width;
//                } else {
//                    scaleWidth = mCaptureHeight * width / height;
//                }
//                float fWidthScale = (float)mCaptureWidth / scaleWidth;
//                float fHeightScale = (float)mCaptureHeight / scaleHeight;
//                Matrix.scaleM(mCaptureMatrix, 0, fWidthScale, fHeightScale, 1.0f);
//                Matrix.translateM(mCaptureMatrix, 0, (1.0f - fWidthScale) / 2.0f, (1.0f - fHeightScale) / 2.0f, 1.0f);
//
//                scaleWidth = mCaptureWidth;
//                scaleHeight = mCaptureHeight;
//            }
//
//            GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);
//
//            // 绘制rgb格式图像
//            captureDrawer.drawRgb(textureId, mCaptureMatrix, 0, 0, scaleWidth, scaleHeight, mosaic);
////
//            // 交换渲染好的buffer 去显示
//            if (mIsEgl14) {
//                ((EglBase14) captureEglBase).swapBuffers(timestamp_ns);
//            } else {
//                captureEglBase.swapBuffers();
//            }
//
//            // 分离当前eglContext
//            captureEglBase.detachCurrent();
//        } catch (RuntimeException e) {
//            e.printStackTrace();
//        }
//    }
//
//    // 销毁传递给ZEGO SDK的surface
//    private void releaseCaptureSurface() {
//        if (captureEglBase == null) {
//            return ;
//        }
//
//        if (captureEglBase.hasSurface()) {
//            // 绑定eglContext、eglDisplay、eglSurface
//            captureEglBase.makeCurrent();
//
//            if (captureDrawer != null) {
//                captureDrawer = null;
//            }
//
//            captureEglBase.releaseSurface();
//            captureEglBase.detachCurrent();
//        }
//    }
//
//    public void setCustomAvatarPath(String avatarPath, boolean hasAvatar) {
//        if (cameraThreadHandler != null) {
//            cameraThreadHandler.post(this::attachDriveMode);
//        }
//        wpPreviewCustom.setAvatarPath(avatarPath, hasAvatar);
//        avatarHelper.updateAvatarModel(avatarPath);
//        debugHelper.updateModel(avatarPath);
//    }
//
//    public void setSdkAvatarPath(String avatarPath, boolean hasAvatar) {
//        if (cameraThreadHandler != null) {
//            cameraThreadHandler.post(this::attachDriveMode);
//        }
//        wpPreviewSdk.setAvatarPath(avatarPath, hasAvatar);
//        avatarHelper.updateAvatarModel(avatarPath);
//        debugHelper.updateModel(avatarPath);
//    }
//
////    @Override
////    public void doFrame(long frameTimeNanos) {
////        if (!mIsRunning) {
////            return ;
////        }
////        Choreographer.getInstance().postFrameCallback(this);
//
//    // 在SurfaceTexture.OnFrameAvailableListener回调中更新采集数据
//    @Override
//    public void onFrameAvailable(SurfaceTexture surfaceTexture) {
//        if (mDummyContext == null) return;
//        mDummyContext.makeCurrent();
//        surfaceTexture.updateTexImage();
//        if (bufferUpdateCount > frameUpdateCount) {
//            try {
//                checkUpdateFrame(surfaceTexture);
//                frameUpdateCount++;
//            } catch (GlUtil.GlOutOfMemoryException e) {
//                e.printStackTrace();
//            } catch (Exception e) {
//                TimeLogger.msgNoStack("error when frame available", e);
//            }
//        } else {
//            ignoreFrameCount++;
//            TimeLogger.msgNoStack("ignore frame count: {}", ignoreFrameCount);
//        }
//    }
//    private int ignoreFrameCount = 0;
//
//
//    // 仅使用一种头套
//    private void checkUpdateFrame(SurfaceTexture surfaceTexture) {
//        if (mDummyContext == null) return;
//        // 绑定eglContext、eglDisplay、eglSurface
//        mDummyContext.makeCurrent();
//        long timestamp = surfaceTexture.getTimestamp();
//        surfaceTexture.getTransformMatrix(mInputMatrix);
//        int width = mCameraWidth;
//        int height = mCameraHeight;
//        if (mImageRotation == 90 || mImageRotation == 270) {
//            int temp = width;
//            width = height;
//            height = temp;
//        }
//        checkInitTextureId(mCameraWidth, mCameraHeight);
//
//        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, mFrameBufferId);
//        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);
//        mDummyDrawer.drawOes(mInputTextureId, mIdentityMatrix, 0, 0, mCameraWidth, mCameraHeight);
//        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0);
//
//        boolean needDrawAvatar = false;
//        long humanActionResult = 0;
//        boolean needCheck = avatarHelper.needCheckAvatar() && wpPreviewSdk.hasAvatar();
//        int pushTextureId;
//        boolean pushMosaic = false;
//        if (needCheck && avatarConfig.isFunctionModelAvailable(false)) {
//            actionHelper.ensureActionInit();
//            avatarHelper.glInitIfNeed(mCameraWidth, mCameraHeight);
//            avatarHelper.glUpdateModel();
//            long detectConfig = avatarHelper.getDetectConfig();
//            debugHelper.detectStart();
//            humanActionResult = actionHelper.detectActionInfo(mImageData, detectConfig, mCameraWidth, mCameraHeight, mCamInfo.orientation);
//            int faceCount = actionHelper.faceCount(humanActionResult);
//            debugHelper.detectEnd(faceCount);
//            needDrawAvatar = faceCount == 1;
//        }
//        if (needDrawAvatar) {
//            mDummyContext.makeCurrent();
//            debugHelper.renderStart();
//            int result = avatarHelper.glProcessAvatar(actionHelper.getActionHandle(), humanActionResult, mTextureId, mCameraWidth, mCameraHeight, getCurrentOrientation(), sharedAvatarTextureId);
//            debugHelper.renderEnd();
//            if (result == 0) {//头套加载成功
//                wpPreviewSdk.drawToPreview(sharedAvatarTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, false);
//                wpPreviewCustom.drawToPreview(sharedAvatarTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, false);
//                pushTextureId = sharedAvatarTextureId;
//            } else {
//                wpPreviewSdk.drawToPreview(mTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, true);
//                wpPreviewCustom.drawToPreview(mTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, true);
//                pushTextureId = mTextureId;
//            }
//        } else {
//            wpPreviewSdk.drawToPreview(mTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, wpPreviewSdk.hasAvatar());
//            wpPreviewCustom.drawToPreview(mTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, wpPreviewCustom.hasAvatar());
//            pushTextureId = mTextureId;
//            pushMosaic = wpPreviewSdk.hasAvatar();
//        }
//        if (mIsCapture && pushVideoSteam) {
//            drawToCapture(pushTextureId, width, height, mInputMatrix, timestamp, pushMosaic);
//        }
//    }
//
//    // 使用两种不同的头套的情况
//    private void checkUpdateFrame2(SurfaceTexture surfaceTexture) {
//        // 绑定eglContext、eglDisplay、eglSurface
//        mDummyContext.makeCurrent();
//        surfaceTexture.updateTexImage();
//        long timestamp = surfaceTexture.getTimestamp();
//        surfaceTexture.getTransformMatrix(mInputMatrix);
//
//        // do preprocessing here such as camera 360 sdk
//
//        // 纠正图像展示方向
//        int width = mCameraWidth;
//        int height = mCameraHeight;
//        if (mImageRotation == 90 || mImageRotation == 270) {
//            int temp = width;
//            width = height;
//            height = temp;
//        }
//        checkInitTextureId(mCameraWidth, mCameraHeight);
//
//        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, mFrameBufferId);
//        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);
//        mDummyDrawer.drawOes(mInputTextureId, mIdentityMatrix, 0, 0, mCameraWidth, mCameraHeight);
//        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0);
//
//        boolean needDrawAvatar = false;
//        long humanActionResult = 0;
//        boolean needCheck = wpPreviewCustom.needCheckAvatar() || wpPreviewSdk.needCheckAvatar();
//        int pushTextureId;
//        boolean pushMosaic = false;
//        if (needCheck && avatarConfig.isFunctionModelAvailable(false)) {
//            actionHelper.ensureActionInit();
//            wpPreviewSdk.initAndUpdateModel(width, height);
//            wpPreviewCustom.initAndUpdateModel(width, height);
//            long detectConfig = wpPreviewSdk.needCheckAvatar() ? wpPreviewSdk.getAvatarDetectConfig() : wpPreviewCustom.getAvatarDetectConfig();
//            humanActionResult = actionHelper.detectActionInfo(mImageData, detectConfig, mCameraWidth, mCameraHeight, mCamInfo.orientation);
//            int faceCount = actionHelper.faceCount(humanActionResult);
//            needDrawAvatar = faceCount == 1;
//        }
//        if (needDrawAvatar) {
////            WpAvatarActionHelper.rotateHumanActionResult(humanActionResult, mCameraWidth, mCameraHeight, mCamInfo.orientation);
//            if (wpPreviewSdk.needCheckAvatar()) {
//                // push 头套
//                wpPreviewSdk.processAvatar(mDummyContext, actionHelper.getActionHandle(), humanActionResult, mTextureId, mCameraWidth, mCameraHeight, getCurrentOrientation(), sharedAvatarTextureId);
//                wpPreviewSdk.drawToPreview(sharedAvatarTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, false);
//                pushTextureId = sharedAvatarTextureId;
//                if (wpPreviewSdk.sameAvatar(wpPreviewCustom)) {
//                    // 预览使用 push 头套
//                    wpPreviewCustom.drawToPreview(sharedAvatarTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, false);
//                } else if (wpPreviewCustom.needCheckAvatar()) {
//                    // 预览使用 其他 头套
//                    wpPreviewCustom.processAvatar(mDummyContext, actionHelper.getActionHandle(), humanActionResult, mTextureId, mCameraWidth, mCameraHeight, getCurrentOrientation(), sharedAvatarTextureId2);
//                    wpPreviewCustom.drawToPreview(sharedAvatarTextureId2, width, height, mInputMatrix, mDummyContext, mViewMode, false);
//                }
//            } else if (wpPreviewCustom.needCheckAvatar()){
//                // 仅预览需要头套
//                wpPreviewSdk.drawToPreview(mTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, wpPreviewSdk.hasAvatar());
//                wpPreviewCustom.processAvatar(mDummyContext, actionHelper.getActionHandle(), humanActionResult, mTextureId, mCameraWidth, mCameraHeight, getCurrentOrientation(), sharedAvatarTextureId);
//                wpPreviewCustom.drawToPreview(sharedAvatarTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, false);
//                pushTextureId = mTextureId;
//                pushMosaic = true;
//            } else {
//                // 无头套, 理论上不应走这个分支
//                pushTextureId = mTextureId;
//            }
//        } else {
//            wpPreviewSdk.drawToPreview(mTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, wpPreviewSdk.hasAvatar());
//            wpPreviewCustom.drawToPreview(mTextureId, width, height, mInputMatrix, mDummyContext, mViewMode, wpPreviewCustom.hasAvatar());
//            pushTextureId = mTextureId;
//            pushMosaic = true;
//        }
//        if (mIsCapture && pushVideoSteam) {
//            drawToCapture(pushTextureId, width, height, mInputMatrix, timestamp, pushMosaic);
//        }
//    }
//
//    public void setPushVideoSteam(boolean pushVideoSteam) {
//        this.pushVideoSteam = pushVideoSteam;
//    }
//
//
//    private void checkInitTextureId(int width, int height) {
//        if (mTextureId == 0) {
//            GLES20.glActiveTexture(GLES20.GL_TEXTURE0);
//            mTextureId = GlUtil.generateTexture(GLES20.GL_TEXTURE_2D);
//            GLES20.glTexImage2D(GLES20.GL_TEXTURE_2D, 0, GLES20.GL_RGBA, width, height, 0, GLES20.GL_RGBA, GLES20.GL_UNSIGNED_BYTE, null);
//            mFrameBufferId = GlUtil.generateFrameBuffer(mTextureId);
//        }
//        if (sharedAvatarTextureId == 0) {
//            GLES20.glActiveTexture(GLES20.GL_TEXTURE0);
//            sharedAvatarTextureId = GlUtil.generateTexture(GLES20.GL_TEXTURE_2D);
//            GLES20.glTexImage2D(GLES20.GL_TEXTURE_2D, 0, GLES20.GL_RGBA, width, height, 0, GLES20.GL_RGBA, GLES20.GL_UNSIGNED_BYTE, null);
//        }
//        if (sharedAvatarTextureId2 == 0) {
//            GLES20.glActiveTexture(GLES20.GL_TEXTURE0);
//            sharedAvatarTextureId2 = GlUtil.generateTexture(GLES20.GL_TEXTURE_2D);
//            GLES20.glTexImage2D(GLES20.GL_TEXTURE_2D, 0, GLES20.GL_RGBA, width, height, 0, GLES20.GL_RGBA, GLES20.GL_UNSIGNED_BYTE, null);
//        }
//    }
//
//    private void checkDelOutTextureId() {
//        if (mTextureId != 0) {
//            int[] textures = new int[]{mTextureId};
//            GLES20.glDeleteTextures(1, textures, 0);
//            mTextureId = 0;
//        }
//
//        if (sharedAvatarTextureId != 0) {
//            int[] textures = new int[]{sharedAvatarTextureId};
//            GLES20.glDeleteTextures(1, textures, 0);
//            sharedAvatarTextureId = 0;
//        }
//
//        if (sharedAvatarTextureId2 != 0) {
//            int[] textures = new int[]{sharedAvatarTextureId2};
//            GLES20.glDeleteTextures(1, textures, 0);
//            sharedAvatarTextureId2 = 0;
//        }
//
//        if (mFrameBufferId != 0) {
//            int[] frameBuffers = new int[]{mFrameBufferId};
//            GLES20.glDeleteFramebuffers(1, frameBuffers, 0);
//            mFrameBufferId = 0;
//        }
//    }
//
//    private int sharedAvatarTextureId = 0;
//    private int sharedAvatarTextureId2 = 0;
//
//    private int getCurrentOrientation() {
//        int dir = Accelerometer.getDirection();
//        int orientation = dir - 1;
//        if (orientation < 0) {
//            orientation = dir ^ 3;
//        }
//        return orientation;
//    }
//
//    public void onResume() {
//        if (cameraNeedOpen()) {
//            startCamera();
//        }
//    }
//
//    public void onPause() {
//        ThreadUtil.runOnChildThread(new Runnable() {
//            @Override
//            public void run() {
//                stopCamera(true);
//            }
//        });
//    }
//}
