SQLite format 3   @    �             D                                               � -�)   �    ��� �              m�%tableroom_inforoom_infoCREATE TABLE room_info (rid integer  PRIMARY KEY AUTOINCREMENT DEFAULT N   P++Ytablesqlite_sequencesqlite_sequenceCREATE TABLE sqlite_sequence(name,seq)�]�tableuseruserCREATE TABLE user (uid INTEGER  PRIMARY KEY AUTOINCREMENT DEFAULT 0,email Varchar(50) DEFAULT NULL,nickname Varchar(50) DEFAULT NULL,gender Smallint DEFAULT 0,headimgurl Varchar(100),sina_uid integer)�[
�
tablemessagemessageCREATE TABLE message (mid Varchar(64),msg_content TEXT,send_uid integer,recv_uid integer,time Timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,status Smallint DEFAULT 0,type Smallint  DEFAULT 20)��tabletalktalkCREATE TABLE talk (user integer  PRIMARY KEY DEFAULT NULL,unread_count integer  DEFAULT 0,last_message Varchar DEFAULT NULL)j%%�tablegame_messagegame_messageCREATE TABLE game_message (serial integer  Primary Key Auto   	0   .
   ' '�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            e W !% 	 fr  ] !C 	 法官http://wespy.u.qiniudn.com/faguan_v2.png2014-05-27直辖市北京出来混迟早是要还的c  W !% 	 fun团队http://wespy.u.qiniudn.com/fun_v1.png2014-05-27直辖市北京Just for fun
      �����                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    � 3new_friend_record� 	db_version   blockuser   room_info   		user
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
    5  5 ����6++YP++Ytablesqlite_sequP++Ytablesqlite_sequencesqlite_sequenceCREATE TABLE sqlite_sequence(name,seq)��tabletalktalkCREATE TABLE talk (user integer  PRIMARY KEY DEFAULT NULL,unread_count integer  DEFAULT 0,last_message Varchar DEFAULT NULL)�##�Qtablefriend_infofriend_info
CREATE TABLE friend_info (user integer  PRIMARY KEY DEFAULT NULL,remark_name integer DEFAULT NULL)�(�YtablemessagemessageCREATE TABLE message (mid Varchar(64)  PRIMARY KEY DEFAULT NULL,content TEXT DEFAULT NULL,send_uid integer,recv_uid integer,time Timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,status Smallint DEFAULT 0,media_type integer DEFAULT 1)-)A indexsqlite_autoindex_message_1message^,�tableblockuserblockuserCREATE TABLE blockuser (user integer  PRIMARY KEY DEFAULT NULL)�G.//�=tablenew_friend_recordnew_friend_recordCREATE TABLE new_friend_record (user integer  PRIMARY KEY DEFAULT NULL,type integer DEFAULT NULL,time integer DEFAULT NULL,content Varchar DEFAULT NULL)
   ! !�� �- �  r))�tablesystem_messagesystem_messageCREATE TABLE system_message (serial i-)A indexsqlite_autoindex_message_1message  ��ytableroom_inforoom_infoCREATE TABLE room_info (rid integer  PRIMARY KEY   �@/�Otableroom_inforoom_infoCREATE TABLE room_info �@/�Otableroom_inforoom_infoCREATE TABLE room_info (rid integer  PRIMARY KEY DEFAULT NULL,name Varchar,gamer_limit integer DEFAULT NULL,now_speaker_num INTEGER DEFAULT NULL,state integer,word_pingmin Varchar DEFAULT NULL,word_wodi Varchar DEFAULT NULL,last_game_message Varchar DEFAULT NULL,start_speaker_num integer DEFAULT NULL,game_type integer DEFAULT 1,allow_enter integer DEFAULT 1,owner integer  DEFAULT 0,unread_count integer  DEFAULT 0)�0%%�utablegame_messagegame_messageCREATE TABLE game_message (mid Varchar  PRIMARY KEY DEFAULT NULL,content Varchar DEFAULT NULL,uid integer,rid integer,status integer,type integer,time integer,num Smallint DEFAULT -1,room_state Smallint  DEFAULT -1,media_type integer DEFAULT 1)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
   � �T ��                                                                                                                                                                             �3�tablecontactcontactCREATE TABLE contact (phone Varchar  PRIMARY KEY DEFAULT NULL,name Varchar,avatar_url Varchar,uid integer,status integer)�)2�5tableuseruserCREATE TABLE user (uid INTEGER  PRIMARY KEY DEFAULT 0,email Varchar(50) DEFAULT NULL,nickname Varchar(50) DEFAULT NULL,gender Smallint DEFAULT 0,headimgurl Varchar(100),sina_uid integer,birthday Date DEFAULT NULL,province Varchar DEFAULT NULL,city Varchar DEFAULT NULL,signature integer,wdid Varchar(50) DEFAULT NULL,wdid_edit_num Smallint DEFAULT 0,version integer DEFAULT 1, phone Varchar DEFAULT NULL)� �A indexsqlite_autoindex_contact_1contact   ��tablecontactcontactCREATE TABLE contact (phone_num Varchar  NOT NULL  PRIMARY KEY,name Var-4A indexsqlite_autoindex_contact_1contact   71K% indexsqlite_autoindex_game_message_1game_message

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             