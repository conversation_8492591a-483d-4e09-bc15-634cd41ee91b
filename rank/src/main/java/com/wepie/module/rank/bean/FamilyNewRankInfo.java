package com.wepie.module.rank.bean;

/**
 * Created by <PERSON><PERSON> on 2019-10-21.
 */
public class FamilyNewRankInfo implements FamilyNewRankInterface {

    private int id;
    private String name = "";
    private String avatar = "";
    private int avatar_frame_id;
    private int level;
    private int leader_uid;
    private String leader_name = "";
    private String announcement = "";
    private int member_count;
    private int member_count_limit;
    private int rank;
    private int active_score;

    private int week_rank;
    private int week_active_score;

    @Override
    public String getFamilyName() {
        return name;
    }

    @Override
    public int getActiveValue() {
        return active_score;
    }

    @Override
    public int getCurMemberNum() {
        return member_count;
    }

    @Override
    public int getTotalMemberNum() {
        return member_count_limit;
    }

    @Override
    public int getLevel() {
        return level;
    }

    @Override
    public String getFamilyAvatar() {
        return avatar;
    }

    @Override
    public int getFamilyId() {
        return id;
    }

    @Override
    public int getRank() {
        return rank;
    }

    @Override
    public int getAvatarFrameId() {
        return avatar_frame_id;
    }

    public int getWeekRank() {
        return week_rank;
    }

    public int getTotalRank() {
        return rank;
    }

    public int getWeekActive() {
        return week_active_score;
    }

    public int getTotalActive() {
        return active_score;
    }
}
