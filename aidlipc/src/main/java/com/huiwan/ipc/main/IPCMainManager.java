package com.huiwan.ipc.main;

import android.app.Service;
import android.content.Context;

import androidx.annotation.NonNull;

import com.huiwan.ipc.IPCInterface;
import com.huiwan.ipc.IPCMain;
import com.huiwan.ipc.IPCUtil;
import com.huiwan.ipc.ReqCallback;
import com.huiwan.ipc.model.IPCMsg;
import com.huiwan.ipc.service.BaseService;
import com.huiwan.ipc.service.ConnectorCallback;
import com.huiwan.ipc.service.RemoteConnection;
import com.huiwan.ipc.service.ServiceUtil;
import com.huiwan.platform.ThreadUtil;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class IPCMainManager {

    private long seq = 1;
    private static IPCMainManager instance;
    private IPCMainInterface ipcMainInterface;
    private final Map<Long, ReqCallback> callbackMap = new ConcurrentHashMap<>();
    private final Map<String, RemoteConnection> connectionMap = new ConcurrentHashMap<>();

    private IPCMainManager() {

    }

    public static IPCMainManager getInstance() {
        if (instance == null) instance = new IPCMainManager();
        return instance;
    }

    public void setUriProcessor(IPCMainInterface uriProcessor) {
        this.ipcMainInterface = uriProcessor;
    }

    void receiveMsg(String json) {
        IPCMsg msg = IPCUtil.getGson().fromJson(json, IPCMsg.class);
        if (ipcMainInterface != null) ipcMainInterface.handlerMsg(msg);
        IPCUtil.print("IPCLocalManager: receiveMsg: " + json);
    }

    public void handlerCallback(IPCMsg msg) {
        ReqCallback callback = callbackMap.remove(msg.getReqId());
        if (callback != null) {
            if (msg.getRspData() == null || msg.getRspData().code != IPCMsg.CODE_SUCCESS) {
                callback.onError(200, "fail");
            } else {
                callback.onSuccess(msg);
            }
        }
    }

    public void sendReq(String uri, String reqData, @NonNull Class<? extends Service> remoteService, ReqCallback callback) {
        long seq = getSeq();
        IPCMsg msg = new IPCMsg(IPCMsg.TYPE_REQ, uri, seq, reqData);
        if (callback != null)
            callbackMap.put(seq, callback);
        sendMsg(msg, remoteService);
    }

    public void sendRsp(IPCMsg msg) {
        msg.setType(IPCMsg.TYPE_RSP);
        Class<? extends Service> service = IPCUtil.getService(msg.getSenderService());
        if (service != null) {
            sendMsg(msg, service);
        } else {
            IPCUtil.print("rsp SendService = null");
        }
    }

    private long getSeq() {
        return seq++;
    }

    public void bind(Class<? extends Service> remoteService) {
//        Context context = IPCMain.getApplicationContext();
//        ServiceUtil.startService(context, remoteService);
//
//        String serviceName = remoteService.getName();
//        unbindRemote(serviceName);
//        RemoteConnection remoteConnection = new RemoteConnection(remoteService, new ConnectorCallback() {
//            @Override
//            public void onConnected() {
//                if (ipcMainInterface != null) {
//                    ipcMainInterface.onConnected(remoteService.getName());
//                }
//            }
//
//            @Override
//            public void onDisconnect(Class<? extends Service> service) {
//                if (ipcMainInterface != null) {
//                    ipcMainInterface.disconnect(service.getName());
//                }
//            }
//        });
//        connectionMap.put(serviceName, remoteConnection);
//        ServiceUtil.bindService(context, remoteConnection, remoteService);
    }

    public void unbindRemote(String serviceName) {
        if (connectionMap.containsKey(serviceName)) {
            RemoteConnection remoteConnection = connectionMap.get(serviceName);
            if (remoteConnection != null) {
                ServiceUtil.unbindService(IPCMain.getApplicationContext(), remoteConnection);
            }
            connectionMap.remove(serviceName);
        }
    }

    public void sendMsg(IPCMsg msg, @NonNull Class<? extends Service> remote) {
        if (msg.getType() == IPCMsg.TYPE_REQ) {
//            Class<? extends BaseService> mainService = ServiceUtil.getMainService();
//            if (mainService != null) {
//                msg.setSenderService(mainService.getName());
//            } else {
//                IPCUtil.print("mainService = null");
//            }
        }
        sendMsgRetry(msg, remote, true);
    }

    private void sendMsgRetry(final IPCMsg msg, @NonNull final Class<? extends Service> remote, final boolean retry) {
//        final String json = IPCUtil.getGson().toJson(msg);
//        try {
//            String serviceName = remote.getName();
//            IPCInterface ipcInterface = null;
//            if (connectionMap.containsKey(serviceName)) {
//                RemoteConnection remoteConnection = connectionMap.get(serviceName);
//                if (remoteConnection != null) {
//                    ipcInterface = remoteConnection.getAidlInterface();
//                }
//            }
//            if (ipcInterface != null) {
//                ipcInterface.handleMsg(json);
//            } else {
//                if (retry) {
//                    bind(remote);
//                    ThreadUtil.runOnUiThreadDelay(300, () -> sendMsgRetry(msg, remote, false));
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }
}
