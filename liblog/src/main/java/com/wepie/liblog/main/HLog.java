package com.wepie.liblog.main;

import android.util.Log;

import androidx.annotation.IntDef;

import com.welib.alinetlog.AliLog;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.LogUtil;

import org.json.JSONObject;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class HLog {
    private static final int VERBOSE = Log.VERBOSE;//调试日志
    private static final int DEBUG = Log.DEBUG;//调试日志
    private static final int INFO = Log.INFO;//关键信息日志
    private static final int WARN = Log.WARN;//警告日志
    private static final int ERROR = Log.ERROR;//错误日志

    public static final int CLR = 10000;// 普通日志
    public static final int USR = 10001;// 用户级别日志，会写入文件

    private static ILogger logger;

    public static void init(ILogger logger) {
        HLog.logger = logger;
    }

    public static void d(String tag, String msg, Object... args) {
        LogUtil.d(tag, CLR, DEBUG, msg, args);
    }

    public static void d(String tag, @UserLevel int userLevel, String msg, Object... args) {
        LogUtil.d(tag, userLevel, DEBUG, msg, args);
    }

    public static void i(String tag, String msg, Object... args) {
        LogUtil.d(tag, CLR, INFO, msg, args);
    }

    public static void i(String tag, @UserLevel int userLevel, String msg, Object... args) {
        LogUtil.d(tag, userLevel, INFO, msg, args);
    }

    public static void w(String tag, String msg, Object... args) {
        LogUtil.d(tag, CLR, WARN, msg, args);
    }

    public static void w(String tag, @UserLevel int userLevel, String msg, Object... args) {
        LogUtil.d(tag, userLevel, WARN, msg, args);
    }

    public static void e(String tag, String msg, Object... args) {
        LogUtil.d(tag, CLR, ERROR, msg, args);
    }

    public static void e(String tag, @UserLevel int userLevel, String msg, Object... args) {
        LogUtil.d(tag, userLevel, ERROR, msg, args);
    }

    public static void e(String tag, Throwable throwable, String msg) {
        if (logger != null) {
            logger.onUploadLog(throwable, tag + " " + msg);
        }
        LogUtil.d(tag, USR, ERROR, msg + "\n" + Log.getStackTraceString(throwable));
    }

    public static void aliLog(AliNetLogUtil.PORT port, String name, AliNetLogUtil.TYPE type, String msg) {
        aliLog(port, name, type, msg, Collections.emptyMap());
    }

    public static void aliLog(AliNetLogUtil.PORT port, String name, AliNetLogUtil.TYPE type, String msg, Map<String, String> map) {
        AliNetLogUtil.upload(AliLog.obtain().setPort(port).setType(type).setName(name).setMsg(msg).setExtMap(map));
    }

    // 统计类上报
    public static void aliLog(AliNetLogUtil.PORT port, AliNetLogUtil.TYPE type, String msg) {
        AliNetLogUtil.upload(AliLog.obtain().setPort(port).setType(type).setMsg(msg));
    }

    public static void aliLog(AliNetLogUtil.PORT port, String name, AliNetLogUtil.TYPE type, JSONObject json) {
        AliNetLogUtil.upload(AliLog.obtain().setPort(port).setType(type).setName(name).setJsonObject(json));
    }

    public static void aliLog(AliNetLogUtil.PORT port, AliNetLogUtil.TYPE type, String msg, Map<String, String> extMap) {
        AliNetLogUtil.upload(AliLog.obtain().setPort(port).setType(type).setMsg(msg).setExtMap(extMap));
    }

    public static void aliPerformance(String moduleName, String operation, boolean success, long duration, String tip, Map<String, String> ext) {
        AliNetLogUtil.TYPE type = success ? AliNetLogUtil.TYPE.normal : AliNetLogUtil.TYPE.err;
        Map<String, String> map = new HashMap<>();
        map.put("name", moduleName);
        map.put("operation", operation);
        map.put("success", String.valueOf(success));
        map.put("event_duration", String.valueOf(duration));
        map.putAll(ext);
        aliLog(AliNetLogUtil.PORT.performance, type, tip, map);
    }

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({CLR, USR})
    public @interface UserLevel {
    }

    public interface ILogger {
        void onUploadLog(Throwable throwable, String msg);
    }
}