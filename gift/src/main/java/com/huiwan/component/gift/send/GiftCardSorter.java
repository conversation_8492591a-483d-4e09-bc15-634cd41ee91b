package com.huiwan.component.gift.send;

import java.util.Comparator;

public class Gift<PERSON>ardSorter implements Comparator<GiftCellData> {
    @Override
    public int compare(GiftCellData data1, GiftCellData data2) {
        GiftCard o1 = data1.giftCard;
        GiftCard o2 = data2.giftCard;
        if (o1 == null && o2 == null) {
            return 0;
        }
        if (o2 == null) {
            return -1;
        }
        if (o1 == null) {
            return 1;
        }
        if (o1.isPermanent() && o2.isPermanent()) {
            return 0;
        }
        if (o2.isPermanent()) {
            return -1;
        }
        if (o1.isPermanent()) {
            return 1;
        }
        return (int) (o1.getExpireTimeInSec() - o2.getExpireTimeInSec());
    }
}
