package com.huiwan.component.gift.show.anim

import android.view.ViewGroup
import com.huiwan.component.gift.show.ICommonAnimData
import com.huiwan.component.gift.show.IGiftAudioPlayer
import com.huiwan.component.gift.show.OnAnimListener
import java.util.Queue
import java.util.concurrent.LinkedBlockingQueue

data class GiftAnimData(
    val showInfo: ICommonAnimData,
    val player: IGiftAudioPlayer?,
    val listener: OnAnimListener,
    var canEnterQueue : Boolean = true
)

interface IGiftAnimController {

    fun filter(showInfo: ICommonAnimData): Boolean

    fun show(data: GiftAnimData)

    fun canPlayNow(): Boolean

    fun weight(): Int

    fun recycle(holder: GiftAnimViewHolder<*>)

    fun clear()
}

open class GiftAnimController(
    protected val parent: ViewGroup,
    val type: String,
    private val scrap: GiftAnimScrap? = null,
    private val pool: GiftAnimPool = GiftAnimPool(),
    private val factory: IGiftAnimViewFactory<*>,
    private var maxNum: Int = 10
) : IGiftAnimController {

    protected val queue: Queue<GiftAnimData> = LinkedBlockingQueue()

    init {
        scrap?.attach(type, pool)
    }

    @Volatile
    protected var runningCount = 0

    override fun filter(showInfo: ICommonAnimData): Boolean = factory.filter(showInfo)

    override fun show(data: GiftAnimData) {
        if (runningCount >= maxNum && data.canEnterQueue) {
            queue.add(data)
            return
        }
        showAnim(data)
    }

    override fun canPlayNow(): Boolean {
        return runningCount < maxNum
    }

    override fun weight(): Int = 1

    override fun recycle(holder: GiftAnimViewHolder<*>) {
        runningCount -= 1
        val data = queue.poll()
        if (data != null) {
            showAnim(data, holder)
        } else {
            if (scrap != null) {
                scrap.recycle(holder)
            } else {
                pool.recycle(holder)
            }
        }
    }

    override fun clear() {
        scrap?.clear()
        pool.clear()
    }

    protected fun showAnim(data: GiftAnimData) {
        val holder = getViewHolder()
        showAnim(data, holder)
    }

    protected open fun showAnim(
        data: GiftAnimData,
        holder: GiftAnimViewHolder<*>,
        hasRegister: Boolean = false
    ) {
        if (!hasRegister) {
            holder.registerOnAnimListener(object : OnGiftAnimListener {
                override fun onAnimStart(showInfo: ICommonAnimData?) {
                }

                override fun onAnimEnd(showInfo: ICommonAnimData?) {
                    data.listener.onAnimEnd(showInfo)
                    recycle(holder)
                }

                override fun onAnimFailed(showInfo: ICommonAnimData?) {
                    onAnimFailed(data, holder)
                }
            })
        }

        runningCount += 1
        holder.showAnim(parent, data)
    }

    protected open fun getViewHolder(): GiftAnimViewHolder<*> {
        var holder: GiftAnimViewHolder<*>? = scrap?.obtain(type)
        if (holder == null) {
            holder = pool.obtain()
        }
        return holder ?: factory.create()
    }

    protected fun onAnimFailed(data: GiftAnimData, oldHolder: GiftAnimViewHolder<*>) {
        val holder = NormalGiftAnimViewHolder()
        holder.registerOnAnimListener(object : OnGiftAnimListener {
            override fun onAnimFailed(showInfo: ICommonAnimData?) {
                data.listener.onFailed(showInfo)
                recycle(oldHolder)
            }

            override fun onAnimStart(showInfo: ICommonAnimData?) {

            }

            override fun onAnimEnd(showInfo: ICommonAnimData?) {
                data.listener.onAnimEnd(showInfo)
                recycle(oldHolder)
            }
        })
        runningCount -= 1
        showAnim(data, holder, true)
    }
}