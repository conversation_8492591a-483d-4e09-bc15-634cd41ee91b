package com.huiwan.component.gift.send

import androidx.lifecycle.ViewModel
import com.huiwan.base.RecyclerViewExt

class GiftViewModel : ViewModel() {

    val pool: RecyclerViewExt.RecycledViewPool by lazy {
        RecyclerViewExt.RecycledViewPool().also {
            it.setMaxRecycledViews(0, Int.MAX_VALUE)
//            it.attachExt(ex)
        }
    }

    val ex = RecyclerViewExt()

    fun onRecycled() {
//        ex.recycler(pool)
    }
}