package com.huiwan.component.gift.send.ar

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.component.gift.R
import com.huiwan.component.gift.send.GiftCellData
import com.huiwan.configservice.ConfigHelper
import com.huiwan.user.LoginHelper
import com.huiwan.widget.image.DrawableUtil
import com.huiwan.widget.rv.RVHolder
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader

/**
 * 真实的每一页礼物
 */
class GiftItemAdapter(private val giftViewAr: GiftViewAr) :
    RecyclerView.Adapter<GiftItemAdapter.ItemHolder>(), View.OnClickListener {
    private var dataList: MutableList<GiftCellData> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemHolder {
        val inflater = LayoutInflater.from(parent.context)
        val v = inflater.inflate(R.layout.send_gift_dialog_gifts_item_view, parent, false)
        return ItemHolder(v)
    }

    override fun onBindViewHolder(holder: ItemHolder, position: Int) {
        val cellData = dataList[position]
        holder.itemView.tag = holder
        holder.itemView.setOnClickListener(this)
        val isSelect = cellData == giftViewAr.selectedGift
        holder.bind(cellData, isSelect, giftViewAr)
        if (isSelect) {
            giftViewAr.checkUpdateSelectedItem(this, holder)
        }
    }

    override fun getItemCount(): Int = dataList.size

    override fun onClick(v: View?) {
        val holder = v?.tag
        if (holder is ItemHolder) {
            giftViewAr.cellItemClick(this, holder)
        }
    }

    fun refresh(newDataList: List<GiftCellData>) {
        if (dataList.isNotEmpty()) {
            dataList.clear()
        }
        dataList.addAll(newDataList)
        notifyItemRangeInserted(0, dataList.size)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun initData(list: List<GiftCellData>) {
        if (dataList.isEmpty()) {
            dataList.addAll(list)
            notifyItemRangeInserted(0, list.size)
        } else if (list != dataList) {
            refresh(list)
        } else {
            notifyDataSetChanged()
        }
    }

    class ItemHolder(v: View) : RVHolder(v) {
        companion object {

            private val color = ResUtil.getColor(R.color.white_alpha15)
            private val vipSymbolTvColor = Color.parseColor("#9A6018")
        }

        private val giftIv: ImageView = v.findViewById(R.id.gift_iv)
        private val nameTv: TextView = v.findViewById(R.id.gift_tv)
        private val priceLay: ViewGroup = v.findViewById(R.id.price_lay)
        private val priceIv:View = v.findViewById(R.id.price_iv)
        private val priceTv: TextView = v.findViewById(R.id.price_tv)
        private val symbolTv: TextView = v.findViewById(R.id.symbol_tv)
        private val bgView: View = v.findViewById(R.id.bg_view)
        private val tipTv: TextView = v.findViewById(R.id.tip_tv)
        private var animator: ValueAnimator? = null

        lateinit var cellData: GiftCellData
        fun bind(data: GiftCellData, isSelect: Boolean, giftViewAr: GiftViewAr) {
            this.cellData = data
            if (data.isVipCard) {
                priceLay.visibility = View.GONE
                nameTv.setText(R.string.vip_card)
                giftIv.setImageResource(R.drawable.gift_vip_card)
                symbolTv.visibility = View.GONE
            } else {
                val gift = data.gift
                nameTv.text = gift.name
                WpImageLoader.load(gift.media_url, giftIv, ImageLoadInfo.getGiftInfo())
                refreshSelect(isSelect)
                refreshBottom(data)
                refreshSymbol(data, giftViewAr.showVipCard)
                refreshCanSend(data, giftViewAr)
            }
        }

        fun refreshSelect(isSelect: Boolean) {
            if (isSelect) {
                bgView.background = DrawableUtil.genColorRadius(color, 12)
                nameTv.ellipsize = TextUtils.TruncateAt.MARQUEE
                nameTv.requestFocus()
                tipTv.ellipsize = TextUtils.TruncateAt.MARQUEE
                tipTv.requestFocus()
            } else {
                bgView.background = ColorDrawable(Color.TRANSPARENT)
                nameTv.ellipsize = TextUtils.TruncateAt.END
                nameTv.clearFocus()
                tipTv.ellipsize = TextUtils.TruncateAt.END
                tipTv.clearFocus()
            }
        }

        private fun refreshSymbol(data: GiftCellData, showVipCard: Boolean) {
            val gift = data.gift
            if (data.isGiftCard) {
                itemView.visibility = if (showVipCard) View.VISIBLE else View.INVISIBLE
                symbolTv.setBackgroundResource(R.drawable.gift_symbol_card_te)
                symbolTv.setTextColor(Color.WHITE)
                symbolTv.text = ResUtil.getStr(R.string.num_x_d, data.cardNum)
                symbolTv.visibility = View.VISIBLE
            } else if (data.isCurrentLotto) {
                val tag = ConfigHelper.getInstance().giftConfig.endorsement.lottoDesc
                symbolTv.text = tag
                symbolTv.setBackgroundResource(R.drawable.gift_tag_bg_lotto)
                symbolTv.setTextColor(Color.WHITE)
                symbolTv.visibility = View.VISIBLE
            } else {
                val tag: String? = gift.tag
                if (!TextUtils.isEmpty(tag)) {
                    symbolTv.visibility = View.VISIBLE
                    if (gift.isVipGift) {
                        symbolTv.setBackgroundResource(R.drawable.gift_symbol_vip_te)
                        symbolTv.setTextColor(vipSymbolTvColor)
                    } else {
                        symbolTv.setTextColor(Color.WHITE)
                        symbolTv.setBackgroundResource(R.drawable.gift_symbol_card_te)
                    }
                    symbolTv.text = tag
                } else {
                    symbolTv.visibility = View.GONE
                }
            }
        }

        private fun refreshBottom(data: GiftCellData) {
            if (data.isGiftCard) {
                priceLay.visibility = View.GONE
                if (data.isGiftCardNotPermanent) {
                    tipTv.visibility = View.VISIBLE
                    tipTv.text = data.expireTimeString
                } else {
                    tipTv.visibility = View.GONE
                }
            } else {
                priceLay.visibility = View.VISIBLE
                tipTv.visibility = View.GONE
                priceTv.text = data.gift.price.toString()
                val coinRes = if (data.gift.isChipGift) R.drawable.game_chip else R.drawable.icon_small_coin
                priceIv.setBackgroundResource(coinRes)
            }
        }

        private fun refreshCanSend(data: GiftCellData, giftViewAr: GiftViewAr) {
            val gift = data.gift
            val cannotSend = !data.canSend || !gift.enableUseVipGift(LoginHelper.getVipLevel())
                    || GiftCellDataClickHelper.userDefinedGiftLevelNotEnough(gift) || gift.isLocked
                    || !giftViewAr.giftCanSendInScope(gift, data.isGiftCard)
            if (cannotSend) {
                if (giftIv.colorFilter == null) {
                    val cm = ColorMatrix()
                    cm.setSaturation(0f)
                    val grayColorFilter = ColorMatrixColorFilter(cm)
                    giftIv.colorFilter = grayColorFilter
                }
            } else {
                giftIv.colorFilter = null
            }
        }

        fun showSelectAnim() {
            showNormalAnim()
        }

        private fun showNormalAnim() {
            // 200ms 不考虑内存泄漏

            val xAnim = ObjectAnimator.ofFloat(giftIv, "scaleX", 1.0f, 1.15f, 1.0f)
            xAnim.setAutoCancel(true)
            xAnim.duration = 200
            xAnim.start()

            val yAnim = ObjectAnimator.ofFloat(giftIv, "scaleY", 1.0f, 1.15f, 1.0f)
            yAnim.setAutoCancel(true)
            yAnim.duration = 200
            yAnim.start()
        }

        /**
         * 设计说暂时不用这个动效了。
         */
        private fun showBilibiliSelectAnim() {
            animator?.cancel()
            val anim = ValueAnimator.ofInt(0, 1000)
            anim.duration = 200
            anim.addUpdateListener {
                val fraction = it.animatedFraction
                if (fraction < 0.3) {
                    itemView.scaleY = 1 + fraction / 0.3f * 0.1f
                } else if (fraction < 0.6) {
                    itemView.scaleY = 1 + (0.6f - fraction) / 0.3f * 0.1f
                } else {
                    itemView.scaleY = 1f
                }
                if (fraction < 0.5) {
                    itemView.scaleX = 1 + fraction / 0.5f * 0.5f / 0.3f * 0.1f
                } else {
                    itemView.scaleX = 1 + (1 - fraction) / 0.5f * 0.5f / 0.3f * 0.1f
                }
            }
            anim.start()
            animator = anim
        }
    }
}