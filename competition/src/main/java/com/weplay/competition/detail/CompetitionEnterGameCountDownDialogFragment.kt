package com.weplay.competition.detail

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import com.weplay.competition.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.Continuation

class CompetitionEnterGameCountDownDialogFragment : BaseDialogFragment() {

    private val viewModel: CompetitionDetailViewModel by lazy {
        ViewModelProvider(requireActivity())[CompetitionDetailViewModel::class.java]
    }

    private lateinit var continuation: Continuation<Int>

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(
            R.layout.fragment_competition_enter_game_countdown, container, false
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isCancelable = false
        val tv = view.findViewById<TextView>(R.id.competition_count_down_tv)
        lifecycleScope.launch {
            for (i in 0 until 3) {
                tv.text = "${3 - i}s"
                delay(1000)
            }
            continuation.resumeWith(Result.success(0))
            dismissAllowingStateLoss()
        }

        val iv = view.findViewById<View>(R.id.competition_loading_iv)
        val animation = RotateAnimation(
            0f, 360f,
            Animation.RELATIVE_TO_SELF, 0.5f,
            Animation.RELATIVE_TO_SELF, 0.5f
        ).apply {
            duration = 1000
            repeatCount = Animation.INFINITE
            repeatMode = Animation.RESTART
        }
        iv.startAnimation(animation)

        lifecycleScope.launch {
            viewModel.eventFlow.collect { event ->
                if (event is ICompetitionDetailEvent.IdChanged) {
                    dismissAllowingStateLoss()
                }
            }
        }
    }

    companion object {
        suspend fun show(activity: FragmentActivity) = suspendCancellableCoroutine {
            val fragment = CompetitionEnterGameCountDownDialogFragment()
            fragment.initFullWidth()
            fragment.isCancelable = false
            fragment.continuation = it
            fragment.show(activity)
        }
    }
}