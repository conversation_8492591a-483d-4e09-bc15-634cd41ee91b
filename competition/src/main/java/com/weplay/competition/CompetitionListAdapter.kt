package com.weplay.competition

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.DiffUtil.DiffResult
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.weplay.competition.databinding.CompetitionListEmptyPageBinding
import com.weplay.competition.databinding.CompetitionListTitleLayBinding
import com.weplay.competition.databinding.CompetitionSimpleCardLayBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


internal class CompetitionListAdapter(
    private val rv: RecyclerView,
    private val competitionAction: ICompetitionCardAction
) : RecyclerView.Adapter<CompetitionCardVh>() {
    private val payloadUtl = CompetitionListPayloadUtil()
    private var dataList: List<CompetitionListItemInfo> = ArrayList()
    private val ioScope = CoroutineScope(Dispatchers.IO)
    private var updateDataJpb: Job? = null
    private val visiblePositionRange: Pair<Int, Int>
        get() {
            val layoutManager = rv.layoutManager as? LinearLayoutManager
                ?: return RecyclerView.NO_POSITION to RecyclerView.NO_POSITION
            return layoutManager.findFirstVisibleItemPosition() to layoutManager.findLastVisibleItemPosition()
        }

    fun refresh(newDataList: List<CompetitionListItemInfo>, onFinishRefresh: () -> Unit) {
        if (updateDataJpb?.isActive == true) {
            updateDataJpb?.cancel()
        }
        updateDataJpb = ioScope.launch {
            val diffResult = diffFrom(dataList, newDataList)
            withContext(Dispatchers.Main) {
                dataList = newDataList
                diffResult.dispatchUpdatesTo(this@CompetitionListAdapter)
                onFinishRefresh()
            }
        }
        updateDataJpb?.invokeOnCompletion {
            updateDataJpb = null
        }
    }

    fun tryScrollToTargetCompetition(competitionId: Int) {
        val targetPosition =
            dataList.indexOfFirst { it is CompetitionListItemInfo.CompetitionCardInfo && it.cid == competitionId }
        if (targetPosition >= 0) {
            rv.scrollToPosition(targetPosition)
        }
    }

    fun scrollToToTop() {
        if (dataList.isNotEmpty()) {
            rv.scrollToPosition(0)
        }
    }

    private fun diffFrom(
        oldDataList: List<CompetitionListItemInfo>,
        newDataList: List<CompetitionListItemInfo>
    ): DiffResult {
        return DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun getOldListSize(): Int = oldDataList.size

            override fun getNewListSize(): Int = newDataList.size

            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldItem = oldDataList[oldItemPosition]
                val newItem = newDataList[newItemPosition]
                if (oldItem.javaClass != newItem.javaClass) {
                    return false
                }
                return when (oldItem) {
                    is CompetitionListItemInfo.TitleInfo -> {
                        oldItem.tile == (newItem as CompetitionListItemInfo.TitleInfo).tile
                    }

                    is CompetitionListItemInfo.CompetitionCardInfo -> {
                        oldItem.cid == (newItem as CompetitionListItemInfo.CompetitionCardInfo).cid
                    }

                    is CompetitionListItemInfo.EmptyData -> {
                        true
                    }
                }
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                // 判断两个对象的内容是否相同
                return oldDataList[oldItemPosition] == newDataList[newItemPosition]
            }

            // 如果 areItemsTheSame 返回 true 但 areContentsTheSame 返回 false， 由此返回一个 payload 来指示哪些字段发生了变化，从而进行局部更新
            override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
                val oldItem = oldDataList[oldItemPosition]
                val newItem = newDataList[newItemPosition]
                return when (oldItem) {
                    is CompetitionListItemInfo.CompetitionCardInfo -> {
                        oldItem.getChangePayload(newItem as CompetitionListItemInfo.CompetitionCardInfo)
                    }

                    else -> null
                }
            }
        })
    }


    private fun CompetitionListItemInfo.CompetitionCardInfo.getChangePayload(newCardInfo: CompetitionListItemInfo.CompetitionCardInfo): Any {
        val payloadBuilder = payloadUtl.PayloadBuilder()
        if (newCardInfo.competitionState != competitionState) {
            payloadBuilder.addFlagCompetitionState().addFlagCountDown()
        }
        if (newCardInfo.competitionName != competitionName) {
            payloadBuilder.addFlagCompetitionName()
        }
        if (newCardInfo.bgImg != bgImg) {
            payloadBuilder.addFlagBgImg()
        }
        if (newCardInfo.signUpNum != signUpNum) {
            payloadBuilder.addFlagPeopleCount()
        }
        if (newCardInfo.playerState != playerState) {
            payloadBuilder.addFlagSignUpState()
        }
        if (newCardInfo.needPwd != needPwd) {
            payloadBuilder.addFlagPwdState()
        }
        if (newCardInfo.reward != reward) {
            payloadBuilder.addFlagRewardInfo()
        }
        if (newCardInfo.competitionScale != competitionScale) {
            payloadBuilder.addFlagCompetitionQualifyAndScale()
        }
        return payloadBuilder.build()
    }

    fun notifyTimeCountDown() {
        val (firstVisiblePos, lastVisiblePos) = visiblePositionRange
        val (start, end) = if (firstVisiblePos == RecyclerView.NO_POSITION) {
            return
        } else {
            firstVisiblePos to lastVisiblePos
        }
        genRefreshSubRangesForCountDownItem(start, end).forEach { (rangeStart, rangeEnd) ->
            notifyItemRangeChanged(
                rangeStart,
                rangeEnd - rangeStart + 1,
                payloadUtl.PayloadBuilder().addFlagCountDown().build()
            )
        }
    }

    private fun genRefreshSubRangesForCountDownItem(start: Int, end: Int): List<Pair<Int, Int>> {
        var rangeStart = -1
        var i = start
        val result = ArrayList<Pair<Int, Int>>()
        while (i <= end) {
            while (dataList.getOrNull(i)?.needShowCountDownTimer() == true) {
                if (rangeStart == -1) {
                    rangeStart = i
                }
                ++i
            }
            if (rangeStart >= 0) {
                result.add(rangeStart to i - 1)
            }
            rangeStart = -1
            ++i
        }
        return result
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CompetitionCardVh {
        return when (viewType) {
            VIEW_TYPE_SIMPLE_CARD_INFO -> {
                CompetitionCardVh.CompetitionListItemViewHolder(
                    CompetitionSimpleCardLayBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    ),
                    competitionAction
                )
            }

            VIEW_TYPE_TITLE -> {
                CompetitionCardVh.CompetitionTitleViewHolder(
                    CompetitionListTitleLayBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }

            else -> {
                CompetitionCardVh.EmptyViewVh(
                    CompetitionListEmptyPageBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }
        }
    }

    override fun onViewRecycled(holder: CompetitionCardVh) {
        holder.clear()
    }

    override fun onBindViewHolder(holder: CompetitionCardVh, position: Int) {
        holder.bindView(dataList[position])
    }

    override fun onBindViewHolder(
        holder: CompetitionCardVh, position: Int, payloads: List<Any?>
    ) {
        when (holder) {
            is CompetitionCardVh.CompetitionTitleViewHolder -> {
                onBindViewHolder(holder, position)
            }

            is CompetitionCardVh.CompetitionListItemViewHolder -> {
                holder.onBindViewHolder(position, payloads)
            }

            is CompetitionCardVh.EmptyViewVh -> {}

        }
    }

    private fun CompetitionCardVh.CompetitionListItemViewHolder.onBindViewHolder(
        position: Int,
        payloads: List<Any?>
    ) {
        if (payloads.isEmpty()) {
            onBindViewHolder(this, position)
        } else {
            val info = dataList[position]
            for (payload in payloads) {
                this.updateByPayload(info, payload)
            }
        }
    }

    private fun CompetitionCardVh.CompetitionListItemViewHolder.updateByPayload(
        info: CompetitionListItemInfo,
        payload: Any?
    ) {
        val cardInfo = info as? CompetitionListItemInfo.CompetitionCardInfo ?: return
        val validPayload = payload as? Int ?: return
        if (payloadUtl.hasFlagCountDown(validPayload)) {
            refreshCountDownUi(info)
        }
        if (payloadUtl.hasFlagCompetitionName(validPayload)) {
            refreshCompetitionName(cardInfo.competitionName)
        }
        if (payloadUtl.hasFlagPeopleCount(validPayload)) {
            refreshSignUpNumTv(cardInfo.signUpNum)
        }
        if (payloadUtl.hasFlagCompetitionState(validPayload) ||
            payloadUtl.hasFlagPwdState(validPayload) ||
            payloadUtl.hasFlagSignUpState(validPayload)
        ) {
            refreshStateBt(cardInfo)
        }
        if (payloadUtl.hasFlagBgImg(validPayload)) {
            refreshCompetitionCover(cardInfo.bgImg)
        }
        if (payloadUtl.hasFlagRewardInfo(validPayload)) {
            refreshRewardUi(info.reward)
        }
        if (payloadUtl.hasFlagCompetitionQualifyScale(validPayload)) {
            refreshQualifyAndScaleUi(cardInfo.toCompetitionQualifyAndScaleData())
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (dataList[position]) {
            is CompetitionListItemInfo.CompetitionCardInfo -> {
                VIEW_TYPE_SIMPLE_CARD_INFO
            }

            is CompetitionListItemInfo.TitleInfo -> {
                VIEW_TYPE_TITLE
            }

            else -> {
                VIEW_TYPE_EMPTY
            }
        }
    }

    override fun getItemCount(): Int = dataList.size

    companion object {
        private const val VIEW_TYPE_TITLE = 1
        private const val VIEW_TYPE_SIMPLE_CARD_INFO = 2
        private const val VIEW_TYPE_EMPTY = 3
    }

    val gridLayoutSpanLookUp = object : GridLayoutManager.SpanSizeLookup() {
        override fun getSpanSize(position: Int): Int {
            return when (getItemViewType(position)) {
                VIEW_TYPE_TITLE -> 2
                VIEW_TYPE_EMPTY -> 2
                VIEW_TYPE_SIMPLE_CARD_INFO -> 1
                else -> 1
            }
        }
    }
}