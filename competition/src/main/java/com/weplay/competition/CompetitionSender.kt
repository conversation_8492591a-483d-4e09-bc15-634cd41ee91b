package com.weplay.competition

import com.huiwan.base.LibBaseUtil
import com.huiwan.base.util.TextUtil
import com.huiwan.libtcp.PacketSendHelper
import com.huiwan.libtcp.callback.SeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.libtcp.sender.TcpBaseSender
import com.huiwan.libtcp.tcpSuspendSend
import com.huiwan.user.LoginHelper
import com.three.http.core.KtResult
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wepie.wespy.net.tcp.packet.CompetitionPackets
import com.wepie.wespy.net.tcp.packet.CompetitionPackets.AllowType
import com.wepie.wespy.net.tcp.packet.CompetitionPackets.BattleItem
import com.wepie.wespy.net.tcp.packet.CompetitionPackets.CompetitionOpType
import com.wepie.wespy.net.tcp.packet.CompetitionPackets.CompetitionStartType
import com.wepie.wespy.net.tcp.packet.HeadPackets
import com.wepie.wespy.net.tcp.packet.HeadPackets.ReqHead
import com.weplay.competition.create.CompetitionCreateViewModel

internal object CompetitionSender {
    const val COMPETITION_DETAILS = 1
    const val COMPETITION_EDIT_SEATING = 2

    fun heatBeat(gameType: Int) {
        val reqHead: ReqHead = buildReqHead(CompetitionOpType.CompetitionOpTypeHeartBeat_VALUE)

        val req = CompetitionPackets.HeartBeatReq.newBuilder()
            .setGameType(gameType)
            .build()

        val reqBody = CompetitionPackets.CompetitionReq.newBuilder()
            .setHeartBeat(req)
            .build()
        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, null)
    }

    suspend fun onlineAsk(gameType: Int, cid: Int): RspHeadInfo {
        val req = CompetitionPackets.OnlineAckReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeOnlineAck_VALUE) {
            it.onlineAck = req
        }
    }

    suspend fun reqCreate(info: CompetitionCreateViewModel.CompetitionCreateInfo): RspHeadInfo {
        val gameBasicInfo = CompetitionPackets.GameBasicInfo.newBuilder()
            .setLittleGameType(info.gameType)
            .setGameMode(info.gameMode)
            .setCurrencyType(info.currencyType)
            .build()
        val cmpPktBuilder = CompetitionPackets.CompetitionInfo.newBuilder()
            .setImage(info.bgImage)
            .setName(info.eventName)
            .setGameBasicInfo(gameBasicInfo)
        cmpPktBuilder.ownerUid = LoginHelper.getLoginUid()
        info.getRewardList().forEachIndexed { i, reward ->
            cmpPktBuilder.addWinReward(
                CompetitionPackets.WinReward.newBuilder()
                    .setRank(i + 1)
                    .setDiamond(reward)
                    .build()
            )
        }
        if (!info.noGradeLimit) {
            cmpPktBuilder.gradeLimit = info.gradeLimit.grade
        } else {
            cmpPktBuilder.clearGradeLimit()
        }
        if (info.useDetailTimeLimit) {
            cmpPktBuilder.startType = CompetitionStartType.CompetitionStartTypeTimed
            cmpPktBuilder.startTime = info.eventStartTimestampMs
        } else {
            cmpPktBuilder.startType = CompetitionStartType.CompetitionStartTypeRecent
        }
        cmpPktBuilder.userNum = info.seatNum
        cmpPktBuilder.needPwd = !TextUtil.isEmpty(info.pwd)
        cmpPktBuilder.pwd = info.pwd
        cmpPktBuilder.serviceFee = info.fee
        val req = CompetitionPackets.CreateReq.newBuilder()
            .setGameType(info.gameType)
            .setCompetitionInfo(cmpPktBuilder.build())
            .build()
        return suspendSend(CompetitionOpType.CompetitionOpTypeCreate_VALUE) {
            it.create = req
        }
    }

    suspend fun requestDetailInfo(gameType: Int, cid: Int): RspHeadInfo {
        val req = CompetitionPackets.DetailReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeDetail_VALUE) {
            it.detail = req
        }
    }

    suspend fun signup(gameType: Int, cid: Int, pswd: String, force: Boolean): RspHeadInfo {
        val req = CompetitionPackets.SignUpReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .setPwd(pswd)
            .setForce(force)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeSignUp_VALUE) {
            it.signUp = req
        }
    }

    suspend fun exitGame(gameType: Int, cid: Int): RspHeadInfo {
        val req = CompetitionPackets.ExitReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeExit_VALUE) {
            it.exit = req
        }
    }

    suspend fun startNow(gameType: Int, cid: Int): RspHeadInfo {
        val req = CompetitionPackets.StartNowReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeStartNow_VALUE) {
            it.startNow = req
        }
    }

    suspend fun modifySettings(
        gameType: Int,
        cid: Int,
        pswd: String,
        enableTyping: Boolean,
        enableVoice: Boolean,
        announcement: String
    ): RspHeadInfo {
        val req = CompetitionPackets.ModifyReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .setNeedPwd(pswd.isNotBlank())
            .setPwd(pswd)
            .setTextTalk(if (enableTyping) AllowType.AllowTypeAllow else AllowType.AllowTypeForbid)
            .setVoiceTalk(if (enableVoice) AllowType.AllowTypeAllow else AllowType.AllowTypeForbid)
            .setAnnounce(announcement)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeModify_VALUE) {
            it.modify = req
        }
    }

    suspend fun joinGroupChat(gameType: Int, cid: Int): RspHeadInfo {
        val req = CompetitionPackets.JoinCompetitionGroupReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeJoinCompetitionGroup_VALUE) {
            it.joinCompetitionGroup = req
        }
    }

    suspend fun editBattle(gameType: Int, cid: Int, battleList: List<BattleItem>): RspHeadInfo {
        val req = CompetitionPackets.EditBattleReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .addAllBattleList(battleList)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeEditBattle_VALUE) {
            it.editBattle = req
        }
    }

    fun getState(cid: Int, callback: SeqCallback) {
        val reqHead: ReqHead = buildReqHead(CompetitionOpType.CompetitionOpTypeGetState_VALUE)
        val req = CompetitionPackets.GetStateReq.newBuilder()
            .setCid(cid)
            .build()
        val reqBody = CompetitionPackets.CompetitionReq.newBuilder()
            .setGetState(req)
            .build()

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback)
    }

    suspend fun reqCompetitionList(gameType: Int): KtResult<CompetitionPackets.ListRsp> {
        return reqCompetitionListRaw(gameType).parse(CompetitionPackets.ListRsp::class.java)
    }

    private suspend fun reqCompetitionListRaw(gameType: Int): RspHeadInfo {
        val req = CompetitionPackets.ListReq.newBuilder()
            .setGameType(gameType)
            .build()
        return suspendSend(CompetitionOpType.CompetitionOpTypeList_VALUE) {
            it.list = req
        }
    }

    suspend fun batchReqCompetition(
        gameType: Int,
        cidList: List<Int>
    ): KtResult<CompetitionPackets.ListByCidsRsp> {
        return batchReqCompetitionRaw(
            gameType,
            cidList
        ).parse(CompetitionPackets.ListByCidsRsp::class.java)
    }

    private suspend fun batchReqCompetitionRaw(gameType: Int, cidList: List<Int>): RspHeadInfo {
        val req = CompetitionPackets.ListByCidsReq.newBuilder()
            .setGameType(gameType)
            .addAllCidList(cidList)
            .build()
        return suspendSend(CompetitionOpType.CompetitionOpTypeListByCids_VALUE) {
            it.listByCids = req
        }
    }

    suspend fun delete(gameType: Int, cid: Int): RspHeadInfo {
        val req = CompetitionPackets.DeleteReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .build()
        return suspendSend(CompetitionOpType.CompetitionOpTypeDelete_VALUE) {
            it.delete = req
        }
    }

    private inline fun <reified T> RspHeadInfo.parse(c: Class<T>): KtResult<T> {
        return if (codeOk() && c.isInstance(message)) {
            KtResultSuccess(message as T)
        } else if (codeOk()) {
            KtResultFailed(-1, "$message fail to  cast to ${c}")
        } else {
            KtResultFailed(code, desc)
        }
    }


    suspend fun suspendSend(
        opType: Int, bodyCreator: (builder: CompetitionPackets.CompetitionReq.Builder) -> Unit
    ): RspHeadInfo {
        val reqHead: ReqHead = buildReqHead(opType)
        val reqBodyBuilder = CompetitionPackets.CompetitionReq.newBuilder()
        bodyCreator.invoke(reqBodyBuilder)
        return tcpSuspendSend(reqHead, reqBodyBuilder.build())
    }

    suspend fun getCompetitionSchedule(gameType: Int, cid: Int): RspHeadInfo {
        val req = CompetitionPackets.ScheduleReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeSchedule_VALUE) {
            it.schedule = req
        }
    }


    suspend fun getSignUpList(gameType: Int, cid: Int, scene: Int): RspHeadInfo {
        val req = CompetitionPackets.SignUpListReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .setScene(scene)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeSignUpList_VALUE) {
            it.signUpList = req
        }
    }

    suspend fun getCompetitionBattleList(gameType: Int, cid: Int): RspHeadInfo {
        val req = CompetitionPackets.BattleListReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeBattleList_VALUE) {
            it.battleList = req
        }
    }

    suspend fun getSearchSignUpUserList(
        gameType: Int,
        cid: Int,
        scope: Int,
        searchKeyword: String
    ): RspHeadInfo {
        val req = CompetitionPackets.SearchSignUpUserReq.newBuilder()
            .setGameType(gameType)
            .setCid(cid)
            .setScope(scope)
            .setKeyword(searchKeyword)
            .build()

        return suspendSend(CompetitionOpType.CompetitionOpTypeSearchSignUpUser_VALUE) {
            it.searchSignUpUser = req
        }
    }

    private fun buildReqHead(opType: Int): ReqHead {
        val seq = TcpBaseSender.getSeq()
        return ReqHead.newBuilder()
            .setCommand(HeadPackets.CommandType.Competition_VALUE)
            .setAppVersion(LibBaseUtil.getBaseConfig().versionName)
            .setType(opType)
            .setUid(LoginHelper.getLoginUid())
            .setSeq(seq)
            .setLang(LibBaseUtil.getLang().value)
            .build()
    }
}