package com.weplay.competition.round

import com.huiwan.widget.treeView.model.NodeModel
import com.huiwan.widget.treeView.model.TreeModel
import com.wepie.liblog.main.HLog
import com.weplay.competition.getCompetitionRoundName
import com.weplay.competition.round.data.CompetitionRound
import com.weplay.competition.round.data.CompetitionRoundInfo
import com.weplay.competition.round.data.MatchRound
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.pow

class CompetitionRoundDataHandleUtil {
    private val treeMap: MutableMap<Int, NodeModel<MatchRound>> =
        ConcurrentHashMap<Int, NodeModel<MatchRound>>()

    lateinit var treeModel: TreeModel<MatchRound>

    private fun updateTreeMap(competitionRoundList: List<CompetitionRound>?) {
        competitionRoundList?.let {
            treeMap.clear()
            val roundSize = it.size
            if (roundSize > 0) {
                var count = TREE_ROOT_NUM
                for (index in roundSize - 1 downTo 0) {
                    val matchList = competitionRoundList[index].matchRound
                    if (matchList.isNotEmpty()) {
                        for (matchRound in matchList) {
                            matchRound.currentTable = count
                            treeMap[count] = NodeModel<MatchRound>(matchRound)
                            count++
                        }
                    }
                }
            }
        }
    }

    fun handleTreeData(competitionRoundList: List<CompetitionRound>?) {
        updateTreeMap(competitionRoundList)  //首先将数据放入map中，下标从TREE_ROOT_NUM开始
        val roundSize = competitionRoundList?.size //根据服务器返回数据长度计算总轮数
        if (roundSize != null && roundSize >= 2 && treeMap.isNotEmpty()) {
            val root = treeMap[TREE_ROOT_NUM] //获取树根
            treeModel = TreeModel(root) //以root为根节点创建树
            for (currentRound in roundSize downTo 2) { //从第roundSize轮开始遍历competitionRoundList，倒数第二轮结束遍历
                val n = roundSize - currentRound //根据比赛总轮数以及当前轮数，计算其差值，用于计算当前根起始值以及子节点起始步长
                var roundRootNum = 2.0.pow(n.toDouble()).toInt() - 1 //获取当前轮数list中对应的第一个的位置
                var startStep = 2.0.pow((n + 1).toDouble()).toInt() - 1 //计算子节点对应的起始步长

                val matchRoundList = competitionRoundList[currentRound - 1].matchRound //获取当前轮中匹配信息
                HLog.d(
                    TAG,
                    HLog.USR,
                    "handleTreeData n = {}, roundRootNum = {}, startStep = {}",
                    n,
                    roundRootNum,
                    startStep
                )
                matchRoundList.forEach { _ -> //遍历匹配数据，将根节点以及对应的两个叶子节点数据放入树中
                    if (roundRootNum >= 0) {
                        HLog.d(
                            TAG,
                            HLog.USR,
                            "matchRoundList forEach n = {}, roundRootNum = {}, startStep = {}",
                            n,
                            roundRootNum,
                            startStep
                        )
                        val currentRoot = treeMap[roundRootNum]
                        val sub0 = treeMap[startStep]
                        val sub1 = treeMap[startStep + 1]
                        treeModel.addNode(currentRoot, sub0, sub1)
                        if (n > 0) {
                            startStep += TREE_STEP
                            roundRootNum++
                        }
                    }
                }
            }
        }
    }

    fun getCompetitionSelectTitleInfo(competitionRoundList: List<CompetitionRound>?): List<CompetitionRoundInfo> {
        val competitionSelectTitleList = mutableListOf<CompetitionRoundInfo>()
        if (competitionRoundList != null) {
            val roundSize = competitionRoundList.size
            competitionRoundList.forEach { competitionRound ->
                val round = competitionRound.round
                val roundInfo =
                    CompetitionRoundInfo(round, getCompetitionRoundName(round, roundSize))
                competitionSelectTitleList.add(roundInfo)
            }
        }
        return competitionSelectTitleList
    }

    fun getNode(position: Int): NodeModel<MatchRound>? {
        return treeMap[position]
    }

    companion object {
        private const val TAG = "CompetitionRoundDataHandleUtil"
        const val TREE_ROOT_NUM = 0
        const val TREE_STEP = 2
    }
}