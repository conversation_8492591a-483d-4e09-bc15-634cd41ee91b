package com.weplay.competition.create

import android.text.format.DateUtils
import androidx.annotation.StringRes
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.TimeUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.ConstV3Info.QualifyingGrade
import com.huiwan.configservice.constentity.JkGameModeInfo
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.wepie.wespy.net.tcp.packet.CompetitionPackets.CreateRsp
import com.weplay.competition.CompetitionSender
import com.weplay.competition.R
import com.weplay.competition.dialog.CompetitionSetRewardDialog
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * 最大奖励数
 */
private const val COMPETITION_MAX_REWARD = 1_000_000

/**
 * 第一名最小奖励
 */
private const val COMPETITION_MIN_REWARD = 5_000

/**
 * 第二名最小奖励
 */
private const val COMPETITION_MIN_REWARD_2 = 3_000

/**
 * 最晚时间 240 分
 */
private const val TIME_RANGE_LATEST = 240 * DateUtils.MINUTE_IN_MILLIS

/**
 * 最早时间 60 - 1 分钟
 * 1 分钟缓冲
 * 考虑到只能选择整分钟，这里的设置 1 分钟缓冲一般没问题
 */
private const val TIME_RANGE_EARLIEST = 59 * DateUtils.MINUTE_IN_MILLIS

/**
 * 段位列表在赛事创建时可选的部分。
 */
internal fun competitionGradeShowList(): List<QualifyingGrade> {
    return ConfigHelper.getInstance().constConfig?.competitionInfo?.displayGrade?.mapNotNull { grade ->
        ConfigHelper.getInstance().constV3Info.qualifyingGradeList?.find { it.grade == grade }
    }?: emptyList()
}
/**
 * 段位列表在赛事创建时可选的第一个。
 */
internal fun competitionGradeFirstShowItem(): QualifyingGrade? {
    val grade = ConfigHelper.getInstance().constConfig?.competitionInfo?.displayGrade?.firstOrNull()
        ?: return null
    return ConfigHelper.getInstance().constV3Info.qualifyingGradeList?.find { it.grade == grade }
}

internal class CompetitionCreateViewModel : ViewModel() {

    var gameType: Int = 0
        private set
    private val _state = MutableStateFlow(CompetitionCreateInfo())
    private val _event = MutableSharedFlow<CreateEvent>()

    val state: StateFlow<CompetitionCreateInfo> = _state
    internal val event: SharedFlow<CreateEvent> = _event

    /**
     * 标记正在发送网络请求
     */
    private var requesting = false

    fun init(gameType: Int) {
        this.gameType = gameType
        _state.update { it.copy(gameType = gameType) }
        viewModelScope.launch {
            UserService.get().selfUser.asFlow().collectLatest { user ->
                _state.update { it.copy(myCoin = user.getCoin()) }
            }
        }
    }

    fun onSetPassword(pwd: String) {
        _state.update { it.copy(pwd = pwd) }
    }

    fun onSetTitleBanner(url: String) {
        _state.update { it.copy(bgImage = url) }
    }

    fun onSetReward(
        reward: Int,
        rewardFlag: Int,
        updateValueCallback: (Int) -> Unit
    ): Result<Unit> {
        var firstReward = state.value.firstReward
        var secondReward = state.value.secondReward
        val competitionConfig = ConfigHelper.getInstance().constConfig.competitionInfo
        val rewardConfig = competitionConfig?.reward
        val maxReward = rewardConfig?.max ?: COMPETITION_MAX_REWARD
        val minReward1 = rewardConfig?.minFirst ?: COMPETITION_MIN_REWARD
        val minReward2 = rewardConfig?.minSecond ?: COMPETITION_MIN_REWARD_2
        val minReward = if (rewardFlag == CompetitionSetRewardDialog.REWARD_TAG_1) {
            minReward1
        } else {
            minReward2
        }
        if (reward < minReward) {
            val tip = ResUtil.getStr(R.string.competition_create_reward_min_x, minReward.toString())
            return Result.failure(IllegalArgumentException(tip))
        }
        if (reward % 1000 != 0) {
            // 必须为 1000 的倍数
            updateValueCallback.invoke(roundUpToNearestThousand(reward))
            return Result.failure(IllegalArgumentException(ResUtil.getString(R.string.competition_create_reward_format)))
        }

        if (rewardFlag == CompetitionSetRewardDialog.REWARD_TAG_1) {
            if (reward > maxReward) {
                firstReward = maxReward
                updateValueCallback.invoke(firstReward)
                return Result.failure(IllegalArgumentException(ResUtil.getString(R.string.competition_create_reward_max_tip)))
            } else if (reward < secondReward) {
                return Result.failure(IllegalArgumentException(ResUtil.getString(R.string.competition_create_reward_2_o_1)))
            } else {
                firstReward = reward
            }
        } else {
            if (firstReward <= 0) {
                val tip = ResUtil.getString(R.string.competition_create_set_first_reward)
                return Result.failure(IllegalArgumentException(tip))
            } else if (firstReward < reward) {
                return Result.failure(IllegalArgumentException(ResUtil.getString(R.string.competition_create_reward_2_o_1)))
            } else {
                secondReward = reward
            }
        }

        var costTotal = firstReward
        if (secondReward >= 0) {
            costTotal += secondReward
        }
        val feeRatio = competitionConfig.createFeePercentRate
        // feeRatio 的值是 10 的时候，代表 10%
        // 由于奖励需要是 1000 的倍数，不会配置小数，所以这里不需要考虑浮点问题
        val fee = costTotal * feeRatio / 100
        _state.update {
            it.copy(
                firstReward = firstReward,
                secondReward = secondReward,
                costTotal = costTotal,
                fee = fee
            )
        }
        return Result.success(Unit)
    }

    fun onSetRankNoLimit() {
        _state.update { it.copy(noGradeLimit = true) }
    }

    fun onSetRankHasLimit() {
        _state.update { it.copy(noGradeLimit = false) }
    }

    fun onSetRankGradeLimit(qualifyingGrade: QualifyingGrade) {
        _state.update {
            it.copy(
                noGradeLimit = false,
                gradeLimit = qualifyingGrade
            )
        }
    }


    private fun roundUpToNearestThousand(value: Int): Int {
        return if (value % 1000 == 0) value else ((value / 1000) + 1) * 1000
    }

    fun onSetDetailEventTime(timestampMs: Long): Result<Unit> {
        val timeDiff = timestampMs - TimeUtil.getServerTime()
        if (timeDiff > TIME_RANGE_LATEST || timeDiff < TIME_RANGE_EARLIEST) {
            return Result.failure(IllegalArgumentException(ResUtil.getString(R.string.competition_create_time_limit_tip)))
        }
        _state.update {
            it.copy(
                useDetailTimeLimit = true,
                eventStartTimestampMs = timestampMs
            )
        }
        return Result.success(Unit)
    }

    fun onSetGameMode(gameModeInfo: JkGameModeInfo) {
        _state.update {
            it.copy(
                gameMode = gameModeInfo.gameMode,
                currencyType = GameConfig.CURRENCY_CHIP
            )
        }
    }

    fun onSetSeatsLimit(isSelected: Int) {
        _state.update { it.copy(seatNum = isSelected) }
    }


    fun onSetEventTimeNoLimit() {
        _state.update { it.copy(useDetailTimeLimit = false) }
    }

    fun onSetEventName(text: String) {
        _state.update { it.copy(eventName = text) }
    }

    fun onReqCreate() {
        viewModelScope.launch {
            val info = state.value
            val eventFee = info.fee
            val eventName = info.eventName
            var diamondReward = info.firstReward
            if (info.secondReward > 0) {
                diamondReward += info.secondReward
            }
            val sum = eventFee + diamondReward
            if (eventName.trim().isEmpty()) {
                _event.emit(CreateEvent.ShowErrTipRes(R.string.competition_create_nonull_name))
            } else if (info.firstReward <= 0) {
                _event.emit(CreateEvent.ShowErrTipRes(R.string.competition_create_set_first_reward))
            } else if (eventFee + diamondReward > LoginHelper.getLoginUser().coin) {
                _event.emit(CreateEvent.ShowErrTipRes(R.string.competition_create_diamond_not_enough))
                _event.emit(CreateEvent.ErrorCoinNotEnough)
            } else {
                _event.emit(CreateEvent.ShowCreateConfirmDialog(sum))
            }
        }
    }

    /**
     * 请求创建赛事
     */
    fun reqCreate() {
        if (requesting) {
            // 上次请求操作并为完成，拦截
            return
        }
        requesting = true
        viewModelScope.launch {
            val rspHead = CompetitionSender.reqCreate(_state.value)
            if (rspHead.codeOk()) {
                val rsp = rspHead.message as CreateRsp
                _event.emit(CreateEvent.JumpDetail(gameType, rsp.cid))
            } else {
                _event.emit(CreateEvent.ShowErrTip(rspHead.desc))
            }
        }.invokeOnCompletion {
            requesting = false
        }
    }

    internal data class CompetitionCreateInfo(
        val gameType: Int = 0,
        val myCoin: Long = LoginHelper.getLoginUser()?.coin ?: 0,
        val eventName: String = "",
        val gameMode: Int = 0,
        val currencyType: Int = GameConfig.CURRENCY_CHIP,
        val firstReward: Int = -1,
        val secondReward: Int = -1,
        val seatNum: Int = 8,
        val noGradeLimit: Boolean = true,
        var gradeLimit: QualifyingGrade = competitionGradeFirstShowItem() ?: QualifyingGrade(),
        val fee: Int = 0,
        val useDetailTimeLimit: Boolean = false,
        val eventStartTimestampMs: Long = -1,
        val costTotal: Int = 0,
        val bgImage: String = "https://res.weplayapp.com/conf/1748935302540deb51-20a2-4eec-b912-f5f885a405cd.webp?is_default=1",
        val pwd: String = "",
    ) {
        fun getRewardList(): List<Int> = listOf(firstReward, secondReward).filter { it > 0 }
    }

    internal sealed class CreateEvent {
        /**
         * 钻石不足提示
         */
        data object ErrorCoinNotEnough : CreateEvent()

        /**
         * 创建赛事的二次确认弹窗
         */
        class ShowCreateConfirmDialog(val sum: Int) : CreateEvent()

        /**
         * 跳转到赛事详情页
         */
        class JumpDetail(val gameType: Int, val cid: Int) : CreateEvent()

        /**
         * 提示出错
         */
        class ShowErrTip(val desc: String) : CreateEvent()

        /**
         * 提示出错
         */
        class ShowErrTipRes(@StringRes val desc: Int) : CreateEvent()
    }
}