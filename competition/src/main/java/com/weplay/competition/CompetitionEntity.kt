package com.weplay.competition

import com.huiwan.user.LoginHelper
import com.wepie.wespy.net.tcp.packet.CompetitionPackets
import com.wepie.wespy.net.tcp.packet.CompetitionPackets.CompetitionStartType
import com.wepie.wespy.net.tcp.packet.CompetitionPackets.CompetitionState
import com.wepie.wespy.net.tcp.packet.CompetitionPackets.PlayerState
import com.weplay.competition.CompetitionInfo.Companion.STATE_INIT
import com.weplay.competition.CompetitionInfo.Companion.STATE_NEXT_ROUND_READY
import com.weplay.competition.CompetitionInfo.Companion.STATE_READY

class CompetitionInfo {
    /** 赛事ID */
    var cid = 0

    /** 赛事主办UID */
    var ownerUid = 0

    /** 赛事名称 */
    var name = ""

    /**
     * 游戏基础信息
     */
    var gameType: Int = 0
    var mode: Int = 0
    var betLevel: Int = 0
    var gameMode: Int = 0
    var currencyType: Int = 0

    /** 限制段位 */
    var gradeLimit: Int = 0

    /** 赛事人数 */
    var userNum = 0

    /** 开始类型 */
    var startType = START_TYPE_UNKNOWN

    /** 开始时间 */
    var startTime = 0L

    /** 赛事准备开始倒计时时间 */
    var readyStartTime = 0L

    var winRewardMap = mutableMapOf<Int, Int>()

    /** 是否需要密码 */
    var needPwd = false

    /** 密码 */
    var pwd = ""

    /** 赛事图 */
    var bgImg = ""

    /** 赛事群聊ID */
    var groupId: Int = 0

    /** 赛事公告 */
    var announce = ""

    /** 是否允许文字发言 */
    var textTalk: Boolean = true

    /** 是否允许语音发言 */
    var voiceTalk: Boolean = true

    /** 赛事当前轮次 */
    var round = 0

    /** 赛事当前状态 */
    var state = CompetitionState.UNRECOGNIZED.ordinal

    /** 报名人数 */
    var signUpNum = 0

    /**
     * 报名状态
     * 0 可报名 1已报名 2不满足报名条件 3未被选中参赛 4被选中参赛 5已退赛
     */
    var playerState = PLAYER_STATE_CAN_SIGNUP

    fun isSelfOwner(): Boolean {
        return ownerUid == LoginHelper.getLoginUid()
    }

    fun getCountDownTime(): Long {
        if (state == STATE_INIT) {
            return startTime
        } else if (state == STATE_READY || state == STATE_NEXT_ROUND_READY) {
            return readyStartTime
        }
        return 0
    }

    fun isGaming(): Boolean {
        return state == STATE_GAMING
    }

    fun hasGradeLimit(): Boolean {
        return gradeLimit != 0
    }

    companion object {
        const val START_TYPE_UNKNOWN = CompetitionStartType.CompetitionStartTypeUnknown_VALUE

        /** 立即开始（15分钟倒计时） */
        const val START_TYPE_RECENT = CompetitionStartType.CompetitionStartTypeRecent_VALUE

        /** 指定时间开始 */
        const val START_TYPE_TIMED = CompetitionStartType.CompetitionStartTypeTimed_VALUE


        /** 创建阶段 */
        const val STATE_INIT = CompetitionState.CompetitionStateInit_VALUE

        /** 准备开始阶段 */
        const val STATE_READY = CompetitionState.CompetitionStateStartReady_VALUE

        /** 游戏阶段 */
        const val STATE_GAMING = CompetitionState.CompetitionStateGaming_VALUE

        /** 下一轮准备解决 */
        const val STATE_NEXT_ROUND_READY = CompetitionState.CompetitionStateNextStartReady_VALUE

        /** 结束阶段 */
        const val STATE_END = CompetitionState.CompetitionStateEnd_VALUE

        /** 销毁阶段 */
        const val STATE_DESTROY = CompetitionState.CompetitionStateDestroyed_VALUE

        /** 可报名 */
        const val PLAYER_STATE_CAN_SIGNUP = PlayerState.PlayerStateCanSignUp_VALUE

        /** 已报名 */
        const val PLAYER_STATE_ALREADY_SIGNUP = PlayerState.PlayerStateSignedUp_VALUE

        /** 不满足报名条件 */
        const val PLAYER_STATE_CAN_NOT_SIGNUP = PlayerState.PlayerStateCannotSignUp_VALUE

        /** 未被选中参赛 */
        const val PLAYER_STATE_NOT_SELECT = PlayerState.PlayerStateNotSelected_VALUE

        /** 被选中参赛 */
        const val PLAYER_STATE_GAMING = PlayerState.PlayerStateSelected_VALUE

        /** 已退赛 */
        const val PLAYER_STATE_EXIT = PlayerState.PlayerStateExit_VALUE

        /** 被淘汰 */
        const val PLAYER_STATE_ELIMINATED = PlayerState.PlayerStateEliminated_VALUE

        fun parse(info: CompetitionPackets.CompetitionInfo?): CompetitionInfo {
            info ?: return CompetitionInfo()
            val targetInfo = CompetitionInfo()
            targetInfo.cid = info.cid
            targetInfo.ownerUid = info.ownerUid
            targetInfo.name = info.name
            targetInfo.gameType = info.gameBasicInfo.littleGameType
            targetInfo.gameMode = info.gameBasicInfo.gameMode
            targetInfo.mode = info.gameBasicInfo.mode
            targetInfo.betLevel = info.gameBasicInfo.betLevel
            targetInfo.currencyType = info.gameBasicInfo.currencyType
            targetInfo.gradeLimit = info.gradeLimit
            targetInfo.userNum = info.userNum
            targetInfo.startType = info.startType.ordinal
            targetInfo.startTime = info.startTime.toLong()
            targetInfo.readyStartTime = info.readyStartTime.toLong()
            targetInfo.winRewardMap.clear()
            info.winRewardList.forEach {
                targetInfo.winRewardMap[it.rank] = it.diamond
            }
            targetInfo.needPwd = info.needPwd
            targetInfo.pwd = info.pwd
            targetInfo.bgImg = info.image
            targetInfo.groupId = info.groupId
            targetInfo.announce = info.announce
            targetInfo.textTalk = info.textTalk == CompetitionPackets.AllowType.AllowTypeAllow
            targetInfo.voiceTalk = info.voiceTalk == CompetitionPackets.AllowType.AllowTypeAllow
            targetInfo.round = info.round
            targetInfo.state = info.state.ordinal
            targetInfo.signUpNum = info.signUpNum
            targetInfo.playerState = info.playerState.ordinal
            return targetInfo
        }

        fun parse(list: List<CompetitionPackets.CompetitionInfo>?): List<CompetitionInfo> {
            list ?: return emptyList()
            return list.map { parse(it) }
        }
    }
}

sealed class CompetitionListItemInfo {
    open fun needShowCountDownTimer(): Boolean {
        return false
    }

    internal data class CompetitionCardInfo(
        val cid: Int = 0,
        //赛事段位限制
        val gradeLimit: Int,
        val competitionName: String = "",
        val startTime: Long = 0,
        val readyStartTime: Long = 0,
        //赛事图
        val bgImg: String = "",
        //报名人数
        val signUpNum: Int = 0,
        val needPwd: Boolean = false,
        val gameType: Int = 0,
        val gameMode: Int = 0,
        val betLevel: Int = 0,
        val mode: Int = 0,
        //发起人
        val ownerUid: Int = 0,
        //赛事状态
        val competitionState: Int = 0,
        val playerState: Int = 0,
        //用户设置的钻石奖励的总和
        val reward: Int = 0,
        //赛事规模,人数
        val competitionScale: Int = 0,
        //比赛轮次
        val round: Int = 0
    ) : CompetitionListItemInfo() {
        override fun needShowCountDownTimer(): Boolean {
            return competitionState == STATE_INIT || competitionState == STATE_READY
                    || competitionState == STATE_NEXT_ROUND_READY
        }
    }

    internal data class TitleInfo(
        //由 分类名称 拼接 当前分类下赛事数量
        val tile: String,
    ) : CompetitionListItemInfo()

    internal data object EmptyData : CompetitionListItemInfo()
}

data class ReqHasSignUpActionData(
    val gameType: Int,
    val competitionId: Int,
    val enterBySignUp: Boolean
)