package com.wepie.wespy.model.entity.fixroom;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.basechat.R;
import com.huiwan.store.database.SmartModel;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.RedPacket;
import com.wepie.wespy.net.tcp.packet.FixRoomPackets;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

// Created by bigwen on 2018/7/17.
public class FixRoomMsg extends ChatMsg {

    private static final int SYSTEM_ID = 100;

    @SmartModel.DBPrimaryKey
    @SmartModel.DBField
    @SerializedName("mid")
    private String mid = "";//本地字段

    @DBField
    @SerializedName("rid")
    private int rid;//本地字段

    @SmartModel.DBField
    @SerializedName("content")
    private String content;

    @DBField
    @SerializedName("sender")
    private int send_uid;

    @DBField
    @SerializedName("recv_uid")
    private int recv_uid;

    @DBField
    @SerializedName("msgseq")
    private int sequence;

    @DBField
    @SerializedName("timestamp")
    private long time;

    @DBField
    @SerializedName("status")
    private int status;//本地字段

    @DBField
    @SerializedName("type")
    private int mediaType;

    @DBField
    @SerializedName("subType")
    private int subType;

    @DBField
    @SerializedName("ext")
    private String ext;

    @DBField
    @SerializedName("bubbleId")
    private int bubbleId;

    @SerializedName("redpacketType")
    private int redpacketType;
    @SerializedName("redpacketMsg")
    private String redpacketMsg;
    @SerializedName("redpacketPwd")
    private String redpacketPwd;
    @SerializedName("redpacketId")
    private String redpacketId;
    @SerializedName("isNote")
    private boolean isNote = false;//本地字段，卧底公屏的公告

    public void setMid(String mid) {
        this.mid = mid;
    }

    @Override
    public void setBlocked(boolean isBlocked) {}

    @Override
    public boolean isBlocked() {
        return false;
    }

    @Override
    public void setExt(String ext) {
        this.ext = ext;
    }

    @Override
    public String getExt() {
        if (ext == null) return "";
        return ext;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    @Override
    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public void setSend_uid(int send_uid) {
        this.send_uid = send_uid;
    }

    @Override
    public void setRecv_uid(int recv_uid) {
        this.recv_uid = recv_uid;
    }

    @Override
    public void setTime(long time) {
        this.time = time;
    }

    @Override
    public void setStatus(int status) {
        this.status = status;
    }

    @Override
    public void setMediaType(int mediaType) {
        this.mediaType = mediaType;
    }

    @Override
    public int getSubType() {
        return subType;
    }

    @Override
    public void setSubType(int subType) {
        this.subType = subType;
    }

    @Override
    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    @Override
    public int getSequence() {
        return sequence;
    }

    @Override
    public String getMid() {
        return mid;
    }

    @Override
    public String getContent() {
        return content;
    }

    @Override
    public int getSend_uid() {
        return send_uid;
    }

    @Override
    public int getRecv_uid() {
        return recv_uid;
    }

    @Override
    public int getStatus() {
        return status;
    }

    @Override
    public int getMediaType() {
        return mediaType;
    }

    @Override
    public long getTime() {
        return time;
    }

    @Override
    public void setBubbleId(int bubbleId) {
        this.bubbleId = bubbleId;
    }

    @Override
    public int getBubbleId() {
        return bubbleId;
    }

    @NonNull
    @Override
    public String getRefMid() {
        return "";
    }

    @Override
    public void setRefMid(String refMid) {

    }

    @Override
    public String getQuoteType() {
        return "";
    }

    @Override
    public void setQuoteType(String quoteType) {

    }

    @NonNull
    @Override
    public String getQuoteId() {
        return "";
    }

    @Override
    public boolean isPasswordPacket() {
        if (redpacketType != 0) return redpacketType == 2;

        try {
            JSONObject jsonObject = new JSONObject(content);
            redpacketType = jsonObject.getInt("type");
            return redpacketType == 2;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void setContentWithRecall() {
        int uid = getSend_uid();
        if (uid == LoginHelper.getLoginUid()) {
            setContent(ResUtil.getStr(R.string.recalled_message));
        } else {
            UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(GlobalLife.INSTANCE) {
                @Override
                public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                    setContent(ResUtil.getResource().getString(R.string.recalled_message_by_x, userInfo.getRemarkName()));
                }

                @Override
                public void onUserInfoFailed(String description) {
                    setContent(ResUtil.getResource().getString(R.string.recalled_message_by_x, " "));
                }
            });
        }
    }

    @Override
    public String getRedPacketMsg() {
        if (!TextUtils.isEmpty(redpacketMsg)) return redpacketMsg;

        try {
            JSONObject jsonObject = new JSONObject(content);
            if (isPasswordPacket()) {
                redpacketMsg = jsonObject.getString("password");
            } else {
                redpacketMsg = jsonObject.getString("msg");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return redpacketMsg;
    }
    public int getRedPacketGrapedFromExtension() {
        try {
            JSONObject jsonObject = new JSONObject(content);
            return jsonObject.getInt("graped");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return RedPacket.STATE_UNGRAPED;
    }

    public int getRedPacketStateFromExtension() {
        try {
            JSONObject jsonObject = new JSONObject(content);
            return jsonObject.getInt("state");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return RedPacket.STATE_INIT;
    }
    public String getRedPacketPassword() {
        if (!TextUtils.isEmpty(redpacketPwd)) return redpacketPwd;

        try {
            JSONObject jsonObject = new JSONObject(content);
            redpacketPwd = jsonObject.getString("password");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return redpacketPwd;
    }

    public String getRedPacketId() {
        if (!TextUtils.isEmpty(redpacketId)) return redpacketId;

        try {
            JSONObject jsonObject = new JSONObject(content);
            redpacketId = jsonObject.getString("rp_id");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return redpacketId;
    }

    public boolean isAtMeMsg() {
        if (getSubType() == ChatMsg.SUBTYPE_AT) {
            String ext = getExt();
            try {
                JSONObject jsonObject = new JSONObject(ext);
                JSONArray uidArray = jsonObject.getJSONArray("at_target_uids");
                for (int i = 0; i < uidArray.length(); i++) {
                    int uid = uidArray.getInt(i);
                    // at我或者at所有人
                    if (uid == LoginHelper.getLoginUid() || uid == ChatMsg.AT_ALL_MSG) {
                        return true;
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    public int getRecallSeq() {
        String ext = getExt();
        try {
            JSONObject jsonObject = new JSONObject(ext);
            int seq = jsonObject.getInt("recalled_msgseq");
            return seq;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return -1;
    }

    public int getReceiverFromExt() {
        if (!TextUtils.isEmpty(getExt())) {
            try {
                JSONObject jsonObject = new JSONObject(getExt());
                if (jsonObject.has("receiver")) {
                    return jsonObject.getInt("receiver");
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return -1;
    }

    public boolean isNote() {
        return isNote;
    }

    //从1计数
    public int getUserNumberFromExt() {
        String ext = getExt();
        if (TextUtils.isEmpty(ext)) return -1;
        try {
            JSONObject jsonObject = new JSONObject(ext);
            if (jsonObject.has("user_number")) {
                return jsonObject.getInt("user_number");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return -1;
    }

    public FixRoomMsgExtLightUp getLightUpFromExt() {
        String ext = getExt();
        if (TextUtils.isEmpty(ext)) return new FixRoomMsgExtLightUp();
        FixRoomMsgExtLightUp fixRoomMsgExtLightUp = JsonUtil.getGson().fromJson(ext, FixRoomMsgExtLightUp.class);
        if (fixRoomMsgExtLightUp != null) return fixRoomMsgExtLightUp;
        return new FixRoomMsgExtLightUp();
    }

    public static FixRoomMsg parse(int rid, FixRoomPackets.FixRoomMsg msg) {
        FixRoomMsg fixRoomMsg = new FixRoomMsg();
        fixRoomMsg.setMediaType(msg.getTypeValue());
        fixRoomMsg.setSubType(msg.getSubtype());
        fixRoomMsg.setContent(msg.getContent());
        fixRoomMsg.setSequence(msg.getMsgseq());
        fixRoomMsg.setTime(msg.getTimestamp());
        fixRoomMsg.setExt(msg.getExt());
        fixRoomMsg.setSend_uid(msg.getSender());
        fixRoomMsg.setBubbleId(msg.getBubbleId());

        fixRoomMsg.setStatus(ChatMsg.STATUS_OK);
        if (msg.getTypeValue() == ChatMsg.MEDIA_TYPE_SYSTEM) {
            fixRoomMsg.setMid(generateFixRoomMid(SYSTEM_ID, rid, fixRoomMsg.getTime()));
        } else {
            fixRoomMsg.setMid(generateFixRoomMid(msg.getSender(), rid, fixRoomMsg.getTime()));
        }
        fixRoomMsg.setRid(rid);
        return fixRoomMsg;
    }

    public static FixRoomMsg generalNoteMsg(int rid, String content) {
        FixRoomMsg fixRoomMsg = new FixRoomMsg();
        fixRoomMsg.isNote = true;
        fixRoomMsg.setMediaType(MEDIA_TYPE_SYSTEM);
        fixRoomMsg.setSubType(SUBTYPE_UNDEFINED);
        fixRoomMsg.setContent(content);
        fixRoomMsg.setSequence(0);
        fixRoomMsg.setTime(System.currentTimeMillis());
        fixRoomMsg.setExt("");
        fixRoomMsg.setSend_uid(1);
        fixRoomMsg.setStatus(ChatMsg.STATUS_OK);
        fixRoomMsg.setMid(generateFixRoomMid(SYSTEM_ID, rid, fixRoomMsg.getTime()));
        fixRoomMsg.setRid(rid);
        return fixRoomMsg;
    }

    public static List<FixRoomMsg> parseList(int rid, List<FixRoomPackets.FixRoomMsg> msg) {
        List<FixRoomMsg> fixRoomMsgList = new ArrayList<>();
        for (FixRoomPackets.FixRoomMsg fixRoomMsg : msg) {
            fixRoomMsgList.add(parse(rid, fixRoomMsg));
        }
        return fixRoomMsgList;
    }

    public static String generateFixRoomMid(int send_uid, int rid, long time) {
        return send_uid + "_" + rid + "_" + time;
    }
}
