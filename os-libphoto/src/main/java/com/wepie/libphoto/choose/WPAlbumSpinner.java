package com.wepie.libphoto.choose;

import android.content.Context;
import android.content.res.TypedArray;
import android.database.Cursor;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.AdapterView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.ListPopupWindow;
import androidx.core.content.ContextCompat;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.wepie.libphoto.R;
import com.zhihu.matisse.internal.entity.Album;
import com.zhihu.matisse.internal.utils.Platform;

public class WPAlbumSpinner implements WPAlbumAdapter.IAlbumAdapter {
    private static final int MAX_SHOWN_COUNT = 5;
    private WPAlbumAdapter mAdapter;
    private AppCompatTextView mSelected;
    private final ListPopupWindow mListPopupWindow;
    private AdapterView.OnItemSelectedListener mOnItemSelectedListener;
    private View maskView;
    private final int dp16 = ScreenUtil.dip2px(16f);

    public WPAlbumSpinner(@NonNull Context context) {
        mListPopupWindow = new ListPopupWindow(context, null, 0, com.zhihu.matisse.R.style.Popup_Zhihu);
        mListPopupWindow.setModal(true);
        mListPopupWindow.setWidth(ListPopupWindow.MATCH_PARENT);
        mListPopupWindow.setHorizontalOffset(0);
        mListPopupWindow.setVerticalOffset(ScreenUtil.dip2px(10));
        Drawable drawable = ContextCompat.getDrawable(context, R.drawable.shape_ffffff_bottom_corner8);
        mListPopupWindow.setBackgroundDrawable(drawable);
        mListPopupWindow.setOnItemClickListener((parent, view, position, id) -> {
            WPAlbumSpinner.this.onItemSelected(parent.getContext(), position);
            if (mOnItemSelectedListener != null) {
                mOnItemSelectedListener.onItemSelected(parent, view, position, id);
            }
        });
        mListPopupWindow.setAnimationStyle(R.style.album_popup_style);
        mListPopupWindow.setOnDismissListener(() -> {
            Drawable drawable1 = ResUtil.getDrawable(R.drawable.album_down_triangle);
            drawable1.setBounds(0, 0, dp16, dp16);
            mSelected.setCompoundDrawablesRelative(null, null, drawable1, null);
            maskView.setVisibility(View.GONE);
        });
    }

    public void setOnItemSelectedListener(AdapterView.OnItemSelectedListener listener) {
        mOnItemSelectedListener = listener;
    }

    public void setSelection(Context context, int position) {
        mListPopupWindow.setSelection(position);
        onItemSelected(context, position);
    }

    private void onItemSelected(Context context, int position) {
        mListPopupWindow.dismiss();
        Cursor cursor = mAdapter.getCursor();
        if (cursor == null) return;
        cursor.moveToPosition(position);
        Album album = Album.valueOf(cursor);
        String displayName = album.getDisplayName(context);
        if (mSelected.getVisibility() == View.VISIBLE) {
            mSelected.setText(displayName);
        } else {
            if (Platform.hasICS()) {
                mSelected.setAlpha(0.0f);
                mSelected.setVisibility(View.VISIBLE);
                mSelected.setText(displayName);
                mSelected.animate().alpha(1.0f).setDuration(context.getResources().getInteger(
                        android.R.integer.config_longAnimTime)).start();
            } else {
                mSelected.setVisibility(View.VISIBLE);
                mSelected.setText(displayName);
            }

        }
    }

    public void setAdapter(WPAlbumAdapter adapter) {
        mListPopupWindow.setAdapter(adapter);
        mAdapter = adapter;
        mAdapter.setInterface(this);
    }

    public void setSelectedTextView(AppCompatTextView textView) {
        mSelected = textView;
        // tint dropdown arrow icon
        Drawable[] drawables = mSelected.getCompoundDrawablesRelative();
        Drawable right = drawables[2];
        TypedArray ta = mSelected.getContext().getTheme().obtainStyledAttributes(
                new int[]{com.zhihu.matisse.R.attr.album_element_color});
        int color = ta.getColor(0, 0);
        ta.recycle();
        right.setColorFilter(color, PorterDuff.Mode.SRC_IN);

        mSelected.setVisibility(View.GONE);
        mSelected.setOnClickListener(v -> {
            int itemHeight = ScreenUtil.dip2px(80);
            mListPopupWindow.setHeight(
                    mAdapter.getCount() > MAX_SHOWN_COUNT ? itemHeight * MAX_SHOWN_COUNT
                            : itemHeight * mAdapter.getCount());
            mListPopupWindow.show();
            Drawable drawable = ResUtil.getDrawable(R.drawable.album_up_triangle);
            drawable.setBounds(0, 0, dp16, dp16);
            mSelected.setCompoundDrawablesRelative(null, null, drawable, null);
            maskView.setVisibility(View.VISIBLE);
        });
        mSelected.setOnTouchListener(mListPopupWindow.createDragToOpenListener(mSelected));
    }

    public void setPopupAnchorView(View view) {
        mListPopupWindow.setAnchorView(view);
    }

    @Override
    public String curText() {
        if (mSelected == null) return "";
        else return mSelected.getText().toString();
    }

    public void setMaskView(View maskView) {
        this.maskView = maskView;
    }
}
