<?xml version="1.0" encoding="utf-8"?>
<com.wepie.libphoto.choose.SquareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/shape_f7f8fa_corner4">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/circle_choose_camera"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="18dp"
        app:srcCompat="@drawable/icon_choose_photo_camera" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="14dp"
        android:gravity="center"
        android:text="@string/circle_shoot"
        android:textColor="#aaabb3"
        android:textSize="13dp" />


</com.wepie.libphoto.choose.SquareRelativeLayout>