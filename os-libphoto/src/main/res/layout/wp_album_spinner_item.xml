<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80dp">

    <com.huiwan.widget.image.RoundedImageView
        android:id="@+id/album_cover"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        app:riv_corner_radius="8dp" />

    <TextView
        android:id="@+id/album_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="12dp"
        android:layout_toEndOf="@id/album_cover"
        android:textColor="#333333"
        android:textSize="14dp"
        android:textStyle="bold"
        tools:text="最近项目" />

    <TextView
        android:id="@+id/album_media_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="4dp"
        android:layout_toEndOf="@id/album_name"
        android:textColor="#aaabb3"
        android:textSize="12dp"
        android:textStyle="bold"
        tools:text="1000" />

    <ImageView
        android:id="@+id/select_iv"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="16dp"
        android:src="@drawable/album_choose"
        android:visibility="gone"
        tools:visibility="visible" />
</RelativeLayout>