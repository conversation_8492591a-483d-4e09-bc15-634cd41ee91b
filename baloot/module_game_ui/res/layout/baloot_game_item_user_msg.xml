<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center_horizontal"
    tools:context=".BalootGameFragment"
    tools:parentTag="android.widget.FrameLayout">


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/top_guide"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="100dp" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/lr_y_guide"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="400dp" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/l_x_guide"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="50dp" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/r_x_guide"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="300dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/bottom_y_guide"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="600dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/bottom_x_guide"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="200dp" />


    <TextView
        android:id="@+id/top_user_msg_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/baloot_user_msg_bubble_arrow_top"
        android:ellipsize="end"
        android:maxWidth="118dp"
        android:maxLines="5"
        android:textColor="@color/baloot_text_primary"
        android:textSize="14sp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_guide"
        tools:text="1234567890123456790234"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/left_user_msg_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:background="@drawable/baloot_user_msg_bubble_arrow_down"
        android:ellipsize="end"
        android:maxWidth="100dp"
        android:maxLines="5"
        android:textColor="@color/baloot_text_primary"
        android:textSize="14sp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/lr_y_guide"
        app:layout_constraintEnd_toEndOf="@id/l_x_guide"
        app:layout_constraintStart_toStartOf="@id/l_x_guide"
        tools:layout_width="100dp"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/right_user_msg_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:background="@drawable/baloot_user_msg_bubble_arrow_down"
        android:ellipsize="end"
        android:maxWidth="100dp"
        android:maxLines="5"
        android:textColor="@color/baloot_text_primary"
        android:textSize="14sp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/lr_y_guide"
        app:layout_constraintEnd_toEndOf="@id/r_x_guide"
        app:layout_constraintStart_toStartOf="@id/r_x_guide"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/bottom_user_msg_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/baloot_user_msg_bubble_arrow_right"
        android:ellipsize="end"
        android:gravity="start"
        android:maxWidth="122dp"
        android:maxLines="5"
        android:textColor="@color/baloot_text_primary"
        android:textSize="14sp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/bottom_y_guide"
        app:layout_constraintRight_toLeftOf="@id/bottom_x_guide"
        app:layout_constraintTop_toTopOf="@id/bottom_y_guide"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>