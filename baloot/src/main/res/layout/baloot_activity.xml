<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <com.huiwan.component.gift.show.GiftContentView
        android:id="@+id/gift_content_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:translationZ="9001dp"
        tools:visibility="gone" />

    <ViewStub
        android:id="@+id/anim_view_stub"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>