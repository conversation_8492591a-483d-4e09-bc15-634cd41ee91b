<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/baloot_main_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_lay"
        android:layout_width="295dp"
        android:layout_height="wrap_content"
        android:background="@drawable/baloot_dialog_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/title_lay"
            layout="@layout/baloot_dialog_title_lay" />

        <TextView
            android:id="@+id/base_score_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="16dp"
            android:text="@string/baloot_base_score"
            android:textColor="@color/baloot_text_primary"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_lay" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/scores_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="6dp"
            android:overScrollMode="never"
            app:layout_constraintTop_toBottomOf="@id/base_score_tv"
            tools:itemCount="3"
            tools:layoutManager="GridLayoutManager"
            tools:listitem="@layout/baloot_dialog_create_room_score_item"
            tools:spanCount="3" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/coin_need_and_ticket_lay"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginTop="6dp"
            android:gravity="center_vertical"
            android:paddingHorizontal="6dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/scores_rv"
            app:layout_constraintTop_toBottomOf="@id/scores_rv"
            tools:background="@color/baloot_text_primary_a70"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/baloot_join_need"
                android:textColor="#FBEFD5"
                android:textFontWeight="500"
                android:textSize="12dp" />

            <View
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="2dp"
                android:background="@drawable/wejoy_coin_icon" />

            <TextView
                android:id="@+id/coin_need_tv"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="#FBEFD5"
                android:textFontWeight="500"
                android:textSize="12dp"
                tools:text="40" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginStart="12dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/baloot_ticket"
                android:textColor="#FBEFD5"
                android:textFontWeight="500"
                android:textSize="12dp" />

            <View
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="2dp"
                android:background="@drawable/wejoy_coin_icon" />

            <TextView
                android:id="@+id/ticket_tv"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="#FBEFD5"
                android:textFontWeight="500"
                android:textSize="12dp"
                tools:text="40" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <View
            android:id="@+id/score_divider"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="15.5dp"
            android:background="@color/baloot_divider"
            app:layout_constraintTop_toBottomOf="@id/coin_need_and_ticket_lay" />

        <TextView
            android:id="@+id/game_speed_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="15.5dp"
            android:text="@string/baloot_game_speed"
            android:textColor="@color/baloot_text_primary"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/score_divider" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/speed_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="6dp"
            android:overScrollMode="never"
            app:layout_constraintTop_toBottomOf="@id/game_speed_tv"
            tools:itemCount="3"
            tools:layoutManager="GridLayoutManager"
            tools:listitem="@layout/baloot_dialog_create_room_speed_item"
            tools:spanCount="3" />

        <View
            android:id="@+id/bottom_line"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="8dp"
            android:background="@color/baloot_divider"
            app:layout_constraintTop_toBottomOf="@id/speed_rv" />

        <TextView
            android:id="@+id/room_pwd_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="26dp"
            android:text="@string/room_password"
            android:textColor="@color/baloot_text_primary"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/speed_rv" />

        <TextView
            android:id="@+id/room_pwd_tv"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginStart="4dp"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="6dp"
            android:textColor="#FBEFD5"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/room_pwd_title_tv"
            app:layout_constraintStart_toEndOf="@id/room_pwd_title_tv"
            app:layout_constraintTop_toTopOf="@id/room_pwd_title_tv"
            app:onlyNum="true"
            tools:background="#805c290e"
            tools:text="密码：1234"
            tools:visibility="visible" />

        <FrameLayout
            android:id="@+id/pwd_lay"
            android:layout_width="66dp"
            android:layout_height="24dp"
            android:layout_marginEnd="12dp"
            app:layout_constraintBottom_toBottomOf="@id/room_pwd_title_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/room_pwd_title_tv">

            <com.huiwan.widget.WpSwitch
                android:id="@+id/pwd_switch"
                android:layout_width="66dp"
                android:layout_height="24dp"
                android:thumb="@drawable/baloot_switch_thumb"
                android:track="@drawable/baloot_switch_bg" />
        </FrameLayout>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/pwd_group"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="visible"
            app:constraint_referenced_ids="pwd_lay,room_pwd_title_tv,bottom_line" />

        <View
            android:id="@+id/ctrl_bg_view"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/baloot_dialog_btns_bg"
            app:layout_constraintTop_toBottomOf="@id/room_pwd_title_tv" />

        <TextView
            android:id="@+id/room_btn"
            android:layout_width="128dp"
            android:layout_height="40dp"
            android:background="@drawable/baloot_btn_light"
            android:gravity="center"
            android:text="@string/baloot_main_create_room"
            android:textColor="@color/baloot_text_primary"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/ctrl_bg_view"
            app:layout_constraintEnd_toEndOf="@id/ctrl_bg_view"
            app:layout_constraintStart_toStartOf="@id/ctrl_bg_view"
            app:layout_constraintTop_toTopOf="@id/ctrl_bg_view" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>