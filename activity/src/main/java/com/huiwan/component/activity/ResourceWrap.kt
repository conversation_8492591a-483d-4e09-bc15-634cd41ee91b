package com.huiwan.component.activity

import android.annotation.TargetApi
import android.content.res.ColorStateList
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.DisplayMetrics
import com.huiwan.base.LibBaseUtil

open class ResourceWrap(private val real: Resources) :
    Resources(real.assets, real.displayMetrics, real.configuration) {

    @Throws(NotFoundException::class)
    override fun getText(id: Int): CharSequence {
        return try {
            real.getText(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ""
        }
    }

    @Throws(NotFoundException::class)
    override fun getQuantityText(id: Int, quantity: Int): CharSequence {
        return try {
            real.getQuantityText(id, quantity)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ""
        }
    }

    @Throws(NotFoundException::class)
    override fun getString(id: Int): String {
        return try {
            real.getString(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ""
        }
    }

    @Throws(NotFoundException::class)
    override fun getString(id: Int, vararg formatArgs: Any?): String {
        return try {
            real.getString(id, *formatArgs)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ""
        }
    }

    @Throws(NotFoundException::class)
    override fun getQuantityString(id: Int, quantity: Int, vararg formatArgs: Any?): String {
        return try {
            real.getQuantityString(id, quantity, *formatArgs)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ""
        }
    }

    @Throws(NotFoundException::class)
    override fun getQuantityString(id: Int, quantity: Int): String {
        return try {
            real.getQuantityString(id, quantity)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ""
        }
    }

    override fun getText(id: Int, def: CharSequence?): CharSequence {
        return try {
            real.getText(id, def)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ""
        }
    }

    @Throws(NotFoundException::class)
    override fun getTextArray(id: Int): Array<CharSequence> {
        return try {
            real.getTextArray(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            emptyArray()
        }
    }

    @Throws(NotFoundException::class)
    override fun getStringArray(id: Int): Array<String> {
        return try {
            real.getStringArray(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            emptyArray()
        }
    }

    @Throws(NotFoundException::class)
    override fun getIntArray(id: Int): IntArray {
        return try {
            return real.getIntArray(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            IntArray(0)
        }
    }

    override fun getDimension(id: Int): Float {
        return try {
            real.getDimension(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            0F
        }
    }

    override fun getDimensionPixelOffset(id: Int): Int {
        return try {
            real.getDimensionPixelOffset(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            0
        }
    }

    override fun getDimensionPixelSize(id: Int): Int {
        return try {
            real.getDimensionPixelSize(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            0
        }
    }

    @TargetApi(Build.VERSION_CODES.CUPCAKE)
    override fun getFraction(id: Int, base: Int, pbase: Int): Float {
        return try {
            real.getFraction(id, base, pbase)
        } catch (e: Exception) {
            LibBaseUtil.flogErr(e);
            0F
        }
    }

    override fun getDrawable(id: Int, theme: Theme?): Drawable? {
        return try {
            real.getDrawable(id, theme)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ColorDrawable(Color.TRANSPARENT)
        }
    }

    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1)
    override fun getDrawableForDensity(id: Int, density: Int, theme: Theme?): Drawable? {
        return try {
            real.getDrawableForDensity(id, density)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ColorDrawable(Color.TRANSPARENT)
        }
    }

    override fun getColor(id: Int, theme: Theme?): Int {
        return try {
            real.getColor(id, theme)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            Color.TRANSPARENT
        }
    }

    override fun getColorStateList(id: Int, theme: Theme?): ColorStateList {
        return try {
            super.getColorStateList(id, theme)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            ColorStateList.valueOf(Color.TRANSPARENT)
        }
    }

    @TargetApi(Build.VERSION_CODES.CUPCAKE)
    @Throws(NotFoundException::class)
    override fun getBoolean(id: Int): Boolean {
        return try {
            real.getBoolean(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            false
        }
    }

    @Throws(NotFoundException::class)
    override fun getInteger(id: Int): Int {
        return try {
            real.getInteger(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            0
        }
    }

    override fun getFloat(id: Int): Float {
        return try {
            real.getFloat(id)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
            0F
        }
    }

    override fun getIdentifier(name: String?, defType: String?, defPackage: String?): Int {
        try {
            return real.getIdentifier(name, defType, defPackage)
        } catch (e: NotFoundException) {
            LibBaseUtil.flogErr(e);
        }
        return 0
    }

    override fun getConfiguration(): Configuration? {
        return real.configuration
    }

    override fun getDisplayMetrics(): DisplayMetrics? {
        return real.displayMetrics
    }
}