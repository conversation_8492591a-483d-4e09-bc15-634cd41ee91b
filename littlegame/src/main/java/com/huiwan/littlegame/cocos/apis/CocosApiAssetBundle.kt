package com.huiwan.littlegame.cocos.apis

import android.os.SystemClock
import com.google.gson.JsonObject
import com.huiwan.base.util.JsonUtil
import com.huiwan.littlegame.model.GameChessboardRes
import com.huiwan.platform.ThreadUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * cocos 在需要使用 asset_bundle 时获取对应信息。
 * 参数: prop_id, int, asset_bundle 对应的道具 id
 * 返回值: has_cache, bool,  客户端本地是否有缓存
 *
 * @since jk-5.1.5
 */
class GetAssetBundleInfo : BaseCocosApi("GetAssetBundleInfo") {

    override fun handleAsync(jsonArg: JsonObject, callback: (CocosApiResult) -> Unit) {
        val propId = JsonUtil.getInt(jsonArg, "prop_id", -1)
        ThreadUtil.runInOtherThread {
            // hasBundleCache 中可能有 IO 操作
            val hasCache = GameChessboardRes.hasBundleCache(propId)
            flog("GetAssetBundleInfo for propId: {}, {}", propId, hasCache)
            // 这里 callback 内部做线程切换。
            callback.invoke(
                CocosApiResult.ok(
                    mapOf(
                        "has_cache" to hasCache
                    )
                )
            )
        }
    }
}

/**
 * cocos 发现缓存缺失或缓存异常时调用，原生强制刷新 prop_id 对应的 asset bundle 资源
 * 参数: prop_id, int, asset_bundle 对应的道具 id
 * 参数: bundle_zip_url, string, asset_bundle 对应的 zip url
 * 返回值: success, bool, 加载成功或失败。
 *
 * @since jk-5.1.5
 */
class ReloadAssetBundle : BaseCocosApi("ReloadAssetBundle") {

    override fun handleAsync(jsonArg: JsonObject, callback: (CocosApiResult) -> Unit) {
        val propId = JsonUtil.getInt(jsonArg, "prop_id", -1)
        val propUrl = JsonUtil.getString(jsonArg, "bundle_zip_url", "")
        if (propId <= 0) {
            flog("PropId Invalid: {}", propId)
            callback.invoke(CocosApiResult.failed("PropId Invalid: $propId"))
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            val start = SystemClock.elapsedRealtime()
            val isLoadSuccess = GameChessboardRes.reloadAssetBundle(propId, propUrl)
            val spent = SystemClock.elapsedRealtime() - start
            flog("success:$isLoadSuccess, spent:$spent for propId: $propId, url:$propUrl")
            // IO 线程是协程调度的，这里的回调也用协程来调度。
            withContext(Dispatchers.Main) {
                callback.invoke(
                    CocosApiResult.ok(
                        mapOf(
                            "success" to isLoadSuccess
                        )
                    )
                )
            }
        }
    }
}