package com.huiwan.littlegame.cocos.resCheck.base

import com.huiwan.base.util.ToastUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.littlegame.cocos.getLocalManifestPath
import com.huiwan.littlegame.cocos.getUnpackDir
import com.huiwan.littlegame.cocos.resCheck.exception.CocosUnpackException
import com.huiwan.littlegame.cocos.resCheck.localLoader.BuildInCocosVersionManager
import com.wejoy.littlegame.ILittleGameApi
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.InputStream

abstract class AbsBuildInCocosGameResLoader : IBuildInCocosGameResLoader {
    abstract val tag: String
    abstract suspend fun getResInputStream(
        gameType: Int,
        isWebGame: Boolean
    ): InputStream

    abstract suspend fun getTargetManifestInputStream(
        gameType: Int,
        isWebGame: Boolean
    ): InputStream

    override suspend fun needLoadInGameRes(
        gameType: Int,
        isWebGame: Boolean,
        targetVersion: Int
    ): Boolean {
        val buildInResVersion = BuildInCocosVersionManager.getBuildInAppGameResVersion(gameType)
        return buildInResVersion == targetVersion || !isLocalManifestExist(gameType, isWebGame)
    }

    private fun isLocalManifestExist(gameType: Int, isWebGame: Boolean) =
        File(getLocalManifestPath(gameType, isWebGame)).exists()

    override suspend fun load(
        gameType: Int,
        isWebGame: Boolean,
        targetVersion: Int
    ): Result<Unit> {
        return loadInGameRes(gameType, isWebGame)
    }

    private suspend fun loadInGameRes(
        gameType: Int,
        isWebGame: Boolean
    ): Result<Unit> = withContext(Dispatchers.IO) {
        kotlin.runCatching {
            copyRestToWorkplace(gameType, isWebGame)
            copyManifestToWorkplace(gameType, isWebGame)
            updateLocalVersion(gameType, isWebGame)
        }.onFailure { e ->
            ToastUtil.debugShow("local load gameRes fail:${e.message}")
            log("gameType:$gameType loadGameAsset failed ,may be have not built in App raw yet ,e=$e")
        }
    }

    private suspend fun copyRestToWorkplace(gameType: Int, isWebGame: Boolean) {
        log("start loadInGameRes ")
        val littleGameApi = ApiService.of(ILittleGameApi::class.java)
        getResInputStream(gameType, isWebGame).use {
            File(getUnpackDir(gameType, isWebGame)).deleteRecursively()
            littleGameApi.unpackCocosInGameRes(it, getUnpackDir(gameType, isWebGame))
                .onSuccess {
                    log("unpack game result isSuccess:true")
                }.onFailure {
                    log("unpack game result isSuccess:false")
                }.getOrElse { e ->
                    CocosUnpackException(e.message ?: "unpack game result isSuccess:false", e)
                }
        }
    }

    private suspend fun copyManifestToWorkplace(gameType: Int, isWebGame: Boolean) {
        getTargetManifestInputStream(gameType, isWebGame).use {
            copyManifest(it, gameType, isWebGame)
        }
    }

    private suspend fun updateLocalVersion(gameType: Int, isWebGame: Boolean) {
        BuildInCocosVersionManager.getBuildInAppGameResVersion(gameType).let {
            val littleGameApi = ApiService.of(ILittleGameApi::class.java)
            littleGameApi.updateCocosGameVersionInfo(gameType, isWebGame, it)
            log("loadGameAsset version:$it success")
        }
    }


    protected fun log(msg: String) {
        HLog.d(tag, HLog.USR, msg)
    }
}