package com.huiwan.littlegame.cocos.resCheck.localLoader

import com.huiwan.base.str.ResUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.littlegame.cocos.resCheck.base.IBuildInCocosMatchResLoader
import com.huiwan.littlegame.cocos.resCheck.exception.CocosUnpackException
import com.wejoy.littlegame.ILittleGameApi
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class CocosMatchRawLoader : IBuildInCocosMatchResLoader {
    private val tag = "CocosMatchRawLoader"
    override suspend fun needLoadMatchRes(gameType: Int, targetVersion: String): Boolean =
        BuildInCocosVersionManager.getBuildInAppMatchResVersion(gameType).contains(targetVersion)

    override suspend fun load(gameType: Int, targetVersion: String): Result<Unit> {
        return loadMatchRes(gameType, targetVersion)
    }

    private suspend fun loadMatchRes(
        gameType: Int,
        targetVersion: String
    ): Result<Unit> = withContext(Dispatchers.IO) {
        kotlin.runCatching {
            copyResToWorkplace(gameType, targetVersion)
            updateLocalVersion(gameType, targetVersion)
        }.onFailure { e ->
            log("gameType:$gameType loadMatchAsset failed ,may be have not built in App raw yet ,e=$e")
        }
    }

    private fun copyResToWorkplace(gameType: Int, targetVersion: String) {
        log("start loadMatchRes")
        val littleGameApi = ApiService.of(ILittleGameApi::class.java)
        val resId = ResUtil.getResourceId("match_$gameType", "raw")
        ResUtil.getResource().openRawResource(resId).use {
            File(littleGameApi.getMatchResDir(gameType)).deleteRecursively()
            littleGameApi.unpackCocosMatchRes(it, gameType)
                .onSuccess {
                    log("unpack match result isSuccess:true,targetVersion:$targetVersion")
                }.onFailure {
                    log("unpack match result isSuccess:false,targetVersion:$targetVersion")
                }.getOrElse { e ->
                    CocosUnpackException(e.message ?: "match unpack failed", e)
                }
        }
    }

    private fun updateLocalVersion(gameType: Int, targetVersion: String) {
        ApiService.of(ILittleGameApi::class.java)
            .updateCocosMatchVersionInfo(gameType, targetVersion)
    }


    private fun log(msg: String) {
        HLog.d(tag, HLog.USR, msg)
    }
}