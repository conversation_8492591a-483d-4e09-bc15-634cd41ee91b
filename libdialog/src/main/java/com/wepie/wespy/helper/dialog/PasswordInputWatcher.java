package com.wepie.wespy.helper.dialog;

import android.text.Editable;
import android.widget.TextView;

import androidx.annotation.NonNull;

public class PasswordInputWatcher implements android.text.TextWatcher {
    private final TextView[] textViews;
    private TextChangeListener listener;

    public PasswordInputWatcher(@NonNull TextView[] textViews, TextChangeListener listener) {
        this.textViews = textViews;
        this.listener = listener;
    }

    public void setListener(TextChangeListener listener) {
        this.listener = listener;
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
    }

    @Override
    public void afterTextChanged(Editable s) {
        for (TextView textView : textViews) {
            textView.setText("");
        }
        int len = Math.min(s.length(), textViews.length);
        for (int i = 0; i < len; i++) {
            textViews[i].setText(s.subSequence(i, i + 1));
        }
        if (listener != null) {
            listener.onTextChange(s.toString());
        }
    }

    public interface TextChangeListener {
        void onTextChange(String text);
    }
}
