package com.wepie.wespy.helper.dialog.bottomsheet;


import android.annotation.SuppressLint;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.PressUtil;
import com.huiwan.dialog.R;

import java.util.ArrayList;
import java.util.List;

public class BaseListDialogAdapter extends RecyclerView.Adapter<BaseListDialogAdapter.BaseViewHolder> {
    SelectItemCallback callback;
    List<String> nameList = new ArrayList<>();
    int res = -1;
    int textColor = Color.parseColor("#1B1D38");
    public BaseListDialogAdapter() {
    }

    public BaseListDialogAdapter(int res) {
        this.res = res;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void updateList(List<String> nameList) {
        this.nameList.clear();
        this.nameList.addAll(nameList);
        notifyDataSetChanged();
    }

    public int getLayoutRes() {
        if (res > 0) return res;
        return R.layout.base_dialog_list_item;
    }

    public void setCallback(SelectItemCallback callback) {
        this.callback = callback;
    }

    public void setTextColor(int color) {
        textColor = color;
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new BaseViewHolder(LayoutInflater.from(parent.getContext()).inflate(getLayoutRes(), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull BaseViewHolder holder, @SuppressLint("RecyclerView") int position) {
        holder.update(nameList.get(position), textColor);
        holder.addPressEffect();
        holder.itemView.setOnClickListener(v -> {
            if (callback != null) {
                callback.onSelect(position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return nameList.size();
    }

    public static class BaseViewHolder extends RecyclerView.ViewHolder {
        TextView nameTv;

        public BaseViewHolder(@NonNull View itemView) {
            super(itemView);
            nameTv = itemView.findViewById(R.id.menu_name_tv);
        }

        public void update(String s, int color) {
            nameTv.setText(s);
            nameTv.setTextColor(color);
        }

        public void addPressEffect(){
            PressUtil.addPressEffect(itemView);
        }
    }
}
