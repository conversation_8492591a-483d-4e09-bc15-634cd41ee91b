package com.huiwan.configservice.model;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class WejoyUserConfig {
    @SerializedName("locked_gifts")
    private List<LockedGift> lockedGifts = new ArrayList();

    private int uid;

    public List<LockedGift> getLockedGifts() {
        return lockedGifts;
    }

    public void setLockedGifts(List<LockedGift> lockedGifts) {
        this.lockedGifts = lockedGifts;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    @Override
    public String toString() {
        return "WejoyUserConfig{" +
                "uid=" + uid +
                ", lockedGifts=" + lockedGifts +
                '}';
    }

    public static class LockedGift{
        @SerializedName("gift_id")
        private int giftId;

        @SerializedName("reason")
        private String lockedReason;

        public int getGiftId() {
            return giftId;
        }

        public void setGiftId(int giftId) {
            this.giftId = giftId;
        }

        public String getLockedReason() {
            return lockedReason;
        }

        public void setLockedReason(String lockedReason) {
            this.lockedReason = lockedReason;
        }

        @Override
        public String toString() {
            return "LockedGift{" +
                    "giftId=" + giftId +
                    ", lockedReason='" + lockedReason + '\'' +
                    '}';
        }
    }
}
