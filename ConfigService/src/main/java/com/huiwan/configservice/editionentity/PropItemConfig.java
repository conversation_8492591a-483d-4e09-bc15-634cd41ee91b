package com.huiwan.configservice.editionentity;

import android.text.TextUtils;
import android.util.SparseArray;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.collection.ListWrapper;
import com.huiwan.configservice.IConfig;
import com.huiwan.configservice.PropItemInterface;
import com.huiwan.configservice.R;
import com.huiwan.configservice.constentity.propextra.GiftExtra;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.constants.ConfigId;
import com.wejoy.libpbksp.PbWireFiled;
import com.wejoy.libpbksp.PbWireObj;
import com.wepie.skynet.apm.Apm;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

// Created by bigwen on 2018/11/28.
@JsonAdapter(PropItemConfig.PropItemConfigAdapter.class)
@PbWireObj
public class PropItemConfig implements PropItemInterface, IConfig {

    @SerializedName("version_num")
    @PbWireFiled(tag = 1)
    private int version = 0;

    @SerializedName("md5_version")
    @PbWireFiled(tag = 2)
    private String versionMD5 = "";

    @SerializedName("region")
    @PbWireFiled(tag = 3)
    private String region = "";

    @SerializedName("item_list")
    @PbWireFiled(tag = 4)
    public List<PropItem> propItemList = new ArrayList<>();

    @Expose(serialize = false, deserialize = false)
    private transient SparseArray<PropItem> propItemMapCache = new SparseArray<>(20);

    /**
     * 注意添加字段时，同时补充 adapter 解析与序列化
     * {@link PropItemConfigAdapter}
     */
    public PropItemConfig() {
    }

    @Override
    public int getConfigId() {
        return ConfigId.PROP_ITEM_CONFIG_ID;
    }

    @Override
    public int getVersion() {
        return version;
    }

    @Override
    public String getVersionMD5() {
        return versionMD5;
    }

    @Override
    public String getRegion() {
        return region;
    }

    @Nullable
    public PropItem getPropItem(int id) {
        PropItem item = propItemMapCache.get(id);
        if (item != null) {
            return item;
        }
        for (PropItem propItem : propItemList) {
            if (propItem.getItemId() == id) {
                propItemMapCache.put(id, propItem);
                return propItem;
            }
        }
        return null;
    }

    public List<PropItem> getAllRingList() {
        return getListByType(PropItem.TYPE_RING);
    }

    public List<PropItem> getMarryRingList() {
        List<PropItem> marryRingList = new ArrayList<>();
        List<PropItem> allRings = getAllRingList();
        for (PropItem propItem : allRings) {
            if (!propItem.isSingleRing() && GlobalConfigManager.showInCurrent(propItem.region)) {
                marryRingList.add(propItem);
            }
        }
        return marryRingList;
    }

    public List<PropItem> getSingleRingList() {
        List<PropItem> singleRingList = new ArrayList<>();
        List<PropItem> allRings = getAllRingList();
        for (PropItem propItem : allRings) {
            if (propItem.isSingleRing() && GlobalConfigManager.showInCurrent(propItem.region)) {
                singleRingList.add(propItem);
            }
        }
        return singleRingList;
    }

    public List<PropItem> getHomeAnimList() {
        return getListByType(PropItem.TYPE_HOME_ANIM);
    }

    public List<PropItem> getDrawboardList() {
        return getListByType(PropItem.TYPE_DRAW_BOARD);
    }

    public List<PropItem> getChatBubbleList() {
        return getListByType(PropItem.TYPE_CHAT_BUBBLE);
    }


    public List<PropItem> getVoiceBubbleList() {
        return getListByType(PropItem.TYPE_VOICE_BUBBLE);
    }

    public List<PropItem> getVoiceEnterList() {
        return getListByType(PropItem.TYPE_VOICE_ENTER_ANIM);
    }

    public List<PropItem> getRedPacketSkinList() {
        return getListByType(PropItem.TYPE_RED_PACKET_SKIN);
    }

    public List<PropItem> getCarAnimList() {
        return getListByType(PropItem.TYPE_CAR_ANIM);
    }

    public List<PropItem> getDiscoverDecorList() {
        return getListByType(PropItem.TYPE_DISCOVER_DECOR);
    }

    public List<PropItem> getDrawBoardListIgnoreOpen() {
        return getListByTypeIgnoreOpen(PropItem.TYPE_DRAW_BOARD);
    }

    public List<PropItem> getHeadDecoList() {
        return getListByType(PropItem.TYPE_HEAD_DECORATION);
    }

    public List<PropItem> getFunctionList() {
        return getListByType(PropItem.TYPE_PROP);
    }

    public List<PropItem> getProposeTemplate() {
        return getListByType(PropItem.TYPE_PROPOSE_TEMPLATE);
    }

    public List<PropItem> getInvitationTemplate() {
        return getListByType(PropItem.TYPE_INVITATION);
    }

    public List<PropItem> getWeddingTemplate() {
        return getListByType(PropItem.TYPE_WEDDING_BG);
    }

    /**
     * 单身戒数量是否大于等于3
     *
     * @return
     */
    public boolean isSingleRingMoreThanThree() {
        List<PropItem> ringList = getAllRingList();
        int singleRingNum = 0;
        for (PropItem propItem : ringList) {
            if (propItem.isSingleRing() && GlobalConfigManager.showInCurrent(propItem.region)) {
                singleRingNum++;
            }
            if (singleRingNum >= 3) return true;
        }
        return false;
    }

    public List<PropItem> getListByType(int type) {
        List<PropItem> typeItemList = new ArrayList<>();
        for (PropItem propItem : propItemList) {
            if (propItem.getType() == type && propItem.isOpen() && GlobalConfigManager.showInCurrent(propItem.region)) {
                typeItemList.add(propItem);
            }
        }
        return typeItemList;
    }

    public List<PropItem> getListByTypeIgnoreOpen(int type) {
        List<PropItem> ringList = new ArrayList<>();
        for (PropItem propItem : propItemList) {
            if (propItem.getType() == type) {
                ringList.add(propItem);
            }
        }
        return ringList;
    }

    public List<PropItem> getPropListByType(int type, boolean isSingleRing) {
        switch (type) {
            case PropItem.TYPE_RING:
                if (isSingleRing) {
                    return getSingleRingList();
                } else {
                    return getMarryRingList();
                }
            case PropItem.TYPE_DRAW_BOARD:
                return getDrawboardList();
            case PropItem.TYPE_HOME_ANIM:
                return getHomeAnimList();
            case PropItem.TYPE_HEAD_DECORATION:
                return getHeadDecoList();
            case PropItem.TYPE_PROP:
                return getFunctionList();
            default:
                return getListByType(type);
        }
    }

    public static String getNameByType(int type, boolean isSingleRing) {
        switch (type) {
            case PropItem.TYPE_RING:
                if (isSingleRing) {
                    return ResUtil.getString(R.string.prop_item_name_single_ring);
                } else {
                    return ResUtil.getString(R.string.prop_item_name_marry_ring);
                }
            case PropItem.TYPE_DRAW_BOARD:
                return ResUtil.getStr(R.string.prop_item_name_draw_board);
            case PropItem.TYPE_HOME_ANIM:
                return ResUtil.getStr(R.string.prop_item_name_home_anim);
            case PropItem.TYPE_HEAD_DECORATION:
                return ResUtil.getStr(R.string.prop_item_name_head_decoration);
            case PropItem.TYPE_PROP:
                return ResUtil.getStr(R.string.prop_item_name_type_prop);
            case PropItem.TYPE_RED_PACKET:
                return ResUtil.getString(R.string.prop_item_name_red_packet);
            case PropItem.TYPE_RED_PACKET_SKIN:
                return ResUtil.getString(R.string.prop_item_name_packet_skin);
            case PropItem.TYPE_VOICE_BUBBLE:
                return ResUtil.getStr(R.string.prop_item_name_voice_bubble);
            case PropItem.TYPE_CHAT_BUBBLE:
                return ResUtil.getStr(R.string.prop_item_name_chat_bubble);
            case PropItem.TYPE_VOICE_ENTER_ANIM:
                return ResUtil.getStr(R.string.prop_item_name_enter_anim);
            case PropItem.TYPE_DISCOVER_DECOR:
                return ResUtil.getStr(R.string.prop_item_name_discover_decor);
            case PropItem.TYPE_FAMILY_AVATAR_DECOR:
                return ResUtil.getStr(R.string.prop_item_name_family_avatar_decor);
            case PropItem.TYPE_FAMILY_BOX:
                return ResUtil.getStr(R.string.prop_item_name_family_supplies);
            case PropItem.TYPE_FAMILY_CHAT_BUBBLE:
                return ResUtil.getStr(R.string.prop_item_name_family_chat_bubble);
            case PropItem.TYPE_FAMILY_LIGHT:
                return ResUtil.getStr(R.string.prop_item_name_family_light);
            case PropItem.TYPE_FAMILY_COINS:
                return ResUtil.getStr(R.string.prop_item_name_family_coin);
            case PropItem.TYPE_GIFT_CARD:
            case PropItem.TYPE_GIFT_CARD_LIMITED:
                return ResUtil.getStr(R.string.prop_item_name_gift_card);
            case PropItem.TYPE_VOICE_THEME:
                return ResUtil.getStr(R.string.prop_item_name_voice_theme);
            case PropItem.TYPE_USER_TAG:
                return ResUtil.getStr(R.string.prop_item_name_user_tag);
            case PropItem.TYPE_VOICE_MIC:
                return ResUtil.getStr(R.string.prop_item_name_mic_anim);
            case PropItem.TYPE_CAR_ANIM:
                return ResUtil.getStr(R.string.prop_item_name_car_anim);
            case PropItem.TYPE_VOICE_ROOM_MARKING:
                return ResUtil.getStr(R.string.prop_item_voice_room_marking);
            case PropItem.TYPE_DRAGON_NAMING:
                return ResUtil.getStr(R.string.prop_item_dragon_naming);
            case PropItem.TYPE_JACKAROO_PIECE:
                return ResUtil.getStr(R.string.prop_item_name_jackaroo_piece);
            case PropItem.TYPE_JACKAROO_BOARD:
                return ResUtil.getStr(R.string.prop_item_name_jackaroo_board);
            case PropItem.TYPE_JACKAROO_PLAY_CARD_ANIMATION:
                return ResUtil.getStr(R.string.prop_item_name_jackaroo_play_card_animation);
            case PropItem.TYPE_JACKAROO_KILL_ANIMATION:
                return ResUtil.getStr(R.string.prop_item_name_jackaroo_kill_animation);
            case PropItem.TYPE_JACKAROO_EXCHANGE_ANIMATION:
                return ResUtil.getStr(R.string.prop_item_name_jackaroo_exchange_animation);
            default:
                return "";
        }
    }

    public List<PropItem> getAllPropItemList() {
        return propItemList;
    }

    public List<PropItem> getRegionShownList() {
        return new ArrayList<>(new ListWrapper<>(propItemList).filter(propItem -> GlobalConfigManager.showInCurrent(propItem.region)));
    }

    public Set<Integer> getGiftCardGiftIdSet() {
        Set<Integer> set = new HashSet<>();
        for (PropItem propItem : propItemList) {
            // 只能判断是否是礼物卡，不能判断是否是open，因为用户背包里可能还有
            if (propItem.getType() == PropItem.TYPE_GIFT_CARD || propItem.getType() == PropItem.TYPE_GIFT_CARD_LIMITED) {
                GiftExtra giftExtra = propItem.getGiftExtra();
                if (giftExtra != null) {
                    set.add(giftExtra.giftId);
                }
            }
        }
        return set;
    }

    public boolean isAvailable() {
        if (!TextUtils.isEmpty(versionMD5)) {
            return true;
        } else {
            return version > 0;
        }
    }


    public static class PropItemConfigAdapter extends TypeAdapter<PropItemConfig> {
        private final PropItem.PropItemGsonAdapter adapter = new PropItem.PropItemGsonAdapter();

        @Keep
        public PropItemConfigAdapter() {
            Apm.recordTimePoint("PropItemConfigAdapter#<init>");
        }

        @Override
        public void write(JsonWriter out, PropItemConfig itemsConfig) throws IOException {
            out.beginObject();
            out.name("version_num").value(itemsConfig.version);
            out.name("md5_version").value(itemsConfig.versionMD5);
            out.name("region").value(itemsConfig.region);
            out.name("item_list");
            out.beginArray();
            for (PropItem item : itemsConfig.propItemList) {
                adapter.write(out, item);
            }
            out.endArray();
            out.endObject();
        }

        @Override
        public PropItemConfig read(JsonReader in) throws IOException {
            PropItemConfig config = new PropItemConfig();
            in.beginObject();
            while (in.hasNext()) {
                switch (in.nextName()) {
                    case "version_num":
                        config.version = in.nextInt();
                        break;
                    case "md5_version":
                        config.versionMD5 = in.nextString();
                        break;
                    case "region":
                        config.region = in.nextString();
                        break;
                    case "item_list": {
                        in.beginArray();
                        while (in.hasNext()) {
                            PropItem item = adapter.read(in);
                            config.propItemList.add(item);
                        }
                        in.endArray();
                        break;
                    }
                    default:
                        in.skipValue();
                        break;
                }
            }
            in.endObject();
            return config;
        }
    }
}
