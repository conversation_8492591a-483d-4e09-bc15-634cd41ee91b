package com.huiwan.configservice.editionentity.loader

import android.os.Trace
import com.huiwan.base.util.FileUtil
import com.huiwan.configservice.editionentity.IVersion
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.PropItemConfig_PbAdapter
import com.wejoy.libpbksp.PbAdapter
import com.wepie.liblog.main.HLog
import java.io.File

///**
// * 针对 [ConstV3Info] 的 本地缓存处理器
// */
//fun localPbLoaderConstV3(): CfgLocalLoader<ConstV3Info> {
//    return RealLocalPbLoader(
//        ConstV3Info_PbAdapter,
//        "ConstV3InfoPbLoader",
//        "constV3",
//        "LoadPbConstV3Info"
//    )
//}

/**
 * 针对 PropItems 的 本地缓存处理器
 */
fun localPbLoaderPropItem(): CfgLocalLoader<PropItemConfig> {
    return RealLocalPbLoader(
        PropItemConfig_PbAdapter,
        "PropItemPbLoader",
        "prop-item",
        "LoadPbItemList"
    )
}

///**
// * 针对 礼物配置的本地缓存处理器。
// */
//fun localPbLoaderGiftsConfig(): CfgLocalLoader<GiftsConfig> {
//    return RealLocalPbLoader(
//        GiftsConfig_PbAdapter,
//        "GiftsConfigPbLoader",
//        "gift",
//        "LoadPbGiftsConfig"
//    )
//}
//
///**
// * 首页配置
// */
//fun localPbLoaderHomeConfig593(): CfgLocalLoader<HomeConfig593> {
//    return RealLocalPbLoader(
//        HomeConfig593_PbAdapter,
//        "HomeConfig593PbLoader",
//        "HomeConfig593",
//        "LoadPbHomeConfig593"
//    )
//}
//
///**
// * 用户称号
// *
// */
//fun localPbUserTagConfig(): CfgLocalLoader<UserTagConfig> {
//    return RealLocalPbLoader(
//        UserTagConfig_PbAdapter,
//        "UserTagConfigPbLoader",
//        "UserTagConfig",
//        "LoadPbUserTagConfig"
//    )
//}
//
///**
// * 游戏配置
// */
//fun localPbLoaderGamesConfig(): CfgLocalLoader<GamesConfig> {
//    return RealLocalPbLoader(
//        GamesConfig_PbAdapter,
//        "GamesConfigPbLoader",
//        "GamesConfig",
//        "LoadPbGamesConfig"
//    )
//}
//
///**
// * 语音房配置
// */
//fun localPbLoaderVoiceRoomConfig(): CfgLocalLoader<VoiceRoomConfig> {
//    return RealLocalPbLoader(
//        VoiceRoomConfig_PbAdapter,
//        "VoiceRoomConfigPbLoader",
//        "VoiceRoomConfig",
//        "LoadPbVoiceRoomConfig"
//    )
//}
//
///**
// * 家族配置
// */
//fun localPbLoaderFamilyConfig(): CfgLocalLoader<FamilyConfig> {
//    return RealLocalPbLoader(
//        FamilyConfig_PbAdapter,
//        "FamilyConfigPbLoader",
//        "FamilyConfig",
//        "LoadPbFamilyConfig"
//    )
//}
//
///**
// * 亲密关系
// */
//fun localPbLoaderRelationConfig(): CfgLocalLoader<RelationConfig> {
//    return RealLocalPbLoader(
//        RelationConfig_PbAdapter,
//        "RelationConfigPbLoader",
//        "RelationConfig",
//        "LoadPbRelationConfig"
//    )
//}
//
///**
// * [AvatarPropConfig] 对应的配置处理 loader
// */
//fun localPbLoaderAvatarProp(): CfgLocalLoader<AvatarPropConfig> {
//    return RealLocalPbLoader(
//        AvatarPropConfig_PbAdapter,
//        "AvatarPropConfigPbLoader",
//        "AvatarPropConfig",
//        "LoadPbAvatarPropConfig"
//    )
//}
//
///**
// * 亲密关系
// */
//fun localPbLoaderAvatarItem(): CfgLocalLoader<AvatarItemConfig> {
//    return RealLocalPbLoader(
//        AvatarItemConfig_PbAdapter,
//        "AvatarItemConfigPbLoader",
//        "AvatarItemConfig",
//        "LoadPbAvatarItemConfig"
//    )
//}
//
///**
// * 国家地区信息配置
// */
//fun localPbLoaderAreaConfig(): CfgLocalLoader<AreaConfig> {
//    return RealLocalPbLoader(
//        AreaConfig_PbAdapter,
//        "AreaConfigPbLoader",
//        "AreaConfig",
//        "LoadPbAreaConfig"
//    )
//}

/**
 * 真实执行的 本地 pb 缓存 loader.
 */
private class RealLocalPbLoader<T : IVersion>(
    private val adapter: PbAdapter<T>,
    private val logTag: String,
    private val typeTag: String,
    private val traceSection: String
) : CfgLocalLoader<T> {

    override fun load(region: String, path: String): T? {
        HLog.d(logTag, "load {} pb start", typeTag)
        Trace.beginSection(traceSection)
        try {
            val p = FileUtil.getFullPath(path) + ".pb"
            val file = File(p)
            if (!file.exists()) {
                HLog.d(logTag, "not exist")
                return null
            }
            val pbConfig = file.inputStream().use {
                adapter.decode(it)
            }
            HLog.d(logTag, "load pb config md5={}", pbConfig.versionMD5)
            return pbConfig
        } catch (e: Throwable) {
            HLog.d(logTag, HLog.USR, "error load pb {} {}", typeTag, e)
        } finally {
            Trace.endSection()
            HLog.d(logTag, "load pb end")
        }
        return null
    }

    override fun save(region: String, path: String, data: T) {
        val p = FileUtil.getFullPath(path) + ".pb"
        val f = File(p)
        if (!f.exists()) {
            f.createNewFile()
        }
        try {
            f.outputStream().use {
                adapter.encode(it, data)
            }
            HLog.d(logTag, "save pb {} config to: {}", typeTag, p)
        } catch (e: Exception) {
            HLog.e(logTag, HLog.USR, "error save local file {} {}", p, e)
        }
    }
}