package com.huiwan.configservice.constentity.propextra;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2019-12-11
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class PriceItem {
    @SerializedName("time_limit")
    private int time_limit = 0;
    @SerializedName("time_limit_string")
    private String time_limit_string = "";
    @SerializedName("price")
    private int price = 0;
    @SerializedName("reward_ids")
    private List<Integer> reward_ids = new ArrayList<>();
    /**
     * 永久时 time 为 0
     */
    @SerializedName("reward_times")
    private List<Integer> reward_times = new ArrayList<>();

    @SerializedName("choose_gift")
    private int choose_gift = 1;

    public int getTimeLimit() {
        return time_limit;
    }

    public String getTimeLimitString() {
        return time_limit_string;
    }

    public int getPrice() {
        return price;
    }

    public List<Integer> getRewardIds() {
        return reward_ids;
    }

    public List<Integer> getRewardTime() {
        return reward_times;
    }

    public boolean chooseAble() {
        return choose_gift == 1;
    }
}
