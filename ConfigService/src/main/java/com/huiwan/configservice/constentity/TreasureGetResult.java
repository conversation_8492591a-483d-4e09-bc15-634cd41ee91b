package com.huiwan.configservice.constentity;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

// http://yapi.wepie.com/project/714/interface/api/30695
public class TreasureGetResult {

    @SerializedName("rewards")
    public List<TreasureReward> rewards=new ArrayList<>();

    public static class TreasureReward{

        @SerializedName("reward_id")
        public int rewardID;

        @SerializedName("reward_val")
        public int rewardVal;
    }
}
