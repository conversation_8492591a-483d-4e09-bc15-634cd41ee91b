package com.huiwan.configservice.constentity

import com.google.gson.annotations.SerializedName
import com.huiwan.configservice.editionentity.GameConfig.CURRENCY_COIN
import com.huiwan.configservice.editionentity.GameConfig.CurrencyType

class BattleBroadcastConfig {
    @SerializedName("battle_record")
    val battleRecordList: MutableList<BattleItem> = ArrayList()
}

class BattleItem {
    @SerializedName("winner_info")
    lateinit var winnerInfo: UserInfo

    @SerializedName("loser_info")
    lateinit var loserInfo: UserInfo

    @SerializedName("game_type")
    var gameType: Int = 0

    @SerializedName("game_mode")
    var gameMode: Int = 0

    @SerializedName("bet_level")
    val betLevel: Int = 0

    @SerializedName("mode")
    var mode: Int = 0

    @SerializedName("game_currency_type")
    @CurrencyType
    var currencyType: Int = CURRENCY_COIN

    @SerializedName("get_coin")
    var getCoin: Int = 0

    @SerializedName("game_currency_unit")
    var unit: String = "";
}

class UserInfo {
    @SerializedName("uid")
    var uid = 0

    @SerializedName("nickname")
    lateinit var nickname: String

    @SerializedName("Headimgurl")
    lateinit var headImgUrl: String
}