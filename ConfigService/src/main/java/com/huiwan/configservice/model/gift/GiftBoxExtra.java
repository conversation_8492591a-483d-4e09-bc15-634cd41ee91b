package com.huiwan.configservice.model.gift;

import com.google.gson.annotations.SerializedName;
import com.wejoy.libpbksp.PbWireObj;
import com.wejoy.libpbksp.PbWireFiled;

/**
 * date 2020/6/29
 * email <EMAIL>
 *
 * <AUTHOR>
 */
@PbWireObj
public class GiftBoxExtra {
    @SerializedName("preview_url")
    @PbWireFiled(tag = 1)
    public String previewUrl = "";
    @SerializedName("btn_url_highlight")
    @PbWireFiled(tag = 2)
    public String btnUrlDown = "";
    @SerializedName("btn_url_normal")
    @PbWireFiled(tag = 3)
    public String btnUrlUp = "";
    @SerializedName("scene_help_url")
    @PbWireFiled(tag = 4)
    public String helpUrl = "";
    @SerializedName("web_loading_url")
    @PbWireFiled(tag = 5)
    public String webLoadingUrl = "";
    @SerializedName("is_h5_fullscreen")
    @PbWireFiled(tag = 6)
    public int h5FullScreen = 0;

    @SerializedName("unlock_tip_id")
    @PbWireFiled(tag = 7)
    public int unlockTipId = 0;

    @SerializedName("extra_prop_id")
    @PbWireFiled(tag = 8)
    public int extraPropId = 0;

    @SerializedName("extra_prop_value")
    @PbWireFiled(tag = 9)
    public int extraPropValue = 0;

}
