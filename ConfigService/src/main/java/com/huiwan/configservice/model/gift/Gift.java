package com.huiwan.configservice.model.gift;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import com.google.gson.annotations.SerializedName;
import com.huiwan.base.util.URIUtil;
import com.huiwan.configservice.WejoyUserConfigHelp;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.configservice.model.WejoyUserConfig;
import com.huiwan.store.file.FileConfig;
import com.wejoy.libpbksp.PbWireFiled;
import com.wejoy.libpbksp.PbWireObj;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by three on 15/9/1.
 */
@PbWireObj
public class Gift {
    public static final int GIFT_TYPE_DEFAULT = 0;
    // 语音房整蛊表情
    public static final int GIFT_TYPE_TRICK_FACE = 1;

    /** 星空盒子等点击预览 url 弹出对话框的 */
    public static final int GIFT_TYPE_BOX = 3;

    /**活动礼物，例如小云雀。点击后可以跳转半屏H5或者全屏H5*/
    public static final int GIFT_TYPE_ACTIVITY = 4;

    /**
     * 二级货币-游戏币礼物
     */
    public static final int GIFT_TYPE_CHIP_COIN = 5;

    private static final int GIFT_BROAD_TMP = 1;
    public static final int GIFT_BROAD_APP = 2;

    public static final int TYPE_IMG = 1;
    public static final int TYPE_GIF = 2;
    public static final int TYPE_JSON = 3;
    public static final int TYPE_SVGA = 4;
    public static final int TYPE_ALPHA_VIDEO = 5;
    public static final int TYPE_VAP_VIDEO = 6;

    public static final int GIFT_ID_FLOWER = 1;
    public static final int GIFT_ID_DUREX = 2;
    public static final int GIFT_ID_FIREWORKS = 25;
    public static final int GIFT_ID_EGG = 30;
    public static final int GIFT_ID_CHEST = 34;
    public static final int GIFT_ID_BRICK = 899;
    public static final int GIFT_ID_PRAISE = 900;

    public static final int SUB_TYPE_SHOW_USER_HEAD = 1;//展示用户头像的礼物类型

    public static final int GIFT_ID_FLOWER_D = 967;//新画猜礼物
    public static final int GIFT_ID_EGG_D = 977;//新画猜礼物

    public static final int GIFT_SAY_GUESS_CAll = 1072;//你说我猜打call礼物
    public static final int GIFT_SAY_GUESS_UNKNOWN = 1071;//你说我猜黑人问号礼物
    public final static int GIFT_FUEL_GOOD_ID = 1122;//火箭助力燃料的id

    public static final int GIFT_ROCKET = 1121;//火箭礼物
    public static final int GIFT_NATION_FLAG = 110080;//世界之旗礼物

    public static final int GIFT_LOCAL_ID_LIKE = Integer.MAX_VALUE - 1;

    public static final int COMBO_BG_TYPE_RED = 2;
    public static final int COMBO_BG_TYPE_BLUE = 1;

    public static final int GIFT_CHARM_TYPE_NONE = -1;
    public static final int GIFT_CHARM_TYPE_POSITIVE = 1;
    public static final int GIFT_CHARM_TYPE_NEGATIVE = 2;

    public static final int GIFT_SCENE_BASE = 1;
    /**
     * 5.9.23 开始将这些细节拆分开
     */
    @Deprecated
    public static final int GIFT_SCENE_CHAT_VOICE_VOC = GIFT_SCENE_BASE;
    public static final int GIFT_SCENE_WEDDING          = GIFT_SCENE_BASE << 1;
    public static final int GIFT_SCENE_PROPOSE          = GIFT_SCENE_BASE << 2;
    public static final int GIFT_SCENE_WEDDING_ALL      = GIFT_SCENE_BASE << 3;
    public static final int GIFT_SCENE_VOICE_ALL        = GIFT_SCENE_BASE << 4;
    public static final int GIFT_SCENE_AUCTION_ROOM     = GIFT_SCENE_BASE << 5;
    /**
     * @since 5.9.23
     */
    public static final int GIFT_SCENE_CHAT             = GIFT_SCENE_BASE << 6;
    /** 语音房，小游戏 */
    public static final int GIFT_SCENE_TMP_ROOM         = GIFT_SCENE_BASE << 7;
    public static final int GIFT_SCENE_SONG             = GIFT_SCENE_BASE << 8;
    public static final int GIFT_SCENE_LOVER_HOME       = GIFT_SCENE_BASE << 9;
    public static final int GIFT_SCENE_GROUP      = GIFT_SCENE_BASE << 10;
    public static final int GIFT_SCENE_FAMILY      = GIFT_SCENE_BASE << 11;
    public static final int GIFT_SCENE_X_ROOM     = GIFT_SCENE_BASE << 12;
    public static final int GIFT_SCENE_BIG_WINNER     = GIFT_SCENE_BASE << 13;

    public static final int GIFT_SCENE_ROOM_MEMBER = GIFT_SCENE_BASE << 19; //房间成员
    public static final int GIFT_SCENE_ROOM_MEMBER_ALL = GIFT_SCENE_BASE << 20; //房间成员全员全麦
    public static final int GIFT_MJ_ROOM = GIFT_SCENE_LOVER_HOME;
    public static final int GIFT_SCENE_ALL              = 0xffffffff;

    public static final int UNITY_GIFT_FLOWER = 1;
    public static final int UNITY_GIFT_EGG = 2;
    public static final int UNITY_GIFT_UNDEFINED = 0;

    public static final int CATEGORY_DEFAULT = 0;
    public static final int CATEGORY_BAG = -1;
    public static final int CATEGORY_VIP = 3;
    public static final int CATEGORY_FLAG = 4;

    public static final int CATEGORY_ROOM_MEMBER = 8;

    public static int getUnityGiftId(int giftId) {
        if (giftId == GIFT_ID_EGG_D) return UNITY_GIFT_EGG;
        if (giftId == GIFT_ID_FLOWER_D) return UNITY_GIFT_FLOWER;
        return UNITY_GIFT_UNDEFINED;
    }

    public static int getGiftIdFromUnity(int gift) {
        if (gift == UNITY_GIFT_FLOWER) return GIFT_ID_FLOWER_D;
        else return GIFT_ID_EGG_D;
    }


    @PbWireFiled(tag = 1)
    @SerializedName("gift_id")
    int giftId;
    // 是否礼盒礼物,  送出去后能爆出其他礼物或若干金币的礼物
    @PbWireFiled(tag = 2)
    @SerializedName("is_loot_gift")
    int isLootGift = 0;
    // 盲盒礼物
    @PbWireFiled(tag = 3)
    @SerializedName("is_blind_gift")
    public boolean isBlindGift = false;

    // 新增类型时，区分默认礼物，整蛊表情，豆豆礼物等，在 supported 处更新一下。
    @PbWireFiled(tag = 4)
    @SerializedName("gift_type")
    int giftType;

    @PbWireFiled(tag = 5)
    @SerializedName("name")
    String name = "";

    @PbWireFiled(tag = 6)
    @SerializedName("price")
    int price;

    @PbWireFiled(tag = 7)
    @SerializedName("unit")
    String unit;

    @PbWireFiled(tag = 8)
    @SerializedName("numbers")
    String numbers = "";

    @PbWireFiled(tag = 9)
    @SerializedName("media_url")
    String mediaUrl;

    @PbWireFiled(tag = 10)
    @SerializedName("tag")
    String tag;

    @PbWireFiled(tag = 12)
    @SerializedName("text_list_v2")
    private String textListV2 = "";

    @PbWireFiled(tag = 13)
    @SerializedName("open")
    int open;

    @PbWireFiled(tag = 14)
    @SerializedName("type")
    public int type = 1;// 1图片，2 gift 3 json 4 svga

    @PbWireFiled(tag = 15)
    @SerializedName("left_margin")
    public float leftMargin = 0.5f;

    @PbWireFiled(tag = 16)
    @SerializedName("top_margin")
    public float topMargin = 0.5f;

    @PbWireFiled(tag = 17)
    @SerializedName("x_offset")
    public float xOffset = -0.5f;

    @PbWireFiled(tag = 18)
    @SerializedName("y_offset")
    public float yOffset = -0.5f;

    @PbWireFiled(tag = 19)
    @SerializedName("display_width")
    public float displayWidth = 0.5f;//展示宽度占屏幕宽度比

    @PbWireFiled(tag = 20)
    @SerializedName("gif_url")
    public String gifUrl = "";

    @PbWireFiled(tag = 21)
    @SerializedName("android_audio_url")
    public String androidAudioUrl = "";

    /** json 和 svga 公用该 url */
    @PbWireFiled(tag = 22)
    @SerializedName("json_url")
    public String jsonUrl = "";

    @PbWireFiled(tag = 23)
    @SerializedName("play_speed")
    int playSpeed = 0;

    @PbWireFiled(tag = 24)
    @SerializedName("adapt_type")
    public int adapt_type = 1;//1 居中， 2 屏幕等宽， 3 屏幕等高 4顶对齐 5底对齐

    @PbWireFiled(tag = 25)
    @SerializedName("is_long_audio")
    int isLongAudio = 0;//是否是长音频，区分播放方法

    @PbWireFiled(tag = 26)
    @SerializedName("charm")
    public int charm = 0;

    @PbWireFiled(tag = 27)
    @SerializedName("desc")
    public String desc = "";

    @PbWireFiled(tag = 28)
    @SerializedName("combo_bg_type")
    int comboBgType = COMBO_BG_TYPE_RED;

    @PbWireFiled(tag = 29)
    @SerializedName("show_combo")
    int showCombo = 0;

    @PbWireFiled(tag = 30)
    @SerializedName("animation_list")
    public List<NumericGiftAnim> numericGiftAnimList = new ArrayList<>();

    /**
     * region, 大区，逗号分割 "U,C"
     */
    @PbWireFiled(tag = 31)
    @SerializedName("region")
    public String region = "";

    @PbWireFiled(tag = 32)
    @SerializedName("introduce_url")
    public String introduceUrl = "";

    /** web dialog,
     * @since 5.9.23
     * */
    @PbWireFiled(tag = 33)
    @SerializedName("skybox_extra")
    GiftBoxExtra boxExtra;

    @PbWireFiled(tag = 34)
    @SerializedName("scene")
    public int scene = 0;

    @PbWireFiled(tag = 35)
    @SerializedName("scene_card")
    public int sceneCard = 0;

    @PbWireFiled(tag = 36)
    @SerializedName("broadcast")
    public int broadcast = 0;

    @PbWireFiled(tag = 37)
    @SerializedName("category")
    public int category = 0;

    @PbWireFiled(tag = 38)
    @SerializedName("broadcast_anim_url")
    public String broadcastAnimUrl = "";

    @PbWireFiled(tag = 39)
    @SerializedName("sub_type")
    public int subType;

    @PbWireFiled(tag = 40)
    @SerializedName("font_size")
    public int fontSize = 22;

    @PbWireFiled(tag = 41)
    @SerializedName("font_color")
    public String fontColor = "#ffffff";

    @PbWireFiled(tag = 43)
    @SerializedName("is_vip")
    int vipLevel;//vip等级，名字为is_vip是因为服务器的某些原因无法改这个字段

    /** vip 用户定制礼物*/
    @PbWireFiled(tag = 44)
    @SerializedName("owner_uids")
    List<Integer> ownerUidList = new ArrayList<>();

    /**
     *  礼物在面板的位置顺序，用于打点
     *  值为礼物的 category + "-" + positon
     *  position该礼物在当前面板的显示位置，若礼物在当前 category的第二页第一个位置，则为9
     */
    @PbWireFiled(tag = 45)
    @SerializedName("giftPosition")
    public String giftPosition = "";

    public boolean isH5FullScreen() {
        return boxExtra.h5FullScreen == 1;
    }

    public int getGift_id() {
        return giftId;
    }

    public String getName() {
        return name;
    }

    public int getPrice() {
        return price;
    }

    public String getUnit() {
        return unit;
    }

    public String[] getNumberArray() {
        return numbers.split(",");
    }

    public String getMedia_url() {
        return mediaUrl;
    }

    public String getTag() {
        return tag;
    }

    public int getOpen() {
        return open;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public WejoyUserConfig.LockedGift getLockedConfig() {
        List<WejoyUserConfig.LockedGift> lockedGifts = WejoyUserConfigHelp.getInstance().getUserConfig().getLockedGifts();
        if (lockedGifts != null && lockedGifts.size() > 0) {
            for (int i = 0; i < lockedGifts.size(); i++) {
                WejoyUserConfig.LockedGift lockedGift = lockedGifts.get(i);
                if (lockedGift.getGiftId() == giftId) {
                    return lockedGift;
                }
            }
        }
        return null;
    }

    public boolean getIsLocked() {
        WejoyUserConfig.LockedGift lockedGift = getLockedConfig();
        return lockedGift != null;
    }

    public String getLockedReason() {
        WejoyUserConfig.LockedGift lockedGift = getLockedConfig();
        if (lockedGift != null) {
            return lockedGift.getLockedReason();
        }
        return "";
    }

    public boolean isVipGift() {
        return vipLevel > 0;
    }

    public boolean enableUseVipGift(int level) {
        return level >= getVipLevel();
    }

    private String[] getTextArray(int index) {
        JsonArray jsonArray = JsonParser.parseString(textListV2).getAsJsonArray();

        JsonArray numTextArray = jsonArray.get(index).getAsJsonArray();
        String[] array = new String[numTextArray.size()];
        for (int i = 0; i < numTextArray.size(); i++) {
            array[i] = numTextArray.get(i).getAsString();
        }
        return array;
    }

    public String[] getNumTextArray(int num) {
        String[] numberArray = getNumberArray();

        String numStr = String.valueOf(num);
        int index = 0;
        for(int i=0;i<numberArray.length;i++) {
            if(numStr.equals(numberArray[i])) {
                index = i;
                break;
            }
        }
        return getTextArray(index);
    }

    public int getGiftType() {
        return giftType;
    }

    public boolean isVoiceFullGift() {
        return broadcast == GIFT_BROAD_TMP;
    }

    public boolean isAppFullGift() {
        return broadcast == GIFT_BROAD_APP;
    }

    public boolean isSupportedGift() {
        return giftType == GIFT_TYPE_DEFAULT ||
                giftType == GIFT_TYPE_TRICK_FACE ||
                giftType == GIFT_TYPE_BOX ||
                giftType == GIFT_TYPE_ACTIVITY ||
                giftType == GIFT_TYPE_CHIP_COIN;
    }

    public boolean isTypeGif() {
        return type == TYPE_GIF;
    }

    public boolean isJsonGift() {
        return type == TYPE_JSON;
    }

    public boolean isTypeSvga() {
        return type == TYPE_SVGA;
    }

    public boolean isTypeAlphaVideo() {
        return type == TYPE_ALPHA_VIDEO;
    }

    public boolean isTypeAlphaVideo(int num, int charmType, int animIndex) {
        return judgeType(num, charmType, animIndex, TYPE_ALPHA_VIDEO);
    }

    public boolean isTypeJson(int num, int charmType, int animIndex) {
        return judgeType(num, charmType, animIndex, TYPE_JSON);
    }

    public boolean isTypeVapVideo(int num, int charmType, int animIndex) {
        return judgeType(num, charmType, animIndex, TYPE_VAP_VIDEO);
    }

    public boolean isTypeVapVideo() {
        return type == TYPE_VAP_VIDEO;
    }

    public boolean isTypeSvga(int num, int charmType, int animIndex) {
        return judgeType(num, charmType, animIndex, TYPE_SVGA);
    }

    public boolean isTypeGif(int num, int charmType, int animIndex) {
        return judgeType(num, charmType, animIndex, TYPE_GIF);
    }

    private boolean judgeType(int num, int charmType, int animIndex, int giftType) {
        int size = numericGiftAnimList.size();
        for (int i = size - 1; i > -1; i--) {
            NumericGiftAnim numericGiftAnim = numericGiftAnimList.get(i);
            if (num >= numericGiftAnim.num) {
                return numericGiftAnim.judgeType(charmType, animIndex, giftType);
            }
        }
        return this.type == giftType;
    }

    public String getLocalAnimUrl(int num, int charmType, int animIndex) {
        int size = numericGiftAnimList.size();
        for (int i = size - 1; i > -1; i--) {
            NumericGiftAnim numericGiftAnim = numericGiftAnimList.get(i);
            if (num >= numericGiftAnim.num) {
                return numericGiftAnim.getLocalAnimUrl(charmType, animIndex);
            }
        }
        if (isTypeGif()) {
            return Gift.url2LocalPath(gifUrl, type);
        } else if (isJsonGift() || isTypeSvga() || isTypeAlphaVideo()||isTypeVapVideo()) {
            return Gift.url2LocalPath(jsonUrl, type);
        } else {
            return Gift.url2LocalPath(getMedia_url(), type);
        }
    }

    public String getAnimUrl(int num, int charmType, int animIndex) {
        int size = numericGiftAnimList.size();
        for (int i = size - 1; i > -1; i--) {
            NumericGiftAnim numericGiftAnim = numericGiftAnimList.get(i);
            if (num >= numericGiftAnim.num) {
                return numericGiftAnim.getAnimUrl(charmType, animIndex);
            }
        }
        if (isTypeGif()) {
            return gifUrl;
        } else if (isJsonGift() || isTypeSvga() || isTypeAlphaVideo() || isTypeVapVideo()) {
            return jsonUrl;
        } else {
            return getMedia_url();
        }
    }


    public String getLocalBroadAnimPath() {
        String pref = FileConfig.getGiftDirPath("broad_");
        return URIUtil.url2LocalPath(broadcastAnimUrl, pref);
    }

    public String getLocalAudioPath() {
        String pref = FileConfig.getGiftDirPath("audio_");
        return URIUtil.url2LocalPath(androidAudioUrl, pref);
    }

    /**
     * @param scene gift scene, 可或操作混合
     */
    public boolean showInScene(int scene) {
        return (this.scene & scene) != 0;
    }

    /**
     * @param scene gift scene, 可或操作混合
     */
    public boolean showGiftCardInScene(int scene) {
        return (this.sceneCard & scene) != 0;
    }

    public boolean isUserDefinedVipGift() {
        return ownerUidList != null && !ownerUidList.isEmpty();
    }

    public boolean isUserDefinedVipGift(int uid) {
        return ownerUidList != null && ownerUidList.contains(uid);
    }

    public boolean hideCauseVipUserGift(int selfUid) {
        return isUserDefinedVipGift() && !ownerUidList.contains(selfUid);
    }

    public GiftBoxExtra getDialogExt() {
        return boxExtra;
    }

    @Nullable
    private NumericGiftAnim getNumericGifByNum(int num) {
        int size = numericGiftAnimList.size();
        int index = 0;
        NumericGiftAnim cur = null;
        while (index < size) {
            NumericGiftAnim anim = numericGiftAnimList.get(index);
            if (anim.num > num) {
                break;
            }
            cur = anim;
            index++;
        }
        return cur;
    }

    public boolean isLongAudio() {
        return isLongAudio == 1;
    }

    public boolean isComboRedBg() {
        return comboBgType == COMBO_BG_TYPE_RED;
    }

    public boolean showCombo() {
        return showCombo == 1;
    }

    public boolean isLootGift() {
        return isLootGift == 1;
    }

    @PbWireObj
    public static class NumericGiftAnim {
        @PbWireFiled(tag = 1)
        @SerializedName("number")
        int num;
        @PbWireFiled(tag = 2)
        @SerializedName("type")
        public int type = TYPE_GIF;
        @PbWireFiled(tag = 3)
        @SerializedName("animation_url")
        public String url = "";

        @PbWireFiled(tag = 4)
        @SerializedName("add_animation_url_list")
        public List<CharmAnim> positiveAnim = new ArrayList<>();
        @PbWireFiled(tag = 5)
        @SerializedName("minus_animation_url_list")
        public List<CharmAnim> negativeAnim = new ArrayList<>();

        public boolean isTypeGif() {
            return type == TYPE_GIF;
        }

        public boolean isTypeJson() {
            return type == TYPE_JSON;
        }

        public boolean isTypeSvga() {
            return type == TYPE_SVGA;
        }

        public boolean isTypeAlphaVideo() {
            return type == TYPE_ALPHA_VIDEO;
        }

        private boolean judgeType(int charmType, int animIndex, int type) {
            if (charmType == GIFT_CHARM_TYPE_POSITIVE) {
                if (animIndex < 0) {
                    return this.type == type;
                } else if (animIndex < positiveAnim.size()) {
                    return positiveAnim.get(animIndex).type == type;
                } else {
                    return this.type == type;
                }
            } else if (charmType == GIFT_CHARM_TYPE_NEGATIVE) {
                if (animIndex < 0) {
                    return this.type == type;
                } else if (animIndex < positiveAnim.size()) {
                    return negativeAnim.get(animIndex).type == type;
                } else {
                    return this.type == type;
                }
            } else {
                return this.type == type;
            }
        }

        private String getLocalAnimUrl(int charmType, int animIndex) {
            String url = this.url;
            int type = this.type;
            if (charmType == GIFT_CHARM_TYPE_POSITIVE) {
                if (animIndex < positiveAnim.size() && animIndex >= 0) {
                    url = positiveAnim.get(animIndex).url;
                    type = positiveAnim.get(animIndex).type;
                }
            } else if (charmType == GIFT_CHARM_TYPE_NEGATIVE) {
                if (animIndex < negativeAnim.size() && animIndex >= 0) {
                    url = negativeAnim.get(animIndex).url;
                    type = negativeAnim.get(animIndex).type;
                }
            }
            return url2LocalPath(url, type);
        }

        private String getAnimUrl(int charmType, int animIndex) {
            String url = this.url;
            if (charmType == GIFT_CHARM_TYPE_POSITIVE) {
                if (animIndex < positiveAnim.size() && animIndex >= 0) {
                    url = positiveAnim.get(animIndex).url;
                }
            } else if (charmType == GIFT_CHARM_TYPE_NEGATIVE) {
                if (animIndex < negativeAnim.size() && animIndex >= 0) {
                    url = negativeAnim.get(animIndex).url;
                }
            }
            return url;
        }

        @PbWireObj
        public static class CharmAnim {
            @PbWireFiled(tag = 1)
            @SerializedName("animation_url")
            public String url = "";
            @PbWireFiled(tag = 2)
            @SerializedName("type")
            public int type = TYPE_GIF;
        }
    }

    public static String url2LocalPath(String url, int type) {
        if (type == TYPE_VAP_VIDEO) {
            return URIUtil.url2LocalPath(url, FileConfig.getGiftDirPath("vap_"));
        } else if (type == TYPE_ALPHA_VIDEO) {
            return URIUtil.url2LocalPath(url, FileConfig.getGiftDirPath("alpha_"));
        } else if (type == TYPE_JSON) {
            return URIUtil.url2LocalPath(url, FileConfig.getGiftDirPath("json_"));
        } else if (type == TYPE_GIF) {
            return URIUtil.url2LocalPath(url, FileConfig.getGiftDirPath("gif_"));
        } else if (type == TYPE_SVGA) {
            return URIUtil.url2LocalPath(url, FileConfig.getGiftDirPath("svga_"));
        } else {
            return URIUtil.url2LocalPath(url, FileConfig.getGiftDirPath("gift_"));
        }
    }

    public static boolean shouldShowInGiftContent(int giftId) {
        return !Gift.isLocalGift(giftId) &&
                giftId != GIFT_ID_PRAISE &&
                giftId != GIFT_ID_BRICK;
    }


    @Retention(RetentionPolicy.SOURCE)
    @IntDef({GIFT_SCENE_WEDDING, GIFT_SCENE_PROPOSE,
            GIFT_SCENE_VOICE_ALL, GIFT_SCENE_WEDDING_ALL, GIFT_SCENE_CHAT,
            GIFT_SCENE_TMP_ROOM, GIFT_SCENE_LOVER_HOME, GIFT_SCENE_SONG})
    public @interface SceneInt {}

    //生成一个虚拟的爆灯礼物
    public static Gift generateLightUpGift() {
        Gift gift = new Gift();
        gift.giftId = -19950108;
        gift.giftType = TYPE_IMG;
        gift.name = "";
        gift.price = 50;
        gift.unit = "";
        gift.numbers = "1";
        gift.mediaUrl = "https://weplay-dl.oss-ap-southeast-1.aliyuncs.com/music_c_light_up.png";
        gift.open = 1;
        gift.comboBgType = COMBO_BG_TYPE_RED;
        gift.showCombo = 1;
        return gift;
    }

    /**
     * 是否达到解锁条件提示toast，通用配置
     */
    public static boolean isUnlockTips(GiftBoxExtra extra) {
        if (extra == null) {
            return false;
        }
        return extra.unlockTipId > 0;
    }

    public static boolean isLocalGift(int giftId) {
        return giftId == Gift.GIFT_ID_EGG_D || giftId == Gift.GIFT_ID_FLOWER_D;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Gift gift = (Gift) o;

        return giftId == gift.giftId;
    }

    @Override
    public int hashCode() {
        return giftId;
    }

    @NonNull
    @Override
    public String toString() {
        return name;
    }

    public int getSubType() {
        return subType;
    }

    public void setGiftName(String giftName) {
        this.name = giftName;
    }

    public void setGiftMediaUrl(String giftMediaUrl) {
        this.mediaUrl = giftMediaUrl;
    }

    public void setGiftScene(int giftScene) {
        this.scene = giftScene;
    }

    public void setGiftOpen(int giftOpen) {
        this.open = giftOpen;
    }

    public void setGiftOwnerUids(List<Integer> giftOwnerUidList) {
        this.ownerUidList = giftOwnerUidList;
    }

    public void setGiftPrice(int giftPrice) {
        this.price = giftPrice;
    }

    public void setGiftUnit(String giftUnit) {
        this.unit = giftUnit;
    }

    public boolean canShow() {
        return open == 1 && isSupportedGift() && GlobalConfigManager.showInCurrent(region);
    }

    /**
     * 是否是游戏币礼物
     * @return
     */
    public boolean isChipGift() {
        return giftType == GIFT_TYPE_CHIP_COIN;
    }


}
