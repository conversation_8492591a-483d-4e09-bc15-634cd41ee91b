package com.huiwan.configservice.model;

import android.graphics.Color;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonSerializer;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.huiwan.base.util.ColorUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.configservice.constentity.propextra.CarAnimExtra;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;
import com.huiwan.configservice.constentity.propextra.DiscoverDecorItem;
import com.huiwan.configservice.constentity.propextra.DragonAnimExtra;
import com.huiwan.configservice.constentity.propextra.FamilyLightUrlInfo;
import com.huiwan.configservice.constentity.propextra.GiftExtra;
import com.huiwan.configservice.constentity.propextra.HeadDecorExtra;
import com.huiwan.configservice.constentity.propextra.HomeAnimExtra;
import com.huiwan.configservice.constentity.propextra.InvitationExtra;
import com.huiwan.configservice.constentity.propextra.PriceItem;
import com.huiwan.configservice.constentity.propextra.PropExtraHelper;
import com.huiwan.configservice.constentity.propextra.ProposeTemplateExtra;
import com.huiwan.configservice.constentity.propextra.RedPacketSkinExtra;
import com.huiwan.configservice.constentity.propextra.RingExtra;
import com.huiwan.configservice.constentity.propextra.VoiceBubbleItem;
import com.huiwan.configservice.constentity.propextra.VoiceEnterAnimItem;
import com.huiwan.configservice.constentity.propextra.WeddingBgExtra;
import com.huiwan.configservice.editionentity.PropItemConfig;
import com.huiwan.configservice.modelinterface.RingBoxInterface;
import com.huiwan.configservice.modelinterface.TemplateInterface;
import com.wejoy.libpbksp.PbWireObj;
import com.wepie.liblog.main.HLog;
import com.wepie.skynet.apm.Apm;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

// Created by bigwen on 2018/11/28.
@JsonAdapter(PropItem.PropItemGsonAdapter.class)
@PbWireObj
public class PropItem implements TemplateInterface, RingBoxInterface {
    private static final String TAG = PropItem.class.getSimpleName();

    public static final int CATEGORY_RING = 0;
    public static final int CATEGORY_DECORATION = 1;
    public static final int CATEGORY_ITEM = 2;
    public static final int CATEGORY_GAME = 3;
    public static final int CATEGORY_FAMILY = 4;
    public static final int CATEGORY_JACKAROO_SKIN = 5;

    public static final int TYPE_RED_PACKET = 0;
    public static final int TYPE_PROP = 1;
    public static final int TYPE_DRAW_BOARD = 2;
    public static final int TYPE_RING = 3;
    public static final int TYPE_HOME_ANIM = 4;
    public static final int TYPE_RESOURCE = 5;
    public static final int TYPE_HEAD_DECORATION = 6;
    public static final int TYPE_PROPOSE_TEMPLATE = 7;
    public static final int TYPE_INVITATION = 8;
    public static final int TYPE_WEDDING_BG = 9;
    public static final int TYPE_VOICE_BUBBLE = 10;
    public static final int TYPE_CHAT_BUBBLE = 11;
    public static final int TYPE_DISCOVER_DECOR = 12;
    public static final int TYPE_VOICE_ENTER_ANIM = 13;
    public static final int TYPE_FAMILY_BOX = 14;
    public static final int TYPE_FAMILY_AVATAR_DECOR = 15;
    public static final int TYPE_FAMILY_CHAT_BUBBLE = 16;
    public static final int TYPE_GIFT_CARD = 17;
    public static final int TYPE_USER_TAG = 18;
    public static final int TYPE_VOICE_THEME = 19;
    public static final int TYPE_ANIM_ITEM = 20;
    public static final int TYPE_VOICE_MIC = 21;
    /**
     * 配置的红包
     */
    public static final int TYPE_RED_PACKET_CONFIG = 22;
    /**
     * 有限期的礼物卡
     *
     * @since 5.9.23
     */
    public static final int TYPE_GIFT_CARD_LIMITED = 23;
    public static final int TYPE_FAMILY_LIGHT = 25;
    /**
     * 龙主对决上场特效
     */
    public static final int TYPE_DRAGON_JOIN = 32;

    /**
     * 战场冠名
     */
    public static final int TYPE_DRAGON_NAMING = 33;
    /**
     * 红包皮肤
     *
     * @since 2.3.0
     * <a href="https://wepie.yuque.com/we_play/ol03s4/yo86iv#jHo8p"/>
     */
    public static final int TYPE_RED_PACKET_SKIN = 50;
    /**
     * 座驾需求
     *
     * @since 2.5.5
     * <a href="https://wepie.yuque.com/we_play/mgbtry/fkdm6l"/>
     */
    public static final int TYPE_CAR_ANIM = 51;

    /**
     * 语音房印记
     */
    public static final int TYPE_VOICE_ROOM_MARKING = 52;
    public static final int TYPE_FAMILY_COINS = 53;

    /**
     * 棋子
     */
    public static final int TYPE_JACKAROO_PIECE = 57;
    /**
     * 棋盘
     */
    public static final int TYPE_JACKAROO_BOARD = 58;
    /**
     * 出牌动效
     */
    public static final int TYPE_JACKAROO_PLAY_CARD_ANIMATION = 66;
    /**
     * 击杀动效
     */
    public static final int TYPE_JACKAROO_KILL_ANIMATION = 67;
    /**
     * 交换动效
     */
    public static final int TYPE_JACKAROO_EXCHANGE_ANIMATION = 68;

    public static final int SOURCE_PURCHASE = 1;
    public static final int SOURCE_ACTIVITY = 2;
    public static final int SOURCE_GIFT = 3;
    public static final int SOURCE_VIP = 4;
    public static final int SOURCE_FAMILY_PURCHASE = 5;
    public static final int SOURCE_FAMILY_ACTIVE = 6;

    public static final int PROP_ID_VISITORS = 6;
    public static final int PROP_ID_RED_PACKET = 7;
    public static final int PROP_ID_INSURANCE = 14;
    public static final int PROP_ID_COIN = 1001;
    public static final int PROP_ID_FAMILY_COIN = 1006;
    public static final int PROP_ID_FAMILY_FUNDS = 1007;
    public static final int PROP_ID_FAMILY_BOX_1 = 1_000_001;
    public static final int PROP_ID_FAMILY_BOX_2 = 1_000_002;
    public static final int PROP_ID_FAMILY_BOX_3 = 1_000_003;
    public static final int PROP_ID_FAMILY_BOX_4 = 1_000_004;
    public static final int PROP_ID_FAMILY_PROTECT = 1_000_100;
    public static final int PROP_ID_FAMILY_STEAL = 1_000_701;
    public static final int PROP_ID_BALLOT_CHIP = 1_500_391; // baloot 游戏币
    public static final int PROP_ID_GIFT_RETURN_PROTECT_CARD = 1_704_626; // 送礼防黑卡

    public static final int PROP_ID_FAMILY_DOUBLE_POINTS_CARD = 1_713_261; // 周赛双倍积分卡,使用1张生成3张次卡
    public static final int PROP_ID_FAMILY_DOUBLE_POINTS_NUMBER_CARD = 1_713_262; //周赛双倍积分卡次卡
    public static final int PROP_ID_QUALIFY_DOUBLE_POINTS_CARD = 1_713_263; //排位赛双倍积分卡 => 使用1张生成3张次卡
    public static final int PROP_ID_QUALIFY_DOUBLE_POINTS_NUMBER_CARD = 1_713_264; //排位赛双倍积分卡次卡
    public static final int PROP_ID_QUALIFY_PROTECT_GRADE_CARD = 1_713_265; // 排位赛段位保护卡

    public static final int PROP_ITEM_N_DRAW_BOARD = 100;
    public static final int PROP_ITEM_W_DRAW_BOARD = 101;
    public static final int PROP_ITEM_WHITE_DRAW_BOARD = 102;

    private static final ArrayMap<Class<?>, Object> gsonAdapters = new ArrayMap<>();

    @SerializedName("item_id")
    private int item_id;
    /**
     * 0、红包 1、道具  2、画板  3、戒指  4、主页特效  5、资源（金币、豆豆、活跃度等） 6、头像框
     */
    @SerializedName("type")
    private int type;
    @SerializedName("name")
    private String name = "";
    @SerializedName("simple_desc")
    private String simple_desc = "";
    @SerializedName("media_url")
    private String media_url = "";
    @SerializedName("square_media_url")
    private String square_media_url = "";
    @SerializedName("extra")
    private String extra = "";
    @SerializedName("price_list")
    private String price_list = "";
    @SerializedName("label_icon")
    private String label_icon = "";
    @SerializedName("is_single_ring")
    private int is_single_ring = 0;
    /**
     * 1、购买  2、活动获得(活动地址配置在jump_url)， 3、礼物获得
     */
    @SerializedName("source")
    private int source = 0;
    @SerializedName("source_desc")
    private String source_desc = "";
    @SerializedName("jump_url")
    private String jump_url = "";
    @SerializedName("detail_desc")
    private String detail_desc = "";
    @SerializedName("open")
    private int open = 0; // 商城是否可见
    @SerializedName("bg_color")
    private String bg_color = "";
    @SerializedName("vip")
    private int vip;

    /**
     * region, 大区，逗号分割 "U,C"
     */
    @SerializedName("region")
    public String region = ""; // 商城可见大区

    /**
     * 注意添加字段时，同时补充 adapter 解析与序列化
     * {@link PropItemGsonAdapter}
     */
    public PropItem() {
    }

    private transient List<PriceItem> priceItemList = new ArrayList<>();
    private transient Object extraObj = null;

    public int getItemId() {
        return item_id;
    }

    public void setItemId(int item_id) {
        this.item_id = item_id;
    }

    public int getType() {
        return type;
    }

    public String getTypeName() {
        return PropItemConfig.getNameByType(type, isSingleRing());
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSimpleDesc() {
        return simple_desc;
    }

    public void setSimpleDesc(String simple_desc) {
        this.simple_desc = simple_desc;
    }

    public String getMediaUrl() {
        return media_url;
    }

    public void setMediaUrl(String media_url) {
        this.media_url = media_url;
    }

    public int getBgColor() {
        return ColorUtil.getColor(bg_color, 0xefefef);
    }

    public void setBg_color(String bg_color) {
        this.bg_color = bg_color;
    }

    public List<PriceItem> getPriceList() {
        if (priceItemList.isEmpty() && !TextUtils.isEmpty(price_list)) {
            try {
                List<PriceItem> itemList = JsonUtil.fromJson(price_list, new TypeToken<List<PriceItem>>() {
                }.getType());
                if (itemList != null) {
                    priceItemList = itemList;
                } else {
                    HLog.e(TAG, "parse price list error");
                }
            } catch (Exception e) {
                HLog.e(TAG, "parse price list error {}", e);
            }
        }
        return priceItemList;
    }

    public boolean isFamilyExchangePropByItemId() {
        return item_id == PROP_ID_FAMILY_DOUBLE_POINTS_CARD || item_id == PROP_ID_QUALIFY_DOUBLE_POINTS_CARD || item_id == PROP_ID_QUALIFY_PROTECT_GRADE_CARD;
    }

    public String getLabelIcon() {
        return label_icon;
    }

    public boolean isSingleRing() {
        return is_single_ring == 1;
    }

    public boolean isOpen() {
        return open == 1;
    }

    public int getSource() {
        return source;
    }

    public String getSourceDesc() {
        return source_desc;
    }

    public String getJumpUrl() {
        return jump_url;
    }

    public String getDetailDesc() {
        return detail_desc;
    }

    public boolean isRedPacket() {
        return item_id == PROP_ID_RED_PACKET;
    }

    public boolean isPacketSkin() {
        return type == TYPE_RED_PACKET_SKIN;
    }

    public boolean isRing() {
        return type == TYPE_RING;
    }

    public int getVipLevel() {
        return vip;
    }

    public boolean isVipItem() {
        return vip > 0;
    }

    public boolean isFamilyWarehouseProp() {
        return type == TYPE_FAMILY_BOX;
    }

    public boolean isFamilyAvatarDecor() {
        return type == TYPE_FAMILY_AVATAR_DECOR;
    }

    public boolean isGiftCard() {
        return type == TYPE_GIFT_CARD || type == TYPE_GIFT_CARD_LIMITED;
    }

    @Nullable
    public RingExtra getRingExtra() {
        return getExtraByType(RingExtra.class);
    }

    @Nullable
    public HomeAnimExtra getHomeAnimExtra() {
        return getExtraByType(HomeAnimExtra.class);
    }

    public CarAnimExtra getCarAnimExtra() {
        return getExtraByType(CarAnimExtra.class);
    }

    @Nullable
    public DragonAnimExtra getDragonAnimExtra() {
        return getExtraByType(DragonAnimExtra.class);
    }

    @Nullable
    public RedPacketSkinExtra getRedPacketSkinExtra() {
        return getExtraByType(RedPacketSkinExtra.class);
    }

    public boolean shouldShowPriceList() {
        return item_id == PROP_ID_FAMILY_DOUBLE_POINTS_CARD || item_id == PROP_ID_QUALIFY_DOUBLE_POINTS_CARD || item_id == PROP_ID_QUALIFY_PROTECT_GRADE_CARD;
    }

    @Nullable
    public HeadDecorExtra getHeadDecorExtra() {
        return getExtraByType(HeadDecorExtra.class);
    }

    @Nullable
    public GiftExtra getGiftExtra() {
        return getExtraByType(GiftExtra.class);
    }

    public String getExtraStr() {
        return extra;
    }

    public boolean isDarryRing() {
        return item_id == 11;
    }

    public boolean isDecor() {
        return isDecor(type);
    }

    public boolean isMarking() {
        return isMarking(type);
    }

    public boolean isGame() {
        return isGame(type);
    }

    public boolean isFamilyCoins() {
        return isFamilyCoins(type);
    }

    @NonNull
    @Override
    public String toString() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("item_id", getItemId());
            jsonObject.put("type", getType());
            jsonObject.put("name", getName());
            jsonObject.put("simple_desc", getSimpleDesc());
            jsonObject.put("media_url", getMediaUrl());
            jsonObject.put("extra", extra);
            jsonObject.put("source", source);
            jsonObject.put("label_icon", label_icon);
            jsonObject.put("source_desc", source_desc);
            jsonObject.put("jump_url", jump_url);
            jsonObject.put("detail_desc", detail_desc);
            jsonObject.put("bg_color", bg_color);

            jsonObject.put("square_media_url", square_media_url);
            jsonObject.put("price_list", price_list);
            jsonObject.put("is_single_ring", is_single_ring);
            jsonObject.put("open", open);
            jsonObject.put("vip", vip);
            jsonObject.put("region", region);
        } catch (JSONException ignored) {
        }
        return jsonObject.toString();
    }

    public int getBeforeDiscountPrice() {
        List<PriceItem> priceItems = getPriceList();
        if (priceItems != null && priceItems.size() > 0) {
            return priceItems.get(0).getPrice();
        }
        return 0;
    }

    @Nullable
    public ProposeTemplateExtra getProposeTemplate() {
        return getExtraByType(ProposeTemplateExtra.class);
    }

    @Nullable
    public InvitationExtra getInvitation() {
        return getExtraByType(InvitationExtra.class);
    }

    @Nullable
    public WeddingBgExtra getWeddingBg() {
        return getExtraByType(WeddingBgExtra.class);
    }

    @Nullable
    public ChatBubbleItem getChatBubbleItem() {
        return getExtraByType(ChatBubbleItem.class);
    }

    @Nullable
    public VoiceBubbleItem getVoiceBubbleItem() {
        return getExtraByType(VoiceBubbleItem.class);
    }

    @Nullable
    public DiscoverDecorItem getDiscoverDecorItem() {
        return getExtraByType(DiscoverDecorItem.class);
    }

    @Nullable
    public VoiceEnterAnimItem getVoiceEnterItem() {
        return getExtraByType(VoiceEnterAnimItem.class);
    }

    @Nullable
    public FamilyLightUrlInfo getFamilyLightUrlInfo() {
        return getExtraByType(FamilyLightUrlInfo.class);
    }

    /**
     * 是否不是靠购买获得的
     *
     * @return
     */
    public boolean isNotBuyable() {
        return source != SOURCE_PURCHASE && source != SOURCE_FAMILY_PURCHASE;
    }

    @Override
    public int getTemplateId() {
        return getItemId();
    }

    @Override
    public String getTemplateIconUrl() {
        return getMediaUrl();
    }

    @Override
    public int getTemplatePrice() {
        List<PriceItem> list = getPriceList();
        if (list.size() != 0) {
            return list.get(0).getPrice();
        }
        return 0;
    }

    @Override
    public String getTemplateName() {
        return getName();
    }

    @Override
    public String getRingName() {
        return getName();
    }

    @Override
    public String getRingIcon() {
        return getMediaUrl();
    }

    @Override
    public int getRingId() {
        return getItemId();
    }

    @Override
    public int getRingBgColor() {
        return getBgColor();
    }

    @Nullable
    public <T> T getExtraByType(Class<T> c) {
        if (PropExtraHelper.matchType(c, type) && !TextUtils.isEmpty(extra)) {
            if (extraObj != null && extraObj.getClass() == c) {
                return c.cast(extraObj);
            }
            try {
                GsonBuilder builder = new GsonBuilder();
                for (Map.Entry<Class<?>, Object> entry : gsonAdapters.entrySet()) {
                    builder.registerTypeAdapter(entry.getKey(), entry.getValue());
                }
                T obj = builder.create().fromJson(extra, c);
                extraObj = obj;
                return obj;
            } catch (Exception e) {
                HLog.d(TAG, "parse extra error: {}, {}, {}, {} ", item_id, name, extra, e);
            }
        }
        return null;
    }

    public static boolean isRing(int type) {
        return type == TYPE_RING;
    }

    public static boolean isDecor(int type) {
        return type == TYPE_HEAD_DECORATION ||
                type == TYPE_HOME_ANIM ||
                type == TYPE_DISCOVER_DECOR ||
                type == TYPE_CHAT_BUBBLE ||
                type == TYPE_VOICE_BUBBLE ||
                type == TYPE_VOICE_ENTER_ANIM ||
                type == TYPE_CAR_ANIM ||
                type == TYPE_VOICE_THEME;
    }

    public static boolean isMarking(int type) {
        return type == TYPE_VOICE_ROOM_MARKING;
    }

    public static boolean isFamilyCoins(int type) {
        return type == TYPE_FAMILY_COINS;
    }

    public static boolean isGame(int type) {
        return type == TYPE_DRAW_BOARD || type == TYPE_VOICE_MIC;
    }

    public static boolean isProp(int type) {
        return type == TYPE_PROP || type == TYPE_RED_PACKET_SKIN;
    }

    public static boolean isFamily(int type) {
        return type == TYPE_FAMILY_BOX ||
                type == TYPE_GIFT_CARD ||
                type == TYPE_FAMILY_AVATAR_DECOR;
    }

    public static boolean isJackarooSkin(int type) {
        return type == TYPE_JACKAROO_PIECE || type == TYPE_JACKAROO_BOARD
                || type == TYPE_JACKAROO_PLAY_CARD_ANIMATION || type == TYPE_JACKAROO_KILL_ANIMATION
                || type == TYPE_JACKAROO_EXCHANGE_ANIMATION;
    }

    public static int getCategoryType(int type) {
        if (isRing(type)) {
            return CATEGORY_RING;
        }
        if (isDecor(type)) {
            return CATEGORY_DECORATION;
        }
        if (isGame(type)) {
            return CATEGORY_GAME;
        }
        if (isFamily(type)) {
            return CATEGORY_FAMILY;
        }
        if (isJackarooSkin(type)) {
            return CATEGORY_JACKAROO_SKIN;
        }


        return CATEGORY_ITEM;
    }

    public int getCategoryType() {
        return getCategoryType(type);
    }

    /**
     * 判断是否有时间限制
     * @return
     */
    public boolean isDayLimit() {
        return type != TYPE_RING;
    }

    public static <T> void registerTypeAdapter(Class<T> c, TypeAdapter<T> adapter) {
        gsonAdapters.put(c, adapter);
    }

    public static <T> void registerTypeAdapter(Class<T> c, JsonSerializer<T> adapter) {
        gsonAdapters.put(c, adapter);
    }

    public static <T> void registerTypeAdapter(Class<T> c, JsonDeserializer<T> adapter) {
        gsonAdapters.put(c, adapter);
    }

    public static void unRegisterTypeAdapter(Class<?> c) {
        gsonAdapters.remove(c);
    }

    public static class PropItemGsonAdapter extends TypeAdapter<PropItem> {

        @Keep
        public PropItemGsonAdapter() {
            Apm.recordTimePoint("PropItemGsonAdapter#<init>");
        }

        @Override
        public PropItem read(JsonReader in) throws IOException {
            PropItem item = new PropItem();
            in.beginObject();
            while (in.hasNext()) {
                switch (in.nextName()) {
                    case "item_id":
                        item.item_id = in.nextInt();
                        break;
                    case "type":
                        item.type = in.nextInt();
                        break;
                    case "name":
                        item.name = in.nextString();
                        break;
                    case "simple_desc":
                        item.simple_desc = in.nextString();
                        break;
                    case "media_url":
                        item.media_url = in.nextString();
                        break;
                    case "square_media_url":
                        item.square_media_url = in.nextString();
                        break;
                    case "extra":
                        item.extra = in.nextString();
                        break;
                    case "price_list":
                        item.price_list = in.nextString();
                        break;
                    case "label_icon":
                        item.label_icon = in.nextString();
                        break;
                    case "is_single_ring":
                        item.is_single_ring = in.nextInt();
                        break;
                    case "source":
                        item.source = in.nextInt();
                        break;
                    case "source_desc":
                        item.source_desc = in.nextString();
                        break;
                    case "jump_url":
                        item.jump_url = in.nextString();
                        break;
                    case "detail_desc":
                        item.detail_desc = in.nextString();
                        break;
                    case "open":
                        item.open = in.nextInt();
                        break;
                    case "bg_color":
                        item.bg_color = in.nextString();
                        break;
                    case "vip":
                        item.vip = in.nextInt();
                        break;
                    case "region":
                        item.region = in.nextString();
                        break;
                    default:
                        in.skipValue();
                        break;
                }
            }
            in.endObject();
            return item;
        }

        @Override
        public void write(JsonWriter out, PropItem item) throws IOException {
            out.beginObject();
            out.name("item_id").value(item.item_id);
            out.name("type").value(item.type);
            out.name("name").value(item.name);
            out.name("simple_desc").value(item.simple_desc);
            out.name("media_url").value(item.media_url);
            out.name("square_media_url").value(item.square_media_url);
            out.name("extra").value(item.extra);
            out.name("price_list").value(item.price_list);
            out.name("label_icon").value(item.label_icon);
            out.name("is_single_ring").value(item.is_single_ring);
            out.name("source").value(item.source);
            out.name("source_desc").value(item.source_desc);
            out.name("jump_url").value(item.jump_url);
            out.name("detail_desc").value(item.detail_desc);
            out.name("open").value(item.open);
            out.name("bg_color").value(item.bg_color);
            out.name("vip").value(item.vip);
            out.name("region").value(item.region);
            out.endObject();
        }
    }
}
