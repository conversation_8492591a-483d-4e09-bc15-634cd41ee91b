package com.huiwan.configservice.model.voiceroom;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class AdvanceRoomLevelConfig {
    @SerializedName("level")
    private int level;
    @SerializedName("icon_url")
    private String iconUrl;
    @SerializedName("min_exp")
    private int minExp;
    @SerializedName("admin_num")
    private int adminNum;
    @SerializedName("rec_card_num")
    private int recCardNum;
    @SerializedName("play_exp_daily_limit")
    private int playExpDailyLimit;
    @SerializedName("gift_exp_daily_limit")
    private int giftExpDailyLimit;

    @SerializedName("exp_start_color")
    private String expStartColor;
    @SerializedName("exp_end_color")
    private String expEndColor;
    @SerializedName("exp_text_color")
    private String expTextColor;
    @SerializedName("upgrade_tips")
    private String upgradeTips;
    @SerializedName("theme_prop_ids")
    private List<Integer> propIDs = new ArrayList<>();

    public int getLevel() {
        return level;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public int getMinExp() {
        return minExp;
    }

    public int getAdminNum() {
        return adminNum;
    }

    public int getRecCardNum() {
        return recCardNum;
    }

    public int getPlayExpDailyLimit() {
        return playExpDailyLimit;
    }

    public int getGiftExpDailyLimit() {
        return giftExpDailyLimit;
    }

    public int getExpDailyLimit() {
        return playExpDailyLimit + giftExpDailyLimit;
    }

    public String getExpStartColor() {
        return expStartColor;
    }

    public String getExpEndColor() {
        return expEndColor;
    }

    public String getExpTextColor() {
        return expTextColor;
    }

    public String getUpgradeTips() {
        return upgradeTips;
    }

    public List<Integer> getPropIDs() {
        return propIDs;
    }
}
