package com.huiwan.configservice.international.regoin;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
/**
 * date 2021/07/13
 *
 * 应用内id分区配置信息
 * 目前仅添加了临时房id、新老桌游房id、太空狼人杀、家族id的配置
 *
 * <AUTHOR>
 */
public class IDRegionConfig {
    public static final int ID_REGION_TYPE_TMP_ROOM = 1;
    public static final int ID_REGION_TYPE_FIX_ROOM = 2;
    public static final int ID_REGION_TYPE_WOLF_ROOM = 3;
    public static final int ID_REGION_TYPE_AMONGUS_ROOM = 4;
    public static final int ID_REGION_TYPE_FAMILY_ID = 5;
    public static final int ID_REGION_TYPE_HWROOM = 6;
    public static final int ID_REGION_TYPE_PARTY_ROOM = 7;

    @SerializedName("tmproom")
    private List<IDRegionInfo> tmpRoomRegion = new ArrayList<>();
    @SerializedName("fixroom")
    private List<IDRegionInfo> fixRoomRegion = new ArrayList<>();
    @SerializedName("wolfroom")
    private List<IDRegionInfo> wolfRoomRegion = new ArrayList<>();
    @SerializedName("amongus")
    private List<IDRegionInfo> amongusRegion = new ArrayList<>();
    @SerializedName("family_id")
    private List<IDRegionInfo> familyIdRegion = new ArrayList<>();
    @SerializedName("hw_room")
    private List<IDRegionInfo> hwRoomIdRegion = new ArrayList<>();
    @SerializedName("partyroom")
    private List<IDRegionInfo> partyIdRegion = new ArrayList<>();


    public void setPartyIdRegion(List<IDRegionInfo> partyIdRegion) {
        this.partyIdRegion = partyIdRegion;
    }

    public List<IDRegionInfo> getPartyIdRegion() {
        return partyIdRegion;
    }

    public void setHwRoomIdRegion(List<IDRegionInfo> hwRoomIdRegion) {
        this.hwRoomIdRegion = hwRoomIdRegion;
    }

    public List<IDRegionInfo> getHwRoomIdRegion() {
        return hwRoomIdRegion;
    }

    public List<IDRegionInfo> getTmpRoomRegion() {
        return tmpRoomRegion;
    }

    public void setTmpRoomRegion(List<IDRegionInfo> tmpRoomRegion) {
        this.tmpRoomRegion = tmpRoomRegion;
    }

    public List<IDRegionInfo> getFixRoomRegion() {
        return fixRoomRegion;
    }

    public void setFixRoomRegion(List<IDRegionInfo> fixRoomRegion) {
        this.fixRoomRegion = fixRoomRegion;
    }

    public List<IDRegionInfo> getWolfRoomRegion() {
        return wolfRoomRegion;
    }

    public void setWolfRoomRegion(List<IDRegionInfo> wolfRoomRegion) {
        this.wolfRoomRegion = wolfRoomRegion;
    }

    public List<IDRegionInfo> getAmongusRegion() {
        return amongusRegion;
    }

    public void setAmongusRegion(List<IDRegionInfo> amongusRegion) {
        this.amongusRegion = amongusRegion;
    }

    public List<IDRegionInfo> getFamilyIdRegion() {
        return familyIdRegion;
    }

    public void setFamilyIdRegion(List<IDRegionInfo> familyIdRegion) {
        this.familyIdRegion = familyIdRegion;
    }

    /**
     * 根据Id类型获取对应类型的Id大区列表
     * @param type  目标Id类型
     * @return 对应类型的Id大区列表
     */
    public List<IDRegionInfo> getRegionListByType(int type){
        switch (type){
            case ID_REGION_TYPE_FIX_ROOM:
                return getFixRoomRegion();
            case ID_REGION_TYPE_WOLF_ROOM:
                return getWolfRoomRegion();
            case ID_REGION_TYPE_AMONGUS_ROOM:
                return getAmongusRegion();
            case ID_REGION_TYPE_FAMILY_ID:
                return getFamilyIdRegion();
            case ID_REGION_TYPE_HWROOM:
                return getHwRoomIdRegion();
            case ID_REGION_TYPE_PARTY_ROOM:
                return getPartyIdRegion();
            default:
                return getTmpRoomRegion();
        }
    }

    public List<IDRegionInfo> getAllRegionList(){
        List<IDRegionInfo> allRegionList = new ArrayList<>();
        allRegionList.addAll(getTmpRoomRegion());
        allRegionList.addAll(getFixRoomRegion());
        allRegionList.addAll(getWolfRoomRegion());
        allRegionList.addAll(getAmongusRegion());
        allRegionList.addAll(getFamilyIdRegion());

        return allRegionList;
    }
}
