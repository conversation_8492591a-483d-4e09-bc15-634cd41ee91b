package com.wepie.lib.api.plugins.voice;

import androidx.annotation.LongDef;
import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class Config {
    public final static long CONTEXT_DEFAULT = 1L;
    public final static long CONTEXT_LITTLE_GAME = CONTEXT_DEFAULT << 1;
    public final static long CONTEXT_WEREWOLF = CONTEXT_DEFAULT << 2;
    public final static long CONTEXT_AB_SPY_VOICE = CONTEXT_DEFAULT << 3;
    public final static long CONTEXT_BLANK_SPY_VOICE = CONTEXT_DEFAULT << 4;
    public final static long CONTEXT_MUSIC_HUM = CONTEXT_DEFAULT << 5;
    public final static long CONTEXT_SIMPLE_VOICE = CONTEXT_DEFAULT << 6;
    public final static long CONTEXT_CP_VOICE = CONTEXT_DEFAULT << 7;
    public final static long CONTEXT_AUCTION_VOICE = CONTEXT_DEFAULT << 8;
    public final static long CONTEXT_WEDDING_VOICE = CONTEXT_DEFAULT << 9;
    public final static long CONTEXT_LOVER_VOICE = CONTEXT_DEFAULT << 10;
    public final static long CONTEXT_SAY_GUESS = CONTEXT_DEFAULT << 11;
    public final static long CONTEXT_VIDEO_VOICE = CONTEXT_DEFAULT << 15;


    @LongDef({CONTEXT_DEFAULT, CONTEXT_LITTLE_GAME, CONTEXT_WEREWOLF, CONTEXT_AB_SPY_VOICE, CONTEXT_BLANK_SPY_VOICE,
            CONTEXT_MUSIC_HUM, CONTEXT_SIMPLE_VOICE, CONTEXT_CP_VOICE, CONTEXT_AUCTION_VOICE, CONTEXT_WEDDING_VOICE,
            CONTEXT_LOVER_VOICE, CONTEXT_SAY_GUESS, CONTEXT_VIDEO_VOICE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Scene {
    }

    @SerializedName("context_mask")
    public @Scene long context = CONTEXT_DEFAULT;
    @SerializedName("quality")
    public Quality quality = new Quality();

    public boolean showInContext(long context) {
        return (this.context & context) == context;
    }

    @NonNull
    @Override
    public String toString() {
        return "Config{" +
                "context=" + context +
                '}';
    }


    public static class Quality {
        @SerializedName("zego_normal")
        public Zego zegoNormal = new Zego();
        @SerializedName("zego_highQuality")
        public Zego zegoHigh = new Zego();
        @SerializedName("tencent_normal")
        public Tx txNormal = new Tx();
        @SerializedName("tencent_highQuality")
        public Tx txHigh = new Tx();
    }

    public static class Zego {
        @SerializedName("audioDeviceMode")
        public int audioDeviceMode = 4;
        @SerializedName("bitRate")
        public int bitRate = 48000;
        @SerializedName("aecWhenHeadsetDetected")
        public int aecWhenHeadsetDetected = 1;

        @SerializedName("dtx")
        public int dtx = 1;
        @SerializedName("vad")
        public int vad = 1;

        @SerializedName("codecID")
        public int codecID = 6;
        @SerializedName("count")
        public int count = 1; // 音频声道数设置，同bitrate双声道下，会降低音质
        @SerializedName("setRecvBufferMinInMs")
        public int setRecvBufferMinInMs = 10;
        @SerializedName("setRecvBufferMaxInMs")
        public int setRecvBufferMaxInMs = 1000;

        //无用字段
        @SerializedName("noiseSuppress")
        public int noiseSuppress = 0;//（Android不使用此字段，SDK默认根据不同通话\媒体模式调整此字段）
        @SerializedName("agc")
        public int agc = 0;//（agc不要调用，可能导致bgm声音随人声时大时小）
        @SerializedName("aec")
        public int aec = 1;//（Android不使用此字段，SDK默认根据不同通话\媒体模式调整此字段）

        //已经去掉视频业务
        @SerializedName("bigSize")
        public int bigSize = 480;
        @SerializedName("bigRate")
        public int bigRate = 900 * 1000;
        @SerializedName("smallSize")
        public int smallSize = 120;
        @SerializedName("smallRate")
        public int smallRate = 120 * 1000;
        @SerializedName("fps")
        public int fps = 10;

        @NonNull
        @Override
        public String toString() {
            return "Zego{" +
                    "audioDeviceMode=" + audioDeviceMode +
                    ", bitRate=" + bitRate +
                    ", aecWhenHeadsetDetected=" + aecWhenHeadsetDetected +
                    ", dtx=" + dtx +
                    ", vad=" + vad +
                    ", codecID=" + codecID +
                    ", count=" + count +
                    ", setRecvBufferMinInMs=" + setRecvBufferMinInMs +
                    ", setRecvBufferMaxInMs=" + setRecvBufferMaxInMs +
                    ", noiseSuppress=" + noiseSuppress +
                    ", agc=" + agc +
                    ", aec=" + aec +
                    ", bigSize=" + bigSize +
                    ", bigRate=" + bigRate +
                    ", smallSize=" + smallSize +
                    ", smallRate=" + smallRate +
                    ", fps=" + fps +
                    '}';
        }
    }

    public static class Tx {
        @SerializedName("volumeType")
        public int volumeType = 0;
        @SerializedName("audioQuality")
        public int audioQuality = 2;
        @SerializedName("enableAudioAEC")
        public int enableAudioAEC = 1;
        @SerializedName("enableAudioANS")
        public int enableAudioANS = 1;
        @SerializedName("ANS_level")
        public int ANS_level = 60;
        @SerializedName("enableAudioAGC")
        public int enableAudioAGC = 1;
        @SerializedName("AGC_level")
        public int AGC_level = 60;

        @NonNull
        @Override
        public String toString() {
            return "Tx{" +
                    "volumeType=" + volumeType +
                    ", audioQuality=" + audioQuality +
                    ", enableAudioAEC=" + enableAudioAEC +
                    ", enableAudioANS=" + enableAudioANS +
                    ", ANS_level=" + ANS_level +
                    ", enableAudioAGC=" + enableAudioAGC +
                    ", AGC_level=" + AGC_level +
                    '}';
        }
    }
}
