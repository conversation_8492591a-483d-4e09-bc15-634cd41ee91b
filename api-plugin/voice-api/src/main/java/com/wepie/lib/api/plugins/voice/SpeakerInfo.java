package com.wepie.lib.api.plugins.voice;

import com.google.gson.annotations.SerializedName;

/**
 * Created by bigwen on 2017/5/16.
 */
public class SpeakerInfo {

    @SerializedName("uid")
    public int uid = -100;//正常为uid，腾讯云为memberId
    @SerializedName("volume")
    public int volume;//音量
    @SerializedName("alwaysShow")
    public boolean alwaysShow = false;//是否恒定显示
    @SerializedName("hide")
    public boolean hide = false;//强制隐藏
    @SerializedName("anim")
    public boolean anim = false;//麦克风动画

    @Override
    public String toString() {
        return "SpeakerInfo{" +
                "uid=" + uid +
                ", volume=" + volume +
                ", alwaysShow=" + alwaysShow +
                ", hide=" + hide +
                ", anim=" + anim +
                '}';
    }
}
