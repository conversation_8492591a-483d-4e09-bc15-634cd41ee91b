package com.wepie.lib.api.plugins.voice;

import android.content.Context;
import android.view.TextureView;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.Collection;
import java.util.List;

/**
 * date 2019-09-10
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public abstract class BaseVoiceManager {
    public static final int JOIN_RES_IGNORED = 0;
    public static final int JOIN_RES_FAILED = -1;
    public static final int JOIN_RES_SUCCESS = 1;
    private SoundChannel channel = SoundChannel.undefined;
    private OnJoinChannelListener joinChannelListener;
    protected INetworkQualityCallback networkQualityCallback = null;

    public Callback callback;

    public void setJoinChannelListener(OnJoinChannelListener joinChannelListener) {
        this.joinChannelListener = joinChannelListener;
    }

    protected void onJoinSuccess() {
        if (joinChannelListener != null) {
            joinChannelListener.onSuccess();
        }
    }

    public void setNetworkQualityCallback(INetworkQualityCallback networkQualityCallback) {
        this.networkQualityCallback = networkQualityCallback;
    }

    public abstract int getServiceType();

    public abstract int getMediaType();

    public abstract int joinChannel(VoiceConfig config);

    public abstract void rejoinChannel();

    public abstract boolean leaveChannel();

    public abstract void setClientRole(boolean isBroadcaster);

    public abstract void setClientRoleForce(boolean isBroadcaster);

    public abstract boolean isBroadcaster();

    public abstract boolean isOnline(String rid);

    public abstract boolean isOnlineStrict(String channelName);

    public abstract boolean isOnlineLoose();

    public abstract void setSpeakerCallback(SpeakCallback callback);

    public abstract void clearCallback();

    public abstract boolean openMic();

    public String getChannelName() {
        return "";
    }

    /**
     * 设置麦克风不录音，但可播放音乐.
     */
    public abstract boolean setMixingWithoutRecorder();

    public abstract boolean closeMic(boolean mute);

    public abstract boolean startAudioMixing(VoiceEffect effect);

    public abstract boolean stopAudioMixing();

    public abstract boolean pauseAudioMixing();

    public abstract boolean resumeAudioMixing();

    public abstract int getAudioMixingPosition();

    public abstract boolean setAudioMixingVolume(int volume);

    public abstract boolean setAudioMixingPosition(int position);

    public abstract boolean closeMic();

    public void setPhoneSpeakerOn(boolean isSpeakerOn) {
    }

    public abstract boolean muteAllRemoteAudioStreams(boolean isMute);

    public abstract boolean muteRemoteAudioStream(int uid, boolean isMute);

    public abstract void setAudioVolumeIndication(int interval, int smooth);

    public abstract void release();

    public abstract void registerRecordFrameObserver(WPRecordFrameObserver observer);

    public abstract void setRecordingAudioFrameParameters(int sampleRate, int channel);

//    public abstract boolean setPlayStreamVolume(int volume);

    public boolean startPushVideo() {
        return false;
    }

    public boolean stopPushVideo() {
        return false;
    }

    public boolean stopPullVideo(String uid) {
        return false;
    }

    public boolean startPreview() {
        return false;
    }

    public boolean stopPreview() {
        return false;
    }

    public boolean setSdkPreview(TextureView textureView) {
        return false;
    }

    public String getSimpleName() {
        return "";
    }

    public void setCustomAvatarPath(String avatarPath, boolean mosaic) {

    }

    public void setSdkAvatarPath(String avatarPath, boolean mosaic) {

    }

    public boolean setCustomPreview(TextureView textureView) {
        return false;
    }

    public boolean setVideoConfig(WPVideoSize zegoVideoSize) {
        return false;
    }

    public void setAudioPushBitrate(int bitrate) {

    }

    public void setPushStreamUrl(String pushStreamUrl) {

    }

    public boolean startPullVideo(String uid, View view) {
        return false;
    }

    public void onResume() {

    }

    public void onPause() {

    }

    public int playEffect(VoiceEffect effect) {
        return 0;
    }

    public void stopEffect(Collection<Integer> ids) {
    }

    public void setEffectVolume(int id, int volume) {
    }

    /**
     * 设置语音拉流播放音量
     *
     * @param volume
     */
    public abstract void setAllRemoteAudioVolume(int volume);

    protected final SoundChannel getPhoneChannel() {
        return channel;
    }

    public void setPhoneChannel(SoundChannel channel) {
        this.channel = channel;
    }

    public void logJoinChannel(BaseVoiceManager baseVoiceManager, long context, @Nullable Object config, String channelName) {
    }


    public abstract void setCallback(Callback callback);

    public abstract void changeRemoteAudioStreamVolume(String uid, int volume, boolean isMute);

    public void setSystemVolumeTypeByRole(boolean isInSeat) {
    }

    /**
     * 拉流
     *
     * @param streamId
     * @param url
     */
    public void pullStreamByUrl(String streamId, String url, View view) {

    }

    /**
     * 停止拉流
     *
     * @param streamId
     */
    public void stopPullStream(String streamId) {

    }

    public abstract List<ChannelStream> getChannelStream();

    public abstract boolean sendSEIMsg(byte[] data);

    public abstract void setSEICallback(SEICallback seiCallback);

    public abstract void setStreamFilter(IStreamFilter filter);

    public View createVideoView(Context context) {
        return null;
    }

    public boolean checkVideoView(View view) {
        return false;
    }

    public void destroy() {
    }

    public abstract void setJoinAndOpenMic(boolean flag);

    public abstract boolean getJoinAndOpenMic();

    public void recordLocalAudio() {

    }

    public void stopRecordLocalAudio() {

    }

    public interface Callback {
        /**
         * 麦克风开关回调
         */
        void onMicStatusChange(boolean open);
    }

    public interface IStreamFilter {
        boolean onStreamUpdate(@NonNull BaseVoiceManager manager, @NonNull ChannelStream stream);

        int priority();
    }
}
