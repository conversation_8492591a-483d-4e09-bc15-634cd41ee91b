package com.wepie.lib.api.plugins.track;

import androidx.collection.ArrayMap;

import com.huiwan.lib.api.ApiService;
import com.wepie.liblog.main.HLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collections;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class TrackUtil {
    private static final String TAG = "TrackUtil";
    private static final HashMap<String, Long> timeMap = new HashMap<>();

    public static void trackEvent(String eventName, String jsonStr) {
        try {
            trackEvent(eventName, new JSONObject(jsonStr));
        } catch (Exception ignored) {
        }
    }

    public static void trackEvent(String eventName, Map<String, Object> properties) {
        ApiService.of(TrackApi.class).trackEvent(eventName, new JSONObject(properties));
    }

    public static void trackEvent(String eventName, JSONObject properties) {
        ApiService.of(TrackApi.class).trackEvent(eventName, properties);
    }

    public static void appClick(String screenName, String btnName, Map<String, Object> map) {
        ApiService.of(TrackApi.class).appClick(screenName, btnName, map);
    }

    public static void appClick(String screenName, String btnName) {
        appClick(screenName, btnName, new HashMap<>());
    }

    public static void appViewScreen(String screenName, boolean isVip) {
        try {
            TrackValue trackValue = trackValue();
            JSONObject properties = new JSONObject();
            properties.put(trackValue.getScreenName(), screenName);
            properties.put("is_vip", isVip);
            trackEvent(trackValue.getAppViewScreen(), properties);
        } catch (Exception e) {
            //ignore
        }
    }

    public static void appViewScreenScene(String screenName, String scene) {
        try {
            TrackValue trackValue = trackValue();
            Map<String, Object> map = new HashMap<>();
            map.put(trackValue.getScreenName(), screenName);
            map.put("scene", scene);
            trackEvent(trackValue.getAppViewScreen(), map);
        } catch (Exception e) {
            //ignore
        }
    }

    public static void appViewScreen(String screenName, Map<String, Object> ext) {
        try {
            TrackValue trackValue = trackValue();
            Map<String, Object> map = new HashMap<>(ext);
            map.put(trackValue.getScreenName(), screenName);
            trackEvent(trackValue.getAppViewScreen(), map);
        } catch (Exception e) {
            //ignore
        }
    }

    public static void appViewScreen(String screenName, String screenSubName) {
        try {
            TrackValue trackValue = trackValue();
            JSONObject properties = new JSONObject();
            properties.put(trackValue.getScreenName(), screenName);
            properties.put("screen_sub_name", screenSubName);
            trackEvent(trackValue.getAppViewScreen(), properties);
        } catch (Exception e) {
            //ignore
        }
    }

    public static void trackInstall(JSONObject properties) {
        ApiService.of(TrackApi.class).trackInstall(properties);
    }

    public static TrackValue trackValue() {
        return ApiService.of(TrackApi.class).getTrackValue();
    }

    public static void funcDurationStart(String funcName) {
        if (timeMap.containsKey(funcName)) return;
        timeMap.put(funcName, System.currentTimeMillis());
    }

    public static void funcDurationEnd(String funcName, int rid) {
        try {
            Long startTime = timeMap.get(funcName);
            if (startTime == null) return;

            long event_duration = System.currentTimeMillis() - startTime;
            timeMap.remove(funcName);

            if (event_duration > 24 * 60 * 60 * 1000) return; // 大于1天的异常值丢弃

            JSONObject properties = new JSONObject();
            properties.put("name", funcName);
            properties.put("rid", String.valueOf(rid));
            properties.put(TrackUtil.trackValue().getEventDuration(), event_duration);
            ApiService.of(TrackApi.class).trackEvent("FuncDuration", properties);
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "funcDurationEnd error! e=" + e);
        }
    }

    public static String getGameTypeSource(int gameType) {
        return String.format(Locale.CHINA, "game_type_%d", gameType);
    }

    public static void appendMapToJson(Map<String, Object> map, JSONObject jo) {
        if (map == null || map.isEmpty()) {
            return;
        }
        try {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                jo.put(entry.getKey(), entry.getValue());
            }
        } catch (JSONException ignored) {
        }
    }


    public static void request(String url, boolean isFirst) {
        request(url, isFirst, Collections.emptyMap());
    }

    public static void request(String url, boolean isFirst, Map<String, Object> data) {
        Map<String, Object> map = new ArrayMap<>();
        map.put("url", url);
        map.put("operate", "start");
        map.put("isFirstTime", isFirst);
        map.putAll(data);
        ApiService.of(TrackApi.class).trackEvent("Request", map);
    }

    public static void response(String url, boolean isSuccess, int code, boolean isFirst) {
        response(url, isSuccess, code, isFirst, Collections.emptyMap());
    }

    public static void response(String url, boolean isSuccess, int code, boolean isFirst, Map<String, Object> data) {
        Map<String, Object> map = new ArrayMap<>();
        map.put("url", url);
        map.put("operate", "end");
        map.put("isSuccess", isSuccess);
        map.put("code", code);
        map.putAll(data);
        map.put("isFirstTime", isFirst);
        ApiService.of(TrackApi.class).trackEvent("Request", map);
    }
}
