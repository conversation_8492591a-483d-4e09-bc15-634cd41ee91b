package com.wepie.lib.api.plugins.share;

import android.content.Context;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ToastUtil;

public class SharePluginHelper {

    /**
     * 包装 SharePlugin, 适配谷歌 pc 拦截
     */
    public static void onShare(SharePlugin plugin, Context context, ShareInfo info, ShareCallback callback) {
        if (plugin == null || context == null || info == null) {
            return;
        }
        if (LibBaseUtil.getBaseConfig().isGooglePc) {
            boolean canWebViewShare = plugin.canWebViewShare();
            boolean isAppInnerShare = plugin.isAppInnerShare();
            boolean isClipboardShare = plugin.isClipboardShare();
            if (isAppInnerShare || isClipboardShare || canWebViewShare) {
                plugin.onShare(context, info, callback);
            } else {
                // 外部大部分情况没有对 callback 的失败场景进行处理
                // 直接在这里 toast. 后期如果有数据优化需求，再添加更完善的处理逻辑。
                ToastUtil.show(R.string.device_not_support_the_op);
                LibBaseUtil.logInfo("SharePlugin", true, "on share not support for pc {}", plugin.getShareType());
            }
        } else {
            plugin.onShare(context, info, callback);
        }
    }
}
