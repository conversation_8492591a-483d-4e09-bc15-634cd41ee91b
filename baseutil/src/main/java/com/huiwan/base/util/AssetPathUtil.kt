package com.huiwan.base.util

object AssetPathUtil {

    @JvmStatic
    fun getUriFromAsset(path: String): String {
        if (path.startsWith("/")) {
            return "file:///android_asset$path"
        }
        return "file:///android_asset/$path"
    }

    @JvmStatic
    fun getImgUriFromAsset(path: String): String {
        if (path.startsWith("/")) {
            return getUriFromAsset("image$path")
        }
        return getUriFromAsset("image/$path")
    }
}