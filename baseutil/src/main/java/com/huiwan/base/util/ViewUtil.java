package com.huiwan.base.util;

import android.app.Activity;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.DimenRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.DefaultLifecycleObserver;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.R;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by bigwen on 2019-08-24.
 */
public class ViewUtil {

    public static void setMargins(View v, int l, int t, int r, int b) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMargins(l, t, r, b);
            p.setMarginStart(l);
            p.setMarginEnd(r);
            v.requestLayout();
        }
    }

    public static void setLeftMargins(View v, int l) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMargins(l, p.topMargin, p.getMarginEnd(), p.bottomMargin);
            p.setMarginStart(l);
            v.setLayoutParams(p);
        }
    }

    public static void setTopMargins(View v, int t) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMargins(p.getMarginStart(), t, p.getMarginEnd(), p.bottomMargin);
            v.setLayoutParams(p);
        }
    }


    public static void setRightMargins(View v, int r) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMarginEnd(r);
            v.setLayoutParams(p);
        }
    }

    public static void setBottomMargins(View v, int b) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMargins(p.getMarginStart(), p.topMargin, p.getMarginEnd(), b);
            v.setLayoutParams(p);
        }
    }

    public static int getTopMargins(View v) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            return p.topMargin;
        }
        return 0;
    }

    public static int getBottomMargins(View v) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            return p.bottomMargin;
        }
        return 0;
    }

    public static int getLeftMargins(View v) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            return p.getMarginStart();
        }
        return 0;
    }

    public static int getRightMargins(View v) {
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            return p.getMarginEnd();
        }
        return 0;
    }

    public static void setPaddingLeft(View v, int left) {
        v.setPaddingRelative(left, v.getPaddingTop(), v.getPaddingRight(), v.getPaddingBottom());
    }

    public static void setPaddingTop(View v, int top) {
        v.setPaddingRelative(v.getPaddingLeft(), top, v.getPaddingRight(), v.getPaddingBottom());
    }

    public static void setPaddingRight(View v, int right) {
        v.setPaddingRelative(v.getPaddingLeft(), v.getPaddingTop(), right, v.getPaddingBottom());
    }

    public static void setPaddingBottom(View v, int bottom) {
        v.setPaddingRelative(v.getPaddingLeft(), v.getPaddingTop(), v.getPaddingRight(), bottom);
    }

    public static void setTextLeftDrawable(TextView textView, Drawable drawable) {
        textView.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null);
    }

    public static void setTextRightDrawable(TextView textView, Drawable drawable) {
        textView.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null);
    }

    public static void setTextTopDrawable(TextView textView, Drawable drawable) {
        textView.setCompoundDrawablesWithIntrinsicBounds(null, drawable, null, null);
    }

    public static void setTextBottomDrawable(TextView textView, Drawable drawable) {
        textView.setCompoundDrawablesWithIntrinsicBounds(null, null, null, drawable);
    }

    public static void setTextEmptyDrawable(TextView textView, Drawable drawable) {
        textView.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
    }

    public static void setViewSize(View view, int width, int height) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        layoutParams.width = width;
        layoutParams.height = height;
        view.setLayoutParams(layoutParams);
    }

    public static void setViewHeight(View view, int height) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        layoutParams.height = height;
        view.setLayoutParams(layoutParams);
    }

    public static void setViewWidth(View view, int width) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        layoutParams.width = width;
        view.setLayoutParams(layoutParams);
    }

    public static int getDimen(@DimenRes int dimenRes) {
        return (int) (LibBaseUtil.getApplication().getResources().getDimension(dimenRes));
    }

    public static void removeFromParent(View view) {
        if (view.getParent() instanceof ViewGroup) {
            view.clearAnimation();
            ((ViewGroup) view.getParent()).removeView(view);
        }
    }

    public static void addView(ViewGroup viewGroup, View childView) {
        addView(viewGroup, childView, -1);
    }

    public static void addView(ViewGroup viewGroup, View childView, int index) {
        if (viewGroup == null) return;
        if (childView == null) return;
        if (childView.getParent() != null) {
            ViewGroup parentView = (ViewGroup) childView.getParent();
            if (parentView != viewGroup) {
                parentView.removeView(childView);
                viewGroup.addView(childView, index);
            }
        } else {
            viewGroup.addView(childView, index);
        }
    }

    /**
     * 根据textview的内容设置textview的宽度
     *
     * @param textView
     * @param bold
     */
    public static void setWidthForTextView(TextView textView, boolean bold) {
        Typeface oldTypeface = textView.getTypeface();
        FontUtil.setTextStyle(textView, bold ? Typeface.BOLD : Typeface.NORMAL);
        textView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        int textWidth = textView.getMeasuredWidth();
        ViewGroup.LayoutParams params = textView.getLayoutParams();
        if (params != null) {
            params.width = textWidth;
        }
        textView.setTypeface(oldTypeface);
        textView.requestLayout();
    }

    /**
     * 获取视图路径
     */
    public static String getXPath(View v) {
        StringBuilder builder = new StringBuilder();
        Stack<String> pathStack = new Stack<>();
        getXPath(v, pathStack);
        builder.append(v.getContext().getClass().getSimpleName());
        while (!pathStack.isEmpty()) {
            builder.append("/").append(pathStack.pop());
        }
        return builder.toString();
    }

    private static void getXPath(View v, Stack<String> pathStack) {
        int id = v.getId();
        String viewTag;
        if (id == View.NO_ID) {
            viewTag = String.valueOf(v.hashCode());
        } else {
            try {
                viewTag = v.getContext().getResources().getResourceEntryName(id);
            } catch (Exception e) {
                viewTag = v.hashCode() + "-" + id;
            }
        }
        pathStack.push(v.getClass().getSimpleName() + "@" + viewTag);
        ViewParent parent = v.getParent();
        if (parent instanceof View) {
            getXPath((View) parent, pathStack);
        }
    }

    public static Rect getRectInWindown(View v) {
        if (v == null) {
            return new Rect();
        }
        int[] location = new int[2];
        v.getLocationInWindow(location);
        return new Rect(
                location[0],
                location[1],
                location[0] + v.getMeasuredWidth(),
                location[1] + v.getMeasuredHeight()
        );
    }

    /**
     * 获取指定Activity的窗口根视图
     *
     * @param activity 当前活动窗口实例，用于获取窗口对象
     * @return 返回窗口的根视图对象，如果活动窗口、Window对象或DecorView不可用则返回null
     */
    public static View getRootView(Activity activity) {
        if (activity == null) {
            return null;
        }

        // 获取当前activity的Window实例
        Window window = activity.getWindow();
        if (window == null) {
            return null;
        }

        View rootView = activity.findViewById(android.R.id.content);
        if (rootView != null) {
            return rootView;
        }

        // 获取窗口装饰视图并校验
        View decorView = window.peekDecorView();
        if (decorView == null) {
            return null;
        }

        // 返回视图层级中的根节点
        return decorView.getRootView();
    }


    /**
     * 检查指定视图是否为目标子视图的父级视图（包含多级嵌套关系）
     *
     * @param rootView 待验证的父级视图对象，若为null直接返回false
     * @param childView 需要验证的子级视图对象，若为null直接返回false
     * @return true表示存在父子关系，false表示不存在父子关系或参数无效
     */
    public static boolean isParent(View rootView, View childView) {
        // 处理无效参数情况
        if (rootView == null || childView == null) {
            return false;
        }

        View tmp = childView;
        // 逐级向上遍历视图层级
        while (tmp != null) {
            // 找到匹配的父级视图
            if (tmp == rootView) {
                return true;
            }

            // 获取当前视图的父级对象
            ViewParent parent = tmp.getParent();
            if (parent instanceof View) {
                // 继续向上级查找
                tmp = (View) parent;
            } else {
                // 遇到非标准视图父级时终止循环
                tmp = null;
            }
        }
        return false;
    }

    public static <T extends View> IViewVisibilityObserver<T> getVisibilityChangedListener(T view) {
        Object object = view.getTag(R.id.view_visibility_listener_tag);
        if (object instanceof IViewVisibilityObserver) {
            return (IViewVisibilityObserver<T>) object;
        }
        IViewVisibilityObserver<T> globalLayoutListener = new ViewVisibilityObserver<T>(view);
        view.setTag(R.id.view_visibility_listener_tag, globalLayoutListener);
        return globalLayoutListener;
    }

    public static <T extends View> void addVisibilityChangedListener(T view, IViewVisibilityChangedListener<T> listener) {
        getVisibilityChangedListener(view).addListener(listener);
    }

    public static <T extends View> void setVisibilityChangedListener(T view, IViewVisibilityChangedListener<T> listener) {
        IViewVisibilityObserver<T> observer = getVisibilityChangedListener(view);
        observer.clear();
        observer.addListener(listener);
    }

    public static LifecycleOwner findLifecycleOwner(@NonNull View target) {
        FragmentActivity activity = ContextUtil.getFragmentActivityFromContext(target.getContext());
        if (activity == null) {
            return null;
        }
        Fragment fragment = findSupportFragment(target, activity);
        if (fragment != null) {
            return fragment.getViewLifecycleOwner();
        }
        return activity;
    }

    public static Fragment findSupportFragment(@NonNull View target) {
        FragmentActivity activity = ContextUtil.getFragmentActivityFromContext(target.getContext());
        if (activity == null) {
            return null;
        }
        return findSupportFragment(target, activity);
    }

    public static Fragment findSupportFragment(@NonNull View target, @NonNull FragmentActivity activity) {
        Map<View, Fragment> tempViewToSupportFragment = new ArrayMap<>();
        findAllSupportFragmentsWithViews(activity.getSupportFragmentManager().getFragments(), tempViewToSupportFragment);
        Fragment result = null;
        View activityRoot = activity.findViewById(android.R.id.content);
        View current = target;
        while (!current.equals(activityRoot)) {
            result = tempViewToSupportFragment.get(current);
            if (result != null) {
                break;
            }
            if (current.getParent() instanceof View) {
                current = (View) current.getParent();
            } else {
                break;
            }
        }

        tempViewToSupportFragment.clear();
        return result;
    }

    private static void findAllSupportFragmentsWithViews(
            @Nullable Collection<Fragment> topLevelFragments, @NonNull Map<View, Fragment> result) {
        if (topLevelFragments == null) {
            return;
        }
        for (Fragment fragment : topLevelFragments) {
            // getFragment()s in the support FragmentManager may contain null values, see #1991.
            if (fragment == null || fragment.getView() == null) {
                continue;
            }
            result.put(fragment.getView(), fragment);
            findAllSupportFragmentsWithViews(fragment.getChildFragmentManager().getFragments(), result);
        }
    }

    public interface IViewVisibilityObserver<T extends View> {
        void addListener(IViewVisibilityChangedListener<T> listener);

        void removeListener(IViewVisibilityChangedListener<T> listener);

        void clear();
    }

    private static class ViewVisibilityObserver<T extends View> implements ViewTreeObserver.OnGlobalLayoutListener, DefaultLifecycleObserver, IViewVisibilityObserver<T> {
        private final List<IViewVisibilityChangedListener<T>> listenerList = new ArrayList<>();
        private final T view;
        private final AtomicInteger flag;
        private LifecycleOwner owner = null;
        private boolean hasGlobal = false;

        public ViewVisibilityObserver(T view) {
            this.view = view;
            this.flag = new AtomicInteger(view.getVisibility());

            if (view.isAttachedToWindow()) {
                onAttach();
            }
            view.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
                @Override
                public void onViewAttachedToWindow(@NonNull View v) {
                    onAttach();
                }

                @Override
                public void onViewDetachedFromWindow(@NonNull View v) {
                    if (owner != null && owner.getLifecycle().getCurrentState() == Lifecycle.State.DESTROYED) {
                        onDetach();
                        return;
                    }
                    onDetach();
                    try {
                        onVisibilityChanged(view, View.GONE);
                    } catch (Exception e) {
                        LibBaseUtil.flogErr(e);
                    }
                }
            });
        }

        @Override
        public void addListener(IViewVisibilityChangedListener<T> listener) {
            listenerList.add(listener);
        }

        @Override
        public void removeListener(IViewVisibilityChangedListener<T> listener) {
            listenerList.remove(listener);
        }

        @Override
        public void clear() {
            listenerList.clear();
        }

        public void onAttach() {
            owner = findLifecycleOwner(view);
            if (owner != null) {
                owner.getLifecycle().addObserver(this);
            }
            hasGlobal = !checkInRecyclerView();
            if (hasGlobal) {
                view.getViewTreeObserver().addOnGlobalLayoutListener(this);
            }
        }

        private boolean checkInRecyclerView() {
            ViewParent parent = view.getParent();
            while (parent instanceof ViewGroup) {
                if (parent instanceof RecyclerView || parent instanceof ScrollView || parent instanceof ViewPager) {
                    return true;
                }
                parent = parent.getParent();
            }
            return false;
        }

        public void onDetach() {
            if (hasGlobal) {
                view.getViewTreeObserver().removeOnGlobalLayoutListener(ViewVisibilityObserver.this);
            }
            if (owner != null) {
                owner.getLifecycle().removeObserver(ViewVisibilityObserver.this);
            }
            owner = null;
        }

        @Override
        public void onStart(@NonNull LifecycleOwner owner) {
            onVisibilityChanged(view, view.getVisibility());
            if (view.isAttachedToWindow() && hasGlobal) {
                ViewTreeObserver observer = view.getViewTreeObserver();
                observer.removeOnGlobalLayoutListener(this);
                observer.addOnGlobalLayoutListener(this);
            }
        }

        @Override
        public void onStop(@NonNull LifecycleOwner owner) {
            onVisibilityChanged(view, View.GONE);
            if (view.isAttachedToWindow() && hasGlobal) {
                view.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        }

        @Override
        public void onGlobalLayout() {
            onVisibilityChanged(view, view.getVisibility());
        }

        private void onVisibilityChanged(T view, int visibility) {
            if (visibility == flag.get()) {
                return;
            }
            flag.set(visibility);
            for (IViewVisibilityChangedListener<T> listener : listenerList) {
                listener.onVisibilityChanged(view, visibility);
            }
        }
    }
}