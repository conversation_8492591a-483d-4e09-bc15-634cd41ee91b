package com.huiwan.base;

import android.os.Handler;
import android.os.Looper;
import android.os.Process;

import com.huiwan.platform.Hw;

public class HwAndroid implements Hw {

    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    @Override
    public void run(Runnable runnable) {
        mainHandler.post(runnable);
    }

    @Override
    public void runDelay(Runnable runnable, long delayTimeInMill) {
        mainHandler.postDelayed(runnable, delayTimeInMill);
    }

    @Override
    public void setThreadPriority(int priority) {
        try {
            Process.setThreadPriority(priority);
        } catch (SecurityException ignored) {

        }
    }

    @Override
    public int getThreadPriority() {
        int priority = Process.THREAD_PRIORITY_URGENT_DISPLAY;
        try {
            priority = Process.getThreadPriority(0);
        } catch (IllegalArgumentException ignored) {

        }
        return priority;
    }

    @Override
    public boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }
}
