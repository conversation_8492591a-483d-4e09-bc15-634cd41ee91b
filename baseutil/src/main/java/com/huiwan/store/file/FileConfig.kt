package com.huiwan.store.file

import android.content.Context
import android.os.Environment
import com.huiwan.base.BuildConfig
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.util.TextUtil
import com.huiwan.base.util.URIUtil
import com.huiwan.platform.ThreadUtil
import java.io.File

object FileConfig {
    private val BASE_CACHE_FOLDER = LibBaseUtil.getApplication().cacheDir.toString() +
            File.separator + FileCacheName.CACHE_PATH
    private val BASE_FILE_FOLDER = LibBaseUtil.getApplication().filesDir.toString()

    private val TEMP_FOLDER = BASE_CACHE_FOLDER + FileCacheName.TEMP_PATH
    private val RESOURCE_FOLDER = BASE_FILE_FOLDER + File.separator + FileCacheName.RESOURCE_PATH
    private val GAME_FOLDER = BASE_FILE_FOLDER + File.separator + FileCacheName.GAME_PATH

    @Deprecated("cocos game store path used app version  < 5.4.5")
    val DEPRECATED_GAME_FOLDER =
        LibBaseUtil.getApplication().filesDir.toString() + File.separator + FileCacheName.RESOURCE_PATH + "games" + File.separator
    private val OTHER_FOLDER = BASE_FILE_FOLDER + File.separator + FileCacheName.OTHER_PATH

    //该目录不参与磁盘清理计算
    private val EXTRA_FOLDER = BASE_FILE_FOLDER + File.separator + FileCacheName.EXTRA_PATH
    private val SHARE_FOLDER =
        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path +
                BuildConfig.BASE_FILE_DIR + BuildConfig.PHOTO_FILE_DIR;

    private var isEnsureFolder = false
    const val DATABASE_BACKUP_DIR_NAME = "db_backup"

    init {
        initBaseFolder()
    }

    private fun initBaseFolder() {
        if (isEnsureFolder) {
            return
        }

        ThreadUtil.runInOtherThread {
            val tempFileResult = FileManager.createFile(TEMP_FOLDER)
            val resourceFileResult = FileManager.createFile(RESOURCE_FOLDER)
            val gameFileResult = FileManager.createFile(GAME_FOLDER)
            val otherFileResult = FileManager.createFile(OTHER_FOLDER)
            isEnsureFolder =
                (tempFileResult && resourceFileResult && gameFileResult && otherFileResult)
        }
    }

    @JvmStatic
    fun getSysCachePath(subFileName: String): String {
        return LibBaseUtil.getApplication().cacheDir.toString() + File.separator + subFileName
    }

    @JvmStatic
    fun getWpCachePath(): String {
        return BASE_CACHE_FOLDER
    }

    @JvmStatic
    fun getBaseFolderPath(subFileName: String): String {
        return BASE_FILE_FOLDER + File.separator + subFileName
    }

    @JvmStatic
    fun getTempFolderPath(): String {
        return TEMP_FOLDER
    }

    @JvmStatic
    fun getResourceFolderPath(): String {
        return RESOURCE_FOLDER
    }

    @JvmStatic
    fun getGameFolder(): String {
        return GAME_FOLDER
    }

    fun getDefaultWebCachePath(context: Context): String {
        return context.getDir("webview", Context.MODE_PRIVATE).path
    }

    @JvmStatic
    fun getOtherFolderPath(): String {
        return OTHER_FOLDER
    }

    @JvmStatic
    fun getExtraFolderPath(): String {
        return EXTRA_FOLDER
    }

    /**
     * 获取临时缓存目录相关子文件
     * @param subFileName 子文件名
     * @return 子文件全路径
     */
    @JvmStatic
    fun getTempFileFullPath(subFileName: String): String {
        return getTempFolderPath() + subFileName
    }

    @JvmStatic
    fun getWebProxyCacheDir(): String {
        return File(LibBaseUtil.getCacheDir(), FileCacheName.WEB_CACHE_DIR).path
    }

    /**
     * 获取资源目录相关子文件
     * @param subFileName 子文件名
     * @return 子文件全路径
     */
    @JvmStatic
    fun getSourceFileFullPath(subFileName: String): String {
        return getResourceFolderPath() + subFileName
    }

    /**
     * 获取游戏目录相关子文件
     * @param subFileName 子文件名
     * @return 子文件全路径
     */
    fun getGameForTypeFolder(gameType: String, subFileName: String): String {
        val path =
            getGameFolder() + FileCacheName.GAME_TYPE_PRE + gameType + File.separator + subFileName;
        return path
    }

    /**
     * 获取其它目录相关子文件
     * @param subFileName 子文件名
     * @return 子文件全路径
     */
    @JvmStatic
    fun getOtherFileFullPath(subFileName: String): String {
        return getOtherFolderPath() + subFileName
    }

    @JvmStatic
    fun getExtraFileFullPath(subFileName: String): String {
        return getExtraFolderPath() + subFileName
    }

    @JvmStatic
    fun getUserFileFullPath(userUid: Int, fileName: String): String {
        return getExtraFileFullPath(userUid.toString()) + File.separator + fileName
    }

    @JvmStatic
    fun getImageCacheDirPath(): String {
        val path = getTempFileFullPath(FileCacheName.IMAGE_PATH)
        FileManager.createFile(path)
        return path
    }

    fun getUserBpCacheDir(uid: Int): String {
        return File(LibBaseUtil.getCacheDir(), uid.toString()).path
    }

    @JvmStatic
    fun getImageCacheSubDirPath(subDir: String): String {
        return getTempFileFullPath(FileCacheName.IMAGE_PATH + subDir)
    }

    @JvmStatic
    fun getAudioCacheDirPath(): String {
        val path = getTempFileFullPath(FileCacheName.AUDIO_PATH)
        FileManager.createFile(path)
        return path
    }

    @JvmStatic
    fun getSvgaCacheDirPath(): String {
        val path = getTempFileFullPath(FileCacheName.SVGA_PATH)
        FileManager.createFile(path)
        return path
    }

    @JvmStatic
    fun getKtvCacheDirPath(): String {
        return getTempFileFullPath(FileCacheName.KTV_PATH)
    }

    @JvmStatic
    fun getGuessSongCacheDirPath(): String {
        return getTempFileFullPath(FileCacheName.GUESS_SONG_PATH)
    }

    @JvmStatic
    fun getVideoCacheDirPath(fileName: String): String {
        return getTempFileFullPath(FileCacheName.VIDEO_PATH) + fileName
    }

    @JvmStatic
    fun getVideoFeedsCacheDirPath(fileName: String): String {
        return getOtherFileFullPath(FileCacheName.VIDEO_PATH) + fileName
    }

    @JvmStatic
    fun getRecordDirPath(): String {
        val path = getTempFileFullPath(FileCacheName.AUDIO_PATH + FileCacheName.RECORD_DIR)
        FileManager.createFile(path)
        return path
    }

    /**
     * 礼物目录
     */
    @JvmStatic
    fun getGiftDirPath(subFilePath: String): String {
        val folderPath = getSourceFileFullPath(FileCacheName.GIFT_PATH)
        val path = folderPath + subFilePath
        FileManager.createFile(folderPath)
        return path
    }

    /**
     * 道具目录
     */
    @JvmStatic
    fun getPropDirPath(subFilePath: String): String {
        val folderPath = getSourceFileFullPath(FileCacheName.PROP_PATH)
        val path = folderPath + subFilePath
        FileManager.createFile(folderPath)
        return path
    }

    /**
     * 日志目录
     */
    @JvmStatic
    fun getLogFolderPath(subFileName: String): String {
        val folderPath = getExtraFileFullPath(FileCacheName.LOG_FOLDER_PATH)
        val path = folderPath + subFileName
        FileManager.createFile(folderPath)
        return path
    }

    @JvmStatic
    fun getFLogFolderPath(subFileName: String): String {
        val folderPath = getExtraFileFullPath(FileCacheName.FIREBASE_FOLDER_LOG_PATH)
        val path = folderPath + subFileName
        FileManager.createFile(folderPath)
        return path
    }

    @JvmStatic
    fun getShareFolderPath(): String {
        return SHARE_FOLDER
    }

    @Volatile
    private var externalFilesDir: File? = null

    @JvmStatic
    fun getUnityFolder(): String {
        // getUnityFolder方法会在首页打开后在子线程中短时间内执行很多次，
        // 而ContextImpl.getExternalFilesDir会占用ContextImpl的mSync锁，并且里面有Binder操作，
        // 短时间多次执行很容易导致主线程调用到ContextImpl对应方法时由于获取不到mSync锁被阻塞，发生ANR，
        // 因此这里提取一个静态变量，减少ContextImpl.getExternalFilesDir方法的调用次数
        if (externalFilesDir == null) {
            externalFilesDir = LibBaseUtil.getApplication().getExternalFilesDir(null)
        }
        return if (externalFilesDir == null)
            ""
        else
            externalFilesDir.toString() + "/"
    }

    @JvmStatic
    fun getUnityBinFolder(): String {
        return getUnityFolder() + "bin/"
    }

    @JvmStatic
    fun getUnityResFolder(): String {
        return getUnityFolder() + "AssetBundles/"
    }

    @JvmStatic
    fun getUnityIL2CPPFolder(): String {
        return getUnityFolder() + "il2cpp/"
    }

    @JvmStatic
    fun getUnityCacheFolder(): String {
        return getUnityFolder() + "UnityCache/Shared/"
    }

    @JvmStatic
    fun getUnityLoadingImgFolder(): String {
        return getUnityResFolder() + "LoadingImg/"
    }

    @JvmStatic
    fun getLoadingImgPath(url: String): String {
        if (TextUtil.isEmpty(url)) {
            return ""
        }
        val index = url.lastIndexOf("/")
        return if (index == -1) {
            url
        } else url.substring(index + 1)
    }

    /**
     * file/games/game_[gameType]/cocos/[fileName]
     */
    @JvmStatic
    fun getCocosGameFolder(gameType: String, fileName: String): String {
        return getGameForTypeFolder(gameType, FileCacheName.COCOS_FOLDER) + fileName
    }

    /**
     * file/games/game_[gameType]/cocos/cocos_zip/[fileName]
     */
    @JvmStatic
    fun getCocosGameZipPath(gameType: String, fileName: String): String {
        return getCocosGameFolder(gameType, FileCacheName.COCOS_ZIP_FOLDER) + fileName
    }

    /**
     * file/games/game_[gameType]/cocos/cocos_unzip/[fileName]
     */
    @JvmStatic
    fun getCocosGameUnZipPath(gameType: String, fileName: String): String {
        return getCocosGameFolder(gameType, FileCacheName.COCOS_UNZIP_FOLDER) + fileName
    }

    fun getCocosMatchTempUnZipDir(gameType: String?): String {
        return getCocosGameFolder(gameType!!, "temp_" + FileCacheName.COCOS_UNZIP_FOLDER)
    }

    fun getCocosGameTempUnZipDir(gameType: String?): String {
        return getCocosGameFolder(gameType!!, "temp_game/" + FileCacheName.COCOS_UNZIP_FOLDER)
    }

    /**
     * file/games/game_[gameType]/cocos/cocos_music/
     */
    @JvmStatic
    fun getCocosMusicFolder(gameType: String): String {
        return getCocosGameFolder(gameType, FileCacheName.COCOS_MUSIC_FOLDER)
    }

    @JvmStatic
    fun getCocosErrorFolder(gameType: Int, fileName: String): String {
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            .toString() +
                File.separator + FileCacheName.CACHE_PATH + File.separator + FileCacheName.GAME_TYPE_PRE +
                gameType + File.separator + fileName
    }

    @JvmStatic
    fun getUserFolder(uid: String): String {
        val folderPath = getBaseFolderPath(uid)
        val file = File(folderPath)
        if (!file.exists()) {
            file.mkdirs()
        }
        return folderPath
    }

    @JvmStatic
    fun getCustomCacheFolder(): String {
        val folderPath =
            getBaseFolderPath(if (LibBaseUtil.envDebug()) "cache_debug" else "cache_release")
        val file = File(folderPath)
        if (!file.exists()) {
            file.mkdirs()
        }
        return folderPath
    }

    fun getUnityMetaDataPath(): String {
        return getUnityIL2CPPFolder() + "Metadata/global-metadata.dat"
    }

    @JvmStatic
    fun getMusicHumAudioPath(): String {
        val path = getOtherFileFullPath(FileCacheName.AUDIO_PATH)
        FileManager.createFile(path)
        return path
    }

    @JvmStatic
    fun getHeadDecorationPath(animationUrl: String): String {
        return getPropDirPath(
            FileCacheName.HEAD_DECOR_PATH +
                    URIUtil.url2LocalPath(animationUrl, "head_decor_")
        )
    }
}