package com.huiwan.store.file

import android.os.Build
import android.util.Log
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.WTrace
import java.io.File
import java.io.FilenameFilter
import java.nio.file.Files
import java.util.Locale

object FileManager {
    const val TAG = "FileManager"

    @JvmStatic
    fun getFile(fullPath: String): File {
        checkFilePath(fullPath)
        return File(fullPath)
    }

    @JvmStatic
    fun getFile(fullPath: String, isCheck: Boolean = true): File {
        if (isCheck) {
            checkFilePath(fullPath)
        }
        return File(fullPath)
    }

    @JvmStatic
    fun getFileNotCheck(fullPath: String): File {
        return File(fullPath)
    }

    @JvmStatic
    fun getFile(parentPath: String, fileName: String): File {
        checkFilePath(parentPath)
        return File(parentPath, fileName)
    }

    @JvmStatic
    fun createFile(fullPath: String): Boolean {
        checkFilePath(fullPath)
        return try {
            val file = File(fullPath)
            if (file.exists()) {
                true
            } else {
                file.mkdirs()
            }
        } catch (ex: Exception) {
            LibBaseUtil.logErr(TAG, true, "createFile fail! stack: {}", Log.getStackTraceString(ex))
            false
        }
    }

    fun createFileNotCheck(fullPath: String): Boolean {
        return try {
            val file = File(fullPath)
            if (file.exists()) {
                true
            } else {
                file.mkdirs()
            }
        } catch (ex: Exception) {
            LibBaseUtil.logErr(TAG, true, "createFile fail! stack: {}", Log.getStackTraceString(ex))
            false
        }
    }

    /**
     * 文件夹复制操作
     * @param srcDirPath 源文件夹路径
     * @param destDirPath 目标文件夹路径
     */
    @JvmStatic
    fun copyFileDir(srcDirPath: String, destDirPath: String) {
        checkFilePath(destDirPath)

        try {
            val srcFile = File(srcDirPath)
            val destFile = File(destDirPath)

            if (srcFile.exists()) {
                destFile.mkdirs()

                val files = srcFile.listFiles()
                if (files.isNullOrEmpty()) {
                    return
                }

                val dirPath = if (destDirPath.endsWith(File.separator)) {
                    destDirPath
                } else {
                    destDirPath + File.separator
                }

                for (src in files) {
                    if (src.isDirectory) {
                        copyFileDir(src.path, dirPath + src.name + File.separator)
                    } else {
                        moveAndDelete(src.path, dirPath + src.name)
                    }
                }
            }
        } catch (ex: Exception) {
            LibBaseUtil.logErr(
                TAG,
                true,
                "copyFileDir fail! stack: {}",
                Log.getStackTraceString(ex)
            )
        }
    }

    /**
     * 文件复制操作
     * @param srcPath 源文件路径
     * @param destPath 目标文件路径
     */
    @JvmStatic
    fun moveAndDelete(srcPath: String, destPath: String) {
        WTrace.warningCallInMain("FileManager#copyFile")
        checkFilePath(destPath)

        val srcFile = File(srcPath)
        val destFile = File(destPath)
        if (!srcFile.isDirectory && srcFile.exists()
        ) {
            var flag = false
            if (!destFile.isDirectory && !destFile.exists()) {
                try {
                    flag = srcFile.renameTo(destFile)
                } catch (e: Exception) {
                    LibBaseUtil.logErr(TAG, false, "move error! msg=$e")
                }
                if (!flag) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        try {
                            Files.move(srcFile.toPath(), destFile.toPath())
                            flag = true
                        } catch (e: Exception) {
                            LibBaseUtil.logErr(TAG, false, "move error! msg=$e")
                        }
                    }
                }
            }
            srcFile.delete()
        } else {
            LibBaseUtil.logErr(TAG, false, "copyFile ignore! srcPath=$srcPath, destPath=$destPath")
        }
    }

    /**
     * 文件删除操作
     * @param filePath 目标文件目录
     */
    @JvmStatic
    fun deleteFileOrDir(filePath: String) {
        try {
            val file = File(filePath)
            if (file.isDirectory) {
                deleteDir(file.absolutePath, null)
            } else {
                file.delete()
            }
        } catch (ex: Exception) {
            LibBaseUtil.logErr(TAG, true, "delete fail! stack: {}", Log.getStackTraceString(ex))
        }
    }

    /**
     * 文件夹删除操作
     * @param fileDirPath 目标文件夹路径
     * @param filter 文件名过滤器
     */
    @JvmStatic
    fun deleteDir(fileDirPath: String, filter: FilenameFilter?) {
        val fileDir = File(fileDirPath)
        if (fileDir.isDirectory) {
            val files = fileDir.listFiles(filter)
            if (!files.isNullOrEmpty()) {
                for (file in files) {
                    if (file.isDirectory) {
                        deleteDir(file.path, filter)
                    } else {
                        file.delete()
                    }
                }
            }

            LibBaseUtil.logInfo(
                "FileManager",
                "deleteDir, dir path: ${fileDirPath}, length: ${fileDir.length()}"
            )
            if (fileDir.listFiles().isNullOrEmpty()) {
                fileDir.delete()
            }
        }
    }

    @JvmStatic
    fun deleteUnityDir(fileDirPath: String, removeDir: Boolean, filter: FilenameFilter?) {
        val fileDir = File(fileDirPath)
        if (fileDir.isDirectory) {
            val files = fileDir.listFiles(filter)
            if (!files.isNullOrEmpty()) {
                for (file in files) {
                    deleteFileOrDir(file.path)
                }
            }

            if (removeDir) {
                deleteFileOrDir(fileDirPath)
            }
        }
    }

    @JvmStatic
    fun getFileSize(filePath: String, fileNameFilter: FilenameFilter? = null): Long {
        var size = 0L
        try {
            val file = File(filePath)
            if (file == null || !file.exists()) return size
            if (file.isFile) {
                return file.length()
            }
            val fileList = file.listFiles(fileNameFilter)
            if (fileList.isNotEmpty()) {
                for (f in fileList) {
                    if (f.isDirectory) {
                        size += getFileSize(f.path, fileNameFilter)
                    } else {
                        size += f.length()
                    }
                }
            }
        } catch (ex: Exception) {
            LibBaseUtil.logErr(
                TAG,
                true,
                "getFileSize fail! path: $filePath, stack: {}",
                Log.getStackTraceString(ex)
            )
        }
        return size
    }

    @JvmStatic
    fun getFileSizeAsync(vararg filePath: String, fileNameFilter: FilenameFilter? = null): Long {
        var size = 0L
        for (path in filePath) {
            if (path.isNullOrEmpty()) {
                continue
            }
            size += getFileSize(path, fileNameFilter)
        }
        return size
    }

    @JvmStatic
    fun getFileSizeAsyncWithFilter(fileNameFilter: FilenameFilter?, vararg filePath: String): Long {
        var size = 0L
        for (path in filePath) {
            if (path.isNullOrEmpty()) {
                continue
            }
            size += getFileSize(path, fileNameFilter)
        }
        return size
    }

    private fun checkFilePath(path: String) {
        if (LibBaseUtil.buildDebug()) {
            val result = path.startsWith(FileConfig.getTempFolderPath()) ||
                    path.startsWith(FileConfig.getResourceFolderPath()) ||
                    path.startsWith(FileConfig.getGameFolder()) ||
                    path.startsWith(FileConfig.getOtherFolderPath()) ||
                    path.startsWith(FileConfig.getExtraFolderPath()) ||
                    path.startsWith(FileConfig.getUnityFolder()) ||
                    path.startsWith(FileConfig.getUnityResFolder()) ||
                    path.startsWith(FileConfig.getUnityBinFolder()) ||
                    path.startsWith(FileConfig.getUnityCacheFolder())

            if (!result) {
                val msg = "File can't clear!!! path=$path"
                ToastUtil.debugShow(msg)
                LibBaseUtil.flogErr(Exception(msg))
            }
        }
    }

    /**
     * 单位转换
     */
    @JvmStatic
    fun getUnit(size: Long): String {
        if (size <= 0) {
            return "0 KB"
        }

        val units = mutableListOf("B", "KB", "MB", "GB", "TB")
        var index = 0
        var showSize = size.toFloat()
        while (showSize > 1024F && index < 4) {
            showSize /= 1024F
            index++
        }
        return String.format(Locale.ENGLISH, "%.1f %s", showSize, units[index])
    }
}